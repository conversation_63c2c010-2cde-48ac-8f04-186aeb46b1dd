import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { ClassModel, SubjectModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import ClassesManager from '@/components/ClassesManager';

export default async function ClassesPage() {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const classes = ClassModel.getAll();
  const subjects = SubjectModel.getAll();

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Classes & Subjects</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your educational structure by organizing classes and subjects.
          </p>
        </div>

        <ClassesManager initialClasses={classes} initialSubjects={subjects} />
      </div>
    </AdminLayout>
  );
}
