import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import Link from 'next/link';

interface BookPageProps {
  params: Promise<{ id: string }>;
}

export default async function BookPage({ params }: BookPageProps) {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const { id } = await params;
  const bookId = parseInt(id);
  
  if (isNaN(bookId)) {
    redirect('/admin/books');
  }

  const book = BookModel.getById(bookId);
  
  if (!book) {
    redirect('/admin/books');
  }

  const images = BookModel.getImages(bookId);

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/admin/books" className="hover:text-gray-700">Books</Link>
              <span>›</span>
              <span>{book.title}</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">{book.title}</h1>
            <p className="mt-1 text-sm text-gray-600">
              {book.class_name} • {book.subject_name}
            </p>
            {book.description && (
              <p className="mt-2 text-gray-700">{book.description}</p>
            )}
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/admin/books/${book.id}/upload`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700"
            >
              Upload PDF
            </Link>
            <Link
              href={`/admin/books/${book.id}/edit`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
            >
              Edit Book
            </Link>
          </div>
        </div>

        {/* Status and Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    book.status === 'completed' ? 'bg-green-500' :
                    book.status === 'processing' ? 'bg-yellow-500' :
                    'bg-gray-500'
                  }`}>
                    <span className="text-white text-sm font-medium">
                      {book.status === 'completed' ? '✓' : 
                       book.status === 'processing' ? '⚡' : '📝'}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Status</dt>
                    <dd className="text-lg font-medium text-gray-900 capitalize">{book.status}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">📄</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pages</dt>
                    <dd className="text-lg font-medium text-gray-900">{book.uploaded_pages}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">✓</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Processed</dt>
                    <dd className="text-lg font-medium text-gray-900">{book.processed_pages}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">❓</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Questions</dt>
                    <dd className="text-lg font-medium text-gray-900">{book.total_questions}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Images Grid */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Book Pages
              </h3>
              {images.length === 0 && (
                <Link
                  href={`/admin/books/${book.id}/upload`}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                >
                  Upload First Images
                </Link>
              )}
            </div>

            {images.length > 0 ? (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {images.map((image: any) => (
                  <div key={image.id} className="relative group bg-gray-50 rounded-lg p-4">
                    <div className="aspect-w-3 aspect-h-4 mb-3">
                      <img
                        src={`/api/admin/images/${image.id}/thumbnail`}
                        alt={image.original_name}
                        className="object-cover rounded-md"
                      />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        Page {image.upload_order || image.id}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">
                        {image.page_type || 'content'}
                      </p>
                      <div className="flex items-center space-x-2">
                        {image.has_ocr ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            OCR ✓
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            No OCR
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-2xl">📄</span>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">No pages yet</h3>
                <p className="mt-2 text-sm text-gray-500">
                  Upload scanned images to start building your textbook.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/admin/books/${book.id}/upload`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700"
                  >
                    Upload Images
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
