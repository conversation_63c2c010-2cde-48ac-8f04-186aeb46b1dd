import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import Link from 'next/link';

interface BookUploadPageProps {
  params: Promise<{ id: string }>;
}

export default async function BookUploadPage({ params }: BookUploadPageProps) {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const { id } = await params;
  const bookId = parseInt(id);
  
  if (isNaN(bookId)) {
    redirect('/admin/books');
  }

  const book = BookModel.getById(bookId);
  
  if (!book) {
    redirect('/admin/books');
  }

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/admin/books" className="hover:text-gray-700">Books</Link>
              <span>›</span>
              <Link href={`/admin/books/${book.id}`} className="hover:text-gray-700">{book.title}</Link>
              <span>›</span>
              <span>Upload</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Upload Images</h1>
            <p className="mt-1 text-sm text-gray-600">
              Upload scanned pages for "{book.title}"
            </p>
          </div>
          <Link
            href={`/admin/books/${book.id}`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
          >
            Back to Book
          </Link>
        </div>

        {/* Upload Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-blue-400 text-lg">📖</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                New Book Container Workflow
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p className="mb-2">You're now uploading images to a specific book container. This is much better than the old approach!</p>
                <ol className="list-decimal list-inside space-y-1">
                  <li>Upload your scanned textbook pages</li>
                  <li>Assign page types (cover, contents, chapters)</li>
                  <li>Set page numbers and organize content</li>
                  <li>Process OCR and extract questions</li>
                  <li>Review and manage your textbook</li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Interface */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Upload Textbook Pages
            </h3>
            
            <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">📤</span>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Upload functionality coming soon</h3>
              <p className="mt-2 text-sm text-gray-500">
                The book container system is now ready. Upload functionality will be integrated next.
              </p>
              <div className="mt-6">
                <p className="text-sm text-gray-600">
                  For now, you can use the old upload system and we'll migrate the images to this book later.
                </p>
                <div className="mt-4 flex justify-center space-x-3">
                  <Link
                    href="/admin/upload"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Use Old Upload (Temporary)
                  </Link>
                  <Link
                    href={`/admin/books/${book.id}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    Back to Book
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Current Book Info */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Book Information
            </h3>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">Title</dt>
                <dd className="mt-1 text-sm text-gray-900">{book.title}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Class</dt>
                <dd className="mt-1 text-sm text-gray-900">{book.class_name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Subject</dt>
                <dd className="mt-1 text-sm text-gray-900">{book.subject_name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    book.status === 'completed' ? 'bg-green-100 text-green-800' :
                    book.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {book.status}
                  </span>
                </dd>
              </div>
              {book.description && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900">{book.description}</dd>
                </div>
              )}
            </dl>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
