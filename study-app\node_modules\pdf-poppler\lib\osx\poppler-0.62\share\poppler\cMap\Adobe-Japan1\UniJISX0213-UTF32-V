%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%DocumentNeededResources: CMap (UniJISX0213-UTF32-H)
%%IncludeResource: ProcSet (CIDInit)
%%IncludeResource: CMap (UniJISX0213-UTF32-H)
%%BeginResource: CMap (UniJISX0213-UTF32-V)
%%Title: (UniJISX0213-UTF32-V Adobe Japan1 6)
%%Version: 1.006
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2015 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/UniJISX0213-UTF32-H usecmap

/CIDSystemInfo 3 dict dup begin
  /Registry (Adobe) def
  /Ordering (Japan1) def
  /Supplement 6 def
end def

/CMapName /UniJISX0213-UTF32-V def
/CMapVersion 1.006 def
/CMapType 1 def

/XUID [1 10 25615] def

/WMode 1 def

100 begincidchar
<00002015> 7892
<00002016> 7895
<00002025> 7898
<00002026> 7897
<00002190> 738
<00002191> 736
<00002192> 739
<00002193> 737
<000021c4> 8311
<000021c5> 8310
<000021c6> 8312
<000021e6> 8012
<000021e7> 8014
<000021e8> 8011
<000021e9> 8013
<0000239b> 12148
<0000239c> 12168
<0000239d> 12147
<0000239e> 12150
<0000239f> 12168
<000023a0> 12149
<000023a1> 12156
<000023a2> 12168
<000023a3> 12155
<000023a4> 12158
<000023a5> 12168
<000023a6> 12157
<000023a7> 8168
<000023a8> 8167
<000023a9> 8166
<000023aa> 12168
<000023ab> 8172
<000023ac> 8171
<000023ad> 8170
<0000250c> 7495
<0000250d> 7497
<0000250e> 7496
<0000250f> 7498
<00002510> 7503
<00002511> 7505
<00002512> 7504
<00002513> 7506
<00002514> 7491
<00002515> 7493
<00002516> 7492
<00002517> 7494
<00002518> 7499
<00002519> 7501
<0000251a> 7500
<0000251b> 7502
<0000251c> 7523
<0000251d> 7527
<0000251e> 7525
<0000251f> 7524
<00002520> 7526
<00002521> 7529
<00002522> 7528
<00002525> 7535
<00002526> 7533
<00002527> 7532
<00002528> 7534
<00002529> 7537
<0000252a> 7536
<0000252b> 7538
<0000252c> 7515
<00002530> 7516
<00002534> 7507
<00002538> 7508
<00002540> 7541
<00002541> 7540
<00002542> 7542
<00002543> 7547
<00002544> 7549
<00002545> 7546
<00002546> 7548
<00002547> 7553
<00002548> 7552
<0000261c> 8221
<0000261d> 8219
<0000261e> 8222
<0000261f> 8220
<00002702> 12178
<000027a1> 8209
<0000301c> 7894
<0000301d> 7956
<0000301f> 7957
<00003041> 7918
<00003043> 7919
<00003045> 7920
<00003047> 7921
<00003049> 7922
<00003063> 7923
<00003083> 7924
<00003085> 7925
<00003087> 7926
<0000308e> 7927
<0000309b> 8272
<0000309c> 8271
<000030a0> 16331
<000030a1> 7928
endcidchar

79 begincidchar
<000030a3> 7929
<000030a5> 7930
<000030a7> 7931
<000030a9> 7932
<000030c3> 7933
<000030e3> 7934
<000030e5> 7935
<000030e7> 7936
<000030ee> 7937
<000030fc> 7891
<00003300> 8350
<00003303> 8338
<00003304> 11960
<00003305> 8333
<00003306> 11961
<00003307> 11965
<00003308> 11963
<00003309> 11968
<0000330a> 11966
<0000330b> 11970
<0000330c> 11972
<0000330d> 7950
<00003314> 7941
<00003315> 8340
<00003316> 8330
<00003317> 11980
<00003318> 8339
<00003319> 11982
<0000331e> 8353
<00003322> 8329
<00003323> 8348
<00003324> 11991
<00003325> 11993
<00003326> 7951
<00003327> 7945
<0000332a> 8356
<0000332b> 7953
<0000332d> 11999
<00003331> 8358
<00003332> 12005
<00003333> 8334
<00003336> 7947
<00003337> 12014
<00003338> 12016
<00003339> 8343
<0000333a> 12017
<0000333b> 8349
<0000333c> 12010
<0000333d> 12018
<00003341> 12019
<00003342> 8347
<00003347> 8357
<00003348> 12027
<00003349> 7940
<0000334a> 7954
<0000334d> 7943
<0000334e> 8337
<00003351> 7948
<00003352> 12034
<00003353> 12038
<00003354> 12035
<00003357> 8344
<0000337b> 12044
<0000337c> 12043
<0000337d> 12042
<0000337e> 12041
<0000337f> 8324
<0000ff0c> 8268
<0000ff0e> 8274
<0000ff1a> 12101
<0000ff1d> 7917
<0000ff3b> 7903
<0000ff3d> 7904
<0000ff3f> 7890
<0000ff5b> 7905
<0000ff5c> 7896
<0000ff5d> 7906
<0000ff5e> 7894
<0000ffe3> 7889
endcidchar

37 begincidrange
<000023b0> <000023b1> 16350
<00002500> <00002501> 7481
<00002502> <00002503> 7479
<00002504> <00002505> 7485
<00002506> <00002507> 7483
<00002508> <00002509> 7489
<0000250a> <0000250b> 7487
<00002523> <00002524> 7530
<0000252d> <0000252f> 7517
<00002531> <00002533> 7520
<00002535> <00002537> 7509
<00002539> <0000253b> 7512
<0000253d> <0000253f> 7543
<00002549> <0000254a> 7550
<00003001> <00003002> 7887
<00003008> <00003011> 7907
<00003014> <00003015> 7901
<00003016> <00003017> 16329
<00003018> <00003019> 12139
<00003095> <00003096> 8264
<000030f5> <000030f6> 7938
<000031f0> <000031f9> 16333
<000031fa> <000031ff> 16344
<00003301> <00003302> 11958
<0000330e> <00003313> 11973
<0000331a> <0000331d> 11984
<0000331f> <00003321> 11988
<00003328> <00003329> 11996
<0000332e> <00003330> 12002
<00003334> <00003335> 12008
<0000333e> <00003340> 12020
<00003343> <00003346> 12023
<0000334b> <0000334c> 12028
<0000334f> <00003350> 12030
<00003355> <00003356> 12039
<0000ff08> <0000ff09> 7899
<0000ff5f> <0000ff60> 12141
endcidrange

endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
