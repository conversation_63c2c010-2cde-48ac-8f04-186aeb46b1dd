import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import Link from 'next/link';
import db from '@/lib/database';
import CopyTextButton from '@/components/CopyTextButton';

interface BookOCRPageProps {
  params: Promise<{ id: string }>;
}

async function getBookOCR(bookId: number) {
  try {
    // Get book info
    const book = BookModel.findById(bookId);
    if (!book) return null;

    // Get all OCR text for this book
    const ocrData = db.prepare(`
      SELECT 
        i.id as image_id,
        i.upload_order,
        i.page_type,
        i.original_name,
        ocr.content,
        ocr.processed,
        ocr.created_at
      FROM images i
      LEFT JOIN ocr_text ocr ON i.id = ocr.image_id
      WHERE i.book_id = ?
      ORDER BY i.upload_order ASC
    `).all(bookId);

    return { book, ocrData };
  } catch (error) {
    console.error('Error fetching book OCR data:', error);
    return null;
  }
}

export default async function BookOCRPage({ params }: BookOCRPageProps) {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/admin/login');
  }

  const { id } = await params;
  const bookId = parseInt(id);
  
  const data = await getBookOCR(bookId);
  
  if (!data) {
    return (
      <AdminLayout>
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Book Not Found</h1>
              <p className="text-gray-600 mb-6">The requested book could not be found.</p>
              <Link
                href="/admin/books"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Back to Books
              </Link>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const { book, ocrData } = data;
  const pagesWithOCR = ocrData.filter(page => page.content);
  const totalText = pagesWithOCR.map(page => page.content).join('\n\n');

  return (
    <AdminLayout>
      <div className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  All OCR Text - {book.title}
                </h1>
                <p className="text-gray-600">
                  {pagesWithOCR.length} of {ocrData.length} pages have OCR text
                </p>
              </div>
              <div className="flex space-x-3">
                <Link
                  href={`/admin/books/${book.id}`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                >
                  Back to Book
                </Link>
                {totalText && (
                  <CopyTextButton text={totalText} />
                )}
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">📄</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Pages</dt>
                      <dd className="text-lg font-medium text-gray-900">{ocrData.length}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">✓</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">With OCR</dt>
                      <dd className="text-lg font-medium text-gray-900">{pagesWithOCR.length}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <span className="text-white text-sm font-medium">📝</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Characters</dt>
                      <dd className="text-lg font-medium text-gray-900">{totalText.length.toLocaleString()}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* OCR Content */}
          <div className="space-y-6">
            {ocrData.map((page: any) => (
              <div key={page.image_id} className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">
                      Page {page.upload_order}
                    </h3>
                    <div className="flex items-center space-x-3">
                      {page.content ? (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                          OCR Available
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                          No OCR
                        </span>
                      )}
                      <Link
                        href={`/admin/images/${page.image_id}/ocr`}
                        className="text-sm text-blue-600 hover:text-blue-500"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
                
                <div className="px-6 py-4">
                  {page.content ? (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="whitespace-pre-wrap text-sm text-gray-900 font-mono">
                        {page.content}
                      </pre>
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p>No OCR text available for this page</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
