//========================================================================
//
// PDFDocBuilder.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright 2010 Hib Eris <<EMAIL>>
// Copyright 2010, 2018 Albert Astals Cid <<EMAIL>>
//
//========================================================================

#ifndef PDFDOCBUILDER_H
#define PDFDOCBUILDER_H

#include "PDFDoc.h"
class GooString;

//------------------------------------------------------------------------
// PDFDocBuilder
//
// PDFDocBuilder is an abstract class that specifies the interface for
// constructing PDFDocs.
//------------------------------------------------------------------------

class PDFDocBuilder {

public:

  PDFDocBuilder() = default;
  virtual ~PDFDocBuilder() = default;

  PDFDocBuilder(const PDFDocBuilder &) = delete;
  PDFDocBuilder& operator=(const PDFDocBuilder &) = delete;

  // Builds a new PDFDoc. Returns a PDFDoc. You should check this PDFDoc
  // with PDFDoc::isOk() for failures.
  // The caller is responsible for deleting ownerPassword, userPassWord and guiData.
  virtual PDFDoc *buildPDFDoc(const GooString &uri, GooString *ownerPassword = NULL,
      GooString *userPassword = NULL, void *guiDataA = NULL) = 0;

  // Returns gTrue if the builder supports building a PDFDoc from the URI.
  virtual GBool supports(const GooString &uri) = 0;

};

#endif /* PDFDOCBUILDER_H */
