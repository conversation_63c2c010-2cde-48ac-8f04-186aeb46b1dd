{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport path from 'path';\nimport fs from 'fs';\n\n// Database file path\nconst dbPath = path.join(process.cwd(), 'data', 'study_app.db');\n\n// Ensure data directory exists\nconst dataDir = path.dirname(dbPath);\nif (!fs.existsSync(dataDir)) {\n  fs.mkdirSync(dataDir, { recursive: true });\n}\n\n// Initialize database\nconst db = new Database(dbPath);\n\n// Enable foreign keys\ndb.pragma('foreign_keys = ON');\n\n// Database schema initialization\nexport function initializeDatabase() {\n  // Users table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),\n      password_hash TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Classes table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS classes (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Subjects table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS subjects (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      UNIQUE(class_id, name)\n    )\n  `);\n\n  // Images table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS images (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      file_path TEXT NOT NULL,\n      original_name TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // OCR text table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS ocr_text (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      image_id INTEGER NOT NULL,\n      content TEXT NOT NULL,\n      processed BOOLEAN DEFAULT FALSE,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Questions table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapter TEXT,\n      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),\n      content TEXT NOT NULL,\n      options TEXT, -- JSON string for MCQ options\n      correct_answer TEXT,\n      marks INTEGER DEFAULT 1,\n      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Tests table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS tests (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapters TEXT, -- JSON string of selected chapters\n      time_min INTEGER NOT NULL, -- Time limit in minutes\n      total_marks INTEGER DEFAULT 0,\n      instructions TEXT,\n      is_active BOOLEAN DEFAULT TRUE,\n      created_by INTEGER NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (created_by) REFERENCES users(id)\n    )\n  `);\n\n  // Test questions junction table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      question_order INTEGER NOT NULL,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      UNIQUE(test_id, question_id),\n      UNIQUE(test_id, question_order)\n    )\n  `);\n\n  // Test results table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_results (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      user_id INTEGER NOT NULL,\n      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      submitted_at DATETIME,\n      total_score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      time_taken INTEGER, -- Time taken in minutes\n      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(test_id, user_id)\n    )\n  `);\n\n  // Test answers table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_answers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      result_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      user_answer TEXT,\n      is_correct BOOLEAN,\n      score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(result_id, question_id)\n    )\n  `);\n\n  // Create indexes for better performance\n  db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);\n    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);\n    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);\n    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);\n  `);\n\n  console.log('Database initialized successfully');\n}\n\n// Create default admin user if none exists\nexport function createDefaultAdmin() {\n  const bcrypt = require('bcryptjs');\n  \n  const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');\n  \n  if (adminExists.count === 0) {\n    const hashedPassword = bcrypt.hashSync('admin123', 10);\n    \n    db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);\n    \n    console.log('Default admin user created: <EMAIL> / admin123');\n  }\n}\n\nexport default db;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,qBAAqB;AACrB,MAAM,SAAS,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAEhD,+BAA+B;AAC/B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;AAC7B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,sBAAsB;AACtB,MAAM,KAAK,IAAI,2HAAA,CAAA,UAAQ,CAAC;AAExB,sBAAsB;AACtB,GAAG,MAAM,CAAC;AAGH,SAAS;IACd,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gBAAgB;IAChB,GAAG,IAAI,CAAC,CAAC;;;;;;;EAOT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,eAAe;IACf,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;EAWT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;EAST,CAAC;IAED,kBAAkB;IAClB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,gCAAgC;IAChC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;EAWT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkBT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,wCAAwC;IACxC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,QAAQ,GAAG,CAAC;AACd;AAGO,SAAS;IACd,MAAM;IAEN,MAAM,cAAc,GAAG,OAAO,CAAC,sDAAsD,GAAG,CAAC;IAEzF,IAAI,YAAY,KAAK,KAAK,GAAG;QAC3B,MAAM,iBAAiB,OAAO,QAAQ,CAAC,YAAY;QAEnD,GAAG,OAAO,CAAC,CAAC;;;IAGZ,CAAC,EAAE,GAAG,CAAC,iBAAiB,sBAAsB,SAAS;QAEvD,QAAQ,GAAG,CAAC;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport db from './database';\n\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.JWT_SECRET || 'your-secret-key-change-this-in-production'\n);\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  role: 'admin' | 'student';\n  created_at: string;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  error?: string;\n}\n\n// Hash password\nexport function hashPassword(password: string): string {\n  return bcrypt.hashSync(password, 10);\n}\n\n// Verify password\nexport function verifyPassword(password: string, hash: string): boolean {\n  return bcrypt.compareSync(password, hash);\n}\n\n// Create JWT token\nexport async function createToken(user: User): Promise<string> {\n  return await new SignJWT({ \n    userId: user.id, \n    email: user.email, \n    role: user.role \n  })\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('24h')\n    .sign(JWT_SECRET);\n}\n\n// Verify JWT token\nexport async function verifyToken(token: string): Promise<any> {\n  try {\n    const { payload } = await jwtVerify(token, JWT_SECRET);\n    return payload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Login user\nexport async function loginUser(email: string, password: string): Promise<AuthResult> {\n  try {\n    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email) as any;\n    \n    if (!user) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    if (!verifyPassword(password, user.password_hash)) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    const userWithoutPassword = {\n      id: user.id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      created_at: user.created_at\n    };\n\n    return { success: true, user: userWithoutPassword };\n  } catch (error) {\n    console.error('Login error:', error);\n    return { success: false, error: 'Login failed' };\n  }\n}\n\n// Get current user from cookies\nexport async function getCurrentUser(): Promise<User | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get('auth-token')?.value;\n    \n    if (!token) {\n      return null;\n    }\n\n    const payload = await verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n\n    const user = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(payload.userId) as User;\n\n    return user || null;\n  } catch (error) {\n    console.error('Get current user error:', error);\n    return null;\n  }\n}\n\n// Set auth cookie\nexport async function setAuthCookie(user: User) {\n  const token = await createToken(user);\n  const cookieStore = await cookies();\n  \n  cookieStore.set('auth-token', token, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 // 24 hours\n  });\n}\n\n// Clear auth cookie\nexport async function clearAuthCookie() {\n  const cookieStore = await cookies();\n  cookieStore.delete('auth-token');\n}\n\n// Register new user (admin only)\nexport function registerUser(name: string, email: string, password: string, role: 'admin' | 'student'): AuthResult {\n  try {\n    // Check if user already exists\n    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get(email);\n    if (existingUser) {\n      return { success: false, error: 'User with this email already exists' };\n    }\n\n    const hashedPassword = hashPassword(password);\n    \n    const result = db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run(name, email, role, hashedPassword);\n\n    const newUser = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(result.lastInsertRowid) as User;\n\n    return { success: true, user: newUser };\n  } catch (error) {\n    console.error('Registration error:', error);\n    return { success: false, error: 'Registration failed' };\n  }\n}\n\n// Middleware to check if user is authenticated\nexport async function requireAuth(): Promise<User | null> {\n  const user = await getCurrentUser();\n  return user;\n}\n\n// Middleware to check if user is admin\nexport async function requireAdmin(): Promise<User | null> {\n  const user = await getCurrentUser();\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  return user;\n}\n\n// Get all users (admin only)\nexport function getAllUsers(): User[] {\n  return db.prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all() as User[];\n}\n\n// Update user\nexport function updateUser(id: number, updates: Partial<Pick<User, 'name' | 'email' | 'role'>>): boolean {\n  try {\n    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');\n    const values = Object.values(updates);\n    \n    db.prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)\n      .run(...values, id);\n    \n    return true;\n  } catch (error) {\n    console.error('Update user error:', error);\n    return false;\n  }\n}\n\n// Delete user\nexport function deleteUser(id: number): boolean {\n  try {\n    db.prepare('DELETE FROM users WHERE id = ?').run(id);\n    return true;\n  } catch (error) {\n    console.error('Delete user error:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,UAAU,IAAI;AAkBrB,SAAS,aAAa,QAAgB;IAC3C,OAAO,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;AACnC;AAGO,SAAS,eAAe,QAAgB,EAAE,IAAY;IAC3D,OAAO,mIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,UAAU;AACtC;AAGO,eAAe,YAAY,IAAU;IAC1C,OAAO,MAAM,IAAI,uJAAA,CAAA,UAAO,CAAC;QACvB,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB,GACG,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,OAClB,IAAI,CAAC;AACV;AAGO,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,UAAU,KAAa,EAAE,QAAgB;IAC7D,IAAI;QACF,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG,CAAC;QAEnE,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,IAAI,CAAC,eAAe,UAAU,KAAK,aAAa,GAAG;YACjD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,MAAM,sBAAsB;YAC1B,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAoB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,eAAe;QAE7C,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,UAAU,MAAM,YAAY;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACrB,GAAG,CAAC,QAAQ,MAAM;QAErB,OAAO,QAAQ;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,eAAe,cAAc,IAAU;IAC5C,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,YAAY,GAAG,CAAC,cAAc,OAAO;QACnC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,KAAK,KAAK,GAAG,WAAW;IAClC;AACF;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAGO,SAAS,aAAa,IAAY,EAAE,KAAa,EAAE,QAAgB,EAAE,IAAyB;IACnG,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,wCAAwC,GAAG,CAAC;QAC5E,IAAI,cAAc;YAChB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE;QAEA,MAAM,iBAAiB,aAAa;QAEpC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,OAAO,MAAM;QAE1B,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACxB,GAAG,CAAC,OAAO,eAAe;QAE7B,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;AACF;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;QAClC,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,gFAAgF,GAAG;AACvG;AAGO,SAAS,WAAW,EAAU,EAAE,OAAuD;IAC5F,IAAI;QACF,MAAM,YAAY,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC;QACrE,MAAM,SAAS,OAAO,MAAM,CAAC;QAE7B,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,6CAA6C,CAAC,EACpF,GAAG,IAAI,QAAQ;QAElB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF;AAGO,SAAS,WAAW,EAAU;IACnC,IAAI;QACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,kCAAkC,GAAG,CAAC;QACjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/models.ts"], "sourcesContent": ["import db from './database';\n\n// Types\nexport interface Class {\n  id: number;\n  name: string;\n  description?: string;\n  created_at: string;\n}\n\nexport interface Subject {\n  id: number;\n  class_id: number;\n  name: string;\n  description?: string;\n  created_at: string;\n  class_name?: string;\n}\n\nexport interface Question {\n  id: number;\n  class_id: number;\n  subject_id: number;\n  chapter?: string;\n  type: 'mcq' | 'true_false' | 'fill_blank' | 'short_answer' | 'long_answer';\n  content: string;\n  options?: string; // JSON string\n  correct_answer?: string;\n  marks: number;\n  difficulty?: 'easy' | 'medium' | 'hard';\n  created_at: string;\n  updated_at: string;\n  class_name?: string;\n  subject_name?: string;\n}\n\nexport interface Test {\n  id: number;\n  title: string;\n  class_id: number;\n  subject_id: number;\n  chapters?: string; // JSON string\n  time_min: number;\n  total_marks: number;\n  instructions?: string;\n  is_active: boolean;\n  created_by: number;\n  created_at: string;\n  class_name?: string;\n  subject_name?: string;\n  creator_name?: string;\n}\n\n// Classes CRUD operations\nexport const ClassModel = {\n  getAll(): Class[] {\n    return db.prepare('SELECT * FROM classes ORDER BY name').all() as Class[];\n  },\n\n  getById(id: number): Class | null {\n    return db.prepare('SELECT * FROM classes WHERE id = ?').get(id) as Class || null;\n  },\n\n  create(name: string, description?: string): Class {\n    const result = db.prepare(`\n      INSERT INTO classes (name, description)\n      VALUES (?, ?)\n    `).run(name, description || null);\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, name: string, description?: string): boolean {\n    try {\n      db.prepare(`\n        UPDATE classes \n        SET name = ?, description = ?\n        WHERE id = ?\n      `).run(name, description || null, id);\n      return true;\n    } catch (error) {\n      console.error('Update class error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM classes WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete class error:', error);\n      return false;\n    }\n  }\n};\n\n// Subjects CRUD operations\nexport const SubjectModel = {\n  getAll(): Subject[] {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      ORDER BY c.name, s.name\n    `).all() as Subject[];\n  },\n\n  getByClassId(classId: number): Subject[] {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      WHERE s.class_id = ?\n      ORDER BY s.name\n    `).all(classId) as Subject[];\n  },\n\n  getById(id: number): Subject | null {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      WHERE s.id = ?\n    `).get(id) as Subject || null;\n  },\n\n  create(classId: number, name: string, description?: string): Subject {\n    const result = db.prepare(`\n      INSERT INTO subjects (class_id, name, description)\n      VALUES (?, ?, ?)\n    `).run(classId, name, description || null);\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, name: string, description?: string): boolean {\n    try {\n      db.prepare(`\n        UPDATE subjects \n        SET name = ?, description = ?\n        WHERE id = ?\n      `).run(name, description || null, id);\n      return true;\n    } catch (error) {\n      console.error('Update subject error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM subjects WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete subject error:', error);\n      return false;\n    }\n  }\n};\n\n// Questions CRUD operations\nexport const QuestionModel = {\n  getAll(filters?: { classId?: number; subjectId?: number; type?: string; chapter?: string }): Question[] {\n    let query = `\n      SELECT q.*, c.name as class_name, s.name as subject_name\n      FROM questions q\n      JOIN classes c ON q.class_id = c.id\n      JOIN subjects s ON q.subject_id = s.id\n    `;\n    \n    const conditions: string[] = [];\n    const params: any[] = [];\n    \n    if (filters?.classId) {\n      conditions.push('q.class_id = ?');\n      params.push(filters.classId);\n    }\n    \n    if (filters?.subjectId) {\n      conditions.push('q.subject_id = ?');\n      params.push(filters.subjectId);\n    }\n    \n    if (filters?.type) {\n      conditions.push('q.type = ?');\n      params.push(filters.type);\n    }\n    \n    if (filters?.chapter) {\n      conditions.push('q.chapter = ?');\n      params.push(filters.chapter);\n    }\n    \n    if (conditions.length > 0) {\n      query += ' WHERE ' + conditions.join(' AND ');\n    }\n    \n    query += ' ORDER BY q.created_at DESC';\n    \n    return db.prepare(query).all(...params) as Question[];\n  },\n\n  getById(id: number): Question | null {\n    return db.prepare(`\n      SELECT q.*, c.name as class_name, s.name as subject_name\n      FROM questions q\n      JOIN classes c ON q.class_id = c.id\n      JOIN subjects s ON q.subject_id = s.id\n      WHERE q.id = ?\n    `).get(id) as Question || null;\n  },\n\n  create(questionData: Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>): Question {\n    const result = db.prepare(`\n      INSERT INTO questions (\n        class_id, subject_id, chapter, type, content, options, \n        correct_answer, marks, difficulty\n      )\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `).run(\n      questionData.class_id,\n      questionData.subject_id,\n      questionData.chapter || null,\n      questionData.type,\n      questionData.content,\n      questionData.options || null,\n      questionData.correct_answer || null,\n      questionData.marks,\n      questionData.difficulty || null\n    );\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, questionData: Partial<Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>>): boolean {\n    try {\n      const fields = Object.keys(questionData).filter(key => questionData[key as keyof typeof questionData] !== undefined);\n      const setClause = fields.map(field => `${field} = ?`).join(', ');\n      const values = fields.map(field => questionData[field as keyof typeof questionData]);\n      \n      db.prepare(`\n        UPDATE questions \n        SET ${setClause}, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ?\n      `).run(...values, id);\n      \n      return true;\n    } catch (error) {\n      console.error('Update question error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM questions WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete question error:', error);\n      return false;\n    }\n  },\n\n  getChapters(classId?: number, subjectId?: number): string[] {\n    let query = 'SELECT DISTINCT chapter FROM questions WHERE chapter IS NOT NULL';\n    const params: any[] = [];\n    \n    if (classId) {\n      query += ' AND class_id = ?';\n      params.push(classId);\n    }\n    \n    if (subjectId) {\n      query += ' AND subject_id = ?';\n      params.push(subjectId);\n    }\n    \n    query += ' ORDER BY chapter';\n    \n    const results = db.prepare(query).all(...params) as { chapter: string }[];\n    return results.map(r => r.chapter);\n  }\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAsDO,MAAM,aAAa;IACxB;QACE,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG;IAC9D;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sCAAsC,GAAG,CAAC,OAAgB;IAC9E;IAEA,QAAO,IAAY,EAAE,WAAoB;QACvC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe;QAE5B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,IAAY,EAAE,WAAoB;QACnD,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIZ,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe,MAAM;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oCAAoC,GAAG,CAAC;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;AACF;AAGO,MAAM,eAAe;IAC1B;QACE,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAKnB,CAAC,EAAE,GAAG;IACR;IAEA,cAAa,OAAe;QAC1B,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAMnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAKnB,CAAC,EAAE,GAAG,CAAC,OAAkB;IAC3B;IAEA,QAAO,OAAe,EAAE,IAAY,EAAE,WAAoB;QACxD,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,SAAS,MAAM,eAAe;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,IAAY,EAAE,WAAoB;QACnD,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIZ,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe,MAAM;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,qCAAqC,GAAG,CAAC;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAO,OAAmF;QACxF,IAAI,QAAQ,CAAC;;;;;IAKb,CAAC;QAED,MAAM,aAAuB,EAAE;QAC/B,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS,SAAS;YACpB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,OAAO;QAC7B;QAEA,IAAI,SAAS,WAAW;YACtB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,SAAS;QAC/B;QAEA,IAAI,SAAS,MAAM;YACjB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,IAAI;QAC1B;QAEA,IAAI,SAAS,SAAS;YACpB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,OAAO;QAC7B;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,SAAS,YAAY,WAAW,IAAI,CAAC;QACvC;QAEA,SAAS;QAET,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;IAClC;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAMnB,CAAC,EAAE,GAAG,CAAC,OAAmB;IAC5B;IAEA,QAAO,YAAgG;QACrG,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAM3B,CAAC,EAAE,GAAG,CACJ,aAAa,QAAQ,EACrB,aAAa,UAAU,EACvB,aAAa,OAAO,IAAI,MACxB,aAAa,IAAI,EACjB,aAAa,OAAO,EACpB,aAAa,OAAO,IAAI,MACxB,aAAa,cAAc,IAAI,MAC/B,aAAa,KAAK,EAClB,aAAa,UAAU,IAAI;QAG7B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,YAAyG;QAC1H,IAAI;YACF,MAAM,SAAS,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,YAAY,CAAC,IAAiC,KAAK;YAC1G,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA,QAAS,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;YAC3D,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA,QAAS,YAAY,CAAC,MAAmC;YAEnF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;YAEN,EAAE,UAAU;;MAElB,CAAC,EAAE,GAAG,IAAI,QAAQ;YAElB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sCAAsC,GAAG,CAAC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,aAAY,OAAgB,EAAE,SAAkB;QAC9C,IAAI,QAAQ;QACZ,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS;YACX,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,WAAW;YACb,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QAET,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;QACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;IACnC;AACF", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/api/admin/subjects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { requireAdmin } from '@/lib/auth';\nimport { SubjectModel } from '@/lib/models';\n\nexport async function GET(request: NextRequest) {\n  const user = await requireAdmin();\n  \n  if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n\n  try {\n    const { searchParams } = new URL(request.url);\n    const classId = searchParams.get('classId');\n\n    const subjects = classId \n      ? SubjectModel.getByClassId(parseInt(classId))\n      : SubjectModel.getAll();\n      \n    return NextResponse.json(subjects);\n  } catch (error) {\n    console.error('Get subjects error:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  const user = await requireAdmin();\n  \n  if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n\n  try {\n    const { class_id, name, description } = await request.json();\n\n    if (!class_id || !name) {\n      return NextResponse.json({ error: 'Class ID and subject name are required' }, { status: 400 });\n    }\n\n    const newSubject = SubjectModel.create(class_id, name, description);\n    return NextResponse.json(newSubject, { status: 201 });\n  } catch (error) {\n    console.error('Create subject error:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE9B,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,WAAW,UACb,sHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,SAAS,YACnC,sHAAA,CAAA,eAAY,CAAC,MAAM;QAEvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE9B,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE1D,IAAI,CAAC,YAAY,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyC,GAAG;gBAAE,QAAQ;YAAI;QAC9F;QAEA,MAAM,aAAa,sHAAA,CAAA,eAAY,CAAC,MAAM,CAAC,UAAU,MAAM;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,YAAY;YAAE,QAAQ;QAAI;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}