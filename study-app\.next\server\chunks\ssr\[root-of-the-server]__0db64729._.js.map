{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Upload Images', href: '/admin/upload', icon: '📤' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;AAEA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAiB,MAAM;YAAiB,MAAM;QAAK;QAC3D;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/PDFUploadComponent.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface PDFUploadComponentProps {\n  bookId: number;\n}\n\nexport default function PDFUploadComponent({ bookId }: PDFUploadComponentProps) {\n  const router = useRouter();\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [error, setError] = useState('');\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      if (file.type !== 'application/pdf') {\n        setError('Please select a PDF file');\n        return;\n      }\n      if (file.size > 50 * 1024 * 1024) { // 50MB limit\n        setError('File size must be less than 50MB');\n        return;\n      }\n      setSelectedFile(file);\n      setError('');\n    }\n  };\n\n  const handleUpload = async () => {\n    if (!selectedFile) return;\n\n    setIsUploading(true);\n    setUploadProgress(0);\n    setError('');\n\n    try {\n      const formData = new FormData();\n      formData.append('pdf', selectedFile);\n      formData.append('bookId', bookId.toString());\n\n      const response = await fetch('/api/admin/books/upload-pdf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error('Upload failed');\n      }\n\n      const result = await response.json();\n      \n      if (result.success) {\n        // Redirect back to book page\n        router.push(`/admin/books/${bookId}`);\n      } else {\n        setError(result.error || 'Upload failed');\n      }\n    } catch (error) {\n      setError('Upload failed. Please try again.');\n    } finally {\n      setIsUploading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault();\n    const file = event.dataTransfer.files[0];\n    if (file && file.type === 'application/pdf') {\n      setSelectedFile(file);\n      setError('');\n    } else {\n      setError('Please drop a PDF file');\n    }\n  };\n\n  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"text-sm text-red-600\">{error}</div>\n        </div>\n      )}\n\n      {/* File Drop Zone */}\n      <div\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors\"\n      >\n        <div className=\"space-y-4\">\n          <div className=\"w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center\">\n            <span className=\"text-3xl\">📄</span>\n          </div>\n          \n          {selectedFile ? (\n            <div className=\"space-y-2\">\n              <p className=\"text-lg font-medium text-gray-900\">\n                {selectedFile.name}\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB\n              </p>\n              <div className=\"flex justify-center space-x-3\">\n                <button\n                  onClick={() => setSelectedFile(null)}\n                  className=\"text-sm text-gray-600 hover:text-gray-800\"\n                >\n                  Remove\n                </button>\n                <button\n                  onClick={handleUpload}\n                  disabled={isUploading}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {isUploading ? 'Processing...' : 'Upload PDF'}\n                </button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-2\">\n              <h3 className=\"text-lg font-medium text-gray-900\">\n                Drop your PDF here or click to browse\n              </h3>\n              <p className=\"text-sm text-gray-500\">\n                Upload your textbook PDF to automatically extract all pages in order\n              </p>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700\"\n              >\n                Select PDF File\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Upload Progress */}\n      {isUploading && (\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">Processing PDF...</span>\n            <span className=\"text-gray-600\">{uploadProgress}%</span>\n          </div>\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n            <div \n              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n              style={{ width: `${uploadProgress}%` }}\n            ></div>\n          </div>\n          <p className=\"text-xs text-gray-500\">\n            Extracting pages, processing OCR, and organizing content...\n          </p>\n        </div>\n      )}\n\n      {/* File Input */}\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\".pdf\"\n        onChange={handleFileSelect}\n        className=\"hidden\"\n      />\n\n      {/* Upload Info */}\n      <div className=\"bg-gray-50 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-gray-900 mb-2\">What happens when you upload:</h4>\n        <ul className=\"text-sm text-gray-600 space-y-1\">\n          <li>• PDF pages are extracted and converted to images</li>\n          <li>• Pages are automatically numbered in correct order</li>\n          <li>• OCR processing begins on all pages</li>\n          <li>• You can then assign page types (cover, contents, chapters)</li>\n          <li>• Questions are extracted and organized by chapter</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,mBAAmB,EAAE,MAAM,EAA2B;IAC5E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,IAAI,KAAK,IAAI,KAAK,mBAAmB;gBACnC,SAAS;gBACT;YACF;YACA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;gBAChC,SAAS;gBACT;YACF;YACA,gBAAgB;YAChB,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;QAEnB,eAAe;QACf,kBAAkB;QAClB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,OAAO;YACvB,SAAS,MAAM,CAAC,UAAU,OAAO,QAAQ;YAEzC,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,6BAA6B;gBAC7B,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;YACtC,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,eAAe;YACf,kBAAkB;QACpB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,MAAM,OAAO,MAAM,YAAY,CAAC,KAAK,CAAC,EAAE;QACxC,IAAI,QAAQ,KAAK,IAAI,KAAK,mBAAmB;YAC3C,gBAAgB;YAChB,SAAS;QACX,OAAO;YACL,SAAS;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAwB;;;;;;;;;;;0BAK3C,8OAAC;gBACC,QAAQ;gBACR,YAAY;gBACZ,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;wBAG5B,6BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,aAAa,IAAI;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;;wCACV,CAAC,aAAa,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;wCAAG;;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,cAAc,kBAAkB;;;;;;;;;;;;;;;;;iDAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCACC,SAAS,IAAM,aAAa,OAAO,EAAE;oCACrC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,8OAAC;gCAAK,WAAU;;oCAAiB;oCAAe;;;;;;;;;;;;;kCAElD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,eAAe,CAAC,CAAC;4BAAC;;;;;;;;;;;kCAGzC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAOzC,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAO;gBACP,UAAU;gBACV,WAAU;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}]}