{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport path from 'path';\nimport fs from 'fs';\n\n// Database file path\nconst dbPath = path.join(process.cwd(), 'data', 'study_app.db');\n\n// Ensure data directory exists\nconst dataDir = path.dirname(dbPath);\nif (!fs.existsSync(dataDir)) {\n  fs.mkdirSync(dataDir, { recursive: true });\n}\n\n// Initialize database\nconst db = new Database(dbPath);\n\n// Enable foreign keys\ndb.pragma('foreign_keys = ON');\n\n// Database migration function\nexport function migrateDatabase() {\n  try {\n    // Check if new columns exist, if not add them\n    const tableInfo = db.prepare(\"PRAGMA table_info(images)\").all() as any[];\n    const hasPageType = tableInfo.some(col => col.name === 'page_type');\n    const hasUploadOrder = tableInfo.some(col => col.name === 'upload_order');\n\n    if (!hasPageType) {\n      db.exec(\"ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'\");\n      console.log('Added page_type column to images table');\n    }\n\n    if (!hasUploadOrder) {\n      db.exec(\"ALTER TABLE images ADD COLUMN upload_order INTEGER\");\n      // Set upload order for existing images based on uploaded_at\n      db.exec(`\n        UPDATE images\n        SET upload_order = (\n          SELECT COUNT(*) + 1\n          FROM images i2\n          WHERE i2.class_id = images.class_id\n          AND i2.subject_id = images.subject_id\n          AND i2.uploaded_at < images.uploaded_at\n        )\n      `);\n      console.log('Added upload_order column to images table');\n    }\n\n    // Check if processed column exists in ocr_text table\n    const ocrTableInfo = db.prepare(\"PRAGMA table_info(ocr_text)\").all() as any[];\n    const hasProcessed = ocrTableInfo.some(col => col.name === 'processed');\n\n    if (!hasProcessed) {\n      db.exec(\"ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1\");\n      console.log('Added processed column to ocr_text table');\n    }\n\n    // Check if books table exists\n    const tablesResult = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n    if (!tablesResult) {\n      console.log('Creating books table...');\n      db.exec(`\n        CREATE TABLE books (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          title TEXT NOT NULL,\n          class_id INTEGER NOT NULL,\n          subject_id INTEGER NOT NULL,\n          description TEXT,\n          cover_image_path TEXT,\n          total_pages INTEGER DEFAULT 0,\n          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n        )\n      `);\n      console.log('Books table created successfully');\n    }\n\n    // Check if images table has book_id column\n    const hasBookId = tableInfo.some(col => col.name === 'book_id');\n    if (!hasBookId) {\n      console.log('Adding book_id column to images table...');\n      db.exec(\"ALTER TABLE images ADD COLUMN book_id INTEGER\");\n      console.log('Added book_id column to images table');\n    }\n\n  } catch (error) {\n    console.error('Migration error:', error);\n  }\n}\n\n// Database schema initialization\nexport function initializeDatabase() {\n  // Users table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),\n      password_hash TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Classes table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS classes (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Subjects table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS subjects (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      UNIQUE(class_id, name)\n    )\n  `);\n\n  // Books table - containers for textbook content\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS books (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      description TEXT,\n      cover_image_path TEXT,\n      total_pages INTEGER DEFAULT 0,\n      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Images table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS images (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      file_path TEXT NOT NULL,\n      original_name TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),\n      upload_order INTEGER,\n      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // OCR text table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS ocr_text (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      image_id INTEGER NOT NULL,\n      content TEXT NOT NULL,\n      processed BOOLEAN DEFAULT FALSE,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Questions table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapter TEXT,\n      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),\n      content TEXT NOT NULL,\n      options TEXT, -- JSON string for MCQ options\n      correct_answer TEXT,\n      marks INTEGER DEFAULT 1,\n      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Tests table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS tests (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapters TEXT, -- JSON string of selected chapters\n      time_min INTEGER NOT NULL, -- Time limit in minutes\n      total_marks INTEGER DEFAULT 0,\n      instructions TEXT,\n      is_active BOOLEAN DEFAULT TRUE,\n      created_by INTEGER NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (created_by) REFERENCES users(id)\n    )\n  `);\n\n  // Test questions junction table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      question_order INTEGER NOT NULL,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      UNIQUE(test_id, question_id),\n      UNIQUE(test_id, question_order)\n    )\n  `);\n\n  // Test results table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_results (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      user_id INTEGER NOT NULL,\n      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      submitted_at DATETIME,\n      total_score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      time_taken INTEGER, -- Time taken in minutes\n      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(test_id, user_id)\n    )\n  `);\n\n  // Test answers table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_answers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      result_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      user_answer TEXT,\n      is_correct BOOLEAN,\n      score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(result_id, question_id)\n    )\n  `);\n\n  // Create indexes for better performance\n  db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);\n    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);\n    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);\n    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);\n  `);\n\n  console.log('Database initialized successfully');\n\n  // Run migrations after initialization\n  migrateDatabase();\n}\n\n// Create default admin user if none exists\nexport function createDefaultAdmin() {\n  const bcrypt = require('bcryptjs');\n\n  const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');\n\n  if (adminExists.count === 0) {\n    const hashedPassword = bcrypt.hashSync('admin123', 10);\n\n    db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);\n\n    console.log('Default admin user created: <EMAIL> / admin123');\n  }\n}\n\n// Clear OCR data to force re-processing with real OCR\nexport function clearOCRData() {\n  try {\n    console.log('Clearing existing OCR data to enable fresh processing...');\n    db.exec('DELETE FROM ocr_text');\n    db.exec('DELETE FROM questions');\n    console.log('OCR data cleared successfully');\n  } catch (error) {\n    console.error('Error clearing OCR data:', error);\n  }\n}\n\n// Initialize database on import\ninitializeDatabase();\ncreateDefaultAdmin();\n\nexport default db;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEA,qBAAqB;AACrB,MAAM,SAAS,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAEhD,+BAA+B;AAC/B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;AAC7B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,sBAAsB;AACtB,MAAM,KAAK,IAAI,2HAAA,CAAA,UAAQ,CAAC;AAExB,sBAAsB;AACtB,GAAG,MAAM,CAAC;AAGH,SAAS;IACd,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,6BAA6B,GAAG;QAC7D,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACvD,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE1D,IAAI,CAAC,aAAa;YAChB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,gBAAgB;YACnB,GAAG,IAAI,CAAC;YACR,4DAA4D;YAC5D,GAAG,IAAI,CAAC,CAAC;;;;;;;;;MAST,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,qDAAqD;QACrD,MAAM,eAAe,GAAG,OAAO,CAAC,+BAA+B,GAAG;QAClE,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE3D,IAAI,CAAC,cAAc;YACjB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,8BAA8B;QAC9B,MAAM,eAAe,GAAG,OAAO,CAAC,sEAAsE,GAAG;QACzG,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;MAeT,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,2CAA2C;QAC3C,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACrD,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAGO,SAAS;IACd,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gBAAgB;IAChB,GAAG,IAAI,CAAC,CAAC;;;;;;;EAOT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gDAAgD;IAChD,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;EAeT,CAAC;IAED,eAAe;IACf,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;EAaT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;EAST,CAAC;IAED,kBAAkB;IAClB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,gCAAgC;IAChC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;EAWT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkBT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,wCAAwC;IACxC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,QAAQ,GAAG,CAAC;IAEZ,sCAAsC;IACtC;AACF;AAGO,SAAS;IACd,MAAM;IAEN,MAAM,cAAc,GAAG,OAAO,CAAC,sDAAsD,GAAG,CAAC;IAEzF,IAAI,YAAY,KAAK,KAAK,GAAG;QAC3B,MAAM,iBAAiB,OAAO,QAAQ,CAAC,YAAY;QAEnD,GAAG,OAAO,CAAC,CAAC;;;IAGZ,CAAC,EAAE,GAAG,CAAC,iBAAiB,sBAAsB,SAAS;QAEvD,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,SAAS;IACd,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC;QACR,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF;AAEA,gCAAgC;AAChC;AACA;uCAEe", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport db from './database';\n\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.JWT_SECRET || 'your-secret-key-change-this-in-production'\n);\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  role: 'admin' | 'student';\n  created_at: string;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  error?: string;\n}\n\n// Hash password\nexport function hashPassword(password: string): string {\n  return bcrypt.hashSync(password, 10);\n}\n\n// Verify password\nexport function verifyPassword(password: string, hash: string): boolean {\n  return bcrypt.compareSync(password, hash);\n}\n\n// Create JWT token\nexport async function createToken(user: User): Promise<string> {\n  return await new SignJWT({ \n    userId: user.id, \n    email: user.email, \n    role: user.role \n  })\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('24h')\n    .sign(JWT_SECRET);\n}\n\n// Verify JWT token\nexport async function verifyToken(token: string): Promise<any> {\n  try {\n    const { payload } = await jwtVerify(token, JWT_SECRET);\n    return payload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Login user\nexport async function loginUser(email: string, password: string): Promise<AuthResult> {\n  try {\n    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email) as any;\n    \n    if (!user) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    if (!verifyPassword(password, user.password_hash)) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    const userWithoutPassword = {\n      id: user.id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      created_at: user.created_at\n    };\n\n    return { success: true, user: userWithoutPassword };\n  } catch (error) {\n    console.error('Login error:', error);\n    return { success: false, error: 'Login failed' };\n  }\n}\n\n// Get current user from cookies\nexport async function getCurrentUser(): Promise<User | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get('auth-token')?.value;\n    \n    if (!token) {\n      return null;\n    }\n\n    const payload = await verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n\n    const user = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(payload.userId) as User;\n\n    return user || null;\n  } catch (error) {\n    console.error('Get current user error:', error);\n    return null;\n  }\n}\n\n// Set auth cookie\nexport async function setAuthCookie(user: User) {\n  const token = await createToken(user);\n  const cookieStore = await cookies();\n  \n  cookieStore.set('auth-token', token, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 // 24 hours\n  });\n}\n\n// Clear auth cookie\nexport async function clearAuthCookie() {\n  const cookieStore = await cookies();\n  cookieStore.delete('auth-token');\n}\n\n// Register new user (admin only)\nexport function registerUser(name: string, email: string, password: string, role: 'admin' | 'student'): AuthResult {\n  try {\n    // Check if user already exists\n    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get(email);\n    if (existingUser) {\n      return { success: false, error: 'User with this email already exists' };\n    }\n\n    const hashedPassword = hashPassword(password);\n    \n    const result = db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run(name, email, role, hashedPassword);\n\n    const newUser = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(result.lastInsertRowid) as User;\n\n    return { success: true, user: newUser };\n  } catch (error) {\n    console.error('Registration error:', error);\n    return { success: false, error: 'Registration failed' };\n  }\n}\n\n// Middleware to check if user is authenticated\nexport async function requireAuth(): Promise<User | null> {\n  const user = await getCurrentUser();\n  return user;\n}\n\n// Middleware to check if user is admin\nexport async function requireAdmin(): Promise<User | null> {\n  const user = await getCurrentUser();\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  return user;\n}\n\n// Get all users (admin only)\nexport function getAllUsers(): User[] {\n  return db.prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all() as User[];\n}\n\n// Update user\nexport function updateUser(id: number, updates: Partial<Pick<User, 'name' | 'email' | 'role'>>): boolean {\n  try {\n    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');\n    const values = Object.values(updates);\n    \n    db.prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)\n      .run(...values, id);\n    \n    return true;\n  } catch (error) {\n    console.error('Update user error:', error);\n    return false;\n  }\n}\n\n// Delete user\nexport function deleteUser(id: number): boolean {\n  try {\n    db.prepare('DELETE FROM users WHERE id = ?').run(id);\n    return true;\n  } catch (error) {\n    console.error('Delete user error:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,UAAU,IAAI;AAkBrB,SAAS,aAAa,QAAgB;IAC3C,OAAO,iIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;AACnC;AAGO,SAAS,eAAe,QAAgB,EAAE,IAAY;IAC3D,OAAO,iIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,UAAU;AACtC;AAGO,eAAe,YAAY,IAAU;IAC1C,OAAO,MAAM,IAAI,qJAAA,CAAA,UAAO,CAAC;QACvB,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB,GACG,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,OAClB,IAAI,CAAC;AACV;AAGO,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,UAAU,KAAa,EAAE,QAAgB;IAC7D,IAAI;QACF,MAAM,OAAO,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG,CAAC;QAEnE,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,IAAI,CAAC,eAAe,UAAU,KAAK,aAAa,GAAG;YACjD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,MAAM,sBAAsB;YAC1B,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAoB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,eAAe;QAE7C,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,UAAU,MAAM,YAAY;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACrB,GAAG,CAAC,QAAQ,MAAM;QAErB,OAAO,QAAQ;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,eAAe,cAAc,IAAU;IAC5C,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,YAAY,GAAG,CAAC,cAAc,OAAO;QACnC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,KAAK,KAAK,GAAG,WAAW;IAClC;AACF;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAGO,SAAS,aAAa,IAAY,EAAE,KAAa,EAAE,QAAgB,EAAE,IAAyB;IACnG,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,wCAAwC,GAAG,CAAC;QAC5E,IAAI,cAAc;YAChB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE;QAEA,MAAM,iBAAiB,aAAa;QAEpC,MAAM,SAAS,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,OAAO,MAAM;QAE1B,MAAM,UAAU,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACxB,GAAG,CAAC,OAAO,eAAe;QAE7B,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;AACF;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;QAClC,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,gFAAgF,GAAG;AACvG;AAGO,SAAS,WAAW,EAAU,EAAE,OAAuD;IAC5F,IAAI;QACF,MAAM,YAAY,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC;QACrE,MAAM,SAAS,OAAO,MAAM,CAAC;QAE7B,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,6CAA6C,CAAC,EACpF,GAAG,IAAI,QAAQ;QAElB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF;AAGO,SAAS,WAAW,EAAU;IACnC,IAAI;QACF,sHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,kCAAkC,GAAG,CAAC;QACjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AdminLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AdminLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AdminLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AdminLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/admin/images/%5Bid%5D/ocr/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation';\nimport { requireAdmin } from '@/lib/auth';\nimport AdminLayout from '@/components/AdminLayout';\nimport Link from 'next/link';\nimport OCRActions from '@/components/OCRActions';\n\ninterface OCRPageProps {\n  params: Promise<{ id: string }>;\n}\n\nasync function getImageOCR(imageId: number) {\n  try {\n    const response = await fetch(`http://localhost:3000/api/admin/images/${imageId}/ocr`, {\n      cache: 'no-store'\n    });\n    \n    if (!response.ok) {\n      return null;\n    }\n    \n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching OCR data:', error);\n    return null;\n  }\n}\n\nexport default async function OCRPage({ params }: OCRPageProps) {\n  const user = await requireAdmin();\n  \n  if (!user) {\n    redirect('/admin/login');\n  }\n\n  const { id } = await params;\n  const imageId = parseInt(id);\n  \n  const data = await getImageOCR(imageId);\n  \n  if (!data || !data.success) {\n    return (\n      <AdminLayout>\n        <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"px-4 py-6 sm:px-0\">\n            <div className=\"text-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Image Not Found</h1>\n              <p className=\"text-gray-600 mb-6\">The requested image could not be found.</p>\n              <Link\n                href=\"/admin/books\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Back to Books\n              </Link>\n            </div>\n          </div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  const { image, ocr } = data;\n\n  return (\n    <AdminLayout>\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">\n                  OCR Text - Page {image.pageNumber}\n                </h1>\n                <p className=\"text-gray-600\">\n                  {image.bookTitle} • {image.name}\n                </p>\n              </div>\n              <div className=\"flex space-x-3\">\n                <Link\n                  href={`/admin/books/${image.bookId}`}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  Back to Book\n                </Link>\n                <Link\n                  href={`/api/admin/images/${image.id}/thumbnail`}\n                  target=\"_blank\"\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  View Image\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* OCR Content */}\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">Extracted Text</h2>\n              {ocr && (\n                <div className=\"mt-1 flex items-center space-x-4 text-sm text-gray-500\">\n                  <span>Characters: {ocr.characterCount}</span>\n                  <span>Words: {ocr.wordCount}</span>\n                  <span>Processed: {ocr.processed ? 'Yes' : 'No'}</span>\n                  {ocr.processedAt && (\n                    <span>At: {new Date(ocr.processedAt).toLocaleString()}</span>\n                  )}\n                </div>\n              )}\n            </div>\n            \n            <div className=\"px-6 py-4\">\n              {ocr && ocr.text ? (\n                <div className=\"space-y-4\">\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <pre className=\"whitespace-pre-wrap text-sm text-gray-900 font-mono\">\n                      {ocr.text}\n                    </pre>\n                  </div>\n                  \n                  {/* Actions */}\n                  <div className=\"flex space-x-3\">\n                    <button\n                      className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n                      onClick={() => {\n                        if (typeof window !== 'undefined') {\n                          navigator.clipboard.writeText(ocr.text);\n                        }\n                      }}\n                    >\n                      Copy Text\n                    </button>\n                    <form action={`/api/admin/images/${image.id}/ocr`} method=\"POST\">\n                      <button\n                        type=\"submit\"\n                        className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n                      >\n                        Re-process OCR\n                      </button>\n                    </form>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <div className=\"text-gray-400 mb-4\">\n                    <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No OCR Text Available</h3>\n                  <p className=\"text-gray-600 mb-4\">This image has not been processed for text extraction yet.</p>\n                  <Link\n                    href={`/api/admin/images/${image.id}/ocr`}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\"\n                  >\n                    Process OCR\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAOA,eAAe,YAAY,OAAe;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,uCAAuC,EAAE,QAAQ,IAAI,CAAC,EAAE;YACpF,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAEe,eAAe,QAAQ,EAAE,MAAM,EAAgB;IAC5D,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAE9B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,UAAU,SAAS;IAEzB,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;QAC1B,qBACE,8OAAC,iIAAA,CAAA,UAAW;sBACV,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IAEvB,qBACE,8OAAC,iIAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAmC;gDAC9B,MAAM,UAAU;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;;gDACV,MAAM,SAAS;gDAAC;gDAAI,MAAM,IAAI;;;;;;;;;;;;;8CAGnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;4CACpC,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC;4CAC/C,QAAO;4CACP,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;oCACjD,qBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDAAa,IAAI,cAAc;;;;;;;0DACrC,8OAAC;;oDAAK;oDAAQ,IAAI,SAAS;;;;;;;0DAC3B,8OAAC;;oDAAK;oDAAY,IAAI,SAAS,GAAG,QAAQ;;;;;;;4CACzC,IAAI,WAAW,kBACd,8OAAC;;oDAAK;oDAAK,IAAI,KAAK,IAAI,WAAW,EAAE,cAAc;;;;;;;;;;;;;;;;;;;0CAM3D,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,IAAI,iBACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,IAAI,IAAI;;;;;;;;;;;sDAKb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,SAAS;wDACP,uCAAmC;;wDAEnC;oDACF;8DACD;;;;;;8DAGD,8OAAC;oDAAK,QAAQ,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC;oDAAE,QAAO;8DACxD,cAAA,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;yDAOP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACxE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC;4CACzC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}