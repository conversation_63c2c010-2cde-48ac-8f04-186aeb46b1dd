{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Upload Images', href: '/admin/upload', icon: '📤' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;AAEA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAiB,MAAM;YAAiB,MAAM;QAAK;QAC3D;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/CreateBookForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface Class {\n  id: number;\n  name: string;\n}\n\ninterface Subject {\n  id: number;\n  class_id: number;\n  name: string;\n}\n\ninterface CreateBookFormProps {\n  classes: Class[];\n  subjects: Subject[];\n}\n\nexport default function CreateBookForm({ classes, subjects }: CreateBookFormProps) {\n  const router = useRouter();\n  const [formData, setFormData] = useState({\n    title: '',\n    class_id: '',\n    subject_id: '',\n    description: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n\n  // Filter subjects based on selected class\n  const filteredSubjects = formData.class_id \n    ? subjects.filter(subject => subject.class_id === parseInt(formData.class_id))\n    : [];\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/admin/books', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          title: formData.title,\n          class_id: parseInt(formData.class_id),\n          subject_id: parseInt(formData.subject_id),\n          description: formData.description || undefined\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        router.push(`/admin/books/${result.bookId}`);\n      } else {\n        setError(result.error || 'Failed to create book');\n      }\n    } catch (error) {\n      setError('An error occurred while creating the book');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      // Reset subject when class changes\n      ...(name === 'class_id' ? { subject_id: '' } : {})\n    }));\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"text-sm text-red-600\">{error}</div>\n        </div>\n      )}\n\n      <div>\n        <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700\">\n          Book Title *\n        </label>\n        <input\n          type=\"text\"\n          id=\"title\"\n          name=\"title\"\n          required\n          value={formData.title}\n          onChange={handleChange}\n          className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n          placeholder=\"e.g., English Textbook Grade 1\"\n        />\n      </div>\n\n      <div>\n        <label htmlFor=\"class_id\" className=\"block text-sm font-medium text-gray-700\">\n          Class *\n        </label>\n        <select\n          id=\"class_id\"\n          name=\"class_id\"\n          required\n          value={formData.class_id}\n          onChange={handleChange}\n          className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n        >\n          <option value=\"\">Select a class</option>\n          {classes.map((cls) => (\n            <option key={cls.id} value={cls.id}>\n              {cls.name}\n            </option>\n          ))}\n        </select>\n      </div>\n\n      <div>\n        <label htmlFor=\"subject_id\" className=\"block text-sm font-medium text-gray-700\">\n          Subject *\n        </label>\n        <select\n          id=\"subject_id\"\n          name=\"subject_id\"\n          required\n          value={formData.subject_id}\n          onChange={handleChange}\n          disabled={!formData.class_id}\n          className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100\"\n        >\n          <option value=\"\">\n            {formData.class_id ? 'Select a subject' : 'Select a class first'}\n          </option>\n          {filteredSubjects.map((subject) => (\n            <option key={subject.id} value={subject.id}>\n              {subject.name}\n            </option>\n          ))}\n        </select>\n      </div>\n\n      <div>\n        <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n          Description\n        </label>\n        <textarea\n          id=\"description\"\n          name=\"description\"\n          rows={3}\n          value={formData.description}\n          onChange={handleChange}\n          className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n          placeholder=\"Optional description of the textbook content...\"\n        />\n      </div>\n\n      <div className=\"flex justify-end space-x-3\">\n        <button\n          type=\"button\"\n          onClick={() => router.back()}\n          className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n        >\n          Cancel\n        </button>\n        <button\n          type=\"submit\"\n          disabled={isSubmitting}\n          className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\"\n        >\n          {isSubmitting ? 'Creating...' : 'Create Book'}\n        </button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAqBe,SAAS,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAuB;IAC/E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;IACf;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,0CAA0C;IAC1C,MAAM,mBAAmB,SAAS,QAAQ,GACtC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK,SAAS,SAAS,QAAQ,KAC1E,EAAE;IAEN,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,SAAS,QAAQ;oBACpC,YAAY,SAAS,SAAS,UAAU;oBACxC,aAAa,SAAS,WAAW,IAAI;gBACvC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,OAAO,MAAM,EAAE;YAC7C,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;gBACR,mCAAmC;gBACnC,GAAI,SAAS,aAAa;oBAAE,YAAY;gBAAG,IAAI,CAAC,CAAC;YACnD,CAAC;IACH;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;YACrC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAwB;;;;;;;;;;;0BAI3C,8OAAC;;kCACC,8OAAC;wBAAM,SAAQ;wBAAQ,WAAU;kCAA0C;;;;;;kCAG3E,8OAAC;wBACC,MAAK;wBACL,IAAG;wBACH,MAAK;wBACL,QAAQ;wBACR,OAAO,SAAS,KAAK;wBACrB,UAAU;wBACV,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;;kCACC,8OAAC;wBAAM,SAAQ;wBAAW,WAAU;kCAA0C;;;;;;kCAG9E,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,QAAQ;wBACR,OAAO,SAAS,QAAQ;wBACxB,UAAU;wBACV,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAG;;;;;;4BAChB,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;oCAAoB,OAAO,IAAI,EAAE;8CAC/B,IAAI,IAAI;mCADE,IAAI,EAAE;;;;;;;;;;;;;;;;;0BAOzB,8OAAC;;kCACC,8OAAC;wBAAM,SAAQ;wBAAa,WAAU;kCAA0C;;;;;;kCAGhF,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,QAAQ;wBACR,OAAO,SAAS,UAAU;wBAC1B,UAAU;wBACV,UAAU,CAAC,SAAS,QAAQ;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CACX,SAAS,QAAQ,GAAG,qBAAqB;;;;;;4BAE3C,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;oCAAwB,OAAO,QAAQ,EAAE;8CACvC,QAAQ,IAAI;mCADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;0BAO7B,8OAAC;;kCACC,8OAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAA0C;;;;;;kCAGjF,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,MAAM;wBACN,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;AAK1C", "debugId": null}}]}