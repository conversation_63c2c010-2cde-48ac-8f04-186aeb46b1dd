module.exports = {

"[project]/.next-internal/server/app/api/admin/images/[id]/ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/better-sqlite3 [external] (better-sqlite3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("better-sqlite3", () => require("better-sqlite3"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createDefaultAdmin": (()=>createDefaultAdmin),
    "default": (()=>__TURBOPACK__default__export__),
    "initializeDatabase": (()=>initializeDatabase),
    "migrateDatabase": (()=>migrateDatabase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/better-sqlite3 [external] (better-sqlite3, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
;
;
;
// Database file path
const dbPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data', 'study_app.db');
// Ensure data directory exists
const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(dbPath);
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dataDir)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dataDir, {
        recursive: true
    });
}
// Initialize database
const db = new __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__["default"](dbPath);
// Enable foreign keys
db.pragma('foreign_keys = ON');
function migrateDatabase() {
    try {
        // Check if new columns exist, if not add them
        const tableInfo = db.prepare("PRAGMA table_info(images)").all();
        const hasPageType = tableInfo.some((col)=>col.name === 'page_type');
        const hasUploadOrder = tableInfo.some((col)=>col.name === 'upload_order');
        if (!hasPageType) {
            db.exec("ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'");
            console.log('Added page_type column to images table');
        }
        if (!hasUploadOrder) {
            db.exec("ALTER TABLE images ADD COLUMN upload_order INTEGER");
            // Set upload order for existing images based on uploaded_at
            db.exec(`
        UPDATE images
        SET upload_order = (
          SELECT COUNT(*) + 1
          FROM images i2
          WHERE i2.class_id = images.class_id
          AND i2.subject_id = images.subject_id
          AND i2.uploaded_at < images.uploaded_at
        )
      `);
            console.log('Added upload_order column to images table');
        }
        // Check if processed column exists in ocr_text table
        const ocrTableInfo = db.prepare("PRAGMA table_info(ocr_text)").all();
        const hasProcessed = ocrTableInfo.some((col)=>col.name === 'processed');
        if (!hasProcessed) {
            db.exec("ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1");
            console.log('Added processed column to ocr_text table');
        }
    } catch (error) {
        console.error('Migration error:', error);
    }
}
function initializeDatabase() {
    // Users table
    db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),
      password_hash TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
    // Classes table
    db.exec(`
    CREATE TABLE IF NOT EXISTS classes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
    // Subjects table
    db.exec(`
    CREATE TABLE IF NOT EXISTS subjects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      class_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      UNIQUE(class_id, name)
    )
  `);
    // Images table
    db.exec(`
    CREATE TABLE IF NOT EXISTS images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      file_path TEXT NOT NULL,
      original_name TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),
      upload_order INTEGER,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);
    // OCR text table
    db.exec(`
    CREATE TABLE IF NOT EXISTS ocr_text (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      image_id INTEGER NOT NULL,
      content TEXT NOT NULL,
      processed BOOLEAN DEFAULT FALSE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE
    )
  `);
    // Questions table
    db.exec(`
    CREATE TABLE IF NOT EXISTS questions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      chapter TEXT,
      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),
      content TEXT NOT NULL,
      options TEXT, -- JSON string for MCQ options
      correct_answer TEXT,
      marks INTEGER DEFAULT 1,
      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);
    // Tests table
    db.exec(`
    CREATE TABLE IF NOT EXISTS tests (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      chapters TEXT, -- JSON string of selected chapters
      time_min INTEGER NOT NULL, -- Time limit in minutes
      total_marks INTEGER DEFAULT 0,
      instructions TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      created_by INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
      FOREIGN KEY (created_by) REFERENCES users(id)
    )
  `);
    // Test questions junction table
    db.exec(`
    CREATE TABLE IF NOT EXISTS test_questions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      test_id INTEGER NOT NULL,
      question_id INTEGER NOT NULL,
      question_order INTEGER NOT NULL,
      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
      UNIQUE(test_id, question_id),
      UNIQUE(test_id, question_order)
    )
  `);
    // Test results table
    db.exec(`
    CREATE TABLE IF NOT EXISTS test_results (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      test_id INTEGER NOT NULL,
      user_id INTEGER NOT NULL,
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      submitted_at DATETIME,
      total_score REAL DEFAULT 0,
      max_score REAL DEFAULT 0,
      time_taken INTEGER, -- Time taken in minutes
      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),
      graded_by INTEGER,
      graded_at DATETIME,
      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (graded_by) REFERENCES users(id),
      UNIQUE(test_id, user_id)
    )
  `);
    // Test answers table
    db.exec(`
    CREATE TABLE IF NOT EXISTS test_answers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      result_id INTEGER NOT NULL,
      question_id INTEGER NOT NULL,
      user_answer TEXT,
      is_correct BOOLEAN,
      score REAL DEFAULT 0,
      max_score REAL DEFAULT 0,
      graded_by INTEGER,
      graded_at DATETIME,
      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,
      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
      FOREIGN KEY (graded_by) REFERENCES users(id),
      UNIQUE(result_id, question_id)
    )
  `);
    // Create indexes for better performance
    db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);
    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);
    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);
    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);
  `);
    console.log('Database initialized successfully');
}
function createDefaultAdmin() {
    const bcrypt = __turbopack_context__.r("[project]/node_modules/bcryptjs/umd/index.js [app-route] (ecmascript)");
    const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');
    if (adminExists.count === 0) {
        const hashedPassword = bcrypt.hashSync('admin123', 10);
        db.prepare(`
      INSERT INTO users (name, email, role, password_hash)
      VALUES (?, ?, ?, ?)
    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);
        console.log('Default admin user created: <EMAIL> / admin123');
    }
}
const __TURBOPACK__default__export__ = db;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAuthCookie": (()=>clearAuthCookie),
    "createToken": (()=>createToken),
    "deleteUser": (()=>deleteUser),
    "getAllUsers": (()=>getAllUsers),
    "getCurrentUser": (()=>getCurrentUser),
    "hashPassword": (()=>hashPassword),
    "loginUser": (()=>loginUser),
    "registerUser": (()=>registerUser),
    "requireAdmin": (()=>requireAdmin),
    "requireAuth": (()=>requireAuth),
    "setAuthCookie": (()=>setAuthCookie),
    "updateUser": (()=>updateUser),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/sign.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/verify.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
;
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key-change-this-in-production');
function hashPassword(password) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hashSync(password, 10);
}
function verifyPassword(password, hash) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compareSync(password, hash);
}
async function createToken(user) {
    return await new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SignJWT"]({
        userId: user.id,
        email: user.email,
        role: user.role
    }).setProtectedHeader({
        alg: 'HS256'
    }).setIssuedAt().setExpirationTime('24h').sign(JWT_SECRET);
}
async function verifyToken(token) {
    try {
        const { payload } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jwtVerify"])(token, JWT_SECRET);
        return payload;
    } catch (error) {
        return null;
    }
}
async function loginUser(email, password) {
    try {
        const user = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT * FROM users WHERE email = ?').get(email);
        if (!user) {
            return {
                success: false,
                error: 'Invalid email or password'
            };
        }
        if (!verifyPassword(password, user.password_hash)) {
            return {
                success: false,
                error: 'Invalid email or password'
            };
        }
        const userWithoutPassword = {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            created_at: user.created_at
        };
        return {
            success: true,
            user: userWithoutPassword
        };
    } catch (error) {
        console.error('Login error:', error);
        return {
            success: false,
            error: 'Login failed'
        };
    }
}
async function getCurrentUser() {
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const token = cookieStore.get('auth-token')?.value;
        if (!token) {
            return null;
        }
        const payload = await verifyToken(token);
        if (!payload) {
            return null;
        }
        const user = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?').get(payload.userId);
        return user || null;
    } catch (error) {
        console.error('Get current user error:', error);
        return null;
    }
}
async function setAuthCookie(user) {
    const token = await createToken(user);
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.set('auth-token', token, {
        httpOnly: true,
        secure: ("TURBOPACK compile-time value", "development") === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 // 24 hours
    });
}
async function clearAuthCookie() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.delete('auth-token');
}
function registerUser(name, email, password, role) {
    try {
        // Check if user already exists
        const existingUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id FROM users WHERE email = ?').get(email);
        if (existingUser) {
            return {
                success: false,
                error: 'User with this email already exists'
            };
        }
        const hashedPassword = hashPassword(password);
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO users (name, email, role, password_hash)
      VALUES (?, ?, ?, ?)
    `).run(name, email, role, hashedPassword);
        const newUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?').get(result.lastInsertRowid);
        return {
            success: true,
            user: newUser
        };
    } catch (error) {
        console.error('Registration error:', error);
        return {
            success: false,
            error: 'Registration failed'
        };
    }
}
async function requireAuth() {
    const user = await getCurrentUser();
    return user;
}
async function requireAdmin() {
    const user = await getCurrentUser();
    if (!user || user.role !== 'admin') {
        return null;
    }
    return user;
}
function getAllUsers() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all();
}
function updateUser(id, updates) {
    try {
        const setClause = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');
        const values = Object.values(updates);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`).run(...values, id);
        return true;
    } catch (error) {
        console.error('Update user error:', error);
        return false;
    }
}
function deleteUser(id) {
    try {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM users WHERE id = ?').run(id);
        return true;
    } catch (error) {
        console.error('Delete user error:', error);
        return false;
    }
}
}}),
"[externals]/worker_threads [external] (worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("worker_threads", () => require("worker_threads"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/src/lib/upload.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "autoAssignPageTypes": (()=>autoAssignPageTypes),
    "deleteImage": (()=>deleteImage),
    "ensureUploadDir": (()=>ensureUploadDir),
    "getFileSize": (()=>getFileSize),
    "getImages": (()=>getImages),
    "getOCRText": (()=>getOCRText),
    "parseQuestionsFromOCR": (()=>parseQuestionsFromOCR),
    "processOCR": (()=>processOCR),
    "saveUploadedFile": (()=>saveUploadedFile),
    "updateImagePageType": (()=>updateImagePageType)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tesseract$2e$js$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tesseract.js/src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
;
function ensureUploadDir(classId, subjectId) {
    const className = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT name FROM classes WHERE id = ?').get(classId);
    const subjectName = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT name FROM subjects WHERE id = ?').get(subjectId);
    if (!className || !subjectName) {
        throw new Error('Invalid class or subject ID');
    }
    const uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'uploads', className.name, subjectName.name);
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(uploadDir)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(uploadDir, {
            recursive: true
        });
    }
    return uploadDir;
}
async function saveUploadedFile(file, classId, subjectId, pageType = 'unassigned') {
    try {
        const uploadDir = ensureUploadDir(classId, subjectId);
        // Generate unique filename
        const timestamp = Date.now();
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file.name);
        const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
        const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, filename);
        // Save file to disk
        const buffer = Buffer.from(await file.arrayBuffer());
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, buffer);
        // Get the next upload order for this class/subject
        const maxOrder = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT MAX(upload_order) as max_order
      FROM images
      WHERE class_id = ? AND subject_id = ?
    `).get(classId, subjectId);
        const uploadOrder = (maxOrder?.max_order || 0) + 1;
        // Save file info to database
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO images (file_path, original_name, class_id, subject_id, page_type, upload_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `).run(filePath, file.name, classId, subjectId, pageType, uploadOrder);
        return {
            id: result.lastInsertRowid,
            filePath: filePath
        };
    } catch (error) {
        console.error('File upload error:', error);
        throw new Error('Failed to save uploaded file');
    }
}
async function processOCR(imageId) {
    try {
        const image = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT file_path FROM images WHERE id = ?').get(imageId);
        if (!image) {
            throw new Error('Image not found');
        }
        // Check if OCR already exists
        const existingOCR = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT content FROM ocr_text WHERE image_id = ?').get(imageId);
        if (existingOCR) {
            console.log(`OCR already exists for image ID: ${imageId}`);
            return existingOCR.content;
        }
        // Check if file exists
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(image.file_path)) {
            throw new Error('Image file not found on disk');
        }
        console.log(`Starting OCR processing for image ID: ${imageId}, file: ${image.file_path}`);
        // Create Tesseract worker with logging
        const worker = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tesseract$2e$js$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createWorker"])('eng', 1, {
            logger: (m)=>console.log(`Tesseract [${imageId}]:`, m)
        });
        try {
            console.log(`Performing OCR recognition for image ID: ${imageId}`);
            // Perform OCR
            const { data: { text } } = await worker.recognize(image.file_path);
            console.log(`OCR text extracted for image ID: ${imageId}, length: ${text.length} characters`);
            // Save OCR text to database
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 1)
      `).run(imageId, text);
            console.log(`OCR completed and saved for image ID: ${imageId}`);
            return text;
        } finally{
            await worker.terminate();
            console.log(`Tesseract worker terminated for image ID: ${imageId}`);
        }
    } catch (error) {
        console.error(`OCR processing error for image ${imageId}:`, error);
        // Save error state to database
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 0)
      `).run(imageId, `OCR Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } catch (dbError) {
            console.error('Failed to save OCR error to database:', dbError);
        }
        throw new Error(`Failed to process OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
function getOCRText(imageId) {
    const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT content FROM ocr_text WHERE image_id = ? ORDER BY created_at DESC LIMIT 1').get(imageId);
    return result ? result.content : null;
}
function getImages(classId, subjectId) {
    let query = `
    SELECT i.*, c.name as class_name, s.name as subject_name,
           ocr.content as ocr_content, ocr.processed
    FROM images i
    JOIN classes c ON i.class_id = c.id
    JOIN subjects s ON i.subject_id = s.id
    LEFT JOIN ocr_text ocr ON i.id = ocr.image_id
  `;
    const params = [];
    const conditions = [];
    if (classId) {
        conditions.push('i.class_id = ?');
        params.push(classId);
    }
    if (subjectId) {
        conditions.push('i.subject_id = ?');
        params.push(subjectId);
    }
    if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
    }
    query += ' ORDER BY COALESCE(i.upload_order, 999999) ASC, i.uploaded_at ASC';
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(query).all(...params);
}
function deleteImage(imageId) {
    try {
        const image = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT file_path FROM images WHERE id = ?').get(imageId);
        if (image && __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(image.file_path)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(image.file_path);
        }
        // Delete from database (OCR text will be deleted by foreign key cascade)
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM images WHERE id = ?').run(imageId);
        return true;
    } catch (error) {
        console.error('Delete image error:', error);
        return false;
    }
}
function parseQuestionsFromOCR(ocrText, imageId) {
    const questions = [];
    // Get chapter context from image if provided
    let chapterContext = '';
    if (imageId) {
        const image = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT page_type FROM images WHERE id = ?').get(imageId);
        if (image && image.page_type && image.page_type.startsWith('chapter-')) {
            chapterContext = image.page_type.replace('chapter-', 'Chapter ');
        }
    }
    // Split text into lines and clean up
    const lines = ocrText.split('\n').map((line)=>line.trim()).filter((line)=>line.length > 0);
    let currentQuestion = '';
    let currentOptions = [];
    let questionType = 'short_answer'; // default
    for(let i = 0; i < lines.length; i++){
        const line = lines[i];
        // Detect question patterns
        if (line.match(/^\d+[\.\)]\s*/) || line.match(/^Q\d*[\.\)]\s*/i)) {
            // Save previous question if exists
            if (currentQuestion) {
                questions.push({
                    type: questionType,
                    content: currentQuestion,
                    options: currentOptions.length > 0 ? currentOptions : undefined,
                    chapter: chapterContext
                });
            }
            // Start new question
            currentQuestion = line.replace(/^\d+[\.\)]\s*/, '').replace(/^Q\d*[\.\)]\s*/i, '');
            currentOptions = [];
            // Detect question type
            if (currentQuestion.toLowerCase().includes('true') && currentQuestion.toLowerCase().includes('false')) {
                questionType = 'true_false';
            } else if (currentQuestion.includes('_____') || currentQuestion.includes('____')) {
                questionType = 'fill_blank';
            } else {
                questionType = 'short_answer';
            }
        } else if (line.match(/^[a-d][\.\)]\s*/i) || line.match(/^\([a-d]\)\s*/i)) {
            if (currentQuestion) {
                questionType = 'mcq';
                const option = line.replace(/^[a-d][\.\)]\s*/i, '').replace(/^\([a-d]\)\s*/i, '');
                currentOptions.push(option);
            }
        } else if (currentQuestion && !line.match(/^[a-d][\.\)]\s*/i)) {
            currentQuestion += ' ' + line;
        }
    }
    // Save last question
    if (currentQuestion) {
        questions.push({
            type: questionType,
            content: currentQuestion,
            options: currentOptions.length > 0 ? currentOptions : undefined,
            chapter: chapterContext
        });
    }
    return questions;
}
function updateImagePageType(imageId, pageType) {
    try {
        const validPageTypes = [
            'cover',
            'contents',
            'unassigned',
            ...Array.from({
                length: 30
            }, (_, i)=>`chapter-${i + 1}`)
        ];
        if (!validPageTypes.includes(pageType)) {
            throw new Error('Invalid page type');
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('UPDATE images SET page_type = ? WHERE id = ?').run(pageType, imageId);
        return true;
    } catch (error) {
        console.error('Update page type error:', error);
        return false;
    }
}
function autoAssignPageTypes(classId, subjectId) {
    try {
        const images = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT id, upload_order, page_type
      FROM images
      WHERE class_id = ? AND subject_id = ?
      ORDER BY upload_order ASC
    `).all(classId, subjectId);
        let currentChapter = 1;
        let foundFirstChapter = false;
        for (const image of images){
            if (image.page_type !== 'unassigned') {
                // If this is a chapter marker, update current chapter
                const chapterMatch = image.page_type.match(/^chapter-(\d+)$/);
                if (chapterMatch) {
                    currentChapter = parseInt(chapterMatch[1]);
                    foundFirstChapter = true;
                }
                continue;
            }
            // Auto-assign based on position
            if (!foundFirstChapter) {
                // Before any chapter is found, assume it's cover or contents
                if (image.upload_order === 1) {
                    updateImagePageType(image.id, 'cover');
                } else {
                    updateImagePageType(image.id, 'contents');
                }
            } else {
                // After a chapter is found, assign to current chapter
                updateImagePageType(image.id, `chapter-${currentChapter}`);
            }
        }
    } catch (error) {
        console.error('Auto-assign page types error:', error);
    }
}
function getFileSize(filePath) {
    try {
        const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
        const bytes = stats.size;
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = [
            'Bytes',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    } catch (error) {
        return 'Unknown';
    }
}
}}),
"[project]/src/app/api/admin/images/[id]/ocr/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$upload$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/upload.ts [app-route] (ecmascript)");
;
;
;
async function POST(request, { params }) {
    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAdmin"])();
    if (!user) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Unauthorized'
        }, {
            status: 401
        });
    }
    try {
        const { id } = await params;
        const imageId = parseInt(id);
        if (isNaN(imageId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid image ID'
            }, {
                status: 400
            });
        }
        console.log(`Starting OCR processing for image ID: ${imageId}`);
        // Process OCR
        const ocrText = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$upload$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processOCR"])(imageId);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            text: ocrText,
            message: 'OCR processing completed successfully'
        });
    } catch (error) {
        console.error('OCR API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process OCR',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__567c5107._.js.map