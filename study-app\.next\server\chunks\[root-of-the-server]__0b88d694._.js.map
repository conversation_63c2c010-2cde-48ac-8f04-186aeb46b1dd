{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport path from 'path';\nimport fs from 'fs';\n\n// Database file path\nconst dbPath = path.join(process.cwd(), 'data', 'study_app.db');\n\n// Ensure data directory exists\nconst dataDir = path.dirname(dbPath);\nif (!fs.existsSync(dataDir)) {\n  fs.mkdirSync(dataDir, { recursive: true });\n}\n\n// Initialize database\nconst db = new Database(dbPath);\n\n// Enable foreign keys\ndb.pragma('foreign_keys = ON');\n\n// Database migration function\nexport function migrateDatabase() {\n  try {\n    // Check if new columns exist, if not add them\n    const tableInfo = db.prepare(\"PRAGMA table_info(images)\").all() as any[];\n    const hasPageType = tableInfo.some(col => col.name === 'page_type');\n    const hasUploadOrder = tableInfo.some(col => col.name === 'upload_order');\n\n    if (!hasPageType) {\n      db.exec(\"ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'\");\n      console.log('Added page_type column to images table');\n    }\n\n    if (!hasUploadOrder) {\n      db.exec(\"ALTER TABLE images ADD COLUMN upload_order INTEGER\");\n      // Set upload order for existing images based on uploaded_at\n      db.exec(`\n        UPDATE images\n        SET upload_order = (\n          SELECT COUNT(*) + 1\n          FROM images i2\n          WHERE i2.class_id = images.class_id\n          AND i2.subject_id = images.subject_id\n          AND i2.uploaded_at < images.uploaded_at\n        )\n      `);\n      console.log('Added upload_order column to images table');\n    }\n\n    // Check if processed column exists in ocr_text table\n    const ocrTableInfo = db.prepare(\"PRAGMA table_info(ocr_text)\").all() as any[];\n    const hasProcessed = ocrTableInfo.some(col => col.name === 'processed');\n\n    if (!hasProcessed) {\n      db.exec(\"ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1\");\n      console.log('Added processed column to ocr_text table');\n    }\n\n    // Check if books table exists\n    const tablesResult = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n    if (!tablesResult) {\n      console.log('Creating books table...');\n      db.exec(`\n        CREATE TABLE books (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          title TEXT NOT NULL,\n          class_id INTEGER NOT NULL,\n          subject_id INTEGER NOT NULL,\n          description TEXT,\n          cover_image_path TEXT,\n          total_pages INTEGER DEFAULT 0,\n          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n        )\n      `);\n      console.log('Books table created successfully');\n    }\n\n    // Check if images table has book_id column\n    const hasBookId = tableInfo.some(col => col.name === 'book_id');\n    if (!hasBookId) {\n      console.log('Adding book_id column to images table...');\n      db.exec(\"ALTER TABLE images ADD COLUMN book_id INTEGER\");\n      console.log('Added book_id column to images table');\n    }\n\n  } catch (error) {\n    console.error('Migration error:', error);\n  }\n}\n\n// Database schema initialization\nexport function initializeDatabase() {\n  // Users table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),\n      password_hash TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Classes table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS classes (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Subjects table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS subjects (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      UNIQUE(class_id, name)\n    )\n  `);\n\n  // Books table - containers for textbook content\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS books (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      description TEXT,\n      cover_image_path TEXT,\n      total_pages INTEGER DEFAULT 0,\n      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Images table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS images (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      file_path TEXT NOT NULL,\n      original_name TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),\n      upload_order INTEGER,\n      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // OCR text table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS ocr_text (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      image_id INTEGER NOT NULL,\n      content TEXT NOT NULL,\n      processed BOOLEAN DEFAULT FALSE,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Questions table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapter TEXT,\n      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),\n      content TEXT NOT NULL,\n      options TEXT, -- JSON string for MCQ options\n      correct_answer TEXT,\n      marks INTEGER DEFAULT 1,\n      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Tests table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS tests (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapters TEXT, -- JSON string of selected chapters\n      time_min INTEGER NOT NULL, -- Time limit in minutes\n      total_marks INTEGER DEFAULT 0,\n      instructions TEXT,\n      is_active BOOLEAN DEFAULT TRUE,\n      created_by INTEGER NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (created_by) REFERENCES users(id)\n    )\n  `);\n\n  // Test questions junction table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      question_order INTEGER NOT NULL,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      UNIQUE(test_id, question_id),\n      UNIQUE(test_id, question_order)\n    )\n  `);\n\n  // Test results table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_results (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      user_id INTEGER NOT NULL,\n      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      submitted_at DATETIME,\n      total_score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      time_taken INTEGER, -- Time taken in minutes\n      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(test_id, user_id)\n    )\n  `);\n\n  // Test answers table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_answers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      result_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      user_answer TEXT,\n      is_correct BOOLEAN,\n      score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(result_id, question_id)\n    )\n  `);\n\n  // Create indexes for better performance\n  db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);\n    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);\n    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);\n    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);\n  `);\n\n  console.log('Database initialized successfully');\n\n  // Run migrations after initialization\n  migrateDatabase();\n}\n\n// Create default admin user if none exists\nexport function createDefaultAdmin() {\n  const bcrypt = require('bcryptjs');\n\n  const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');\n\n  if (adminExists.count === 0) {\n    const hashedPassword = bcrypt.hashSync('admin123', 10);\n\n    db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);\n\n    console.log('Default admin user created: <EMAIL> / admin123');\n  }\n}\n\n// Clear OCR data to force re-processing with real OCR\nexport function clearOCRData() {\n  try {\n    console.log('Clearing existing OCR data to enable fresh processing...');\n    db.exec('DELETE FROM ocr_text');\n    db.exec('DELETE FROM questions');\n    console.log('OCR data cleared successfully');\n  } catch (error) {\n    console.error('Error clearing OCR data:', error);\n  }\n}\n\n// Initialize database on import\ninitializeDatabase();\ncreateDefaultAdmin();\n\nexport default db;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEA,qBAAqB;AACrB,MAAM,SAAS,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAEhD,+BAA+B;AAC/B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;AAC7B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,sBAAsB;AACtB,MAAM,KAAK,IAAI,2HAAA,CAAA,UAAQ,CAAC;AAExB,sBAAsB;AACtB,GAAG,MAAM,CAAC;AAGH,SAAS;IACd,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,6BAA6B,GAAG;QAC7D,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACvD,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE1D,IAAI,CAAC,aAAa;YAChB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,gBAAgB;YACnB,GAAG,IAAI,CAAC;YACR,4DAA4D;YAC5D,GAAG,IAAI,CAAC,CAAC;;;;;;;;;MAST,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,qDAAqD;QACrD,MAAM,eAAe,GAAG,OAAO,CAAC,+BAA+B,GAAG;QAClE,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE3D,IAAI,CAAC,cAAc;YACjB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,8BAA8B;QAC9B,MAAM,eAAe,GAAG,OAAO,CAAC,sEAAsE,GAAG;QACzG,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;MAeT,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,2CAA2C;QAC3C,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACrD,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAGO,SAAS;IACd,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gBAAgB;IAChB,GAAG,IAAI,CAAC,CAAC;;;;;;;EAOT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gDAAgD;IAChD,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;EAeT,CAAC;IAED,eAAe;IACf,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;EAaT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;EAST,CAAC;IAED,kBAAkB;IAClB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,gCAAgC;IAChC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;EAWT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkBT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,wCAAwC;IACxC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,QAAQ,GAAG,CAAC;IAEZ,sCAAsC;IACtC;AACF;AAGO,SAAS;IACd,MAAM;IAEN,MAAM,cAAc,GAAG,OAAO,CAAC,sDAAsD,GAAG,CAAC;IAEzF,IAAI,YAAY,KAAK,KAAK,GAAG;QAC3B,MAAM,iBAAiB,OAAO,QAAQ,CAAC,YAAY;QAEnD,GAAG,OAAO,CAAC,CAAC;;;IAGZ,CAAC,EAAE,GAAG,CAAC,iBAAiB,sBAAsB,SAAS;QAEvD,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,SAAS;IACd,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC;QACR,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF;AAEA,gCAAgC;AAChC;AACA;uCAEe", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/models.ts"], "sourcesContent": ["import db from './database';\n\n// Types\nexport interface Class {\n  id: number;\n  name: string;\n  description?: string;\n  created_at: string;\n}\n\nexport interface Subject {\n  id: number;\n  class_id: number;\n  name: string;\n  description?: string;\n  created_at: string;\n  class_name?: string;\n}\n\nexport interface Book {\n  id: number;\n  title: string;\n  class_id: number;\n  subject_id: number;\n  description?: string;\n  cover_image_path?: string;\n  total_pages: number;\n  status: 'draft' | 'processing' | 'completed';\n  created_at: string;\n  updated_at: string;\n  // Joined fields\n  class_name?: string;\n  subject_name?: string;\n}\n\nexport interface BookWithStats extends Book {\n  uploaded_pages: number;\n  processed_pages: number;\n  total_questions: number;\n}\n\nexport interface Question {\n  id: number;\n  class_id: number;\n  subject_id: number;\n  chapter?: string;\n  type: 'mcq' | 'true_false' | 'fill_blank' | 'short_answer' | 'long_answer';\n  content: string;\n  options?: string; // JSON string\n  correct_answer?: string;\n  marks: number;\n  difficulty?: 'easy' | 'medium' | 'hard';\n  created_at: string;\n  updated_at: string;\n  class_name?: string;\n  subject_name?: string;\n}\n\nexport interface Test {\n  id: number;\n  title: string;\n  class_id: number;\n  subject_id: number;\n  chapters?: string; // JSON string\n  time_min: number;\n  total_marks: number;\n  instructions?: string;\n  is_active: boolean;\n  created_by: number;\n  created_at: string;\n  class_name?: string;\n  subject_name?: string;\n  creator_name?: string;\n}\n\n// Classes CRUD operations\nexport const ClassModel = {\n  getAll(): Class[] {\n    return db.prepare('SELECT * FROM classes ORDER BY name').all() as Class[];\n  },\n\n  getById(id: number): Class | null {\n    return db.prepare('SELECT * FROM classes WHERE id = ?').get(id) as Class || null;\n  },\n\n  create(name: string, description?: string): Class {\n    const result = db.prepare(`\n      INSERT INTO classes (name, description)\n      VALUES (?, ?)\n    `).run(name, description || null);\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, name: string, description?: string): boolean {\n    try {\n      db.prepare(`\n        UPDATE classes \n        SET name = ?, description = ?\n        WHERE id = ?\n      `).run(name, description || null, id);\n      return true;\n    } catch (error) {\n      console.error('Update class error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM classes WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete class error:', error);\n      return false;\n    }\n  }\n};\n\n// Subjects CRUD operations\nexport const SubjectModel = {\n  getAll(): Subject[] {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      ORDER BY c.name, s.name\n    `).all() as Subject[];\n  },\n\n  getByClassId(classId: number): Subject[] {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      WHERE s.class_id = ?\n      ORDER BY s.name\n    `).all(classId) as Subject[];\n  },\n\n  getById(id: number): Subject | null {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      WHERE s.id = ?\n    `).get(id) as Subject || null;\n  },\n\n  create(classId: number, name: string, description?: string): Subject {\n    const result = db.prepare(`\n      INSERT INTO subjects (class_id, name, description)\n      VALUES (?, ?, ?)\n    `).run(classId, name, description || null);\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, name: string, description?: string): boolean {\n    try {\n      db.prepare(`\n        UPDATE subjects \n        SET name = ?, description = ?\n        WHERE id = ?\n      `).run(name, description || null, id);\n      return true;\n    } catch (error) {\n      console.error('Update subject error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM subjects WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete subject error:', error);\n      return false;\n    }\n  }\n};\n\n// Questions CRUD operations\nexport const QuestionModel = {\n  getAll(filters?: { classId?: number; subjectId?: number; type?: string; chapter?: string }): Question[] {\n    let query = `\n      SELECT q.*, c.name as class_name, s.name as subject_name\n      FROM questions q\n      JOIN classes c ON q.class_id = c.id\n      JOIN subjects s ON q.subject_id = s.id\n    `;\n    \n    const conditions: string[] = [];\n    const params: any[] = [];\n    \n    if (filters?.classId) {\n      conditions.push('q.class_id = ?');\n      params.push(filters.classId);\n    }\n    \n    if (filters?.subjectId) {\n      conditions.push('q.subject_id = ?');\n      params.push(filters.subjectId);\n    }\n    \n    if (filters?.type) {\n      conditions.push('q.type = ?');\n      params.push(filters.type);\n    }\n    \n    if (filters?.chapter) {\n      conditions.push('q.chapter = ?');\n      params.push(filters.chapter);\n    }\n    \n    if (conditions.length > 0) {\n      query += ' WHERE ' + conditions.join(' AND ');\n    }\n    \n    query += ' ORDER BY q.created_at DESC';\n    \n    return db.prepare(query).all(...params) as Question[];\n  },\n\n  getById(id: number): Question | null {\n    return db.prepare(`\n      SELECT q.*, c.name as class_name, s.name as subject_name\n      FROM questions q\n      JOIN classes c ON q.class_id = c.id\n      JOIN subjects s ON q.subject_id = s.id\n      WHERE q.id = ?\n    `).get(id) as Question || null;\n  },\n\n  create(questionData: Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>): Question {\n    const result = db.prepare(`\n      INSERT INTO questions (\n        class_id, subject_id, chapter, type, content, options, \n        correct_answer, marks, difficulty\n      )\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `).run(\n      questionData.class_id,\n      questionData.subject_id,\n      questionData.chapter || null,\n      questionData.type,\n      questionData.content,\n      questionData.options || null,\n      questionData.correct_answer || null,\n      questionData.marks,\n      questionData.difficulty || null\n    );\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, questionData: Partial<Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>>): boolean {\n    try {\n      const fields = Object.keys(questionData).filter(key => questionData[key as keyof typeof questionData] !== undefined);\n      const setClause = fields.map(field => `${field} = ?`).join(', ');\n      const values = fields.map(field => questionData[field as keyof typeof questionData]);\n      \n      db.prepare(`\n        UPDATE questions \n        SET ${setClause}, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ?\n      `).run(...values, id);\n      \n      return true;\n    } catch (error) {\n      console.error('Update question error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM questions WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete question error:', error);\n      return false;\n    }\n  },\n\n  getChapters(classId?: number, subjectId?: number): string[] {\n    let query = 'SELECT DISTINCT chapter FROM questions WHERE chapter IS NOT NULL';\n    const params: any[] = [];\n\n    if (classId) {\n      query += ' AND class_id = ?';\n      params.push(classId);\n    }\n\n    if (subjectId) {\n      query += ' AND subject_id = ?';\n      params.push(subjectId);\n    }\n\n    query += ' ORDER BY chapter';\n\n    const results = db.prepare(query).all(...params) as { chapter: string }[];\n    return results.map(r => r.chapter);\n  },\n\n  // Get available chapters from page types\n  getChaptersFromPageTypes(classId?: number, subjectId?: number): string[] {\n    let query = `\n      SELECT DISTINCT page_type\n      FROM images\n      WHERE page_type LIKE 'chapter-%'\n    `;\n    const params: any[] = [];\n\n    if (classId) {\n      query += ' AND class_id = ?';\n      params.push(classId);\n    }\n\n    if (subjectId) {\n      query += ' AND subject_id = ?';\n      params.push(subjectId);\n    }\n\n    query += ' ORDER BY page_type';\n\n    const results = db.prepare(query).all(...params) as { page_type: string }[];\n    return results.map(r => r.page_type.replace('chapter-', 'Chapter '));\n  }\n};\n\n// Book Model\nexport const BookModel = {\n  getAll(): BookWithStats[] {\n    try {\n      // Check if books table exists\n      const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n      if (!tableExists) {\n        console.log('Books table does not exist yet, returning empty array');\n        return [];\n      }\n\n      const books = db.prepare(`\n        SELECT\n          b.*,\n          c.name as class_name,\n          s.name as subject_name,\n          COUNT(DISTINCT i.id) as uploaded_pages,\n          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,\n          0 as total_questions\n        FROM books b\n        LEFT JOIN classes c ON b.class_id = c.id\n        LEFT JOIN subjects s ON b.subject_id = s.id\n        LEFT JOIN images i ON b.id = i.book_id\n        LEFT JOIN ocr_text ot ON i.id = ot.image_id\n        GROUP BY b.id\n        ORDER BY b.updated_at DESC\n      `).all() as BookWithStats[];\n\n      return books;\n    } catch (error) {\n      console.error('Error fetching books:', error);\n      return [];\n    }\n  },\n\n  getById(id: number): BookWithStats | null {\n    try {\n      // Check if books table exists\n      const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n      if (!tableExists) {\n        return null;\n      }\n\n      const book = db.prepare(`\n        SELECT\n          b.*,\n          c.name as class_name,\n          s.name as subject_name,\n          COUNT(DISTINCT i.id) as uploaded_pages,\n          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,\n          0 as total_questions\n        FROM books b\n        LEFT JOIN classes c ON b.class_id = c.id\n        LEFT JOIN subjects s ON b.subject_id = s.id\n        LEFT JOIN images i ON b.id = i.book_id\n        LEFT JOIN ocr_text ot ON i.id = ot.image_id\n        WHERE b.id = ?\n        GROUP BY b.id\n      `).get(id) as BookWithStats | undefined;\n\n      return book || null;\n    } catch (error) {\n      console.error('Error fetching book by id:', error);\n      return null;\n    }\n  },\n\n  create(data: {\n    title: string;\n    class_id: number;\n    subject_id: number;\n    description?: string;\n  }): number {\n    const result = db.prepare(`\n      INSERT INTO books (title, class_id, subject_id, description)\n      VALUES (?, ?, ?, ?)\n    `).run(data.title, data.class_id, data.subject_id, data.description || null);\n\n    return result.lastInsertRowid as number;\n  },\n\n  update(id: number, data: Partial<Book>): boolean {\n    const fields = [];\n    const values = [];\n\n    if (data.title !== undefined) {\n      fields.push('title = ?');\n      values.push(data.title);\n    }\n    if (data.description !== undefined) {\n      fields.push('description = ?');\n      values.push(data.description);\n    }\n    if (data.status !== undefined) {\n      fields.push('status = ?');\n      values.push(data.status);\n    }\n\n    if (fields.length === 0) return false;\n\n    fields.push('updated_at = CURRENT_TIMESTAMP');\n    values.push(id);\n\n    const result = db.prepare(`\n      UPDATE books SET ${fields.join(', ')} WHERE id = ?\n    `).run(...values);\n\n    return result.changes > 0;\n  },\n\n  delete(id: number): boolean {\n    const result = db.prepare('DELETE FROM books WHERE id = ?').run(id);\n    return result.changes > 0;\n  },\n\n  getImages(bookId: number) {\n    try {\n      // Check if book_id column exists in images table\n      const tableInfo = db.prepare(\"PRAGMA table_info(images)\").all() as any[];\n      const hasBookId = tableInfo.some(col => col.name === 'book_id');\n\n      if (!hasBookId) {\n        // If book_id column doesn't exist yet, return empty array\n        return [];\n      }\n\n      return db.prepare(`\n        SELECT\n          i.*,\n          CASE WHEN ot.id IS NOT NULL THEN 1 ELSE 0 END as has_ocr,\n          CASE WHEN ot.processed = 1 THEN 1 ELSE 0 END as ocr_processed\n        FROM images i\n        LEFT JOIN ocr_text ot ON i.id = ot.image_id\n        WHERE i.book_id = ?\n        ORDER BY i.upload_order ASC, i.id ASC\n      `).all(bookId);\n    } catch (error) {\n      console.error('Error fetching book images:', error);\n      return [];\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AA4EO,MAAM,aAAa;IACxB;QACE,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG;IAC9D;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sCAAsC,GAAG,CAAC,OAAgB;IAC9E;IAEA,QAAO,IAAY,EAAE,WAAoB;QACvC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe;QAE5B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,IAAY,EAAE,WAAoB;QACnD,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIZ,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe,MAAM;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oCAAoC,GAAG,CAAC;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;AACF;AAGO,MAAM,eAAe;IAC1B;QACE,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAKnB,CAAC,EAAE,GAAG;IACR;IAEA,cAAa,OAAe;QAC1B,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAMnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAKnB,CAAC,EAAE,GAAG,CAAC,OAAkB;IAC3B;IAEA,QAAO,OAAe,EAAE,IAAY,EAAE,WAAoB;QACxD,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,SAAS,MAAM,eAAe;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,IAAY,EAAE,WAAoB;QACnD,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIZ,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe,MAAM;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,qCAAqC,GAAG,CAAC;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAO,OAAmF;QACxF,IAAI,QAAQ,CAAC;;;;;IAKb,CAAC;QAED,MAAM,aAAuB,EAAE;QAC/B,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS,SAAS;YACpB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,OAAO;QAC7B;QAEA,IAAI,SAAS,WAAW;YACtB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,SAAS;QAC/B;QAEA,IAAI,SAAS,MAAM;YACjB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,IAAI;QAC1B;QAEA,IAAI,SAAS,SAAS;YACpB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,OAAO;QAC7B;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,SAAS,YAAY,WAAW,IAAI,CAAC;QACvC;QAEA,SAAS;QAET,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;IAClC;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAMnB,CAAC,EAAE,GAAG,CAAC,OAAmB;IAC5B;IAEA,QAAO,YAAgG;QACrG,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAM3B,CAAC,EAAE,GAAG,CACJ,aAAa,QAAQ,EACrB,aAAa,UAAU,EACvB,aAAa,OAAO,IAAI,MACxB,aAAa,IAAI,EACjB,aAAa,OAAO,EACpB,aAAa,OAAO,IAAI,MACxB,aAAa,cAAc,IAAI,MAC/B,aAAa,KAAK,EAClB,aAAa,UAAU,IAAI;QAG7B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,YAAyG;QAC1H,IAAI;YACF,MAAM,SAAS,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,YAAY,CAAC,IAAiC,KAAK;YAC1G,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA,QAAS,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;YAC3D,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA,QAAS,YAAY,CAAC,MAAmC;YAEnF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;YAEN,EAAE,UAAU;;MAElB,CAAC,EAAE,GAAG,IAAI,QAAQ;YAElB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sCAAsC,GAAG,CAAC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,aAAY,OAAgB,EAAE,SAAkB;QAC9C,IAAI,QAAQ;QACZ,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS;YACX,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,WAAW;YACb,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QAET,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;QACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;IACnC;IAEA,yCAAyC;IACzC,0BAAyB,OAAgB,EAAE,SAAkB;QAC3D,IAAI,QAAQ,CAAC;;;;IAIb,CAAC;QACD,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS;YACX,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,WAAW;YACb,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QAET,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;QACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,OAAO,CAAC,YAAY;IAC1D;AACF;AAGO,MAAM,YAAY;IACvB;QACE,IAAI;YACF,8BAA8B;YAC9B,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sEAAsE,GAAG;YACxG,IAAI,CAAC,aAAa;gBAChB,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;;;;;MAe1B,CAAC,EAAE,GAAG;YAEN,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,EAAE;QACX;IACF;IAEA,SAAQ,EAAU;QAChB,IAAI;YACF,8BAA8B;YAC9B,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sEAAsE,GAAG;YACxG,IAAI,CAAC,aAAa;gBAChB,OAAO;YACT;YAEA,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;;;;;MAezB,CAAC,EAAE,GAAG,CAAC;YAEP,OAAO,QAAQ;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,QAAO,IAKN;QACC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,QAAQ,EAAE,KAAK,UAAU,EAAE,KAAK,WAAW,IAAI;QAEvE,OAAO,OAAO,eAAe;IAC/B;IAEA,QAAO,EAAU,EAAE,IAAmB;QACpC,MAAM,SAAS,EAAE;QACjB,MAAM,SAAS,EAAE;QAEjB,IAAI,KAAK,KAAK,KAAK,WAAW;YAC5B,OAAO,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,KAAK,KAAK;QACxB;QACA,IAAI,KAAK,WAAW,KAAK,WAAW;YAClC,OAAO,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,KAAK,WAAW;QAC9B;QACA,IAAI,KAAK,MAAM,KAAK,WAAW;YAC7B,OAAO,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,KAAK,MAAM;QACzB;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;QAEhC,OAAO,IAAI,CAAC;QACZ,OAAO,IAAI,CAAC;QAEZ,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;uBACR,EAAE,OAAO,IAAI,CAAC,MAAM;IACvC,CAAC,EAAE,GAAG,IAAI;QAEV,OAAO,OAAO,OAAO,GAAG;IAC1B;IAEA,QAAO,EAAU;QACf,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,kCAAkC,GAAG,CAAC;QAChE,OAAO,OAAO,OAAO,GAAG;IAC1B;IAEA,WAAU,MAAc;QACtB,IAAI;YACF,iDAAiD;YACjD,MAAM,YAAY,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,6BAA6B,GAAG;YAC7D,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YAErD,IAAI,CAAC,WAAW;gBACd,0DAA0D;gBAC1D,OAAO,EAAE;YACX;YAEA,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;MASnB,CAAC,EAAE,GAAG,CAAC;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;IACF;AACF", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/api/admin/books/upload-pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { BookModel } from '@/lib/models';\nimport fs from 'fs';\nimport path from 'path';\nimport pdf from 'pdf-parse';\nimport db from '@/lib/database';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const pdfFile = formData.get('pdf') as File;\n    const bookId = parseInt(formData.get('bookId') as string);\n\n    if (!pdfFile || !bookId) {\n      return NextResponse.json(\n        { success: false, error: 'PDF file and book ID are required' },\n        { status: 400 }\n      );\n    }\n\n    // Verify book exists\n    const book = BookModel.getById(bookId);\n    if (!book) {\n      return NextResponse.json(\n        { success: false, error: 'Book not found' },\n        { status: 404 }\n      );\n    }\n\n    // Create upload directory for this book\n    const uploadDir = path.join(process.cwd(), 'uploads', 'books', bookId.toString());\n    if (!fs.existsSync(uploadDir)) {\n      fs.mkdirSync(uploadDir, { recursive: true });\n    }\n\n    // Save PDF file\n    const pdfBuffer = Buffer.from(await pdfFile.arrayBuffer());\n    const timestamp = Date.now();\n    const pdfPath = path.join(uploadDir, `${timestamp}_${pdfFile.name}`);\n    fs.writeFileSync(pdfPath, pdfBuffer);\n\n    try {\n      // Parse PDF to get basic info and text content\n      const pdfData = await pdf(pdfBuffer);\n      const pageCount = pdfData.numpages;\n      const textContent = pdfData.text;\n\n      console.log(`PDF parsed: ${pageCount} pages, ${textContent.length} characters of text`);\n\n      // For now, create placeholder image records for each page\n      // In a production system, you'd use a proper PDF-to-image converter\n      const insertImage = db.prepare(`\n        INSERT INTO images (book_id, original_name, file_path, page_type, upload_order)\n        VALUES (?, ?, ?, ?, ?)\n      `);\n\n      // Create placeholder entries for each page\n      for (let i = 1; i <= pageCount; i++) {\n        const fileName = `page-${i}.pdf-placeholder`;\n        const placeholderPath = path.join(uploadDir, fileName);\n\n        // Create a simple placeholder file\n        fs.writeFileSync(placeholderPath, `PDF Page ${i}\\nFrom: ${pdfFile.name}\\nBook ID: ${bookId}`);\n\n        insertImage.run(\n          bookId,\n          fileName,\n          placeholderPath,\n          i === 1 ? 'cover' : 'content', // First page is cover\n          i\n        );\n      }\n\n      // Save the extracted text as OCR for the first page (as an example)\n      if (pageCount > 0 && textContent.trim()) {\n        const firstPageId = db.prepare('SELECT id FROM images WHERE book_id = ? ORDER BY upload_order LIMIT 1').get(bookId) as any;\n        if (firstPageId) {\n          db.prepare(`\n            INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n            VALUES (?, ?, 1)\n          `).run(firstPageId.id, textContent.substring(0, 2000)); // First 2000 chars\n        }\n      }\n\n      // Update book status and page count\n      BookModel.update(bookId, {\n        status: 'processing',\n        total_pages: pageCount\n      });\n\n      // Keep PDF file for now (don't delete it)\n      // fs.unlinkSync(pdfPath);\n\n      return NextResponse.json({\n        success: true,\n        message: `PDF processed successfully! Extracted ${pageCount} pages. PDF-to-image conversion will be implemented next.`,\n        pageCount,\n        bookId,\n        textLength: textContent.length\n      });\n\n    } catch (processingError) {\n      console.error('Error processing PDF:', processingError);\n\n      // Clean up PDF file on error\n      if (fs.existsSync(pdfPath)) {\n        fs.unlinkSync(pdfPath);\n      }\n\n      return NextResponse.json({\n        success: false,\n        error: 'Failed to process PDF. Please ensure the PDF is not corrupted and try again.'\n      }, { status: 500 });\n    }\n\n  } catch (error) {\n    console.error('Error uploading PDF:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to upload PDF' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,UAAU,SAAS,GAAG,CAAC;QAC7B,MAAM,SAAS,SAAS,SAAS,GAAG,CAAC;QAErC,IAAI,CAAC,WAAW,CAAC,QAAQ;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,OAAO,sHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAiB,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,SAAS,OAAO,QAAQ;QAC9E,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;YAC7B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW;gBAAE,WAAW;YAAK;QAC5C;QAEA,gBAAgB;QAChB,MAAM,YAAY,OAAO,IAAI,CAAC,MAAM,QAAQ,WAAW;QACvD,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,EAAE,QAAQ,IAAI,EAAE;QACnE,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,SAAS;QAE1B,IAAI;YACF,+CAA+C;YAC/C,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAG,AAAD,EAAE;YAC1B,MAAM,YAAY,QAAQ,QAAQ;YAClC,MAAM,cAAc,QAAQ,IAAI;YAEhC,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU,QAAQ,EAAE,YAAY,MAAM,CAAC,mBAAmB,CAAC;YAEtF,0DAA0D;YAC1D,oEAAoE;YACpE,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;MAGhC,CAAC;YAED,2CAA2C;YAC3C,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,IAAK;gBACnC,MAAM,WAAW,CAAC,KAAK,EAAE,EAAE,gBAAgB,CAAC;gBAC5C,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;gBAE7C,mCAAmC;gBACnC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,QAAQ,IAAI,CAAC,WAAW,EAAE,QAAQ;gBAE5F,YAAY,GAAG,CACb,QACA,UACA,iBACA,MAAM,IAAI,UAAU,WACpB;YAEJ;YAEA,oEAAoE;YACpE,IAAI,YAAY,KAAK,YAAY,IAAI,IAAI;gBACvC,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,yEAAyE,GAAG,CAAC;gBAC5G,IAAI,aAAa;oBACf,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;UAGZ,CAAC,EAAE,GAAG,CAAC,YAAY,EAAE,EAAE,YAAY,SAAS,CAAC,GAAG,QAAQ,mBAAmB;gBAC7E;YACF;YAEA,oCAAoC;YACpC,sHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,QAAQ;gBACvB,QAAQ;gBACR,aAAa;YACf;YAEA,0CAA0C;YAC1C,0BAA0B;YAE1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS,CAAC,sCAAsC,EAAE,UAAU,yDAAyD,CAAC;gBACtH;gBACA;gBACA,YAAY,YAAY,MAAM;YAChC;QAEF,EAAE,OAAO,iBAAiB;YACxB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,6BAA6B;YAC7B,IAAI,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;gBAC1B,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC;YAChB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAuB,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}