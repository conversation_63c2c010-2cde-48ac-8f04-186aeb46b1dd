import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { autoAssignPageTypes } from '@/lib/upload';

export async function POST(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { classId, subjectId } = await request.json();

    if (!classId || !subjectId) {
      return NextResponse.json({ error: 'Class ID and Subject ID are required' }, { status: 400 });
    }

    autoAssignPageTypes(classId, subjectId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Auto-assign page types error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
