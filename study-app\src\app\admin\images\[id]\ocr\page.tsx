import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import AdminLayout from '@/components/AdminLayout';
import Link from 'next/link';
import CopyTextButton from '@/components/CopyTextButton';

interface OCRPageProps {
  params: Promise<{ id: string }>;
}

async function getImageOCR(imageId: number) {
  try {
    const response = await fetch(`http://localhost:3000/api/admin/images/${imageId}/ocr`, {
      cache: 'no-store'
    });
    
    if (!response.ok) {
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching OCR data:', error);
    return null;
  }
}

export default async function OCRPage({ params }: OCRPageProps) {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/admin/login');
  }

  const { id } = await params;
  const imageId = parseInt(id);
  
  const data = await getImageOCR(imageId);
  
  if (!data || !data.success) {
    return (
      <AdminLayout>
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Image Not Found</h1>
              <p className="text-gray-600 mb-6">The requested image could not be found.</p>
              <Link
                href="/admin/books"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Back to Books
              </Link>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const { image, ocr } = data;

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  OCR Text - Page {image.pageNumber}
                </h1>
                <p className="text-gray-600">
                  {image.bookTitle} • {image.name}
                </p>
              </div>
              <div className="flex space-x-3">
                <Link
                  href={`/admin/books/${image.bookId}`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                >
                  Back to Book
                </Link>
                <Link
                  href={`/api/admin/images/${image.id}/thumbnail`}
                  target="_blank"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                >
                  View Image
                </Link>
              </div>
            </div>
          </div>

          {/* OCR Content */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Extracted Text</h2>
              {ocr && (
                <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                  <span>Characters: {ocr.characterCount}</span>
                  <span>Words: {ocr.wordCount}</span>
                  <span>Processed: {ocr.processed ? 'Yes' : 'No'}</span>
                  {ocr.processedAt && (
                    <span>At: {new Date(ocr.processedAt).toLocaleString()}</span>
                  )}
                </div>
              )}
            </div>
            
            <div className="px-6 py-4">
              {ocr && ocr.text ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <pre className="whitespace-pre-wrap text-sm text-gray-900 font-mono">
                      {ocr.text}
                    </pre>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex space-x-3">
                    <CopyTextButton text={ocr.text} />
                    <form action={`/api/admin/images/${image.id}/ocr`} method="POST">
                      <button
                        type="submit"
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Re-process OCR
                      </button>
                    </form>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-4">
                    <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No OCR Text Available</h3>
                  <p className="text-gray-600 mb-4">This image has not been processed for text extraction yet.</p>
                  <Link
                    href={`/api/admin/images/${image.id}/ocr`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Process OCR
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
