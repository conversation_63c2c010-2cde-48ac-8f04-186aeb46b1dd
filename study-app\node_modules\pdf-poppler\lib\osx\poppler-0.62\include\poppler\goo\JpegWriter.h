//========================================================================
//
// JpegWriter.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright (C) 2009 <PERSON> <<EMAIL>>
// Copyright (C) 2010, 2012, 2017 <PERSON> <ajo<PERSON><PERSON>@redneon.com>
// Copyright (C) 2010 Jürg Billeter <<EMAIL>>
// Copyright (C) 2010 <PERSON> <<EMAIL>>
// Copyright (C) 2010 <PERSON> <<EMAIL>>
// Copyright (C) 2011 <PERSON> <<EMAIL>>
// Copyright (C) 2011 <PERSON> <<EMAIL>>
//
//========================================================================

#ifndef JPEGWRITER_H
#define JPEGWRITER_H

#include "poppler-config.h"

#ifdef ENABLE_LIBJPEG

#include <sys/types.h>
#include "ImgWriter.h"

struct JpegWriterPrivate;

class JpegWriter : public ImgWriter
{
public:
  /* RGB                 - 3 bytes/pixel
   * GRAY                - 1 byte/pixel
   * CMYK                - 4 bytes/pixel
   */
  enum Format { RGB, GRAY, CMYK };

  JpegWriter(int quality, bool progressive, Format format = RGB);
  JpegWriter(Format format = RGB);
  ~JpegWriter();

  void setQuality(int quality);
  void setProgressive(bool progressive);
  bool init(FILE *f, int width, int height, int hDPI, int vDPI) override;

  bool writePointers(unsigned char **rowPointers, int rowCount) override;
  bool writeRow(unsigned char **row) override;

  bool close() override;
  bool supportCMYK() override;

private:
  JpegWriter(const JpegWriter &other);
  JpegWriter& operator=(const JpegWriter &other);

  JpegWriterPrivate *priv;
};

#endif

#endif
