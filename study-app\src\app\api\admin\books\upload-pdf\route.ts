import { NextRequest, NextResponse } from 'next/server';
import { BookModel } from '@/lib/models';
import fs from 'fs';
import path from 'path';
import db from '@/lib/database';
import PDFParser from 'pdf2json';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const pdfFile = formData.get('pdf') as File;
    const bookId = parseInt(formData.get('bookId') as string);

    if (!pdfFile || !bookId) {
      return NextResponse.json(
        { success: false, error: 'PDF file and book ID are required' },
        { status: 400 }
      );
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json(
        { success: false, error: 'Book not found' },
        { status: 404 }
      );
    }

    // Create upload directory for this book
    const uploadDir = path.join(process.cwd(), 'uploads', 'books', bookId.toString());
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Save PDF file
    const pdfBuffer = Buffer.from(await pdfFile.arrayBuffer());
    const timestamp = Date.now();
    const pdfPath = path.join(uploadDir, `${timestamp}_${pdfFile.name}`);
    fs.writeFileSync(pdfPath, pdfBuffer);

    try {
      console.log(`PDF uploaded: ${pdfFile.name}, size: ${pdfBuffer.length} bytes`);

      // Parse PDF to get actual page count
      const pageCount = await new Promise<number>((resolve, reject) => {
        const pdfParser = new (PDFParser as any)(null, 1);

        pdfParser.on('pdfParser_dataError', (errData: any) => {
          console.error('PDF parsing error:', errData.parserError);
          reject(new Error('Failed to parse PDF'));
        });

        pdfParser.on('pdfParser_dataReady', (pdfData: any) => {
          const numPages = pdfData.Pages ? pdfData.Pages.length : 0;
          console.log(`PDF parsed successfully: ${numPages} pages found`);
          resolve(numPages);
        });

        // Parse the PDF buffer
        pdfParser.parseBuffer(pdfBuffer);
      });

      if (pageCount === 0) {
        throw new Error('PDF appears to be empty or corrupted');
      }

      const insertImage = db.prepare(`
        INSERT INTO images (book_id, class_id, subject_id, original_name, file_path, page_type, upload_order)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      // Create placeholder entries for each page
      for (let i = 1; i <= pageCount; i++) {
        const fileName = `page-${i}.pdf-placeholder`;
        const placeholderPath = path.join(uploadDir, fileName);

        // Create a simple placeholder file
        fs.writeFileSync(placeholderPath, `PDF Page ${i}\nFrom: ${pdfFile.name}\nBook ID: ${bookId}\nUploaded: ${new Date().toISOString()}`);

        insertImage.run(
          bookId,
          book.class_id,
          book.subject_id,
          fileName,
          placeholderPath,
          i === 1 ? 'cover' : 'content', // First page is cover
          i
        );
      }

      // Update book status and page count
      BookModel.update(bookId, {
        status: 'processing',
        uploaded_pages: pageCount,
        processed_pages: 0
      });

      // Keep PDF file for future processing
      console.log(`PDF saved to: ${pdfPath}`);

      return NextResponse.json({
        success: true,
        message: `PDF uploaded successfully! Created ${pageCount} placeholder pages. Full PDF processing will be implemented next.`,
        pageCount,
        bookId,
        pdfSize: pdfBuffer.length
      });

    } catch (processingError) {
      console.error('Error processing PDF:', processingError);

      // Clean up PDF file on error
      if (fs.existsSync(pdfPath)) {
        fs.unlinkSync(pdfPath);
      }

      return NextResponse.json({
        success: false,
        error: 'Failed to process PDF. Please ensure the PDF is not corrupted and try again.'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error uploading PDF:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload PDF' },
      { status: 500 }
    );
  }
}
