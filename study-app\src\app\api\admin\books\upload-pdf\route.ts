import { NextRequest, NextResponse } from 'next/server';
import { BookModel } from '@/lib/models';
import fs from 'fs';
import path from 'path';
import { pdf2pic } from 'pdf2pic';
import db from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const pdfFile = formData.get('pdf') as File;
    const bookId = parseInt(formData.get('bookId') as string);

    if (!pdfFile || !bookId) {
      return NextResponse.json(
        { success: false, error: 'PDF file and book ID are required' },
        { status: 400 }
      );
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json(
        { success: false, error: 'Book not found' },
        { status: 404 }
      );
    }

    // Create upload directory for this book
    const uploadDir = path.join(process.cwd(), 'uploads', 'books', bookId.toString());
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Save PDF file
    const pdfBuffer = Buffer.from(await pdfFile.arrayBuffer());
    const timestamp = Date.now();
    const pdfPath = path.join(uploadDir, `${timestamp}_${pdfFile.name}`);
    fs.writeFileSync(pdfPath, pdfBuffer);

    try {
      // Convert PDF to images
      const convert = pdf2pic.fromPath(pdfPath, {
        density: 200,           // Output resolution
        saveFilename: "page",   // Output filename
        savePath: uploadDir,    // Output directory
        format: "jpg",          // Output format
        width: 1200,           // Output width
        height: 1600           // Output height
      });

      // Get total pages and convert all
      const results = await convert.bulk(-1); // -1 means all pages

      // Save each page as an image record in the database
      const insertImage = db.prepare(`
        INSERT INTO images (book_id, original_name, file_path, page_type, upload_order)
        VALUES (?, ?, ?, ?, ?)
      `);

      let pageCount = 0;
      for (const result of results) {
        pageCount++;
        const imagePath = result.path;
        const fileName = `page.${pageCount}.jpg`;

        insertImage.run(
          bookId,
          fileName,
          imagePath,
          pageCount === 1 ? 'cover' : 'content', // First page is cover
          pageCount
        );
      }

      // Update book status and page count
      BookModel.update(bookId, {
        status: 'processing',
        total_pages: pageCount
      });

      // Clean up PDF file (optional)
      fs.unlinkSync(pdfPath);

      return NextResponse.json({
        success: true,
        message: `PDF processed successfully! Extracted ${pageCount} pages.`,
        pageCount,
        bookId
      });

    } catch (processingError) {
      console.error('Error processing PDF:', processingError);

      // Clean up PDF file on error
      if (fs.existsSync(pdfPath)) {
        fs.unlinkSync(pdfPath);
      }

      return NextResponse.json({
        success: false,
        error: 'Failed to process PDF. Please ensure the PDF is not corrupted and try again.'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error uploading PDF:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload PDF' },
      { status: 500 }
    );
  }
}
