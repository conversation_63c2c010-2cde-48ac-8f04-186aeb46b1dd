import { NextRequest, NextResponse } from 'next/server';
import { BookModel } from '@/lib/models';
import fs from 'fs';
import path from 'path';
import db from '@/lib/database';
import PDFParser from 'pdf2json';
import sharp from 'sharp';
import { createWorker } from 'tesseract.js';
import { createCanvas } from 'canvas';
import { fromPath } from 'pdf2pic';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const pdfFile = formData.get('pdf') as File;
    const bookId = parseInt(formData.get('bookId') as string);

    if (!pdfFile || !bookId) {
      return NextResponse.json(
        { success: false, error: 'PDF file and book ID are required' },
        { status: 400 }
      );
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json(
        { success: false, error: 'Book not found' },
        { status: 404 }
      );
    }

    // Create upload directory for this book
    const uploadDir = path.join(process.cwd(), 'uploads', 'books', bookId.toString());
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Save PDF file
    const pdfBuffer = Buffer.from(await pdfFile.arrayBuffer());
    const timestamp = Date.now();
    const pdfPath = path.join(uploadDir, `${timestamp}_${pdfFile.name}`);
    fs.writeFileSync(pdfPath, pdfBuffer);

    try {
      console.log(`PDF uploaded: ${pdfFile.name}, size: ${pdfBuffer.length} bytes`);

      // Parse PDF to get actual page count
      const pageCount = await new Promise<number>((resolve, reject) => {
        const pdfParser = new (PDFParser as any)(null, 1);

        pdfParser.on('pdfParser_dataError', (errData: any) => {
          console.error('PDF parsing error:', errData.parserError);
          reject(new Error('Failed to parse PDF'));
        });

        pdfParser.on('pdfParser_dataReady', (pdfData: any) => {
          const numPages = pdfData.Pages ? pdfData.Pages.length : 0;
          console.log(`PDF parsed successfully: ${numPages} pages found`);
          resolve(numPages);
        });

        // Parse the PDF buffer
        pdfParser.parseBuffer(pdfBuffer);
      });

      if (pageCount === 0) {
        throw new Error('PDF appears to be empty or corrupted');
      }

      // Clear existing pages for this book before adding new ones
      console.log(`Clearing existing pages for book ${bookId}`);
      const existingImages = db.prepare('SELECT file_path FROM images WHERE book_id = ?').all(bookId) as any[];

      // Delete physical files
      for (const img of existingImages) {
        try {
          if (fs.existsSync(img.file_path)) {
            fs.unlinkSync(img.file_path);
          }
        } catch (error) {
          console.warn(`Failed to delete file: ${img.file_path}`, error);
        }
      }

      // Delete database records
      db.prepare('DELETE FROM ocr_text WHERE image_id IN (SELECT id FROM images WHERE book_id = ?)').run(bookId);
      db.prepare('DELETE FROM images WHERE book_id = ?').run(bookId);
      console.log(`Cleared ${existingImages.length} existing pages`);

      const insertImage = db.prepare(`
        INSERT INTO images (book_id, class_id, subject_id, original_name, file_path, page_type, upload_order)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      console.log('Extracting real pages from PDF...');

      try {
        // Configure pdf2pic for page extraction
        const convert = fromPath(pdfPath, {
          density: 200,           // DPI for image quality
          saveFilename: "page",   // Base filename
          savePath: uploadDir,    // Output directory
          format: "jpeg",         // Output format
          width: 800,            // Image width
          height: 1200           // Image height
        });

        console.log(`Converting ${pageCount} PDF pages to images...`);

        // Convert all pages at once
        const results = await convert.bulk(-1, { responseType: "image" });

        console.log(`Successfully converted ${results.length} pages`);

        // Process each converted page
        for (let i = 0; i < results.length; i++) {
          const pageNumber = (i + 1).toString().padStart(3, '0');
          const originalPath = results[i].path;
          const finalImagePath = path.join(uploadDir, `page_${pageNumber}.jpg`);
          const thumbnailPath = path.join(uploadDir, `thumb_${pageNumber}.jpg`);

          // Move the converted image to our naming convention
          if (originalPath !== finalImagePath) {
            fs.renameSync(originalPath, finalImagePath);
          }

          // Create thumbnail using sharp
          await sharp(finalImagePath)
            .resize(200, 280, { fit: 'inside', withoutEnlargement: true })
            .jpeg({ quality: 80 })
            .toFile(thumbnailPath);

          // Insert into database
          const dbResult = insertImage.run(
            bookId,
            book.class_id,
            book.subject_id,
            `page_${pageNumber}.jpg`,
            finalImagePath,
            i === 0 ? 'cover' : 'content',
            i + 1
          );

          const imageId = dbResult.lastInsertRowid;

          // Process OCR for this page (async, don't wait) - NO placeholder text!
          processOCR(imageId as number, finalImagePath).catch(error => {
            console.error(`OCR failed for page ${i + 1}:`, error);
          });

          console.log(`Processed real PDF page ${i + 1}/${pageCount}`);
        }

        console.log(`Successfully extracted ${pageCount} real PDF pages`);
      } catch (conversionError) {
        console.error('PDF conversion error:', conversionError);
        throw new Error('Failed to extract pages from PDF');
      }

      // Update book status and page count
      BookModel.update(bookId, {
        status: 'processing',
        uploaded_pages: pageCount,
        processed_pages: 0
      });

      // Keep PDF file for future processing
      console.log(`PDF saved to: ${pdfPath}`);

      return NextResponse.json({
        success: true,
        message: `PDF uploaded successfully! Created ${pageCount} placeholder pages. Full PDF processing will be implemented next.`,
        pageCount,
        bookId,
        pdfSize: pdfBuffer.length
      });

    } catch (processingError) {
      console.error('Error processing PDF:', processingError);

      // Clean up PDF file on error
      if (fs.existsSync(pdfPath)) {
        fs.unlinkSync(pdfPath);
      }

      return NextResponse.json({
        success: false,
        error: 'Failed to process PDF. Please ensure the PDF is not corrupted and try again.'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error uploading PDF:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload PDF' },
      { status: 500 }
    );
  }
}

// Async OCR processing function
async function processOCR(imageId: number, imagePath: string, placeholderText?: string) {
  try {
    console.log(`Starting OCR for image ${imageId}`);

    let extractedText = '';
    let confidence = 0.95;

    if (placeholderText) {
      // For placeholder images, use the provided text
      extractedText = placeholderText;
      confidence = 1.0; // Perfect confidence for placeholder text
      console.log(`Using placeholder text for image ${imageId}`);
    } else {
      // For real images, use Tesseract OCR
      const worker = await createWorker('eng');
      const { data: { text, confidence: ocrConfidence } } = await worker.recognize(imagePath);
      await worker.terminate();

      extractedText = text.trim();
      confidence = ocrConfidence / 100; // Convert percentage to decimal
    }

    // Store OCR text in database
    const insertOCR = db.prepare(`
      INSERT OR REPLACE INTO ocr_text (image_id, content, processed, created_at)
      VALUES (?, ?, ?, datetime('now'))
    `);

    insertOCR.run(imageId, extractedText, 1);

    console.log(`OCR completed for image ${imageId}, extracted ${extractedText.length} characters (confidence: ${(confidence * 100).toFixed(1)}%)`);

    // Update processed pages count
    const imageInfo = db.prepare('SELECT book_id FROM images WHERE id = ?').get(imageId) as any;
    if (imageInfo) {
      const processedCount = db.prepare('SELECT COUNT(*) as count FROM ocr_text WHERE processed = 1 AND image_id IN (SELECT id FROM images WHERE book_id = ?)').get(imageInfo.book_id) as any;

      BookModel.update(imageInfo.book_id, {
        processed_pages: processedCount.count
      });
    }

  } catch (error) {
    console.error(`OCR processing failed for image ${imageId}:`, error);
  }
}
