'use client';

import { useState } from 'react';
import { Class, Subject } from '@/lib/models';

interface ClassesManagerProps {
  initialClasses: Class[];
  initialSubjects: Subject[];
}

export default function ClassesManager({ initialClasses, initialSubjects }: ClassesManagerProps) {
  const [classes, setClasses] = useState(initialClasses);
  const [subjects, setSubjects] = useState(initialSubjects);
  const [showClassForm, setShowClassForm] = useState(false);
  const [showSubjectForm, setShowSubjectForm] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [selectedClassId, setSelectedClassId] = useState<number | null>(null);

  // Class form state
  const [className, setClassName] = useState('');
  const [classDescription, setClassDescription] = useState('');

  // Subject form state
  const [subjectName, setSubjectName] = useState('');
  const [subjectDescription, setSubjectDescription] = useState('');
  const [subjectClassId, setSubjectClassId] = useState<number | null>(null);

  const resetClassForm = () => {
    setClassName('');
    setClassDescription('');
    setEditingClass(null);
    setShowClassForm(false);
  };

  const resetSubjectForm = () => {
    setSubjectName('');
    setSubjectDescription('');
    setSubjectClassId(null);
    setEditingSubject(null);
    setShowSubjectForm(false);
  };

  const handleCreateClass = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/admin/classes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: className, description: classDescription }),
      });

      if (response.ok) {
        const newClass = await response.json();
        setClasses([...classes, newClass]);
        resetClassForm();
      }
    } catch (error) {
      console.error('Error creating class:', error);
    }
  };

  const handleUpdateClass = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingClass) return;

    try {
      const response = await fetch(`/api/admin/classes/${editingClass.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: className, description: classDescription }),
      });

      if (response.ok) {
        const updatedClass = await response.json();
        setClasses(classes.map(c => c.id === editingClass.id ? updatedClass : c));
        resetClassForm();
      }
    } catch (error) {
      console.error('Error updating class:', error);
    }
  };

  const handleDeleteClass = async (classId: number) => {
    if (!confirm('Are you sure? This will delete all subjects and questions in this class.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/classes/${classId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setClasses(classes.filter(c => c.id !== classId));
        setSubjects(subjects.filter(s => s.class_id !== classId));
      }
    } catch (error) {
      console.error('Error deleting class:', error);
    }
  };

  const handleCreateSubject = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!subjectClassId) return;

    try {
      const response = await fetch('/api/admin/subjects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          class_id: subjectClassId, 
          name: subjectName, 
          description: subjectDescription 
        }),
      });

      if (response.ok) {
        const newSubject = await response.json();
        setSubjects([...subjects, newSubject]);
        resetSubjectForm();
      }
    } catch (error) {
      console.error('Error creating subject:', error);
    }
  };

  const handleUpdateSubject = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingSubject) return;

    try {
      const response = await fetch(`/api/admin/subjects/${editingSubject.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: subjectName, description: subjectDescription }),
      });

      if (response.ok) {
        const updatedSubject = await response.json();
        setSubjects(subjects.map(s => s.id === editingSubject.id ? updatedSubject : s));
        resetSubjectForm();
      }
    } catch (error) {
      console.error('Error updating subject:', error);
    }
  };

  const handleDeleteSubject = async (subjectId: number) => {
    if (!confirm('Are you sure? This will delete all questions in this subject.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/subjects/${subjectId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSubjects(subjects.filter(s => s.id !== subjectId));
      }
    } catch (error) {
      console.error('Error deleting subject:', error);
    }
  };

  const startEditClass = (cls: Class) => {
    setEditingClass(cls);
    setClassName(cls.name);
    setClassDescription(cls.description || '');
    setShowClassForm(true);
  };

  const startEditSubject = (subject: Subject) => {
    setEditingSubject(subject);
    setSubjectName(subject.name);
    setSubjectDescription(subject.description || '');
    setSubjectClassId(subject.class_id);
    setShowSubjectForm(true);
  };

  const filteredSubjects = selectedClassId
    ? subjects.filter(s => s.class_id === selectedClassId)
    : subjects;

  return (
    <div className="space-y-8">
      {/* Classes Section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Classes</h3>
            <button
              onClick={() => setShowClassForm(true)}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Add Class
            </button>
          </div>

          {/* Class Form */}
          {showClassForm && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h4 className="text-md font-medium text-gray-900 mb-3">
                {editingClass ? 'Edit Class' : 'Add New Class'}
              </h4>
              <form onSubmit={editingClass ? handleUpdateClass : handleCreateClass}>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Class Name
                    </label>
                    <input
                      type="text"
                      value={className}
                      onChange={(e) => setClassName(e.target.value)}
                      required
                      className="mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white"
                      placeholder="e.g., Grade 10"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Description (Optional)
                    </label>
                    <input
                      type="text"
                      value={classDescription}
                      onChange={(e) => setClassDescription(e.target.value)}
                      className="mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white"
                      placeholder="Class description"
                    />
                  </div>
                </div>
                <div className="mt-4 flex space-x-3">
                  <button
                    type="submit"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    {editingClass ? 'Update' : 'Create'} Class
                  </button>
                  <button
                    type="button"
                    onClick={resetClassForm}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Classes List */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {classes.map((cls) => (
              <div key={cls.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-md font-medium text-gray-900">{cls.name}</h4>
                    {cls.description && (
                      <p className="text-sm text-gray-600 mt-1">{cls.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-2">
                      {subjects.filter(s => s.class_id === cls.id).length} subjects
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => startEditClass(cls)}
                      className="text-indigo-600 hover:text-indigo-900 text-sm"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteClass(cls.id)}
                      className="text-red-600 hover:text-red-900 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Subjects Section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Subjects</h3>
            <div className="flex space-x-3">
              <select
                value={selectedClassId || ''}
                onChange={(e) => setSelectedClassId(e.target.value ? Number(e.target.value) : null)}
                className="h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white"
              >
                <option value="">All Classes</option>
                {classes.map((cls) => (
                  <option key={cls.id} value={cls.id}>{cls.name}</option>
                ))}
              </select>
              <button
                onClick={() => setShowSubjectForm(true)}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Add Subject
              </button>
            </div>
          </div>

          {/* Subject Form */}
          {showSubjectForm && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h4 className="text-md font-medium text-gray-900 mb-3">
                {editingSubject ? 'Edit Subject' : 'Add New Subject'}
              </h4>
              <form onSubmit={editingSubject ? handleUpdateSubject : handleCreateSubject}>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Class
                    </label>
                    <select
                      value={subjectClassId || ''}
                      onChange={(e) => setSubjectClassId(Number(e.target.value))}
                      required
                      disabled={!!editingSubject}
                      className="mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white disabled:bg-gray-100 disabled:text-gray-500"
                    >
                      <option value="">Select a class</option>
                      {classes.map((cls) => (
                        <option key={cls.id} value={cls.id}>{cls.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Subject Name
                    </label>
                    <input
                      type="text"
                      value={subjectName}
                      onChange={(e) => setSubjectName(e.target.value)}
                      required
                      className="mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white"
                      placeholder="e.g., Mathematics"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Description (Optional)
                    </label>
                    <input
                      type="text"
                      value={subjectDescription}
                      onChange={(e) => setSubjectDescription(e.target.value)}
                      className="mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white"
                      placeholder="Subject description"
                    />
                  </div>
                </div>
                <div className="mt-4 flex space-x-3">
                  <button
                    type="submit"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    {editingSubject ? 'Update' : 'Create'} Subject
                  </button>
                  <button
                    type="button"
                    onClick={resetSubjectForm}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Subjects List */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {filteredSubjects.map((subject) => (
              <div key={subject.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-md font-medium text-gray-900">{subject.name}</h4>
                    <p className="text-sm text-gray-600">{subject.class_name}</p>
                    {subject.description && (
                      <p className="text-sm text-gray-600 mt-1">{subject.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => startEditSubject(subject)}
                      className="text-indigo-600 hover:text-indigo-900 text-sm"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteSubject(subject.id)}
                      className="text-red-600 hover:text-red-900 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredSubjects.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {selectedClassId ? 'No subjects found for the selected class.' : 'No subjects created yet.'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
