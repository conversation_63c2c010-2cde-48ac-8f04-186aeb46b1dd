'use client';

import { useState } from 'react';

interface OCRActionsProps {
  text: string;
  imageId: number;
}

export default function OCRActions({ text, imageId }: OCRActionsProps) {
  const [copied, setCopied] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleReprocess = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch(`/api/admin/images/${imageId}/ocr`, {
        method: 'POST',
      });
      
      if (response.ok) {
        // Refresh the page to show updated OCR
        window.location.reload();
      } else {
        alert('Failed to reprocess OCR');
      }
    } catch (error) {
      console.error('Error reprocessing OCR:', error);
      alert('Error reprocessing OCR');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex space-x-3">
      <button
        onClick={handleCopy}
        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
      >
        {copied ? (
          <>
            <svg className="w-4 h-4 mr-1 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Copied!
          </>
        ) : (
          <>
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy Text
          </>
        )}
      </button>
      <button
        onClick={handleReprocess}
        disabled={isProcessing}
        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
      >
        {isProcessing ? 'Processing...' : 'Re-process OCR'}
      </button>
    </div>
  );
}
