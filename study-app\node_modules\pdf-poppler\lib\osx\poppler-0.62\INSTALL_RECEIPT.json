{"homebrew_version": "1.3.8-110-g4f5e938", "used_options": [], "unused_options": ["--with-qt", "--with-little-cms2"], "built_as_bottle": true, "poured_from_bottle": true, "installed_as_dependency": false, "installed_on_request": true, "changed_files": ["ChangeLog", "INSTALL_RECEIPT.json", "lib/pkgconfig/poppler-cairo.pc", "lib/pkgconfig/poppler-cpp.pc", "lib/pkgconfig/poppler-glib.pc", "lib/pkgconfig/poppler-splash.pc", "lib/pkgconfig/poppler.pc", "share/pkgconfig/poppler-data.pc"], "time": 1519876202, "source_modified_time": 1512329402, "HEAD": null, "stdlib": "libcxx", "compiler": "clang", "aliases": [], "runtime_dependencies": [{"full_name": "libpng", "version": "1.6.34"}, {"full_name": "freetype", "version": "2.8.1"}, {"full_name": "fontconfig", "version": "2.12.6"}, {"full_name": "pixman", "version": "0.34.0"}, {"full_name": "gettext", "version": "********"}, {"full_name": "libffi", "version": "3.2.1"}, {"full_name": "pcre", "version": "8.41"}, {"full_name": "glib", "version": "2.54.2"}, {"full_name": "cairo", "version": "1.14.10"}, {"full_name": "pkg-config", "version": "0.29.2"}, {"full_name": "gobject-introspection", "version": "1.54.1"}, {"full_name": "jpeg", "version": "9b"}, {"full_name": "libtiff", "version": "4.0.8"}, {"full_name": "little-cms2", "version": "2.9"}, {"full_name": "openjpeg", "version": "2.3.0"}], "source": {"path": "/usr/local/Homebrew/Library/Taps/homebrew/homebrew-core/Formula/poppler.rb", "tap": "homebrew/core", "spec": "stable", "versions": {"stable": "0.62.0", "devel": null, "head": null, "version_scheme": 0}}}