import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { SubjectModel } from '@/lib/models';

export async function GET(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId');

    const subjects = classId 
      ? SubjectModel.getByClassId(parseInt(classId))
      : SubjectModel.getAll();
      
    return NextResponse.json(subjects);
  } catch (error) {
    console.error('Get subjects error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { class_id, name, description } = await request.json();

    if (!class_id || !name) {
      return NextResponse.json({ error: 'Class ID and subject name are required' }, { status: 400 });
    }

    const newSubject = SubjectModel.create(class_id, name, description);
    return NextResponse.json(newSubject, { status: 201 });
  } catch (error) {
    console.error('Create subject error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
