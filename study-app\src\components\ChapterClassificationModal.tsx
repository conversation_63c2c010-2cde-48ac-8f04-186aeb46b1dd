'use client';

import { useState, useEffect } from 'react';

interface ChapterClassificationModalProps {
  isOpen: boolean;
  images: any[];
  onClose: () => void;
  onSave: (classifications: { [key: number]: string }) => void;
}

export default function ChapterClassificationModal({ 
  isOpen, 
  images, 
  onClose, 
  onSave 
}: ChapterClassificationModalProps) {
  const [classifications, setClassifications] = useState<{ [key: number]: string }>({});
  const [currentChapter, setCurrentChapter] = useState(1);
  const [autoMode, setAutoMode] = useState(true);

  useEffect(() => {
    if (isOpen) {
      // Initialize classifications from existing data
      const initial: { [key: number]: string } = {};
      images.forEach(img => {
        initial[img.id] = img.page_type || 'unassigned';
      });
      setClassifications(initial);
    }
  }, [isOpen, images]);

  const pageTypeOptions = [
    { value: 'cover', label: 'Cover Page', color: 'bg-purple-100 text-purple-800' },
    { value: 'contents', label: 'Contents/Index', color: 'bg-blue-100 text-blue-800' },
    { value: 'unassigned', label: 'Unassigned', color: 'bg-gray-100 text-gray-800' },
    ...Array.from({length: 30}, (_, i) => ({
      value: `chapter-${i + 1}`,
      label: `Chapter ${i + 1}`,
      color: 'bg-green-100 text-green-800'
    }))
  ];

  const handleClassificationChange = (imageId: number, pageType: string) => {
    setClassifications(prev => ({
      ...prev,
      [imageId]: pageType
    }));
  };

  const handleAutoClassify = () => {
    const newClassifications: { [key: number]: string } = {};
    let currentChapterNum = 1;
    let foundFirstChapter = false;

    images.forEach((image, index) => {
      if (index === 0) {
        newClassifications[image.id] = 'cover';
      } else if (index === 1 || index === 2) {
        newClassifications[image.id] = 'contents';
      } else {
        // Look for chapter markers in existing classifications
        const existingType = classifications[image.id];
        if (existingType && existingType.startsWith('chapter-')) {
          const chapterMatch = existingType.match(/chapter-(\d+)/);
          if (chapterMatch) {
            currentChapterNum = parseInt(chapterMatch[1]);
            foundFirstChapter = true;
          }
          newClassifications[image.id] = existingType;
        } else if (foundFirstChapter) {
          newClassifications[image.id] = `chapter-${currentChapterNum}`;
        } else {
          // Default to chapter 1 if no chapter markers found
          newClassifications[image.id] = 'chapter-1';
          foundFirstChapter = true;
        }
      }
    });

    setClassifications(newClassifications);
  };

  const handleBulkAssign = (startIndex: number, endIndex: number, pageType: string) => {
    const newClassifications = { ...classifications };
    for (let i = startIndex; i <= endIndex; i++) {
      if (images[i]) {
        newClassifications[images[i].id] = pageType;
      }
    }
    setClassifications(newClassifications);
  };

  const handleSave = () => {
    onSave(classifications);
    onClose();
  };

  const getPageTypeInfo = (pageType: string) => {
    return pageTypeOptions.find(opt => opt.value === pageType) || pageTypeOptions[2];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Chapter Classification - {images.length} Images
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Quick Actions */}
          <div className="mb-6 flex flex-wrap gap-3">
            <button
              onClick={handleAutoClassify}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              🤖 Auto-Classify All
            </button>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Bulk assign:</label>
              <select
                value={currentChapter}
                onChange={(e) => setCurrentChapter(Number(e.target.value))}
                className="h-8 px-2 border-gray-300 rounded text-sm text-gray-900 bg-white"
              >
                {Array.from({length: 30}, (_, i) => (
                  <option key={i + 1} value={i + 1}>Chapter {i + 1}</option>
                ))}
              </select>
              <button
                onClick={() => {
                  const unassigned = images
                    .map((img, idx) => ({ img, idx }))
                    .filter(({ img }) => classifications[img.id] === 'unassigned' || !classifications[img.id]);
                  
                  if (unassigned.length > 0) {
                    handleBulkAssign(
                      unassigned[0].idx,
                      unassigned[unassigned.length - 1].idx,
                      `chapter-${currentChapter}`
                    );
                  }
                }}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
              >
                Assign to Unassigned
              </button>
            </div>
          </div>

          {/* Images Grid */}
          <div className="overflow-y-auto max-h-96">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {images.map((image, index) => {
                const pageTypeInfo = getPageTypeInfo(classifications[image.id] || 'unassigned');
                
                return (
                  <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="relative">
                      <img
                        src={`/api/admin/images/${image.id}/preview`}
                        alt={image.original_name}
                        className="w-full h-32 object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                        }}
                      />
                      <div className="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                        #{index + 1}
                      </div>
                    </div>
                    
                    <div className="p-3">
                      <div className="text-xs text-gray-600 mb-2 truncate">
                        {image.original_name}
                      </div>
                      
                      <div className="mb-2">
                        <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${pageTypeInfo.color}`}>
                          {pageTypeInfo.label}
                        </span>
                      </div>
                      
                      <select
                        value={classifications[image.id] || 'unassigned'}
                        onChange={(e) => handleClassificationChange(image.id, e.target.value)}
                        className="w-full h-8 px-2 border-gray-300 rounded text-xs text-gray-900 bg-white"
                      >
                        {pageTypeOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Summary */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Classification Summary:</h3>
            <div className="flex flex-wrap gap-2">
              {pageTypeOptions.map(option => {
                const count = Object.values(classifications).filter(c => c === option.value).length;
                if (count === 0) return null;
                
                return (
                  <span key={option.value} className={`px-2 py-1 rounded-full text-xs font-medium ${option.color}`}>
                    {option.label}: {count}
                  </span>
                );
              })}
            </div>
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm font-medium"
          >
            Save Classifications
          </button>
        </div>
      </div>
    </div>
  );
}
