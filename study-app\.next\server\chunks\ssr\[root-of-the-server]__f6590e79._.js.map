{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Upload Images', href: '/admin/upload', icon: '📤' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user.name}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAiB,MAAM;YAAiB,MAAM;QAAK;QAC3D;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,KAAK,IAAI;;;;;;;8DAC3D,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/ClassesManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Class, Subject } from '@/lib/models';\n\ninterface ClassesManagerProps {\n  initialClasses: Class[];\n  initialSubjects: Subject[];\n}\n\nexport default function ClassesManager({ initialClasses, initialSubjects }: ClassesManagerProps) {\n  const [classes, setClasses] = useState(initialClasses);\n  const [subjects, setSubjects] = useState(initialSubjects);\n  const [showClassForm, setShowClassForm] = useState(false);\n  const [showSubjectForm, setShowSubjectForm] = useState(false);\n  const [editingClass, setEditingClass] = useState<Class | null>(null);\n  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);\n  const [selectedClassId, setSelectedClassId] = useState<number | null>(null);\n\n  // Class form state\n  const [className, setClassName] = useState('');\n  const [classDescription, setClassDescription] = useState('');\n\n  // Subject form state\n  const [subjectName, setSubjectName] = useState('');\n  const [subjectDescription, setSubjectDescription] = useState('');\n  const [subjectClassId, setSubjectClassId] = useState<number | null>(null);\n\n  const resetClassForm = () => {\n    setClassName('');\n    setClassDescription('');\n    setEditingClass(null);\n    setShowClassForm(false);\n  };\n\n  const resetSubjectForm = () => {\n    setSubjectName('');\n    setSubjectDescription('');\n    setSubjectClassId(null);\n    setEditingSubject(null);\n    setShowSubjectForm(false);\n  };\n\n  const handleCreateClass = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    try {\n      const response = await fetch('/api/admin/classes', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name: className, description: classDescription }),\n      });\n\n      if (response.ok) {\n        const newClass = await response.json();\n        setClasses([...classes, newClass]);\n        resetClassForm();\n      }\n    } catch (error) {\n      console.error('Error creating class:', error);\n    }\n  };\n\n  const handleUpdateClass = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!editingClass) return;\n\n    try {\n      const response = await fetch(`/api/admin/classes/${editingClass.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name: className, description: classDescription }),\n      });\n\n      if (response.ok) {\n        const updatedClass = await response.json();\n        setClasses(classes.map(c => c.id === editingClass.id ? updatedClass : c));\n        resetClassForm();\n      }\n    } catch (error) {\n      console.error('Error updating class:', error);\n    }\n  };\n\n  const handleDeleteClass = async (classId: number) => {\n    if (!confirm('Are you sure? This will delete all subjects and questions in this class.')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/admin/classes/${classId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setClasses(classes.filter(c => c.id !== classId));\n        setSubjects(subjects.filter(s => s.class_id !== classId));\n      }\n    } catch (error) {\n      console.error('Error deleting class:', error);\n    }\n  };\n\n  const handleCreateSubject = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!subjectClassId) return;\n\n    try {\n      const response = await fetch('/api/admin/subjects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ \n          class_id: subjectClassId, \n          name: subjectName, \n          description: subjectDescription \n        }),\n      });\n\n      if (response.ok) {\n        const newSubject = await response.json();\n        setSubjects([...subjects, newSubject]);\n        resetSubjectForm();\n      }\n    } catch (error) {\n      console.error('Error creating subject:', error);\n    }\n  };\n\n  const handleUpdateSubject = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!editingSubject) return;\n\n    try {\n      const response = await fetch(`/api/admin/subjects/${editingSubject.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name: subjectName, description: subjectDescription }),\n      });\n\n      if (response.ok) {\n        const updatedSubject = await response.json();\n        setSubjects(subjects.map(s => s.id === editingSubject.id ? updatedSubject : s));\n        resetSubjectForm();\n      }\n    } catch (error) {\n      console.error('Error updating subject:', error);\n    }\n  };\n\n  const handleDeleteSubject = async (subjectId: number) => {\n    if (!confirm('Are you sure? This will delete all questions in this subject.')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/admin/subjects/${subjectId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setSubjects(subjects.filter(s => s.id !== subjectId));\n      }\n    } catch (error) {\n      console.error('Error deleting subject:', error);\n    }\n  };\n\n  const startEditClass = (cls: Class) => {\n    setEditingClass(cls);\n    setClassName(cls.name);\n    setClassDescription(cls.description || '');\n    setShowClassForm(true);\n  };\n\n  const startEditSubject = (subject: Subject) => {\n    setEditingSubject(subject);\n    setSubjectName(subject.name);\n    setSubjectDescription(subject.description || '');\n    setSubjectClassId(subject.class_id);\n    setShowSubjectForm(true);\n  };\n\n  const filteredSubjects = selectedClassId\n    ? subjects.filter(s => s.class_id === selectedClassId)\n    : subjects;\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Classes Section */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Classes</h3>\n            <button\n              onClick={() => setShowClassForm(true)}\n              className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n            >\n              Add Class\n            </button>\n          </div>\n\n          {/* Class Form */}\n          {showClassForm && (\n            <div className=\"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50\">\n              <h4 className=\"text-md font-medium text-gray-900 mb-3\">\n                {editingClass ? 'Edit Class' : 'Add New Class'}\n              </h4>\n              <form onSubmit={editingClass ? handleUpdateClass : handleCreateClass}>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      Class Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={className}\n                      onChange={(e) => setClassName(e.target.value)}\n                      required\n                      className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n                      placeholder=\"e.g., Grade 10\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      Description (Optional)\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={classDescription}\n                      onChange={(e) => setClassDescription(e.target.value)}\n                      className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n                      placeholder=\"Class description\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-4 flex space-x-3\">\n                  <button\n                    type=\"submit\"\n                    className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                  >\n                    {editingClass ? 'Update' : 'Create'} Class\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={resetClassForm}\n                    className=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          )}\n\n          {/* Classes List */}\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n            {classes.map((cls) => (\n              <div key={cls.id} className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <h4 className=\"text-md font-medium text-gray-900\">{cls.name}</h4>\n                    {cls.description && (\n                      <p className=\"text-sm text-gray-600 mt-1\">{cls.description}</p>\n                    )}\n                    <p className=\"text-xs text-gray-500 mt-2\">\n                      {subjects.filter(s => s.class_id === cls.id).length} subjects\n                    </p>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => startEditClass(cls)}\n                      className=\"text-indigo-600 hover:text-indigo-900 text-sm\"\n                    >\n                      Edit\n                    </button>\n                    <button\n                      onClick={() => handleDeleteClass(cls.id)}\n                      className=\"text-red-600 hover:text-red-900 text-sm\"\n                    >\n                      Delete\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Subjects Section */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">Subjects</h3>\n            <div className=\"flex space-x-3\">\n              <select\n                value={selectedClassId || ''}\n                onChange={(e) => setSelectedClassId(e.target.value ? Number(e.target.value) : null)}\n                className=\"h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n              >\n                <option value=\"\">All Classes</option>\n                {classes.map((cls) => (\n                  <option key={cls.id} value={cls.id}>{cls.name}</option>\n                ))}\n              </select>\n              <button\n                onClick={() => setShowSubjectForm(true)}\n                className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Add Subject\n              </button>\n            </div>\n          </div>\n\n          {/* Subject Form */}\n          {showSubjectForm && (\n            <div className=\"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50\">\n              <h4 className=\"text-md font-medium text-gray-900 mb-3\">\n                {editingSubject ? 'Edit Subject' : 'Add New Subject'}\n              </h4>\n              <form onSubmit={editingSubject ? handleUpdateSubject : handleCreateSubject}>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      Class\n                    </label>\n                    <select\n                      value={subjectClassId || ''}\n                      onChange={(e) => setSubjectClassId(Number(e.target.value))}\n                      required\n                      disabled={!!editingSubject}\n                      className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white disabled:bg-gray-100 disabled:text-gray-500\"\n                    >\n                      <option value=\"\">Select a class</option>\n                      {classes.map((cls) => (\n                        <option key={cls.id} value={cls.id}>{cls.name}</option>\n                      ))}\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      Subject Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={subjectName}\n                      onChange={(e) => setSubjectName(e.target.value)}\n                      required\n                      className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n                      placeholder=\"e.g., Mathematics\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">\n                      Description (Optional)\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={subjectDescription}\n                      onChange={(e) => setSubjectDescription(e.target.value)}\n                      className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n                      placeholder=\"Subject description\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-4 flex space-x-3\">\n                  <button\n                    type=\"submit\"\n                    className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                  >\n                    {editingSubject ? 'Update' : 'Create'} Subject\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={resetSubjectForm}\n                    className=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          )}\n\n          {/* Subjects List */}\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n            {filteredSubjects.map((subject) => (\n              <div key={subject.id} className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <h4 className=\"text-md font-medium text-gray-900\">{subject.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{subject.class_name}</p>\n                    {subject.description && (\n                      <p className=\"text-sm text-gray-600 mt-1\">{subject.description}</p>\n                    )}\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => startEditSubject(subject)}\n                      className=\"text-indigo-600 hover:text-indigo-900 text-sm\"\n                    >\n                      Edit\n                    </button>\n                    <button\n                      onClick={() => handleDeleteSubject(subject.id)}\n                      className=\"text-red-600 hover:text-red-900 text-sm\"\n                    >\n                      Delete\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {filteredSubjects.length === 0 && (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">\n                {selectedClassId ? 'No subjects found for the selected class.' : 'No subjects created yet.'}\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EAAE,cAAc,EAAE,eAAe,EAAuB;IAC7F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,mBAAmB;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBAAqB;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,iBAAiB;QACrB,aAAa;QACb,oBAAoB;QACpB,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,kBAAkB;QAClB,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAW,aAAa;gBAAiB;YACxE;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,WAAW;uBAAI;oBAAS;iBAAS;gBACjC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,aAAa,EAAE,EAAE,EAAE;gBACpE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAW,aAAa;gBAAiB;YACxE;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,eAAe,MAAM,SAAS,IAAI;gBACxC,WAAW,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG,eAAe;gBACtE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,6EAA6E;YACxF;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE;gBAC5D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACxC,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;oBACV,MAAM;oBACN,aAAa;gBACf;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,aAAa,MAAM,SAAS,IAAI;gBACtC,YAAY;uBAAI;oBAAU;iBAAW;gBACrC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,eAAe,EAAE,EAAE,EAAE;gBACvE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAa,aAAa;gBAAmB;YAC5E;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,iBAAiB,MAAM,SAAS,IAAI;gBAC1C,YAAY,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;gBAC5E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,kEAAkE;YAC7E;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,WAAW,EAAE;gBAC/D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa,IAAI,IAAI;QACrB,oBAAoB,IAAI,WAAW,IAAI;QACvC,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,eAAe,QAAQ,IAAI;QAC3B,sBAAsB,QAAQ,WAAW,IAAI;QAC7C,kBAAkB,QAAQ,QAAQ;QAClC,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,kBACrB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,mBACpC;IAEJ,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;;;;;;;wBAMF,+BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,eAAe,eAAe;;;;;;8CAEjC,8OAAC;oCAAK,UAAU,eAAe,oBAAoB;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAG3D,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAG3D,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4DACnD,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;;wDAET,eAAe,WAAW;wDAAS;;;;;;;8DAEtC,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;sCAST,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;oCAAiB,WAAU;8CAC1B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC,IAAI,IAAI;;;;;;oDAC1D,IAAI,WAAW,kBACd,8OAAC;wDAAE,WAAU;kEAA8B,IAAI,WAAW;;;;;;kEAE5D,8OAAC;wDAAE,WAAU;;4DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI,EAAE,EAAE,MAAM;4DAAC;;;;;;;;;;;;;0DAGxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,eAAe;wDAC9B,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,kBAAkB,IAAI,EAAE;wDACvC,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCArBG,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;0BAiCxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO,mBAAmB;4CAC1B,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;4CAC9E,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;wDAAoB,OAAO,IAAI,EAAE;kEAAG,IAAI,IAAI;uDAAhC,IAAI,EAAE;;;;;;;;;;;sDAGvB,8OAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;sDACX;;;;;;;;;;;;;;;;;;wBAOJ,iCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,iBAAiB,iBAAiB;;;;;;8CAErC,8OAAC;oCAAK,UAAU,iBAAiB,sBAAsB;;sDACrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAG3D,8OAAC;4DACC,OAAO,kBAAkB;4DACzB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;4DACxD,QAAQ;4DACR,UAAU,CAAC,CAAC;4DACZ,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;wEAAoB,OAAO,IAAI,EAAE;kFAAG,IAAI,IAAI;uEAAhC,IAAI,EAAE;;;;;;;;;;;;;;;;;8DAIzB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAG3D,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA0C;;;;;;sEAG3D,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;4DACrD,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;;wDAET,iBAAiB,WAAW;wDAAS;;;;;;;8DAExC,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;sCAST,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;oCAAqB,WAAU;8CAC9B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC,QAAQ,IAAI;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,UAAU;;;;;;oDACvD,QAAQ,WAAW,kBAClB,8OAAC;wDAAE,WAAU;kEAA8B,QAAQ,WAAW;;;;;;;;;;;;0DAGlE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,iBAAiB;wDAChC,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;wDAC7C,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCAnBG,QAAQ,EAAE;;;;;;;;;;wBA4BvB,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,kBAAkB,8CAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF", "debugId": null}}]}