import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { ClassModel, SubjectModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import CreateBookForm from '@/components/CreateBookForm';

export default async function NewBookPage() {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const classes = ClassModel.getAll();
  const subjects = SubjectModel.getAll();

  return (
    <AdminLayout user={user}>
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Create New Book</h1>
          <p className="mt-1 text-sm text-gray-600">
            Create a new textbook container to organize your scanned pages and content.
          </p>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <CreateBookForm classes={classes} subjects={subjects} />
          </div>
        </div>

        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-blue-400 text-lg">💡</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                How the new workflow works:
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <ol className="list-decimal list-inside space-y-1">
                  <li>Create a book container (this page)</li>
                  <li>Upload and organize scanned pages</li>
                  <li>Assign page types (cover, contents, chapters)</li>
                  <li>Process OCR and extract questions</li>
                  <li>Review and edit content</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
