import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import fs from 'fs';
import path from 'path';
import db from '@/lib/database';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const imageId = parseInt(id);
    
    // Get image info from database
    const image = db.prepare('SELECT file_path FROM images WHERE id = ?').get(imageId) as any;
    
    if (!image) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Check if file exists
    if (!fs.existsSync(image.file_path)) {
      return NextResponse.json({ error: 'Image file not found' }, { status: 404 });
    }

    // Read file and return as response
    const fileBuffer = fs.readFileSync(image.file_path);
    const ext = path.extname(image.file_path).toLowerCase();
    
    let contentType = 'image/jpeg'; // default
    switch (ext) {
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
    }

    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
      },
    });
  } catch (error) {
    console.error('Image preview error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
