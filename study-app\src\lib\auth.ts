import bcrypt from 'bcryptjs';
import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import db from './database';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-change-this-in-production'
);

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'student';
  created_at: string;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  error?: string;
}

// Hash password
export function hashPassword(password: string): string {
  return bcrypt.hashSync(password, 10);
}

// Verify password
export function verifyPassword(password: string, hash: string): boolean {
  return bcrypt.compareSync(password, hash);
}

// Create JWT token
export async function createToken(user: User): Promise<string> {
  return await new SignJWT({ 
    userId: user.id, 
    email: user.email, 
    role: user.role 
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h')
    .sign(JWT_SECRET);
}

// Verify JWT token
export async function verifyToken(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

// Login user
export async function loginUser(email: string, password: string): Promise<AuthResult> {
  try {
    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email) as any;
    
    if (!user) {
      return { success: false, error: 'Invalid email or password' };
    }

    if (!verifyPassword(password, user.password_hash)) {
      return { success: false, error: 'Invalid email or password' };
    }

    const userWithoutPassword = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      created_at: user.created_at
    };

    return { success: true, user: userWithoutPassword };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, error: 'Login failed' };
  }
}

// Get current user from cookies
export async function getCurrentUser(): Promise<User | null> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('auth-token')?.value;
    
    if (!token) {
      return null;
    }

    const payload = await verifyToken(token);
    if (!payload) {
      return null;
    }

    const user = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')
      .get(payload.userId) as User;

    return user || null;
  } catch (error) {
    console.error('Get current user error:', error);
    return null;
  }
}

// Set auth cookie
export async function setAuthCookie(user: User) {
  const token = await createToken(user);
  const cookieStore = await cookies();
  
  cookieStore.set('auth-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 // 24 hours
  });
}

// Clear auth cookie
export async function clearAuthCookie() {
  const cookieStore = await cookies();
  cookieStore.delete('auth-token');
}

// Register new user (admin only)
export function registerUser(name: string, email: string, password: string, role: 'admin' | 'student'): AuthResult {
  try {
    // Check if user already exists
    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get(email);
    if (existingUser) {
      return { success: false, error: 'User with this email already exists' };
    }

    const hashedPassword = hashPassword(password);
    
    const result = db.prepare(`
      INSERT INTO users (name, email, role, password_hash)
      VALUES (?, ?, ?, ?)
    `).run(name, email, role, hashedPassword);

    const newUser = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')
      .get(result.lastInsertRowid) as User;

    return { success: true, user: newUser };
  } catch (error) {
    console.error('Registration error:', error);
    return { success: false, error: 'Registration failed' };
  }
}

// Middleware to check if user is authenticated
export async function requireAuth(): Promise<User | null> {
  const user = await getCurrentUser();
  return user;
}

// Middleware to check if user is admin
export async function requireAdmin(): Promise<User | null> {
  const user = await getCurrentUser();
  if (!user || user.role !== 'admin') {
    return null;
  }
  return user;
}

// Get all users (admin only)
export function getAllUsers(): User[] {
  return db.prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all() as User[];
}

// Update user
export function updateUser(id: number, updates: Partial<Pick<User, 'name' | 'email' | 'role'>>): boolean {
  try {
    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    db.prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)
      .run(...values, id);
    
    return true;
  } catch (error) {
    console.error('Update user error:', error);
    return false;
  }
}

// Delete user
export function deleteUser(id: number): boolean {
  try {
    db.prepare('DELETE FROM users WHERE id = ?').run(id);
    return true;
  } catch (error) {
    console.error('Delete user error:', error);
    return false;
  }
}
