import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { getImages } from '@/lib/upload';

export async function GET(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId');
    const subjectId = searchParams.get('subjectId');

    const images = getImages(
      classId ? parseInt(classId) : undefined,
      subjectId ? parseInt(subjectId) : undefined
    );
    
    return NextResponse.json(images);
  } catch (error) {
    console.error('Get images error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
