%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (Add-H)
%%Title: (Add-H Adobe Japan1 1)
%%Version: 10.003
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2009 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/CIDSystemInfo 3 dict dup begin
  /Registry (Adobe) def
  /Ordering (Japan1) def
  /Supplement 1 def
end def

/CMapName /Add-H def
/CMapVersion 10.003 def
/CMapType 1 def

/UIDOffset 580 def
/XUID [1 10 25325] def

/WMode 0 def

1 begincodespacerange
  <2121> <7E7E>
endcodespacerange

100 begincidrange
<2121> <217e>  633
<2221> <222e>  727
<223a> <2241>  741
<224a> <2250>  749
<225c> <226a>  756
<2272> <2279>  771
<227e> <227e>  779
<2330> <2339>  780
<2341> <235a>  790
<2361> <237a>  816
<2421> <2473>  842
<2474> <2476> 7958
<2521> <2576>  925
<2621> <2638> 1011
<2641> <2658> 1035
<2721> <2741> 1059
<2751> <2771> 1092
<2821> <2821> 7479
<2822> <2822> 7481
<2823> <2823> 7491
<2824> <2824> 7495
<2825> <2825> 7503
<2826> <2826> 7499
<2827> <2827> 7507
<2828> <2828> 7523
<2829> <2829> 7515
<282a> <282a> 7531
<282b> <282b> 7539
<282c> <282c> 7480
<282d> <282d> 7482
<282e> <282e> 7494
<282f> <282f> 7498
<2830> <2830> 7506
<2831> <2831> 7502
<2832> <2832> 7514
<2833> <2833> 7530
<2834> <2834> 7522
<2835> <2835> 7538
<2836> <2836> 7554
<2837> <2837> 7511
<2838> <2838> 7526
<2839> <2839> 7519
<283a> <283a> 7534
<283b> <283b> 7542
<283c> <283c> 7508
<283d> <283d> 7527
<283e> <283e> 7516
<283f> <283f> 7535
<2840> <2840> 7545
<3021> <3021> 1125
<3022> <3022> 7633
<3023> <3031> 1127
<3032> <3032> 7961
<3033> <303a> 1143
<303b> <303b> 7634
<303c> <306d> 1152
<306e> <306e> 7635
<306f> <3070> 1203
<3071> <3071> 7962
<3072> <3072> 1206
<3073> <3073> 7636
<3074> <307b> 1208
<307c> <307c> 7637
<307d> <307e> 1217
<3121> <3129> 1219
<312a> <312a> 7638
<312b> <3132> 1229
<3133> <3133> 7963
<3134> <3134> 1238
<3135> <3135> 7639
<3136> <3138> 1240
<3139> <3139> 7964
<313a> <313c> 1244
<313d> <313d> 7642
<313e> <3141> 1248
<3142> <3142> 7643
<3143> <316a> 1253
<316b> <316b> 7644
<316c> <317e> 1294
<3221> <3227> 1313
<3228> <3228> 7645
<3229> <3229> 1321
<322a> <322a> 7646
<322b> <325f> 1323
<3260> <3260> 7647
<3261> <3266> 1377
<3267> <3267> 7965
<3268> <3279> 1384
<327a> <327a> 7648
<327b> <327e> 1403
<3321> <3321> 1407
<3322> <3322> 7650
<3323> <336a> 1409
<336b> <336b> 7652
<336c> <3372> 1482
<3373> <3373> 7653
<3374> <3379> 1490
<337a> <337a> 7654
<337b> <337e> 1497
<3421> <3441> 1501
endcidrange

100 begincidrange
<3442> <3442> 7655
<3443> <344c> 1535
<344d> <344d> 7656
<344e> <3464> 1546
<3465> <3465> 7657
<3466> <347e> 1570
<3521> <352a> 1595
<352b> <352b> 7658
<352c> <353f> 1606
<3540> <3540> 7659
<3541> <3561> 1627
<3562> <3562> 7966
<3563> <3567> 1661
<3568> <3568> 7967
<3569> <357e> 1667
<3621> <3621> 1689
<3622> <3622> 7660
<3623> <3629> 1691
<362a> <362a> 7661
<362b> <3641> 1699
<3642> <3642> 7968
<3643> <364e> 1723
<364f> <364f> 7662
<3650> <366c> 1736
<366d> <366d> 7663
<366e> <3673> 1766
<3674> <3674> 7664
<3675> <367a> 1773
<367b> <367b> 7665
<367c> <367c> 1780
<367d> <367d> 7666
<367e> <367e> 1782
<3721> <3736> 1783
<3737> <3737> 7668
<3738> <3751> 1806
<3752> <3752> 7671
<3753> <3770> 1833
<3771> <3771> 7674
<3772> <377c> 1864
<377d> <377d> 7969
<377e> <377e> 7676
<3821> <3833> 1877
<3834> <3834> 7677
<3835> <3840> 1897
<3841> <3841> 7678
<3842> <387e> 1910
<3921> <392a> 1971
<392b> <392b> 7679
<392c> <396c> 1982
<396d> <396d> 7682
<396e> <3973> 2048
<3974> <3974> 7683
<3975> <3978> 2055
<3979> <3979> 7684
<397a> <397e> 2060
<3a21> <3a52> 2065
<3a53> <3a53> 7685
<3a54> <3a66> 2116
<3a67> <3a67> 7686
<3a68> <3a73> 2136
<3a74> <3a74> 7687
<3a75> <3a7e> 2149
<3b21> <3b26> 2159
<3b27> <3b27> 7688
<3b28> <3b29> 2166
<3b2a> <3b2a> 7689
<3b2b> <3b2b> 2169
<3b2c> <3b2c> 7690
<3b2d> <3b38> 2171
<3b39> <3b39> 7691
<3b3a> <3b40> 2184
<3b41> <3b41> 7970
<3b42> <3b7e> 2192
<3c21> <3c47> 2253
<3c48> <3c48> 7693
<3c49> <3c5c> 2293
<3c5d> <3c5e> 7695
<3c5f> <3c7e> 2315
<3d21> <3d2a> 2347
<3d2b> <3d2b> 7697
<3d2c> <3d35> 2358
<3d36> <3d36> 7698
<3d37> <3d6b> 2369
<3d6c> <3d6c> 7699
<3d6d> <3d71> 2423
<3d72> <3d73> 7701
<3d74> <3d7e> 2430
<3e21> <3e24> 2441
<3e25> <3e25> 7703
<3e26> <3e32> 2446
<3e33> <3e33> 7704
<3e34> <3e54> 2460
<3e55> <3e55> 7706
<3e56> <3e5e> 2494
<3e5f> <3e5f> 7707
<3e60> <3e63> 2504
<3e64> <3e64> 7708
<3e65> <3e7e> 2509
<3f21> <3f29> 2535
<3f2a> <3f2a> 7709
endcidrange

100 begincidrange
<3f2b> <3f58> 2545
<3f59> <3f59> 7971
<3f5a> <3f5f> 2592
<3f60> <3f60> 7711
<3f61> <3f7e> 2599
<4021> <4021> 2629
<4022> <4022> 7713
<4023> <4065> 2631
<4066> <4066> 7715
<4067> <4070> 2699
<4071> <4071> 7716
<4072> <4078> 2710
<4079> <4079> 7718
<407a> <407a> 7972
<407b> <407b> 2719
<407c> <407d> 7973
<407e> <407e> 2722
<4121> <4126> 2723
<4127> <4127> 7720
<4128> <4138> 2730
<4139> <4139> 7721
<413a> <414b> 2748
<414c> <414c> 7722
<414d> <414e> 2767
<414f> <414f> 7723
<4150> <415e> 2770
<415f> <415f> 7724
<4160> <4168> 2786
<4169> <4169> 7725
<416a> <417e> 2796
<4221> <4236> 2817
<4237> <4237> 7975
<4238> <423c> 2840
<423d> <423d> 7726
<423e> <424c> 2846
<424d> <424d> 7727
<424e> <425b> 2862
<425c> <425c> 7728
<425d> <4262> 2877
<4263> <4263> 7729
<4264> <426e> 2884
<426f> <426f> 7730
<4270> <427c> 2896
<427d> <427d> 7733
<427e> <427e> 2910
<4321> <4326> 2911
<4327> <4327> 7734
<4328> <4328> 2918
<4329> <4329> 7735
<432a> <432c> 2920
<432d> <432e> 7737
<432f> <433c> 2925
<433d> <433d> 7739
<433e> <436f> 2940
<4370> <4370> 7740
<4371> <4374> 2991
<4375> <4375> 7741
<4376> <437b> 2996
<437c> <437c> 7742
<437d> <437e> 3003
<4421> <443c> 3005
<443d> <443d> 7743
<443e> <4447> 3034
<4448> <4448> 7744
<4449> <4449> 3045
<444a> <444a> 7745
<444b> <444e> 3047
<444f> <444f> 7747
<4450> <447e> 3052
<4521> <4521> 3099
<4522> <4522> 7748
<4523> <4526> 3101
<4527> <4527> 7749
<4528> <452d> 3106
<452e> <452e> 7750
<452f> <4535> 3113
<4536> <4536> 7751
<4537> <453e> 3121
<453f> <453f> 7752
<4540> <4547> 3130
<4548> <4548> 7753
<4549> <454a> 3139
<454b> <454b> 7754
<454c> <4550> 3142
<4551> <4551> 7976
<4552> <4552> 7756
<4553> <4563> 3149
<4564> <4564> 7757
<4565> <4577> 3167
<4578> <4578> 7758
<4579> <457e> 3187
<4621> <4641> 3193
<4642> <4642> 7760
<4643> <4653> 3227
<4654> <4654> 7761
<4655> <465a> 3245
<465b> <465b> 7763
<465c> <4665> 3252
<4666> <4667> 7766
<4668> <4669> 3264
endcidrange

100 begincidrange
<466a> <466a> 7768
<466b> <4675> 3267
<4676> <4676> 7872
<4677> <467e> 3279
<4721> <4728> 3287
<4729> <4729> 7769
<472a> <472a> 7977
<472b> <4738> 3297
<4739> <4739> 7770
<473a> <4756> 3312
<4757> <4757> 7771
<4758> <4766> 3342
<4767> <4767> 7772
<4768> <4768> 3358
<4769> <4769> 7773
<476a> <476c> 3360
<476d> <476d> 7774
<476e> <477e> 3364
<4821> <4823> 3381
<4824> <4824> 7775
<4825> <482d> 3385
<482e> <482e> 7776
<482f> <482f> 3395
<4830> <4830> 7777
<4831> <483f> 3397
<4840> <4840> 7978
<4841> <4853> 3413
<4854> <4854> 7778
<4855> <4874> 3433
<4875> <4875> 7780
<4876> <487e> 3466
<4921> <4921> 3475
<4922> <4923> 7781
<4924> <492e> 3478
<492f> <492f> 7783
<4930> <4931> 3490
<4932> <4932> 7784
<4933> <4934> 3493
<4935> <4935> 7785
<4936> <493f> 3496
<4940> <4940> 7786
<4941> <494d> 3507
<494e> <494e> 7787
<494f> <497e> 3521
<4a21> <4a42> 3569
<4a43> <4a43> 7789
<4a44> <4a4c> 3604
<4a4d> <4a4d> 7790
<4a4e> <4a52> 3614
<4a53> <4a53> 7979
<4a54> <4a59> 3620
<4a5a> <4a5a> 7791
<4a5b> <4a78> 3627
<4a79> <4a79> 7792
<4a7a> <4a7e> 3658
<4b21> <4b28> 3663
<4b29> <4b29> 7794
<4b2a> <4b4a> 3672
<4b4b> <4b4b> 7795
<4b4c> <4b6f> 3706
<4b70> <4b70> 7796
<4b71> <4b77> 3743
<4b78> <4b78> 7980
<4b79> <4b7e> 3751
<4c21> <4c4c> 3757
<4c4d> <4c4d> 7797
<4c4e> <4c58> 3802
<4c59> <4c59> 7798
<4c5a> <4c5e> 3814
<4c5f> <4c5f> 7799
<4c60> <4c61> 3820
<4c62> <4c62> 7800
<4c63> <4c79> 3823
<4c7a> <4c7a> 7801
<4c7b> <4c7b> 3847
<4c7c> <4c7c> 7802
<4c7d> <4c7e> 3849
<4d21> <4d31> 3851
<4d32> <4d32> 7804
<4d33> <4d4f> 3869
<4d50> <4d50> 7805
<4d51> <4d53> 3899
<4d54> <4d54> 7806
<4d55> <4d68> 3903
<4d69> <4d69> 7807
<4d6a> <4d7e> 3924
<4e21> <4e79> 3945
<4e7a> <4e7b> 7809
<4e7c> <4e7c> 7981
<4e7d> <4e7e> 4037
<4f21> <4f21> 7811
<4f22> <4f30> 4040
<4f31> <4f31> 7812
<4f32> <4f38> 4056
<4f39> <4f39> 7813
<4f3a> <4f53> 4064
<5021> <507e> 4090
<5121> <5121> 4184
<5122> <5122> 7982
<5123> <513c> 4186
endcidrange

100 begincidrange
<513d> <513d> 7814
<513e> <514c> 4213
<514d> <514d> 7817
<514e> <517e> 4229
<5221> <5237> 4278
<5238> <5238> 7983
<5239> <527e> 4302
<5321> <532f> 4372
<5330> <5330> 7818
<5331> <5339> 4388
<533a> <533a> 7819
<533b> <5347> 4398
<5348> <5348> 7984
<5349> <535d> 4412
<535e> <535e> 7821
<535f> <536a> 4434
<536b> <536b> 7822
<536c> <536c> 7985
<536d> <537e> 4448
<5421> <5443> 4466
<5444> <5444> 7823
<5445> <5445> 7986
<5446> <546b> 4503
<546c> <546c> 7987
<546d> <547e> 4542
<5521> <553c> 4560
<553d> <553d> 7824
<553e> <5562> 4589
<5563> <5563> 7825
<5564> <5577> 4627
<5578> <5578> 7988
<5579> <557e> 4648
<5621> <5621> 4654
<5622> <5622> 7826
<5623> <567c> 4656
<567d> <567e> 7989
<5721> <577e> 4748
<5821> <5823> 4842
<5824> <5824> 7828
<5825> <587e> 4846
<5921> <5927> 4936
<5928> <5928> 7991
<5929> <595f> 4944
<5960> <5960> 7829
<5961> <596b> 5000
<596c> <596c> 7830
<596d> <597e> 5012
<5a21> <5a38> 5030
<5a39> <5a39> 7831
<5a3a> <5a79> 5055
<5a7a> <5a7a> 7992
<5a7b> <5a7e> 5120
<5b21> <5b44> 5124
<5b45> <5b45> 7833
<5b46> <5b6a> 5161
<5b6b> <5b6b> 7835
<5b6c> <5b7e> 5199
<5c21> <5c7e> 5218
<5d21> <5d60> 5312
<5d61> <5d61> 7993
<5d62> <5d7e> 5377
<5e21> <5e4f> 5406
<5e50> <5e50> 7837
<5e51> <5e55> 5454
<5e56> <5e56> 7994
<5e57> <5e75> 5460
<5e76> <5e76> 7995
<5e77> <5e7e> 5492
<5f21> <5f72> 5500
<5f73> <5f73> 7838
<5f74> <5f7e> 5583
<6021> <6025> 5594
<6026> <6026> 7839
<6027> <605e> 5600
<605f> <605f> 7840
<6060> <607e> 5657
<6121> <612a> 5688
<612b> <612b> 7841
<612c> <617e> 5699
<6221> <626e> 5782
<626f> <626f> 7845
<6270> <627e> 5861
<6321> <6349> 5876
<634a> <634a> 7846
<634b> <6353> 5918
<6354> <6354> 7847
<6355> <6358> 5928
<6359> <6359> 7996
<635a> <637e> 5933
<6421> <6438> 5970
<6439> <6439> 7848
<643a> <643f> 5995
<6440> <6440> 7997
<6441> <6463> 6002
<6464> <6464> 7849
<6465> <646d> 6038
<646e> <646e> 7850
<646f> <647e> 6048
<6521> <6538> 6064
<6539> <6539> 7851
endcidrange

100 begincidrange
<653a> <653a> 6089
<653b> <653b> 7852
<653c> <6545> 6091
<6546> <6546> 7853
<6547> <657e> 6102
<6621> <6648> 6158
<6649> <6649> 7998
<664a> <667e> 6199
<6721> <6763> 6252
<6764> <6764> 7855
<6765> <6768> 6320
<6769> <6769> 7856
<676a> <6771> 6325
<6772> <6772> 7857
<6773> <677e> 6334
<6821> <683a> 6346
<683b> <683b> 7859
<683c> <684c> 6373
<684d> <684d> 7999
<684e> <687e> 6391
<6921> <697d> 6440
<697e> <697e> 8000
<6a21> <6a3b> 6534
<6a3c> <6a3d> 8001
<6a3e> <6a6e> 6563
<6a6f> <6a6f> 7864
<6a70> <6a7e> 6613
<6b21> <6b31> 6628
<6b32> <6b32> 7865
<6b33> <6b65> 6646
<6b66> <6b66> 7866
<6b67> <6b7e> 6698
<6c21> <6c68> 6722
<6c69> <6c69> 7868
<6c6a> <6c7e> 6795
<6d21> <6d4d> 6816
<6d4e> <6d4e> 7870
<6d4f> <6d7e> 6862
<6e21> <6e28> 6910
<6e29> <6e29> 7873
<6e2a> <6e3c> 6919
<6e3d> <6e3d> 7874
<6e3e> <6e7e> 6939
<6f21> <6f7e> 7004
<7021> <7050> 7098
<7051> <7051> 7879
<7052> <707e> 7147
<7121> <7158> 7192
<7159> <7159> 8003
<715a> <717e> 7249
<7221> <722c> 7286
<722d> <722d> 7882
<722e> <723b> 7299
<723c> <723c> 7883
<723d> <724d> 7314
<724e> <724e> 7884
<724f> <727e> 7332
<7321> <7350> 7380
<7351> <7351> 7885
<7352> <7352> 8004
<7353> <737c> 7430
<737d> <737d> 7886
<737e> <737e> 7473
<7421> <7424> 7474
<7425> <7426> 8284
<7721> <7723> 8005
<7727> <7727> 8008
<7728> <7728>  768
<7729> <7729>  762
<772a> <772a>  761
<772e> <7738> 8009
<773c> <773e> 7601
<773f> <773f> 8020
<7740> <7740> 7607
<7741> <7743> 8021
<7744> <7746> 7604
<7747> <7750> 8024
<7751> <7751>  771
<7752> <7752> 8034
<7753> <7753>  772
<7754> <7755> 8035
<7757> <7757> 8037
<7759> <7759> 7588
<775a> <775a> 7585
<775b> <775b> 8038
<775c> <775c> 7586
<775d> <775f> 8039
<7760> <7760> 7590
<7761> <7761> 8042
<7762> <7762> 7592
<7763> <7763> 7596
<7764> <7764> 8043
<7765> <7765> 7598
<7766> <7766> 7595
<7767> <7768> 8044
<7769> <7769> 7599
<776a> <7770> 8046
<7774> <7779> 8053
<777a> <777a> 7610
<777b> <777b> 8059
endcidrange

31 begincidrange
<777e> <777e> 8060
<7829> <7832> 8061
<7834> <7847> 8071
<7849> <785c> 7555
<785d> <785d> 8091
<785e> <7867> 7575
<786b> <787e> 8092
<7921> <793a> 8112
<7945> <7945> 7958
<7949> <794a> 8138
<794b> <794b> 7620
<794c> <794e> 8140
<794f> <794f> 7619
<7950> <7954> 8143
<7955> <7955> 7618
<7956> <7959> 8148
<795d> <796a> 8152
<796f> <797e> 8166
<7d21> <7d22> 7887
<7d23> <7d23> 8268
<7d24> <7d24> 8274
<7d25> <7d2e> 7889
<7d2f> <7d2f> 8282
<7d30> <7d30> 8275
<7d31> <7d31> 8280
<7d32> <7d32> 8277
<7d33> <7d44> 7899
<7d45> <7d5a> 7918
<7d5b> <7d5c> 8264
<7d6d> <7d70>  736
<7d71> <7d74> 8182
endcidrange
endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
