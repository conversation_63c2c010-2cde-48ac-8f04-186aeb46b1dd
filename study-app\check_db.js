const db = require('better-sqlite3')('data/study_app.db');

console.log('All tables:');
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log(tables);

console.log('\nChecking for OCR-related tables:');
const ocrTables = tables.filter(t => t.name.toLowerCase().includes('ocr'));
console.log(ocrTables);

if (ocrTables.length > 0) {
  console.log('\nSample data from first OCR table:');
  const sample = db.prepare(`SELECT * FROM ${ocrTables[0].name} LIMIT 3`).all();
  console.log(sample);

  console.log('\nTotal OCR records:');
  const count = db.prepare(`SELECT COUNT(*) as count FROM ${ocrTables[0].name}`).get();
  console.log(count);
}

console.log('\nChecking images table:');
const images = db.prepare("SELECT COUNT(*) as count FROM images").get();
console.log('Total images:', images);

db.close();
