import { NextRequest, NextResponse } from 'next/server';
import { BookModel } from '@/lib/models';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, class_id, subject_id, description } = body;

    // Validate required fields
    if (!title || !class_id || !subject_id) {
      return NextResponse.json(
        { success: false, error: 'Title, class, and subject are required' },
        { status: 400 }
      );
    }

    // Create the book
    const bookId = BookModel.create({
      title,
      class_id,
      subject_id,
      description
    });

    return NextResponse.json({ 
      success: true, 
      bookId,
      message: 'Book created successfully' 
    });
  } catch (error) {
    console.error('Error creating book:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create book' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const books = BookModel.getAll();
    return NextResponse.json({ success: true, books });
  } catch (error) {
    console.error('Error fetching books:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch books' },
      { status: 500 }
    );
  }
}
