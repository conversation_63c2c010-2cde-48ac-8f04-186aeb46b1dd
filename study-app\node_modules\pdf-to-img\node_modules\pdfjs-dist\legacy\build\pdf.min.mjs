/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */var t,e,i,s,n={9306:(t,e,i)=>{var s=i(4901),n=i(6823),r=TypeError;t.exports=function(t){if(s(t))return t;throw new r(n(t)+" is not a function")}},3506:(t,e,i)=>{var s=i(3925),n=String,r=TypeError;t.exports=function(t){if(s(t))return t;throw new r("Can't set "+n(t)+" as a prototype")}},7080:(t,e,i)=>{var s=i(4402).has;t.exports=function(t){s(t);return t}},679:(t,e,i)=>{var s=i(1625),n=TypeError;t.exports=function(t,e){if(s(e,t))return t;throw new n("Incorrect invocation")}},8551:(t,e,i)=>{var s=i(34),n=String,r=TypeError;t.exports=function(t){if(s(t))return t;throw new r(n(t)+" is not an object")}},7811:t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7394:(t,e,i)=>{var s=i(6706),n=i(4576),r=TypeError;t.exports=s(ArrayBuffer.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==n(t))throw new r("ArrayBuffer expected");return t.byteLength}},3238:(t,e,i)=>{var s=i(9504),n=i(7394),r=s(ArrayBuffer.prototype.slice);t.exports=function(t){if(0!==n(t))return!1;try{r(t,0,0);return!1}catch(t){return!0}}},5636:(t,e,i)=>{var s=i(4475),n=i(9504),r=i(6706),a=i(7696),o=i(3238),l=i(7394),h=i(4483),d=i(1548),c=s.structuredClone,u=s.ArrayBuffer,p=s.DataView,g=s.TypeError,f=Math.min,m=u.prototype,b=p.prototype,v=n(m.slice),y=r(m,"resizable","get"),A=r(m,"maxByteLength","get"),E=n(b.getInt8),w=n(b.setInt8);t.exports=(d||h)&&function(t,e,i){var s,n=l(t),r=void 0===e?n:a(e),m=!y||!y(t);if(o(t))throw new g("ArrayBuffer is detached");if(d){t=c(t,{transfer:[t]});if(n===r&&(i||m))return t}if(n>=r&&(!i||m))s=v(t,0,r);else{var b=i&&!m&&A?{maxByteLength:A(t)}:void 0;s=new u(r,b);for(var x=new p(t),_=new p(s),S=f(r,n),T=0;T<S;T++)w(_,T,E(x,T))}d||h(t);return s}},4644:(t,e,i)=>{var s,n,r,a=i(7811),o=i(3724),l=i(4475),h=i(4901),d=i(34),c=i(9297),u=i(6955),p=i(6823),g=i(6699),f=i(6840),m=i(2106),b=i(1625),v=i(2787),y=i(2967),A=i(8227),E=i(3392),w=i(1181),x=w.enforce,_=w.get,S=l.Int8Array,T=S&&S.prototype,C=l.Uint8ClampedArray,M=C&&C.prototype,P=S&&v(S),R=T&&v(T),k=Object.prototype,D=l.TypeError,I=A("toStringTag"),L=E("TYPED_ARRAY_TAG"),O="TypedArrayConstructor",N=a&&!!y&&"Opera"!==u(l.opera),B=!1,H={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},U={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(t){var e=v(t);if(d(e)){var i=_(e);return i&&c(i,O)?i[O]:getTypedArrayConstructor(e)}},isTypedArray=function(t){if(!d(t))return!1;var e=u(t);return c(H,e)||c(U,e)};for(s in H)(r=(n=l[s])&&n.prototype)?x(r)[O]=n:N=!1;for(s in U)(r=(n=l[s])&&n.prototype)&&(x(r)[O]=n);if(!N||!h(P)||P===Function.prototype){P=function TypedArray(){throw new D("Incorrect invocation")};if(N)for(s in H)l[s]&&y(l[s],P)}if(!N||!R||R===k){R=P.prototype;if(N)for(s in H)l[s]&&y(l[s].prototype,R)}N&&v(M)!==R&&y(M,R);if(o&&!c(R,I)){B=!0;m(R,I,{configurable:!0,get:function(){return d(this)?this[L]:void 0}});for(s in H)l[s]&&g(l[s],L,s)}t.exports={NATIVE_ARRAY_BUFFER_VIEWS:N,TYPED_ARRAY_TAG:B&&L,aTypedArray:function(t){if(isTypedArray(t))return t;throw new D("Target is not a typed array")},aTypedArrayConstructor:function(t){if(h(t)&&(!y||b(P,t)))return t;throw new D(p(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,i,s){if(o){if(i)for(var n in H){var r=l[n];if(r&&c(r.prototype,t))try{delete r.prototype[t]}catch(i){try{r.prototype[t]=e}catch(t){}}}R[t]&&!i||f(R,t,i?e:N&&T[t]||e,s)}},exportTypedArrayStaticMethod:function(t,e,i){var s,n;if(o){if(y){if(i)for(s in H)if((n=l[s])&&c(n,t))try{delete n[t]}catch(t){}if(P[t]&&!i)return;try{return f(P,t,i?e:N&&P[t]||e)}catch(t){}}for(s in H)!(n=l[s])||n[t]&&!i||f(n,t,e)}},getTypedArrayConstructor,isView:function isView(t){if(!d(t))return!1;var e=u(t);return"DataView"===e||c(H,e)||c(U,e)},isTypedArray,TypedArray:P,TypedArrayPrototype:R}},5370:(t,e,i)=>{var s=i(6198);t.exports=function(t,e,i){for(var n=0,r=arguments.length>2?i:s(e),a=new t(r);r>n;)a[n]=e[n++];return a}},9617:(t,e,i)=>{var s=i(5397),n=i(5610),r=i(6198),createMethod=function(t){return function(e,i,a){var o=s(e),l=r(o);if(0===l)return!t&&-1;var h,d=n(a,l);if(t&&i!=i){for(;l>d;)if((h=o[d++])!=h)return!0}else for(;l>d;d++)if((t||d in o)&&o[d]===i)return t||d||0;return!t&&-1}};t.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},4527:(t,e,i)=>{var s=i(3724),n=i(4376),r=TypeError,a=Object.getOwnPropertyDescriptor,o=s&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(n(t)&&!a(t,"length").writable)throw new r("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},7628:(t,e,i)=>{var s=i(6198);t.exports=function(t,e){for(var i=s(t),n=new e(i),r=0;r<i;r++)n[r]=t[i-r-1];return n}},9928:(t,e,i)=>{var s=i(6198),n=i(1291),r=RangeError;t.exports=function(t,e,i,a){var o=s(t),l=n(i),h=l<0?o+l:l;if(h>=o||h<0)throw new r("Incorrect index");for(var d=new e(o),c=0;c<o;c++)d[c]=c===h?a:t[c];return d}},6319:(t,e,i)=>{var s=i(8551),n=i(9539);t.exports=function(t,e,i,r){try{return r?e(s(i)[0],i[1]):e(i)}catch(e){n(t,"throw",e)}}},4576:(t,e,i)=>{var s=i(9504),n=s({}.toString),r=s("".slice);t.exports=function(t){return r(n(t),8,-1)}},6955:(t,e,i)=>{var s=i(2140),n=i(4901),r=i(4576),a=i(8227)("toStringTag"),o=Object,l="Arguments"===r(function(){return arguments}());t.exports=s?r:function(t){var e,i,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?i:l?r(e):"Object"===(s=r(e))&&n(e.callee)?"Arguments":s}},7740:(t,e,i)=>{var s=i(9297),n=i(5031),r=i(7347),a=i(4913);t.exports=function(t,e,i){for(var o=n(e),l=a.f,h=r.f,d=0;d<o.length;d++){var c=o[d];s(t,c)||i&&s(i,c)||l(t,c,h(e,c))}}},2211:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},2529:t=>{t.exports=function(t,e){return{value:t,done:e}}},6699:(t,e,i)=>{var s=i(3724),n=i(4913),r=i(6980);t.exports=s?function(t,e,i){return n.f(t,e,r(1,i))}:function(t,e,i){t[e]=i;return t}},6980:t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4659:(t,e,i)=>{var s=i(3724),n=i(4913),r=i(6980);t.exports=function(t,e,i){s?n.f(t,e,r(0,i)):t[e]=i}},2106:(t,e,i)=>{var s=i(283),n=i(4913);t.exports=function(t,e,i){i.get&&s(i.get,e,{getter:!0});i.set&&s(i.set,e,{setter:!0});return n.f(t,e,i)}},6840:(t,e,i)=>{var s=i(4901),n=i(4913),r=i(283),a=i(9433);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,h=void 0!==o.name?o.name:e;s(i)&&r(i,h,o);if(o.global)l?t[e]=i:a(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=i:n.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},6279:(t,e,i)=>{var s=i(6840);t.exports=function(t,e,i){for(var n in e)s(t,n,e[n],i);return t}},9433:(t,e,i)=>{var s=i(4475),n=Object.defineProperty;t.exports=function(t,e){try{n(s,t,{value:e,configurable:!0,writable:!0})}catch(i){s[t]=e}return e}},3724:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4483:(t,e,i)=>{var s,n,r,a,o=i(4475),l=i(9714),h=i(1548),d=o.structuredClone,c=o.ArrayBuffer,u=o.MessageChannel,p=!1;if(h)p=function(t){d(t,{transfer:[t]})};else if(c)try{u||(s=l("worker_threads"))&&(u=s.MessageChannel);if(u){n=new u;r=new c(2);a=function(t){n.port1.postMessage(null,[t])};if(2===r.byteLength){a(r);0===r.byteLength&&(p=a)}}}catch(t){}t.exports=p},4055:(t,e,i)=>{var s=i(4475),n=i(34),r=s.document,a=n(r)&&n(r.createElement);t.exports=function(t){return a?r.createElement(t):{}}},6837:t=>{var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},5002:t=>{t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},7290:(t,e,i)=>{var s=i(516),n=i(9088);t.exports=!s&&!n&&"object"==typeof window&&"object"==typeof document},516:t=>{t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},9088:(t,e,i)=>{var s=i(4475),n=i(4576);t.exports="process"===n(s.process)},9392:t=>{t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},7388:(t,e,i)=>{var s,n,r=i(4475),a=i(9392),o=r.process,l=r.Deno,h=o&&o.versions||l&&l.version,d=h&&h.v8;d&&(n=(s=d.split("."))[0]>0&&s[0]<4?1:+(s[0]+s[1]));!n&&a&&(!(s=a.match(/Edge\/(\d+)/))||s[1]>=74)&&(s=a.match(/Chrome\/(\d+)/))&&(n=+s[1]);t.exports=n},8727:t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6193:(t,e,i)=>{var s=i(9504),n=Error,r=s("".replace),a=String(new n("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(a);t.exports=function(t,e){if(l&&"string"==typeof t&&!n.prepareStackTrace)for(;e--;)t=r(t,o,"");return t}},6518:(t,e,i)=>{var s=i(4475),n=i(7347).f,r=i(6699),a=i(6840),o=i(9433),l=i(7740),h=i(2796);t.exports=function(t,e){var i,d,c,u,p,g=t.target,f=t.global,m=t.stat;if(i=f?s:m?s[g]||o(g,{}):s[g]&&s[g].prototype)for(d in e){u=e[d];c=t.dontCallGetSet?(p=n(i,d))&&p.value:i[d];if(!h(f?d:g+(m?".":"#")+d,t.forced)&&void 0!==c){if(typeof u==typeof c)continue;l(u,c)}(t.sham||c&&c.sham)&&r(u,"sham",!0);a(i,d,u,t)}}},9039:t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},6080:(t,e,i)=>{var s=i(7476),n=i(9306),r=i(616),a=s(s.bind);t.exports=function(t,e){n(t);return void 0===e?t:r?a(t,e):function(){return t.apply(e,arguments)}}},616:(t,e,i)=>{var s=i(9039);t.exports=!s((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},9565:(t,e,i)=>{var s=i(616),n=Function.prototype.call;t.exports=s?n.bind(n):function(){return n.apply(n,arguments)}},350:(t,e,i)=>{var s=i(3724),n=i(9297),r=Function.prototype,a=s&&Object.getOwnPropertyDescriptor,o=n(r,"name"),l=o&&"something"===function something(){}.name,h=o&&(!s||s&&a(r,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:h}},6706:(t,e,i)=>{var s=i(9504),n=i(9306);t.exports=function(t,e,i){try{return s(n(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}}},7476:(t,e,i)=>{var s=i(4576),n=i(9504);t.exports=function(t){if("Function"===s(t))return n(t)}},9504:(t,e,i)=>{var s=i(616),n=Function.prototype,r=n.call,a=s&&n.bind.bind(r,r);t.exports=s?a:function(t){return function(){return r.apply(t,arguments)}}},7751:(t,e,i)=>{var s=i(4475),n=i(4901);t.exports=function(t,e){return arguments.length<2?(i=s[t],n(i)?i:void 0):s[t]&&s[t][e];var i}},1767:t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},8646:(t,e,i)=>{var s=i(9565),n=i(8551),r=i(1767),a=i(851);t.exports=function(t,e){e&&"string"==typeof t||n(t);var i=a(t);return r(n(void 0!==i?s(i,t):t))}},851:(t,e,i)=>{var s=i(6955),n=i(5966),r=i(4117),a=i(6269),o=i(8227)("iterator");t.exports=function(t){if(!r(t))return n(t,o)||n(t,"@@iterator")||a[s(t)]}},81:(t,e,i)=>{var s=i(9565),n=i(9306),r=i(8551),a=i(6823),o=i(851),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(n(i))return r(s(i,t));throw new l(a(t)+" is not iterable")}},5966:(t,e,i)=>{var s=i(9306),n=i(4117);t.exports=function(t,e){var i=t[e];return n(i)?void 0:s(i)}},3789:(t,e,i)=>{var s=i(9306),n=i(8551),r=i(9565),a=i(1291),o=i(1767),l="Invalid size",h=RangeError,d=TypeError,c=Math.max,SetRecord=function(t,e){this.set=t;this.size=c(e,0);this.has=s(t.has);this.keys=s(t.keys)};SetRecord.prototype={getIterator:function(){return o(n(r(this.keys,this.set)))},includes:function(t){return r(this.has,this.set,t)}};t.exports=function(t){n(t);var e=+t.size;if(e!=e)throw new d(l);var i=a(e);if(i<0)throw new h(l);return new SetRecord(t,i)}},4475:function(t){var check=function(t){return t&&t.Math===Math&&t};t.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:(t,e,i)=>{var s=i(9504),n=i(8981),r=s({}.hasOwnProperty);t.exports=Object.hasOwn||function hasOwn(t,e){return r(n(t),e)}},421:t=>{t.exports={}},397:(t,e,i)=>{var s=i(7751);t.exports=s("document","documentElement")},5917:(t,e,i)=>{var s=i(3724),n=i(9039),r=i(4055);t.exports=!s&&!n((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},7055:(t,e,i)=>{var s=i(9504),n=i(9039),r=i(4576),a=Object,o=s("".split);t.exports=n((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===r(t)?o(t,""):a(t)}:a},3167:(t,e,i)=>{var s=i(4901),n=i(34),r=i(2967);t.exports=function(t,e,i){var a,o;r&&s(a=e.constructor)&&a!==i&&n(o=a.prototype)&&o!==i.prototype&&r(t,o);return t}},3706:(t,e,i)=>{var s=i(9504),n=i(4901),r=i(7629),a=s(Function.toString);n(r.inspectSource)||(r.inspectSource=function(t){return a(t)});t.exports=r.inspectSource},1181:(t,e,i)=>{var s,n,r,a=i(8622),o=i(4475),l=i(34),h=i(6699),d=i(9297),c=i(7629),u=i(6119),p=i(421),g="Object already initialized",f=o.TypeError,m=o.WeakMap;if(a||c.state){var b=c.state||(c.state=new m);b.get=b.get;b.has=b.has;b.set=b.set;s=function(t,e){if(b.has(t))throw new f(g);e.facade=t;b.set(t,e);return e};n=function(t){return b.get(t)||{}};r=function(t){return b.has(t)}}else{var v=u("state");p[v]=!0;s=function(t,e){if(d(t,v))throw new f(g);e.facade=t;h(t,v,e);return e};n=function(t){return d(t,v)?t[v]:{}};r=function(t){return d(t,v)}}t.exports={set:s,get:n,has:r,enforce:function(t){return r(t)?n(t):s(t,{})},getterFor:function(t){return function(e){var i;if(!l(e)||(i=n(e)).type!==t)throw new f("Incompatible receiver, "+t+" required");return i}}}},4209:(t,e,i)=>{var s=i(8227),n=i(6269),r=s("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||a[r]===t)}},4376:(t,e,i)=>{var s=i(4576);t.exports=Array.isArray||function isArray(t){return"Array"===s(t)}},1108:(t,e,i)=>{var s=i(6955);t.exports=function(t){var e=s(t);return"BigInt64Array"===e||"BigUint64Array"===e}},4901:t=>{var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},2796:(t,e,i)=>{var s=i(9039),n=i(4901),r=/#|\.prototype\./,isForced=function(t,e){var i=o[a(t)];return i===h||i!==l&&(n(e)?s(e):!!e)},a=isForced.normalize=function(t){return String(t).replace(r,".").toLowerCase()},o=isForced.data={},l=isForced.NATIVE="N",h=isForced.POLYFILL="P";t.exports=isForced},4117:t=>{t.exports=function(t){return null==t}},34:(t,e,i)=>{var s=i(4901);t.exports=function(t){return"object"==typeof t?null!==t:s(t)}},3925:(t,e,i)=>{var s=i(34);t.exports=function(t){return s(t)||null===t}},6395:t=>{t.exports=!1},757:(t,e,i)=>{var s=i(7751),n=i(4901),r=i(1625),a=i(7040),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=s("Symbol");return n(e)&&r(e.prototype,o(t))}},507:(t,e,i)=>{var s=i(9565);t.exports=function(t,e,i){for(var n,r,a=i?t:t.iterator,o=t.next;!(n=s(o,a)).done;)if(void 0!==(r=e(n.value)))return r}},2652:(t,e,i)=>{var s=i(6080),n=i(9565),r=i(8551),a=i(6823),o=i(4209),l=i(6198),h=i(1625),d=i(81),c=i(851),u=i(9539),p=TypeError,Result=function(t,e){this.stopped=t;this.result=e},g=Result.prototype;t.exports=function(t,e,i){var f,m,b,v,y,A,E,w=i&&i.that,x=!(!i||!i.AS_ENTRIES),_=!(!i||!i.IS_RECORD),S=!(!i||!i.IS_ITERATOR),T=!(!i||!i.INTERRUPTED),C=s(e,w),stop=function(t){f&&u(f,"normal",t);return new Result(!0,t)},callFn=function(t){if(x){r(t);return T?C(t[0],t[1],stop):C(t[0],t[1])}return T?C(t,stop):C(t)};if(_)f=t.iterator;else if(S)f=t;else{if(!(m=c(t)))throw new p(a(t)+" is not iterable");if(o(m)){for(b=0,v=l(t);v>b;b++)if((y=callFn(t[b]))&&h(g,y))return y;return new Result(!1)}f=d(t,m)}A=_?t.next:f.next;for(;!(E=n(A,f)).done;){try{y=callFn(E.value)}catch(t){u(f,"throw",t)}if("object"==typeof y&&y&&h(g,y))return y}return new Result(!1)}},9539:(t,e,i)=>{var s=i(9565),n=i(8551),r=i(5966);t.exports=function(t,e,i){var a,o;n(t);try{if(!(a=r(t,"return"))){if("throw"===e)throw i;return i}a=s(a,t)}catch(t){o=!0;a=t}if("throw"===e)throw i;if(o)throw a;n(a);return i}},9462:(t,e,i)=>{var s=i(9565),n=i(2360),r=i(6699),a=i(6279),o=i(8227),l=i(1181),h=i(5966),d=i(7657).IteratorPrototype,c=i(2529),u=i(9539),p=o("toStringTag"),g="IteratorHelper",f="WrapForValidIterator",m=l.set,createIteratorProxyPrototype=function(t){var e=l.getterFor(t?f:g);return a(n(d),{next:function next(){var i=e(this);if(t)return i.nextHandler();try{var s=i.done?void 0:i.nextHandler();return c(s,i.done)}catch(t){i.done=!0;throw t}},return:function(){var i=e(this),n=i.iterator;i.done=!0;if(t){var r=h(n,"return");return r?s(r,n):c(void 0,!0)}if(i.inner)try{u(i.inner.iterator,"normal")}catch(t){return u(n,"throw",t)}u(n,"normal");return c(void 0,!0)}})},b=createIteratorProxyPrototype(!0),v=createIteratorProxyPrototype(!1);r(v,p,"Iterator Helper");t.exports=function(t,e){var i=function Iterator(i,s){if(s){s.iterator=i.iterator;s.next=i.next}else s=i;s.type=e?f:g;s.nextHandler=t;s.counter=0;s.done=!1;m(this,s)};i.prototype=e?b:v;return i}},713:(t,e,i)=>{var s=i(9565),n=i(9306),r=i(8551),a=i(1767),o=i(9462),l=i(6319),h=o((function(){var t=this.iterator,e=r(s(this.next,t));if(!(this.done=!!e.done))return l(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function map(t){r(this);n(t);return new h(a(this),{mapper:t})}},7657:(t,e,i)=>{var s,n,r,a=i(9039),o=i(4901),l=i(34),h=i(2360),d=i(2787),c=i(6840),u=i(8227),p=i(6395),g=u("iterator"),f=!1;[].keys&&("next"in(r=[].keys())?(n=d(d(r)))!==Object.prototype&&(s=n):f=!0);!l(s)||a((function(){var t={};return s[g].call(t)!==t}))?s={}:p&&(s=h(s));o(s[g])||c(s,g,(function(){return this}));t.exports={IteratorPrototype:s,BUGGY_SAFARI_ITERATORS:f}},6269:t=>{t.exports={}},6198:(t,e,i)=>{var s=i(8014);t.exports=function(t){return s(t.length)}},283:(t,e,i)=>{var s=i(9504),n=i(9039),r=i(4901),a=i(9297),o=i(3724),l=i(350).CONFIGURABLE,h=i(3706),d=i(1181),c=d.enforce,u=d.get,p=String,g=Object.defineProperty,f=s("".slice),m=s("".replace),b=s([].join),v=o&&!n((function(){return 8!==g((function(){}),"length",{value:8}).length})),y=String(String).split("String"),A=t.exports=function(t,e,i){"Symbol("===f(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]");i&&i.getter&&(e="get "+e);i&&i.setter&&(e="set "+e);(!a(t,"name")||l&&t.name!==e)&&(o?g(t,"name",{value:e,configurable:!0}):t.name=e);v&&i&&a(i,"arity")&&t.length!==i.arity&&g(t,"length",{value:i.arity});try{i&&a(i,"constructor")&&i.constructor?o&&g(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var s=c(t);a(s,"source")||(s.source=b(y,"string"==typeof e?e:""));return t};Function.prototype.toString=A((function toString(){return r(this)&&u(this).source||h(this)}),"toString")},741:t=>{var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function trunc(t){var s=+t;return(s>0?i:e)(s)}},6043:(t,e,i)=>{var s=i(9306),n=TypeError,PromiseCapability=function(t){var e,i;this.promise=new t((function(t,s){if(void 0!==e||void 0!==i)throw new n("Bad Promise constructor");e=t;i=s}));this.resolve=s(e);this.reject=s(i)};t.exports.f=function(t){return new PromiseCapability(t)}},2603:(t,e,i)=>{var s=i(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:s(t)}},2360:(t,e,i)=>{var s,n=i(8551),r=i(6801),a=i(8727),o=i(421),l=i(397),h=i(4055),d=i(6119),c="prototype",u="script",p=d("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<"+u+">"+t+"</"+u+">"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag(""));t.close();var e=t.parentWindow.Object;t=null;return e},NullProtoObject=function(){try{s=new ActiveXObject("htmlfile")}catch(t){}NullProtoObject="undefined"!=typeof document?document.domain&&s?NullProtoObjectViaActiveX(s):function(){var t,e=h("iframe"),i="java"+u+":";e.style.display="none";l.appendChild(e);e.src=String(i);(t=e.contentWindow.document).open();t.write(scriptTag("document.F=Object"));t.close();return t.F}():NullProtoObjectViaActiveX(s);for(var t=a.length;t--;)delete NullProtoObject[c][a[t]];return NullProtoObject()};o[p]=!0;t.exports=Object.create||function create(t,e){var i;if(null!==t){EmptyConstructor[c]=n(t);i=new EmptyConstructor;EmptyConstructor[c]=null;i[p]=t}else i=NullProtoObject();return void 0===e?i:r.f(i,e)}},6801:(t,e,i)=>{var s=i(3724),n=i(8686),r=i(4913),a=i(8551),o=i(5397),l=i(1072);e.f=s&&!n?Object.defineProperties:function defineProperties(t,e){a(t);for(var i,s=o(e),n=l(e),h=n.length,d=0;h>d;)r.f(t,i=n[d++],s[i]);return t}},4913:(t,e,i)=>{var s=i(3724),n=i(5917),r=i(8686),a=i(8551),o=i(6969),l=TypeError,h=Object.defineProperty,d=Object.getOwnPropertyDescriptor,c="enumerable",u="configurable",p="writable";e.f=s?r?function defineProperty(t,e,i){a(t);e=o(e);a(i);if("function"==typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var s=d(t,e);if(s&&s[p]){t[e]=i.value;i={configurable:u in i?i[u]:s[u],enumerable:c in i?i[c]:s[c],writable:!1}}}return h(t,e,i)}:h:function defineProperty(t,e,i){a(t);e=o(e);a(i);if(n)try{return h(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new l("Accessors not supported");"value"in i&&(t[e]=i.value);return t}},7347:(t,e,i)=>{var s=i(3724),n=i(9565),r=i(8773),a=i(6980),o=i(5397),l=i(6969),h=i(9297),d=i(5917),c=Object.getOwnPropertyDescriptor;e.f=s?c:function getOwnPropertyDescriptor(t,e){t=o(t);e=l(e);if(d)try{return c(t,e)}catch(t){}if(h(t,e))return a(!n(r.f,t,e),t[e])}},8480:(t,e,i)=>{var s=i(1828),n=i(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function getOwnPropertyNames(t){return s(t,n)}},3717:(t,e)=>{e.f=Object.getOwnPropertySymbols},2787:(t,e,i)=>{var s=i(9297),n=i(4901),r=i(8981),a=i(6119),o=i(2211),l=a("IE_PROTO"),h=Object,d=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=r(t);if(s(e,l))return e[l];var i=e.constructor;return n(i)&&e instanceof i?i.prototype:e instanceof h?d:null}},1625:(t,e,i)=>{var s=i(9504);t.exports=s({}.isPrototypeOf)},1828:(t,e,i)=>{var s=i(9504),n=i(9297),r=i(5397),a=i(9617).indexOf,o=i(421),l=s([].push);t.exports=function(t,e){var i,s=r(t),h=0,d=[];for(i in s)!n(o,i)&&n(s,i)&&l(d,i);for(;e.length>h;)n(s,i=e[h++])&&(~a(d,i)||l(d,i));return d}},1072:(t,e,i)=>{var s=i(1828),n=i(8727);t.exports=Object.keys||function keys(t){return s(t,n)}},8773:(t,e)=>{var i={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,n=s&&!i.call({1:2},1);e.f=n?function propertyIsEnumerable(t){var e=s(this,t);return!!e&&e.enumerable}:i},2967:(t,e,i)=>{var s=i(6706),n=i(34),r=i(7750),a=i(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=s(Object.prototype,"__proto__","set"))(i,[]);e=i instanceof Array}catch(t){}return function setPrototypeOf(i,s){r(i);a(s);if(!n(i))return i;e?t(i,s):i.__proto__=s;return i}}():void 0)},4270:(t,e,i)=>{var s=i(9565),n=i(4901),r=i(34),a=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&n(i=t.toString)&&!r(o=s(i,t)))return o;if(n(i=t.valueOf)&&!r(o=s(i,t)))return o;if("string"!==e&&n(i=t.toString)&&!r(o=s(i,t)))return o;throw new a("Can't convert object to primitive value")}},5031:(t,e,i)=>{var s=i(7751),n=i(9504),r=i(8480),a=i(3717),o=i(8551),l=n([].concat);t.exports=s("Reflect","ownKeys")||function ownKeys(t){var e=r.f(o(t)),i=a.f;return i?l(e,i(t)):e}},8235:(t,e,i)=>{var s=i(9504),n=i(9297),r=SyntaxError,a=parseInt,o=String.fromCharCode,l=s("".charAt),h=s("".slice),d=s(/./.exec),c={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;t.exports=function(t,e){for(var i=!0,s="";e<t.length;){var g=l(t,e);if("\\"===g){var f=h(t,e,e+2);if(n(c,f)){s+=c[f];e+=2}else{if("\\u"!==f)throw new r('Unknown escape sequence: "'+f+'"');var m=h(t,e+=2,e+4);if(!d(u,m))throw new r("Bad Unicode escape at: "+e);s+=o(a(m,16));e+=4}}else{if('"'===g){i=!1;e++;break}if(d(p,g))throw new r("Bad control character in string literal at: "+e);s+=g;e++}}if(i)throw new r("Unterminated string at: "+e);return{value:s,end:e}}},7750:(t,e,i)=>{var s=i(4117),n=TypeError;t.exports=function(t){if(s(t))throw new n("Can't call method on "+t);return t}},9286:(t,e,i)=>{var s=i(4402),n=i(8469),r=s.Set,a=s.add;t.exports=function(t){var e=new r;n(t,(function(t){a(e,t)}));return e}},3440:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(9286),a=i(5170),o=i(3789),l=i(8469),h=i(507),d=n.has,c=n.remove;t.exports=function difference(t){var e=s(this),i=o(t),n=r(e);a(e)<=i.size?l(e,(function(t){i.includes(t)&&c(n,t)})):h(i.getIterator(),(function(t){d(e,t)&&c(n,t)}));return n}},4402:(t,e,i)=>{var s=i(9504),n=Set.prototype;t.exports={Set,add:s(n.add),has:s(n.has),remove:s(n.delete),proto:n}},8750:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(5170),a=i(3789),o=i(8469),l=i(507),h=n.Set,d=n.add,c=n.has;t.exports=function intersection(t){var e=s(this),i=a(t),n=new h;r(e)>i.size?l(i.getIterator(),(function(t){c(e,t)&&d(n,t)})):o(e,(function(t){i.includes(t)&&d(n,t)}));return n}},4449:(t,e,i)=>{var s=i(7080),n=i(4402).has,r=i(5170),a=i(3789),o=i(8469),l=i(507),h=i(9539);t.exports=function isDisjointFrom(t){var e=s(this),i=a(t);if(r(e)<=i.size)return!1!==o(e,(function(t){if(i.includes(t))return!1}),!0);var d=i.getIterator();return!1!==l(d,(function(t){if(n(e,t))return h(d,"normal",!1)}))}},3838:(t,e,i)=>{var s=i(7080),n=i(5170),r=i(8469),a=i(3789);t.exports=function isSubsetOf(t){var e=s(this),i=a(t);return!(n(e)>i.size)&&!1!==r(e,(function(t){if(!i.includes(t))return!1}),!0)}},8527:(t,e,i)=>{var s=i(7080),n=i(4402).has,r=i(5170),a=i(3789),o=i(507),l=i(9539);t.exports=function isSupersetOf(t){var e=s(this),i=a(t);if(r(e)<i.size)return!1;var h=i.getIterator();return!1!==o(h,(function(t){if(!n(e,t))return l(h,"normal",!1)}))}},8469:(t,e,i)=>{var s=i(9504),n=i(507),r=i(4402),a=r.Set,o=r.proto,l=s(o.forEach),h=s(o.keys),d=h(new a).next;t.exports=function(t,e,i){return i?n({iterator:h(t),next:d},e):l(t,e)}},4916:(t,e,i)=>{var s=i(7751),createSetLike=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};t.exports=function(t){var e=s("Set");try{(new e)[t](createSetLike(0));try{(new e)[t](createSetLike(-1));return!1}catch(t){return!0}}catch(t){return!1}}},5170:(t,e,i)=>{var s=i(6706),n=i(4402);t.exports=s(n.proto,"size","get")||function(t){return t.size}},3650:(t,e,i)=>{var s=i(7080),n=i(4402),r=i(9286),a=i(3789),o=i(507),l=n.add,h=n.has,d=n.remove;t.exports=function symmetricDifference(t){var e=s(this),i=a(t).getIterator(),n=r(e);o(i,(function(t){h(e,t)?d(n,t):l(n,t)}));return n}},4204:(t,e,i)=>{var s=i(7080),n=i(4402).add,r=i(9286),a=i(3789),o=i(507);t.exports=function union(t){var e=s(this),i=a(t).getIterator(),l=r(e);o(i,(function(t){n(l,t)}));return l}},6119:(t,e,i)=>{var s=i(5745),n=i(3392),r=s("keys");t.exports=function(t){return r[t]||(r[t]=n(t))}},7629:(t,e,i)=>{var s=i(6395),n=i(4475),r=i(9433),a="__core-js_shared__",o=t.exports=n[a]||r(a,{});(o.versions||(o.versions=[])).push({version:"3.37.0",mode:s?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:(t,e,i)=>{var s=i(7629);t.exports=function(t,e){return s[t]||(s[t]=e||{})}},1548:(t,e,i)=>{var s=i(4475),n=i(9039),r=i(7388),a=i(7290),o=i(516),l=i(9088),h=s.structuredClone;t.exports=!!h&&!n((function(){if(o&&r>92||l&&r>94||a&&r>97)return!1;var t=new ArrayBuffer(8),e=h(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},4495:(t,e,i)=>{var s=i(7388),n=i(9039),r=i(4475).String;t.exports=!!Object.getOwnPropertySymbols&&!n((function(){var t=Symbol("symbol detection");return!r(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&s&&s<41}))},5610:(t,e,i)=>{var s=i(1291),n=Math.max,r=Math.min;t.exports=function(t,e){var i=s(t);return i<0?n(i+e,0):r(i,e)}},5854:(t,e,i)=>{var s=i(2777),n=TypeError;t.exports=function(t){var e=s(t,"number");if("number"==typeof e)throw new n("Can't convert number to bigint");return BigInt(e)}},7696:(t,e,i)=>{var s=i(1291),n=i(8014),r=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=s(t),i=n(e);if(e!==i)throw new r("Wrong length or index");return i}},5397:(t,e,i)=>{var s=i(7055),n=i(7750);t.exports=function(t){return s(n(t))}},1291:(t,e,i)=>{var s=i(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:s(e)}},8014:(t,e,i)=>{var s=i(1291),n=Math.min;t.exports=function(t){var e=s(t);return e>0?n(e,9007199254740991):0}},8981:(t,e,i)=>{var s=i(7750),n=Object;t.exports=function(t){return n(s(t))}},2777:(t,e,i)=>{var s=i(9565),n=i(34),r=i(757),a=i(5966),o=i(4270),l=i(8227),h=TypeError,d=l("toPrimitive");t.exports=function(t,e){if(!n(t)||r(t))return t;var i,l=a(t,d);if(l){void 0===e&&(e="default");i=s(l,t,e);if(!n(i)||r(i))return i;throw new h("Can't convert object to primitive value")}void 0===e&&(e="number");return o(t,e)}},6969:(t,e,i)=>{var s=i(2777),n=i(757);t.exports=function(t){var e=s(t,"string");return n(e)?e:e+""}},2140:(t,e,i)=>{var s={};s[i(8227)("toStringTag")]="z";t.exports="[object z]"===String(s)},655:(t,e,i)=>{var s=i(6955),n=String;t.exports=function(t){if("Symbol"===s(t))throw new TypeError("Cannot convert a Symbol value to a string");return n(t)}},9714:(t,e,i)=>{var s=i(9088);t.exports=function(t){try{if(s)return Function('return require("'+t+'")')()}catch(t){}}},6823:t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},3392:(t,e,i)=>{var s=i(9504),n=0,r=Math.random(),a=s(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++n+r,36)}},7040:(t,e,i)=>{var s=i(4495);t.exports=s&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:(t,e,i)=>{var s=i(3724),n=i(9039);t.exports=s&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:t=>{var e=TypeError;t.exports=function(t,i){if(t<i)throw new e("Not enough arguments");return t}},8622:(t,e,i)=>{var s=i(4475),n=i(4901),r=s.WeakMap;t.exports=n(r)&&/native code/.test(String(r))},8227:(t,e,i)=>{var s=i(4475),n=i(5745),r=i(9297),a=i(3392),o=i(4495),l=i(7040),h=s.Symbol,d=n("wks"),c=l?h.for||h:h&&h.withoutSetter||a;t.exports=function(t){r(d,t)||(d[t]=o&&r(h,t)?h[t]:c("Symbol."+t));return d[t]}},6573:(t,e,i)=>{var s=i(3724),n=i(2106),r=i(3238),a=ArrayBuffer.prototype;s&&!("detached"in a)&&n(a,"detached",{configurable:!0,get:function detached(){return r(this)}})},7936:(t,e,i)=>{var s=i(6518),n=i(5636);n&&s({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return n(this,arguments.length?arguments[0]:void 0,!1)}})},8100:(t,e,i)=>{var s=i(6518),n=i(5636);n&&s({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return n(this,arguments.length?arguments[0]:void 0,!0)}})},4114:(t,e,i)=>{var s=i(6518),n=i(8981),r=i(6198),a=i(4527),o=i(6837);s({target:"Array",proto:!0,arity:1,forced:i(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function push(t){var e=n(this),i=r(e),s=arguments.length;o(i+s);for(var l=0;l<s;l++){e[i]=arguments[l];i++}a(e,i);return i}})},4628:(t,e,i)=>{var s=i(6518),n=i(6043);s({target:"Promise",stat:!0},{withResolvers:function withResolvers(){var t=n.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}})},7642:(t,e,i)=>{var s=i(6518),n=i(3440);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("difference")},{difference:n})},8004:(t,e,i)=>{var s=i(6518),n=i(9039),r=i(8750);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("intersection")||n((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:r})},3853:(t,e,i)=>{var s=i(6518),n=i(4449);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isDisjointFrom")},{isDisjointFrom:n})},5876:(t,e,i)=>{var s=i(6518),n=i(3838);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSubsetOf")},{isSubsetOf:n})},2475:(t,e,i)=>{var s=i(6518),n=i(8527);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("isSupersetOf")},{isSupersetOf:n})},5024:(t,e,i)=>{var s=i(6518),n=i(3650);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("symmetricDifference")},{symmetricDifference:n})},1698:(t,e,i)=>{var s=i(6518),n=i(4204);s({target:"Set",proto:!0,real:!0,forced:!i(4916)("union")},{union:n})},7467:(t,e,i)=>{var s=i(7628),n=i(4644),r=n.aTypedArray,a=n.exportTypedArrayMethod,o=n.getTypedArrayConstructor;a("toReversed",(function toReversed(){return s(r(this),o(this))}))},4732:(t,e,i)=>{var s=i(4644),n=i(9504),r=i(9306),a=i(5370),o=s.aTypedArray,l=s.getTypedArrayConstructor,h=s.exportTypedArrayMethod,d=n(s.TypedArrayPrototype.sort);h("toSorted",(function toSorted(t){void 0!==t&&r(t);var e=o(this),i=a(l(e),e);return d(i,t)}))},9577:(t,e,i)=>{var s=i(9928),n=i(4644),r=i(1108),a=i(1291),o=i(5854),l=n.aTypedArray,h=n.getTypedArrayConstructor,d=n.exportTypedArrayMethod,c=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();d("with",{with:function(t,e){var i=l(this),n=a(t),d=r(i)?o(e):+e;return s(i,h(i),n,d)}}.with,!c)},8992:(t,e,i)=>{var s=i(6518),n=i(4475),r=i(679),a=i(8551),o=i(4901),l=i(2787),h=i(2106),d=i(4659),c=i(9039),u=i(9297),p=i(8227),g=i(7657).IteratorPrototype,f=i(3724),m=i(6395),b="constructor",v="Iterator",y=p("toStringTag"),A=TypeError,E=n[v],w=m||!o(E)||E.prototype!==g||!c((function(){E({})})),x=function Iterator(){r(this,g);if(l(this)===g)throw new A("Abstract class Iterator not directly constructable")},defineIteratorPrototypeAccessor=function(t,e){f?h(g,t,{configurable:!0,get:function(){return e},set:function(e){a(this);if(this===g)throw new A("You can't redefine this property");u(this,t)?this[t]=e:d(this,t,e)}}):g[t]=e};u(g,y)||defineIteratorPrototypeAccessor(y,v);!w&&u(g,b)&&g[b]!==Object||defineIteratorPrototypeAccessor(b,x);x.prototype=g;s({global:!0,constructor:!0,forced:w},{Iterator:x})},3215:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767);s({target:"Iterator",proto:!0,real:!0},{every:function every(t){a(this);r(t);var e=o(this),i=0;return!n(e,(function(e,s){if(!t(e,i++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},4520:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(9462),h=i(6319),d=i(6395),c=l((function(){for(var t,e,i=this.iterator,s=this.predicate,r=this.next;;){t=a(n(r,i));if(this.done=!!t.done)return;e=t.value;if(h(i,s,[e,this.counter++],!0))return e}}));s({target:"Iterator",proto:!0,real:!0,forced:d},{filter:function filter(t){a(this);r(t);return new c(o(this),{predicate:t})}})},670:(t,e,i)=>{var s=i(6518),n=i(9565),r=i(9306),a=i(8551),o=i(1767),l=i(8646),h=i(9462),d=i(9539),c=i(6395),u=h((function(){for(var t,e,i=this.iterator,s=this.mapper;;){if(e=this.inner)try{if(!(t=a(n(e.next,e.iterator))).done)return t.value;this.inner=null}catch(t){d(i,"throw",t)}t=a(n(this.next,i));if(this.done=!!t.done)return;try{this.inner=l(s(t.value,this.counter++),!1)}catch(t){d(i,"throw",t)}}}));s({target:"Iterator",proto:!0,real:!0,forced:c},{flatMap:function flatMap(t){a(this);r(t);return new u(o(this),{mapper:t,inner:null})}})},1454:(t,e,i)=>{var s=i(6518),n=i(713);s({target:"Iterator",proto:!0,real:!0,forced:i(6395)},{map:n})},7550:(t,e,i)=>{var s=i(6518),n=i(2652),r=i(9306),a=i(8551),o=i(1767);s({target:"Iterator",proto:!0,real:!0},{some:function some(t){a(this);r(t);var e=o(this),i=0;return n(e,(function(e,s){if(t(e,i++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},8335:(t,e,i)=>{var s=i(6518),n=i(3724),r=i(4475),a=i(7751),o=i(9504),l=i(9565),h=i(4901),d=i(34),c=i(4376),u=i(9297),p=i(655),g=i(6198),f=i(4659),m=i(9039),b=i(8235),v=i(4495),y=r.JSON,A=r.Number,E=r.SyntaxError,w=y&&y.parse,x=a("Object","keys"),_=Object.getOwnPropertyDescriptor,S=o("".charAt),T=o("".slice),C=o(/./.exec),M=o([].push),P=/^\d$/,R=/^[1-9]$/,k=/^(?:-|\d)$/,D=/^[\t\n\r ]$/,internalize=function(t,e,i,s){var n,r,a,o,h,p=t[e],f=s&&p===s.value,m=f&&"string"==typeof s.source?{source:s.source}:{};if(d(p)){var b=c(p),v=f?s.nodes:b?[]:{};if(b){n=v.length;a=g(p);for(o=0;o<a;o++)internalizeProperty(p,o,internalize(p,""+o,i,o<n?v[o]:void 0))}else{r=x(p);a=g(r);for(o=0;o<a;o++){h=r[o];internalizeProperty(p,h,internalize(p,h,i,u(v,h)?v[h]:void 0))}}}return l(i,t,e,p,m)},internalizeProperty=function(t,e,i){if(n){var s=_(t,e);if(s&&!s.configurable)return}void 0===i?delete t[e]:f(t,e,i)},Node=function(t,e,i,s){this.value=t;this.end=e;this.source=i;this.nodes=s},Context=function(t,e){this.source=t;this.index=e};Context.prototype={fork:function(t){return new Context(this.source,t)},parse:function(){var t=this.source,e=this.skip(D,this.index),i=this.fork(e),s=S(t,e);if(C(k,s))return i.number();switch(s){case"{":return i.object();case"[":return i.array();case'"':return i.string();case"t":return i.keyword(!0);case"f":return i.keyword(!1);case"n":return i.keyword(null)}throw new E('Unexpected character: "'+s+'" at: '+e)},node:function(t,e,i,s,n){return new Node(e,s,t?null:T(this.source,i,s),n)},object:function(){for(var t=this.source,e=this.index+1,i=!1,s={},n={};e<t.length;){e=this.until(['"',"}"],e);if("}"===S(t,e)&&!i){e++;break}var r=this.fork(e).string(),a=r.value;e=r.end;e=this.until([":"],e)+1;e=this.skip(D,e);r=this.fork(e).parse();f(n,a,r);f(s,a,r.value);e=this.until([",","}"],r.end);var o=S(t,e);if(","===o){i=!0;e++}else if("}"===o){e++;break}}return this.node(1,s,this.index,e,n)},array:function(){for(var t=this.source,e=this.index+1,i=!1,s=[],n=[];e<t.length;){e=this.skip(D,e);if("]"===S(t,e)&&!i){e++;break}var r=this.fork(e).parse();M(n,r);M(s,r.value);e=this.until([",","]"],r.end);if(","===S(t,e)){i=!0;e++}else if("]"===S(t,e)){e++;break}}return this.node(1,s,this.index,e,n)},string:function(){var t=this.index,e=b(this.source,this.index+1);return this.node(0,e.value,t,e.end)},number:function(){var t=this.source,e=this.index,i=e;"-"===S(t,i)&&i++;if("0"===S(t,i))i++;else{if(!C(R,S(t,i)))throw new E("Failed to parse number at: "+i);i=this.skip(P,++i)}"."===S(t,i)&&(i=this.skip(P,++i));if("e"===S(t,i)||"E"===S(t,i)){i++;"+"!==S(t,i)&&"-"!==S(t,i)||i++;if(i===(i=this.skip(P,i)))throw new E("Failed to parse number's exponent value at: "+i)}return this.node(0,A(T(t,e,i)),e,i)},keyword:function(t){var e=""+t,i=this.index,s=i+e.length;if(T(this.source,i,s)!==e)throw new E("Failed to parse value at: "+i);return this.node(0,t,i,s)},skip:function(t,e){for(var i=this.source;e<i.length&&C(t,S(i,e));e++);return e},until:function(t,e){e=this.skip(D,e);for(var i=S(this.source,e),s=0;s<t.length;s++)if(t[s]===i)return e;throw new E('Unexpected character: "'+i+'" at: '+e)}};var I=m((function(){var t,e="9007199254740993";w(e,(function(e,i,s){t=s.source}));return t!==e})),L=v&&!m((function(){return 1/w("-0 \t")!=-1/0}));s({target:"JSON",stat:!0,forced:I},{parse:function parse(t,e){return L&&!h(e)?w(t):function(t,e){t=p(t);var i=new Context(t,0,""),s=i.parse(),n=s.value,r=i.skip(D,s.end);if(r<t.length)throw new E('Unexpected extra character: "'+S(t,r)+'" after the parsed data at: '+r);return h(e)?internalize({"":n},"",e,s):n}(t,e)}})},3375:(t,e,i)=>{i(7642)},9225:(t,e,i)=>{i(8004)},3972:(t,e,i)=>{i(3853)},9209:(t,e,i)=>{i(5876)},5714:(t,e,i)=>{i(2475)},7561:(t,e,i)=>{i(5024)},6197:(t,e,i)=>{i(1698)},4979:(t,e,i)=>{var s=i(6518),n=i(4475),r=i(7751),a=i(6980),o=i(4913).f,l=i(9297),h=i(679),d=i(3167),c=i(2603),u=i(5002),p=i(6193),g=i(3724),f=i(6395),m="DOMException",b=r("Error"),v=r(m),y=function DOMException(){h(this,A);var t=arguments.length,e=c(t<1?void 0:arguments[0]),i=c(t<2?void 0:arguments[1],"Error"),s=new v(e,i),n=new b(e);n.name=m;o(s,"stack",a(1,p(n.stack,1)));d(s,this,y);return s},A=y.prototype=v.prototype,E="stack"in new b(m),w="stack"in new v(1,2),x=v&&g&&Object.getOwnPropertyDescriptor(n,m),_=!(!x||x.writable&&x.configurable),S=E&&!_&&!w;s({global:!0,constructor:!0,forced:f||S},{DOMException:S?y:v});var T=r(m),C=T.prototype;if(C.constructor!==T){f||o(C,"constructor",a(1,T));for(var M in u)if(l(u,M)){var P=u[M],R=P.s;l(T,R)||o(T,R,a(6,P.c))}}},4603:(t,e,i)=>{var s=i(6840),n=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=n(l.append),d=n(l.delete),c=n(l.forEach),u=n([].push),p=new o("a=1&a=2&b=3");p.delete("a",1);p.delete("b",void 0);p+""!="a=2"&&s(l,"delete",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return d(this,t);var s=[];c(this,(function(t,e){u(s,{key:e,value:t})}));a(e,1);for(var n,o=r(t),l=r(i),p=0,g=0,f=!1,m=s.length;p<m;){n=s[p++];if(f||n.key===o){f=!0;d(this,n.key)}else g++}for(;g<m;)(n=s[g++]).key===o&&n.value===l||h(this,n.key,n.value)}),{enumerable:!0,unsafe:!0})},7566:(t,e,i)=>{var s=i(6840),n=i(9504),r=i(655),a=i(2812),o=URLSearchParams,l=o.prototype,h=n(l.getAll),d=n(l.has),c=new o("a=1");!c.has("a",2)&&c.has("a",void 0)||s(l,"has",(function has(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return d(this,t);var s=h(this,t);a(e,1);for(var n=r(i),o=0;o<s.length;)if(s[o++]===n)return!0;return!1}),{enumerable:!0,unsafe:!0})},8721:(t,e,i)=>{var s=i(3724),n=i(9504),r=i(2106),a=URLSearchParams.prototype,o=n(a.forEach);s&&!("size"in a)&&r(a,"size",{get:function size(){var t=0;o(this,(function(){t++}));return t},configurable:!0,enumerable:!0})},6976:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{AnnotationLayer:()=>AnnotationLayer,FreeTextAnnotationElement:()=>FreeTextAnnotationElement,InkAnnotationElement:()=>InkAnnotationElement,StampAnnotationElement:()=>StampAnnotationElement});e(4114),e(8992),e(670),e(1454),e(3375),e(9225),e(3972),e(9209),e(5714),e(7561),e(6197);var i=e(4292),s=e(5419),n=e(9792);function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function scaleAndClamp(t){return Math.max(0,Math.min(255,255*t))}class ColorConverters{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=scaleAndClamp(t),t,t]}static G_HTML([t]){const e=makeColorComp(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(scaleAndClamp)}static RGB_HTML(t){return`#${t.map(makeColorComp).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[scaleAndClamp(1-Math.min(1,t+s)),scaleAndClamp(1-Math.min(1,i+s)),scaleAndClamp(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,r=1-i;return["CMYK",s,n,r,Math.min(s,n,r)]}}var r=e(8284);const a=1e3,o=new WeakSet;function getRectDims(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case i.AnnotationType.LINK:return new LinkAnnotationElement(t);case i.AnnotationType.TEXT:return new TextAnnotationElement(t);case i.AnnotationType.WIDGET:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t);case"Sig":return new SignatureWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case i.AnnotationType.POPUP:return new PopupAnnotationElement(t);case i.AnnotationType.FREETEXT:return new FreeTextAnnotationElement(t);case i.AnnotationType.LINE:return new LineAnnotationElement(t);case i.AnnotationType.SQUARE:return new SquareAnnotationElement(t);case i.AnnotationType.CIRCLE:return new CircleAnnotationElement(t);case i.AnnotationType.POLYLINE:return new PolylineAnnotationElement(t);case i.AnnotationType.CARET:return new CaretAnnotationElement(t);case i.AnnotationType.INK:return new InkAnnotationElement(t);case i.AnnotationType.POLYGON:return new PolygonAnnotationElement(t);case i.AnnotationType.HIGHLIGHT:return new HighlightAnnotationElement(t);case i.AnnotationType.UNDERLINE:return new UnderlineAnnotationElement(t);case i.AnnotationType.SQUIGGLY:return new SquigglyAnnotationElement(t);case i.AnnotationType.STRIKEOUT:return new StrikeOutAnnotationElement(t);case i.AnnotationType.STAMP:return new StampAnnotationElement(t);case i.AnnotationType.FILEATTACHMENT:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{#t=null;#e=!1;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this.parent=t.parent;e&&(this.container=this._createContainer(i));s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get hasPopupData(){return AnnotationElement._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#t||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#i(e)}resetEdited(){if(this.#t){this.#i(this.#t.rect);this.#t=null}}#i(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:r,pageX:a,pageY:o}}}}=this;i?.splice(0,4,...t);const{width:l,height:h}=getRectDims(t);e.left=100*(t[0]-a)/n+"%";e.top=100*(r-t[3]+o)/r+"%";if(0===s){e.width=100*l/n+"%";e.height=100*h/r+"%"}else this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:s,viewport:n}}=this,r=document.createElement("section");r.setAttribute("data-annotation-id",e.id);this instanceof WidgetAnnotationElement||(r.tabIndex=a);const{style:o}=r;o.zIndex=this.parent.zIndex++;e.popupRef&&r.setAttribute("aria-haspopup","dialog");e.alternativeText&&(r.title=e.alternativeText);e.noRotate&&r.classList.add("norotate");if(!e.rect||this instanceof PopupAnnotationElement){const{rotation:t}=e;e.hasOwnCanvas||0===t||this.setRotation(t,r);return r}const{width:l,height:h}=getRectDims(e.rect);if(!t&&e.borderStyle.width>0){o.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,s=e.borderStyle.verticalCornerRadius;if(t>0||s>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${s}px * var(--scale-factor))`;o.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${l}px * var(--scale-factor)) / calc(${h}px * var(--scale-factor))`;o.borderRadius=t}switch(e.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:o.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:o.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:o.borderBottomStyle="solid"}const n=e.borderColor||null;if(n){this.#e=!0;o.borderColor=i.Util.makeHexColor(0|n[0],0|n[1],0|n[2])}else o.borderWidth=0}const d=i.Util.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:c,pageHeight:u,pageX:p,pageY:g}=n.rawDims;o.left=100*(d[0]-p)/c+"%";o.top=100*(d[1]-g)/u+"%";const{rotation:f}=e;if(e.hasOwnCanvas||0===f){o.width=100*l/c+"%";o.height=100*h/u+"%"}else this.setRotation(f,r);return r}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims,{width:n,height:r}=getRectDims(this.data.rect);let a,o;if(t%180==0){a=100*n/i;o=100*r/s}else{a=100*r/i;o=100*n/s}e.style.width=`${a}%`;e.style.height=`${o}%`;e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,i)=>{const s=i.detail[t],n=s[0],r=s.slice(1);i.target.style[e]=ColorConverters[`${n}_HTML`](r);this.annotationStorage.setValue(this.data.id,{[e]:ColorConverters[`${n}_rgb`](r)})};return(0,i.shadow)(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){const n=t[s]||i[s];n?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const r=i[s];if(r){r({detail:{[s]:n},target:t});delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect;if(1===t.length){const[,{x:r,y:a},{x:o,y:l}]=t[0];if(s===r&&n===a&&e===o&&i===l)return}const{style:r}=this.container;let a;if(this.#e){const{borderColor:t,borderWidth:e}=r;r.borderWidth=0;a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`];this.container.classList.add("hasBorder")}const o=s-e,l=n-i,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer");d.setAttribute("width",0);d.setAttribute("height",0);const c=h.createElement("defs");d.append(c);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p);u.setAttribute("clipPathUnits","objectBoundingBox");c.append(u);for(const[,{x:i,y:s},{x:r,y:d}]of t){const t=h.createElement("rect"),c=(r-e)/o,p=(n-s)/l,g=(i-r)/o,f=(s-d)/l;t.setAttribute("x",c);t.setAttribute("y",p);t.setAttribute("width",g);t.setAttribute("height",f);u.append(t);a?.push(`<rect vector-effect="non-scaling-stroke" x="${c}" y="${p}" width="${g}" height="${f}"/>`)}if(this.#e){a.push("</g></svg>')");r.backgroundImage=a.join("")}this.container.append(d);this.container.style.clipPath=`url(#${p})`}_createPopup(){const{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");const i=new PopupAnnotationElement({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(i.render())}render(){(0,i.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const n=this._fieldObjects[t];if(n)for(const{page:t,id:r,exportValues:a}of n){if(-1===t)continue;if(r===e)continue;const n="string"==typeof a?a:null,l=document.querySelector(`[data-element-id="${r}"]`);!l||o.has(l)?s.push({id:r,exportValue:n,domElement:l}):(0,i.warn)(`_getElementsByName - element not allowed: ${r}`)}return s}for(const i of document.getElementsByName(t)){const{exportValue:t}=i,n=i.getAttribute("data-element-id");n!==e&&(o.has(i)&&s.push({id:n,exportValue:t,domElement:i}))}return s}show(){this.container&&(this.container.hidden=!1);this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0);this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}get _isEditable(){return!1}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}}class LinkAnnotationElement extends AnnotationElement{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;if(t.url){e.addLinkAttributes(i,t.url,t.newWindow);s=!0}else if(t.action){this._bindNamedAction(i,t.action);s=!0}else if(t.attachment){this.#s(i,t.attachment,t.attachmentDest);s=!0}else if(t.setOCGState){this.#n(i,t.setOCGState);s=!0}else if(t.dest){this._bindLink(i,t.dest);s=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(i,t);s=!0}if(t.resetForm){this._bindResetFormAction(i,t.resetForm);s=!0}else if(this.isTooltipOnly&&!s){this._bindLink(i,"");s=!0}}this.container.classList.add("linkAnnotation");s&&this.container.append(i);return this.container}#r(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&this.#r()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};this.#r()}#s(t,e,i=null){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.downloadManager?.openOrDownloadData(e.content,e.filename,i);return!1};this.#r()}#n(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};this.#r()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}});return!1})}t.onclick||(t.onclick=()=>!1);this.#r()}_bindResetFormAction(t,e){const s=t.onclick;s||(t.href=this.linkService.getAnchorUrl(""));this.#r();if(this._fieldObjects)t.onclick=()=>{s?.();const{fields:t,refs:n,include:r}=e,a=[];if(0!==t.length||0!==n.length){const e=new Set(n);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===r&&a.push(i)}else for(const t of Object.values(this._fieldObjects))a.push(...t);const l=this.annotationStorage,h=[];for(const t of a){const{id:e}=t;h.push(e);switch(t.type){case"text":{const i=t.defaultValue||"";l.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;l.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";l.setValue(e,{value:i});break}default:continue}const s=document.querySelector(`[data-element-id="${e}"]`);s&&(o.has(s)?s.dispatchEvent(new Event("resetform")):(0,i.warn)(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:h,name:"ResetForm"}});return!1};else{(0,i.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');s||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.setAttribute("data-l10n-id","pdfjs-text-annotation-type");t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name}));!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){return this.container}showElementAndHideCanvas(t){if(this.data.hasOwnCanvas){"CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0);t.hidden=!1}}_getKeyModifier(t){return i.FeatureTest.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,r]of i)if("Action"===r||this.data.actions?.[r]){"Focus"!==r&&"Blur"!==r||(e||={focused:!1});this._setEventListener(t,e,n,r,s);"Focus"!==r||this.data.actions?.Blur?"Blur"!==r||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":i.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,n=this.data.defaultAppearanceData.fontSize||9,r=t.style;let a;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(i.LINE_FACTOR*n))||1);a=Math.min(n,roundToOneDecimal(e/i.LINE_FACTOR))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(n,roundToOneDecimal(t/i.LINE_FACTOR))}r.fontSize=`calc(${a}px * var(--scale-factor))`;r.color=i.Util.makeHexColor(s[0],s[1],s[2]);null!==this.data.textAlignment&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id)){r.domElement&&(r.domElement[e]=i);n.setValue(r.id,{[s]:i})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&n.length>r&&(n=n.slice(0,r));let l=s.formattedValue||this.data.textContent?.join("\n")||null;l&&this.data.comb&&(l=l.replaceAll(/\s+/g,""));const h={userValue:n,formattedValue:l,lastCommittedValue:null,commitKey:1,focused:!1};if(this.data.multiLine){i=document.createElement("textarea");i.textContent=l??n;this.data.doNotScroll&&(i.style.overflowY="hidden")}else{i=document.createElement("input");i.type="text";i.setAttribute("value",l??n);this.data.doNotScroll&&(i.style.overflowX="hidden")}this.data.hasOwnCanvas&&(i.hidden=!0);o.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;i.name=this.data.fieldName;i.tabIndex=a;this._setRequired(i,this.data.required);r&&(i.maxLength=r);i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value});this.setPropertyOnSiblings(i,"value",s.target.value,"value");h.formattedValue=null}));i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=h.userValue=e;h.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=h;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(h.focused)return;const{target:e}=t;h.userValue&&(e.value=h.userValue);h.lastCommittedValue=e.value;h.commitKey=1;this.data.actions?.Focus||(h.focused=!0)}));i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){h.userValue=i.detail.value??"";t.setValue(e,{value:h.userValue.toString()});i.target.value=h.userValue},formattedValue(i){const{formattedValue:s}=i.detail;h.formattedValue=s;null!=s&&i.target!==document.activeElement&&(i.target.value=s);t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s){n.removeAttribute("maxLength");return}n.setAttribute("maxLength",s);let r=h.userValue;if(r&&!(r.length<=s)){r=r.slice(0,s);n.value=h.userValue=r;t.setValue(e,{value:r});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}})}}};this._dispatchEventFromSandbox(s,i)}));i.addEventListener("keydown",(t=>{h.commitKey=1;let i=-1;"Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(h.commitKey=3):i=2;if(-1===i)return;const{value:s}=t.target;if(h.lastCommittedValue!==s){h.lastCommittedValue=s;h.userValue=s;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const s=blurListener;blurListener=null;i.addEventListener("blur",(t=>{if(!h.focused||!t.relatedTarget)return;this.data.actions?.Blur||(h.focused=!1);const{value:i}=t.target;h.userValue=i;h.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:h.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});s(t)}));this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{h.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:r,selectionEnd:a}=s;let o=r,l=a;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,r).match(/\w*[^\w]*$/);t&&(o-=t[0].length);break}case"deleteWordForward":{const t=n.substring(r).match(/^[^\w]*\w*/);t&&(l+=t[0].length);break}case"deleteContentBackward":r===a&&(o-=1);break;case"deleteContentForward":r===a&&(l+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:o,selEnd:l}})}));this._setEventListeners(i,h,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&i.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/r;i.classList.add("comb");i.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else{i=document.createElement("div");i.textContent=this.data.fieldValue;i.style.verticalAlign="middle";i.style.display="table-cell";this.data.hasOwnCanvas&&(i.hidden=!0)}this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class SignatureWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof s){s="Off"!==s;t.setValue(i,{value:s})}this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");o.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="checkbox";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.setAttribute("exportValue",e.exportValue);n.tabIndex=a;n.addEventListener("change",(s=>{const{name:n,checked:r}=s.target;for(const s of this._getElementsByName(n,i)){const i=r&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i);t.setValue(s.id,{value:i})}t.setValue(i,{value:r})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}));if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s){s=s!==e.buttonValue;t.setValue(i,{value:s})}if(s)for(const s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});const n=document.createElement("input");o.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="radio";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.tabIndex=a;n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:n})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e);t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");o.add(s);s.setAttribute("data-element-id",e);s.disabled=this.data.readOnly;this._setRequired(s,this.data.required);s.name=this.data.fieldName;s.tabIndex=a;let n=this.data.combo&&this.data.options.length>0;if(!this.data.combo){s.size=this.data.options.length;this.data.multiSelect&&(s.multiple=!0)}s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of s.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(i.value.includes(t.exportValue)){e.setAttribute("selected",!0);n=!1}s.append(e)}let r=null;if(n){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);s.prepend(t);r=()=>{t.remove();s.removeEventListener("input",r);r=null};s.addEventListener("input",r)}const getValue=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let l=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){s.addEventListener("updatefromsandbox",(i=>{const n={value(i){r?.();const n=i.detail.value,a=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=a.has(t.value);t.setValue(e,{value:getValue(!0)});l=getValue(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,r=i.detail.remove;n[r].selected=!1;s.remove(r);if(n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(i)});l=getValue(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]});l=getValue(!1)},insert(i){const{index:n,displayValue:r,exportValue:a}=i.detail.insert,o=s.children[n],h=document.createElement("option");h.textContent=r;h.value=a;o?o.before(h):s.append(h);t.setValue(e,{value:getValue(!0),items:getItems(i)});l=getValue(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e;n.value=i;s.append(n)}s.options.length>0&&(s.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(i)});l=getValue(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:getValue(!0)});l=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)}));s.addEventListener("input",(i=>{const s=getValue(!0),n=getValue(!1);t.setValue(e,{value:s});i.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:l,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else s.addEventListener("input",(function(i){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(s);this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:AnnotationElement._hasPopupData(e)});this.elements=i}render(){this.container.classList.add("popupAnnotation");const t=new PopupElement({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements){i.popup=t;e.push(i.data.id);i.addHighlightArea()}this.container.setAttribute("aria-controls",e.map((t=>`${i.AnnotationPrefix}${t}`)).join(","));return this.container}}class PopupElement{#a=this.#o.bind(this);#l=this.#h.bind(this);#d=this.#c.bind(this);#u=this.#p.bind(this);#g=null;#f=null;#m=null;#b=null;#v=null;#y=null;#A=null;#E=!1;#w=null;#x=null;#_=null;#S=null;#T=!1;constructor({container:t,color:e,elements:i,titleObj:n,modificationDate:r,contentsObj:a,richText:o,parent:l,rect:h,parentRect:d,open:c}){this.#f=t;this.#S=n;this.#m=a;this.#_=o;this.#y=l;this.#g=e;this.#x=h;this.#A=d;this.#v=i;this.#b=s.PDFDateString.toDateObject(r);this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger){t.addEventListener("click",this.#u);t.addEventListener("mouseenter",this.#d);t.addEventListener("mouseleave",this.#l);t.classList.add("popupTriggerArea")}for(const t of i)t.container?.addEventListener("keydown",this.#a);this.#f.hidden=!0;c&&this.#p()}render(){if(this.#w)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:n,pageY:a}}}=this.#y,o=this.#w=document.createElement("div");o.className="popup";if(this.#g){const t=o.style.outlineColor=i.Util.makeHexColor(...this.#g);if(CSS.supports("background-color","color-mix(in srgb, red 30%, white)"))o.style.backgroundColor=`color-mix(in srgb, ${t} 30%, white)`;else{const t=.7;o.style.backgroundColor=i.Util.makeHexColor(...this.#g.map((e=>Math.floor(t*(255-e)+e))))}}const l=document.createElement("span");l.className="header";const h=document.createElement("h1");l.append(h);({dir:h.dir,str:h.textContent}=this.#S);o.append(l);if(this.#b){const t=document.createElement("span");t.classList.add("popupDate");t.setAttribute("data-l10n-id","pdfjs-annotation-date-string");t.setAttribute("data-l10n-args",JSON.stringify({date:this.#b.toLocaleDateString(),time:this.#b.toLocaleTimeString()}));l.append(t)}const d=this.#m,c=this.#_;if(!c?.str||d?.str&&d.str!==c.str){const t=this._formatContents(d);o.append(t)}else{r.XfaLayer.render({xfaHtml:c.html,intent:"richText",div:o});o.lastChild.classList.add("richText","popupContent")}let u=!!this.#A,p=u?this.#A:this.#x;for(const t of this.#v)if(!p||null!==i.Util.intersect(t.data.rect,p)){p=t.data.rect;u=!0;break}const g=i.Util.normalizeRect([p[0],t[3]-p[1]+t[1],p[2],t[3]-p[3]+t[1]]),f=u?p[2]-p[0]+5:0,m=g[0]+f,b=g[1],{style:v}=this.#f;v.left=100*(m-n)/e+"%";v.top=100*(b-a)/s+"%";this.#f.append(o)}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent");i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const n=s[t];i.append(document.createTextNode(n));t<e-1&&i.append(document.createElement("br"))}return i}#o(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#E)&&this.#p()}#p(){this.#E=!this.#E;if(this.#E){this.#c();this.#f.addEventListener("click",this.#u);this.#f.addEventListener("keydown",this.#a)}else{this.#h();this.#f.removeEventListener("click",this.#u);this.#f.removeEventListener("keydown",this.#a)}}#c(){this.#w||this.render();if(this.isVisible)this.#E&&this.#f.classList.add("focused");else{this.#f.hidden=!1;this.#f.style.zIndex=parseInt(this.#f.style.zIndex)+1e3}}#h(){this.#f.classList.remove("focused");if(!this.#E&&this.isVisible){this.#f.hidden=!0;this.#f.style.zIndex=parseInt(this.#f.style.zIndex)-1e3}}forceHide(){this.#T=this.isVisible;this.#T&&(this.#f.hidden=!0)}maybeShow(){if(this.#T){this.#T=!1;this.#f.hidden=!1}}get isVisible(){return!1===this.#f.hidden}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.textContent=t.data.textContent;this.textPosition=t.data.textPosition;this.annotationEditorType=i.AnnotationEditorType.FREETEXT}render(){this.container.classList.add("freeTextAnnotation");if(this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent");t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e;t.append(i)}this.container.append(t)}!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}get _isEditable(){return this.data.hasOwnCanvas}}class LineAnnotationElement extends AnnotationElement{#C=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=this.#C=this.svgFactory.createElement("svg:line");n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);n.setAttribute("stroke-width",t.borderStyle.width||1);n.setAttribute("stroke","transparent");n.setAttribute("fill","transparent");s.append(n);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#C}addHighlightArea(){this.container.classList.add("highlightArea")}}class SquareAnnotationElement extends AnnotationElement{#M=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#M=this.svgFactory.createElement("svg:rect");r.setAttribute("x",n/2);r.setAttribute("y",n/2);r.setAttribute("width",e-n);r.setAttribute("height",i-n);r.setAttribute("stroke-width",n||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#M}addHighlightArea(){this.container.classList.add("highlightArea")}}class CircleAnnotationElement extends AnnotationElement{#P=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#P=this.svgFactory.createElement("svg:ellipse");r.setAttribute("cx",e/2);r.setAttribute("cy",i/2);r.setAttribute("rx",e/2-n/2);r.setAttribute("ry",i/2-n/2);r.setAttribute("stroke-width",n||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#P}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolylineAnnotationElement extends AnnotationElement{#R=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0);let n=[];for(const e of t.vertices){const i=e.x-t.rect[0],s=t.rect[3]-e.y;n.push(i+","+s)}n=n.join(" ");const r=this.#R=this.svgFactory.createElement(this.svgElementName);r.setAttribute("points",n);r.setAttribute("stroke-width",t.borderStyle.width||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");s.append(r);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#R}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("caretAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class InkAnnotationElement extends AnnotationElement{#k=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline";this.annotationEditorType=i.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0);for(const e of t.inkLists){let i=[];for(const s of e){const e=s.x-t.rect[0],n=t.rect[3]-s.y;i.push(`${e},${n}`)}i=i.join(" ");const n=this.svgFactory.createElement(this.svgElementName);this.#k.push(n);n.setAttribute("points",i);n.setAttribute("stroke-width",t.borderStyle.width||1);n.setAttribute("stroke","transparent");n.setAttribute("fill","transparent");!t.popupRef&&this.hasPopupData&&this._createPopup();s.append(n)}this.container.append(s);return this.container}getElementsToTriggerPopup(){return this.#k}addHighlightArea(){this.container.classList.add("highlightArea")}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("highlightAnnotation");return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("underlineAnnotation");return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("squigglyAnnotation");return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("strikeoutAnnotation");return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("stampAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{#F=null;constructor(t){super(t,{isRenderable:!0});const{filename:e,content:i}=this.data.file;this.filename=(0,s.getFilenameFromUrl)(e,!0);this.content=i;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,filename:e,content:i})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let s;if(e.hasAppearance||0===e.fillAlpha)s=document.createElement("div");else{s=document.createElement("img");s.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`;e.fillAlpha&&e.fillAlpha<1&&(s.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)}s.addEventListener("dblclick",this.#D.bind(this));this.#F=s;const{isMac:n}=i.FeatureTest.platform;t.addEventListener("keydown",(t=>{"Enter"===t.key&&(n?t.metaKey:t.ctrlKey)&&this.#D()}));!e.popupRef&&this.hasPopupData?this._createPopup():s.classList.add("popupTriggerArea");t.append(s);return t}getElementsToTriggerPopup(){return this.#F}addHighlightArea(){this.container.classList.add("highlightArea")}#D(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class AnnotationLayer{#I=null;#L=null;#O=new Map;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:r}){this.div=t;this.#I=e;this.#L=i;this.page=n;this.viewport=r;this.zIndex=0;this._annotationEditorUIManager=s}#N(t,e){const s=t.firstChild||t;s.id=`${i.AnnotationPrefix}${e}`;this.div.append(t);this.#I?.moveElementInDOM(this.div,t,s,!1)}async render(t){const{annotations:e}=t,r=this.div;(0,s.setLayerDimensions)(r,this.viewport);const a=new Map,o={data:null,layer:r,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new s.DOMSVGFactory,annotationStorage:t.annotationStorage||new n.AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===i.AnnotationType.POPUP;if(e){const e=a.get(t.id);if(!e)continue;o.elements=e}else{const{width:e,height:i}=getRectDims(t.rect);if(e<=0||i<=0)continue}o.data=t;const s=AnnotationElementFactory.create(o);if(!s.isRenderable)continue;if(!e&&t.popupRef){const e=a.get(t.popupRef);e?e.push(s):a.set(t.popupRef,[s])}const n=s.render();t.hidden&&(n.style.visibility="hidden");this.#N(n,t.id);if(s.annotationEditorType>0){this.#O.set(s.data.id,s);this._annotationEditorUIManager?.renderAnnotationElement(s)}}this.#B()}update({viewport:t}){const e=this.div;this.viewport=t;(0,s.setLayerDimensions)(e,{rotation:t.rotation});this.#B();e.hidden=!1}#B(){if(!this.#L)return;const t=this.div;for(const[e,i]of this.#L){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i)}this.#L.clear()}getEditableAnnotations(){return Array.from(this.#O.values())}getEditableAnnotation(t){return this.#O.get(t)}}},9792:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{AnnotationStorage:()=>AnnotationStorage,PrintAnnotationStorage:()=>PrintAnnotationStorage,SerializableEmpty:()=>r});e(4114),e(1454),e(4979);var i=e(4292),s=e(310),n=e(7651);const r=Object.freeze({map:null,hash:"",transfer:void 0});class AnnotationStorage{#H=!1;#U=new Map;constructor(){this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const i=this.#U.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#U.get(t)}remove(t){this.#U.delete(t);0===this.#U.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this.#U.values())if(t instanceof s.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#U.get(t);let n=!1;if(void 0!==i){for(const[t,s]of Object.entries(e))if(i[t]!==s){n=!0;i[t]=s}}else{n=!0;this.#U.set(t,e)}n&&this.#z();e instanceof s.AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#U.has(t)}getAll(){return this.#U.size>0?(0,i.objectFromMap)(this.#U):null}setAll(t){for(const[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return this.#U.size}#z(){if(!this.#H){this.#H=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this.#H){this.#H=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this.#U.size)return r;const t=new Map,e=new n.MurmurHash3_64,i=[],a=Object.create(null);let o=!1;for(const[i,n]of this.#U){const r=n instanceof s.AnnotationEditor?n.serialize(!1,a):n;if(r){t.set(i,r);e.update(`${i}:${JSON.stringify(r)}`);o||=!!r.bitmap}}if(o)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:r}get editorStats(){let t=null;const e=new Map;for(const i of this.#U.values()){if(!(i instanceof s.AnnotationEditor))continue;const n=i.telemetryFinalData;if(!n)continue;const{type:r}=n;e.has(r)||e.set(r,Object.getPrototypeOf(i).constructor);t||=Object.create(null);const a=t[r]||=new Map;for(const[t,e]of Object.entries(n)){if("type"===t)continue;let i=a.get(t);if(!i){i=new Map;a.set(t,i)}const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}}class PrintAnnotationStorage extends AnnotationStorage{#j;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#j={map:n,hash:i,transfer:s}}get print(){(0,i.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#j}}},3831:(t,__webpack_exports__,e)=>{e.a(t,(async(t,i)=>{try{e.d(__webpack_exports__,{PDFDataRangeTransport:()=>PDFDataRangeTransport,PDFWorker:()=>PDFWorker,build:()=>R,getDocument:()=>getDocument,version:()=>P});e(4114),e(6573),e(8100),e(7936),e(4628),e(7467),e(4732),e(9577),e(1454),e(3375),e(9225),e(3972),e(9209),e(5714),e(7561),e(6197),e(4979),e(4603),e(7566),e(8721);var s=e(4292),n=e(9792),r=e(5419),a=e(10),o=e(1573),l=e(4923),h=e(6814),d=e(6164),c=e(5178),u=e(6062),p=e(5626),g=e(585),f=e(4094),m=e(7457),b=e(4786),v=e(2050),y=t([o,b]);[o,b]=y.then?(await y)():y;const A=65536,E=100,w=5e3,x=s.isNodeJS?o.NodeCanvasFactory:r.DOMCanvasFactory,_=s.isNodeJS?o.NodeCMapReaderFactory:r.DOMCMapReaderFactory,S=s.isNodeJS?o.NodeFilterFactory:r.DOMFilterFactory,T=s.isNodeJS?o.NodeStandardFontDataFactory:r.DOMStandardFontDataFactory;function getDocument(t){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});if("object"!=typeof t)throw new Error("Invalid parameter in getDocument, need parameter object.");if(!t.url&&!t.data&&!t.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const e=new PDFDocumentLoadingTask,{docId:i}=e,n=t.url?getUrlProp(t.url):null,a=t.data?getDataProp(t.data):null,o=t.httpHeaders||null,l=!0===t.withCredentials,h=t.password??null,u=t.range instanceof PDFDataRangeTransport?t.range:null,p=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:A;let v=t.worker instanceof PDFWorker?t.worker:null;const y=t.verbosity,E="string"!=typeof t.docBaseUrl||(0,r.isDataScheme)(t.docBaseUrl)?null:t.docBaseUrl,w="string"==typeof t.cMapUrl?t.cMapUrl:null,C=!1!==t.cMapPacked,M=t.CMapReaderFactory||_,P="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,R=t.StandardFontDataFactory||T,k=!0!==t.stopAtErrors,D=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,I=!1!==t.isEvalSupported,L="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!s.isNodeJS,O=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,N="boolean"==typeof t.disableFontFace?t.disableFontFace:s.isNodeJS,B=!0===t.fontExtraProperties,H=!0===t.enableXfa,U=t.ownerDocument||globalThis.document,z=!0===t.disableRange,j=!0===t.disableStream,V=!0===t.disableAutoFetch,G=!0===t.pdfBug,W=u?u.length:t.length??NaN,$="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!s.isNodeJS&&!N,q="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:M===r.DOMCMapReaderFactory&&R===r.DOMStandardFontDataFactory&&w&&P&&(0,r.isValidFetchUrl)(w,document.baseURI)&&(0,r.isValidFetchUrl)(P,document.baseURI),K=t.canvasFactory||new x({ownerDocument:U}),X=t.filterFactory||new S({docId:i,ownerDocument:U});(0,s.setVerbosityLevel)(y);const Y={canvasFactory:K,filterFactory:X};if(!q){Y.cMapReaderFactory=new M({baseUrl:w,isCompressed:C});Y.standardFontDataFactory=new R({baseUrl:P})}if(!v){const t={verbosity:y,port:d.GlobalWorkerOptions.workerPort};v=t.port?PDFWorker.fromPort(t):new PDFWorker(t);e._worker=v}const J={docId:i,apiVersion:"4.2.67",data:a,password:h,disableAutoFetch:V,rangeChunkSize:p,length:W,docBaseUrl:E,enableXfa:H,evaluatorOptions:{maxImageSize:D,disableFontFace:N,ignoreErrors:k,isEvalSupported:I,isOffscreenCanvasSupported:L,canvasMaxAreaInBytes:O,fontExtraProperties:B,useSystemFonts:$,cMapUrl:q?w:null,standardFontDataUrl:q?P:null}},Q={ignoreErrors:k,disableFontFace:N,fontExtraProperties:B,enableXfa:H,ownerDocument:U,disableAutoFetch:V,pdfBug:G,styleElement:null};v.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");const t=_fetchDocument(v,J),h=new Promise((function(t){let e;if(u)e=new g.PDFDataTransportStream(u,{disableRange:z,disableStream:j});else if(!a){e=(t=>{if(s.isNodeJS){return function(){return"undefined"!=typeof fetch&&"undefined"!=typeof Response&&"body"in Response.prototype}()&&(0,r.isValidFetchUrl)(t.url)?new f.PDFFetchStream(t):new b.PDFNodeStream(t)}return(0,r.isValidFetchUrl)(t.url)?new f.PDFFetchStream(t):new m.PDFNetworkStream(t)})({url:n,length:W,httpHeaders:o,withCredentials:l,rangeChunkSize:p,disableRange:z,disableStream:j})}t(e)}));return Promise.all([t,h]).then((function([t,s]){if(e.destroyed)throw new Error("Loading aborted");const n=new c.MessageHandler(i,t,v.port),r=new WorkerTransport(n,e,s,Q,Y);e._transport=r;n.send("Ready",null)}))})).catch(e._capability.reject);return e}async function _fetchDocument(t,e){if(t.destroyed)throw new Error("Worker was destroyed");const i=await t.messageHandler.sendWithPromise("GetDocRequest",e,e.data?[e.data.buffer]:null);if(t.destroyed)throw new Error("Worker was destroyed");return i}function getUrlProp(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(s.isNodeJS&&"string"==typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(t){if(s.isNodeJS&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return(0,s.stringToBytes)(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function isRefProxy(t){return"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0}class PDFDocumentLoadingTask{static#V=0;constructor(){this._capability=Promise.withResolvers();this._transport=null;this._worker=null;this.docId="d"+PDFDocumentLoadingTask.#V++;this.destroyed=!1;this.onPassword=null;this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0);await(this._transport?.destroy())}catch(t){this._worker?.port&&delete this._worker._pendingDestroy;throw t}this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}}class PDFDataRangeTransport{constructor(t,e,i=!1,s=null){this.length=t;this.initialData=e;this.progressiveDone=i;this.contentDispositionFilename=s;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){(0,s.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,s.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class PDFPageProxy{#G=null;#W=!1;constructor(t,e,i,s=!1){this._pageIndex=t;this._pageInfo=e;this._transport=i;this._stats=s?new r.StatTimer:null;this._pdfBug=s;this.commonObjs=i.commonObjs;this.objs=new PDFObjects;this._maybeCleanupAfterRender=!1;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new r.PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,s.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:n=s.AnnotationMode.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:h=null,printAnnotationStorage:d=null}){this._stats?.time("Overall");const c=this._transport.getRenderingIntent(i,n,d),{renderingIntent:u,cacheKey:p}=c;this.#W=!1;this.#$();o||=this._transport.getOptionalContentConfig(u);let g=this._intentStates.get(p);if(!g){g=Object.create(null);this._intentStates.set(p,g)}if(g.streamReaderCancelTimeout){clearTimeout(g.streamReaderCancelTimeout);g.streamReaderCancelTimeout=null}const f=!!(u&s.RenderingIntentFlag.PRINT);if(!g.displayReadyCapability){g.displayReadyCapability=Promise.withResolvers();g.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(c)}const complete=t=>{g.renderTasks.delete(m);(this._maybeCleanupAfterRender||f)&&(this.#W=!0);this.#q(!f);if(t){m.capability.reject(t);this._abortOperatorList({intentState:g,reason:t instanceof Error?t:new Error(t)})}else m.capability.resolve();this._stats?.timeEnd("Rendering");this._stats?.timeEnd("Overall")},m=new InternalRenderTask({callback:complete,params:{canvasContext:t,viewport:e,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:g.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!f,pdfBug:this._pdfBug,pageColors:h});(g.renderTasks||=new Set).add(m);const b=m.task;Promise.all([g.displayReadyCapability.promise,o]).then((([t,e])=>{if(this.destroyed)complete();else{this._stats?.time("Rendering");if(!(e.renderingIntent&u))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");m.initializeGraphics({transparency:t,optionalContentConfig:e});m.operatorListChanged()}})).catch(complete);return b}getOperatorList({intent:t="display",annotationMode:e=s.AnnotationMode.ENABLE,printAnnotationStorage:i=null}={}){const n=this._transport.getRenderingIntent(t,e,i,!0);let r,a=this._intentStates.get(n.cacheKey);if(!a){a=Object.create(null);this._intentStates.set(n.cacheKey,a)}if(!a.opListReadCapability){r=Object.create(null);r.operatorListChanged=function operatorListChanged(){if(a.operatorList.lastChunk){a.opListReadCapability.resolve(a.operatorList);a.renderTasks.delete(r)}};a.opListReadCapability=Promise.withResolvers();(a.renderTasks||=new Set).add(r);a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(n)}return a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>v.XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null)};!function pump(){s.read().then((function({value:e,done:i}){if(i)t(n);else{Object.assign(n.styles,e.styles);n.items.push(...e.items);pump()}}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const i of e.renderTasks){t.push(i.completed);i.cancel()}}this.objs.clear();this.#W=!1;this.#$();return Promise.all(t)}cleanup(t=!1){this.#W=!0;const e=this.#q(!1);t&&e&&(this._stats&&=new r.StatTimer);return e}#q(t=!1){this.#$();if(!this.#W||this.destroyed)return!1;if(t){this.#G=setTimeout((()=>{this.#G=null;this.#q(!1)}),w);return!1}for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this.#W=!1;return!0}#$(){if(this.#G){clearTimeout(this.#G);this.#G=null}}_startRenderPage(t,e){const i=this._intentStates.get(e);if(i){this._stats?.timeEnd("Page Request");i.displayReadyCapability?.resolve(t)}}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++){e.operatorList.fnArray.push(t.fnArray[i]);e.operatorList.argsArray.push(t.argsArray[i])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#q(!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i}){const{map:s,transfer:n}=i,r=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:s},n).getReader(),a=this._intentStates.get(e);a.streamReader=r;const pump=()=>{r.read().then((({value:t,done:e})=>{if(e)a.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(t,a);pump()}}),(t=>{a.streamReader=null;if(!this._transport.destroyed){if(a.operatorList){a.operatorList.lastChunk=!0;for(const t of a.renderTasks)t.operatorListChanged();this.#q(!0)}if(a.displayReadyCapability)a.displayReadyCapability.reject(t);else{if(!a.opListReadCapability)throw t;a.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout){clearTimeout(t.streamReaderCancelTimeout);t.streamReaderCancelTimeout=null}if(!i){if(t.renderTasks.size>0)return;if(e instanceof r.RenderingCancelledException){let i=E;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay);t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:t,reason:e,force:!0})}),i);return}}t.streamReader.cancel(new s.AbortException(e.message)).catch((()=>{}));t.streamReader=null;if(!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class LoopbackPort{#K=new Set;#X=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#X.then((()=>{for(const t of this.#K)t.call(this,i)}))}addEventListener(t,e){this.#K.add(e)}removeEventListener(t,e){this.#K.delete(e)}terminate(){this.#K.clear()}}const C={isWorkerDisabled:!1,fakeWorkerId:0};if(s.isNodeJS){C.isWorkerDisabled=!0;d.GlobalWorkerOptions.workerSrc||="./pdf.worker.mjs"}C.isSameOrigin=function(t,e){let i;try{i=new URL(t);if(!i.origin||"null"===i.origin)return!1}catch{return!1}const s=new URL(e,i);return i.origin===s.origin};C.createCDNWrapper=function(t){const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};class PDFWorker{static#Y;constructor({name:t=null,port:e=null,verbosity:i=(0,s.getVerbosityLevel)()}={}){this.name=t;this.destroyed=!1;this.verbosity=i;this._readyCapability=Promise.withResolvers();this._port=null;this._webWorker=null;this._messageHandler=null;if(e){if(PDFWorker.#Y?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(PDFWorker.#Y||=new WeakMap).set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new c.MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this._readyCapability.resolve();this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!C.isWorkerDisabled&&!PDFWorker.#J){let{workerSrc:t}=PDFWorker;try{C.isSameOrigin(window.location.href,t)||(t=C.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new c.MessageHandler("main","worker",e),terminateEarly=()=>{e.removeEventListener("error",onWorkerError);i.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},onWorkerError=()=>{this._webWorker||terminateEarly()};e.addEventListener("error",onWorkerError);i.on("test",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else if(t){this._messageHandler=i;this._port=e;this._webWorker=e;this._readyCapability.resolve();i.send("configure",{verbosity:this.verbosity})}else{this._setupFakeWorker();i.destroy();e.terminate()}}));i.on("ready",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else try{sendTest()}catch{this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};sendTest();return}catch{(0,s.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){if(!C.isWorkerDisabled){(0,s.warn)("Setting up fake worker.");C.isWorkerDisabled=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const i="fake"+C.fakeWorkerId++,s=new c.MessageHandler(i+"_worker",i,e);t.setup(s,e);const n=new c.MessageHandler(i,i+"_worker",e);this._messageHandler=n;this._readyCapability.resolve();n.send("configure",{verbosity:this.verbosity})})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}PDFWorker.#Y?.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#Y?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(d.GlobalWorkerOptions.workerSrc)return d.GlobalWorkerOptions.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#J(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return(0,s.shadow)(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#J)return this.#J;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class WorkerTransport{#Q=new Map;#Z=new Map;#tt=new Map;#et=new Map;#it=null;constructor(t,e,i,s,n){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new a.FontLoader({ownerDocument:s.ownerDocument,styleElement:s.styleElement});this._params=s;this.canvasFactory=n.canvasFactory;this.filterFactory=n.filterFactory;this.cMapReaderFactory=n.cMapReaderFactory;this.standardFontDataFactory=n.standardFontDataFactory;this.destroyed=!1;this.destroyCapability=null;this._networkStream=i;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=Promise.withResolvers();this.setupMessageHandler()}#st(t,e=null){const i=this.#Q.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);this.#Q.set(t,s);return s}get annotationStorage(){return(0,s.shadow)(this,"annotationStorage",new n.AnnotationStorage)}getRenderingIntent(t,e=s.AnnotationMode.ENABLE,i=null,r=!1){let a=s.RenderingIntentFlag.DISPLAY,o=n.SerializableEmpty;switch(t){case"any":a=s.RenderingIntentFlag.ANY;break;case"display":break;case"print":a=s.RenderingIntentFlag.PRINT;break;default:(0,s.warn)(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case s.AnnotationMode.DISABLE:a+=s.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case s.AnnotationMode.ENABLE:break;case s.AnnotationMode.ENABLE_FORMS:a+=s.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case s.AnnotationMode.ENABLE_STORAGE:a+=s.RenderingIntentFlag.ANNOTATIONS_STORAGE;o=(a&s.RenderingIntentFlag.PRINT&&i instanceof n.PrintAnnotationStorage?i:this.annotationStorage).serializable;break;default:(0,s.warn)(`getRenderingIntent - invalid annotationMode: ${e}`)}r&&(a+=s.RenderingIntentFlag.OPLIST);return{renderingIntent:a,cacheKey:`${a}_${o.hash}`,annotationStorageSerializable:o}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=Promise.withResolvers();this.#it?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#Z.values())t.push(e._destroy());this.#Z.clear();this.#tt.clear();this.#et.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#Q.clear();this.filterFactory.destroy();(0,h.cleanupTextLayer)();this._networkStream?.cancelAllRequests(new s.AbortException("Worker was terminated."));if(this.messageHandler){this.messageHandler.destroy();this.messageHandler=null}this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{(0,s.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){if(i)e.close();else{(0,s.assert)(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(t=>{const i=Promise.withResolvers(),s=this._fullReader;s.headersReady.then((()=>{if(!s.isStreamingSupported||!s.isRangeSupported){this._lastProgress&&e.onProgress?.(this._lastProgress);s.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}i.resolve({isStreamingSupported:s.isStreamingSupported,isRangeSupported:s.isRangeSupported,contentLength:s.contentLength})}),i.reject);return i.promise}));t.on("GetRangeReader",((t,e)=>{(0,s.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);if(i){e.onPull=()=>{i.read().then((function({value:t,done:i}){if(i)e.close();else{(0,s.assert)(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{i.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages;this._htmlForXfa=t.htmlForXfa;delete t.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(t,this))}));t.on("DocException",(function(t){let i;switch(t.name){case"PasswordException":i=new s.PasswordException(t.message,t.code);break;case"InvalidPDFException":i=new s.InvalidPDFException(t.message);break;case"MissingPDFException":i=new s.MissingPDFException(t.message);break;case"UnexpectedResponseException":i=new s.UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":i=new s.UnknownErrorException(t.message,t.details);break;default:(0,s.unreachable)("DocException - expected a valid Error.")}e._capability.reject(i)}));t.on("PasswordRequest",(t=>{this.#it=Promise.withResolvers();if(e.onPassword){const updatePassword=t=>{t instanceof Error?this.#it.reject(t):this.#it.resolve({password:t})};try{e.onPassword(updatePassword,t.code)}catch(t){this.#it.reject(t)}}else this.#it.reject(new s.PasswordException(t.message,t.code));return this.#it.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#Z.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(([e,i,n])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":const r=this._params;if("error"in n){const t=n.error;(0,s.warn)(`Error during font loading: ${t}`);this.commonObjs.resolve(e,t);break}const o=r.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,l=new a.FontFaceObject(n,{disableFontFace:r.disableFontFace,ignoreErrors:r.ignoreErrors,inspectFont:o});this.fontLoader.bind(l).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!r.fontExtraProperties&&l.data&&(l.data=null);this.commonObjs.resolve(e,l)}));break;case"CopyLocalImage":const{imageRef:h}=n;(0,s.assert)(h,"The imageRef must be defined.");for(const t of this.#Z.values())for(const[,i]of t.objs)if(i.ref===h){if(!i.dataLen)return null;this.commonObjs.resolve(e,structuredClone(i));return i.dataLen}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,n);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}));t.on("obj",(([t,e,i,n])=>{if(this.destroyed)return;const r=this.#Z.get(e);if(!r.objs.has(t))if(0!==r._intentStates.size)switch(i){case"Image":r.objs.resolve(t,n);n?.dataLen>s.MAX_IMAGE_SIZE_TO_CACHE&&(r._maybeCleanupAfterRender=!0);break;case"Pattern":r.objs.resolve(t,n);break;default:throw new Error(`Got unknown object type ${i}`)}else n?.bitmap?.close()}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))));t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&(0,s.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#tt.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#et.set(i.refStr,t);const s=new PDFPageProxy(e,i,this,this._params.pdfBug);this.#Z.set(e,s);return s}));this.#tt.set(e,s);return s}getPageIndex(t){return isRefProxy(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#st("GetFieldObjects")}hasJSActions(){return this.#st("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#st("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#st("GetOptionalContentConfig").then((e=>new p.OptionalContentConfig(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Q.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new u.Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));this.#Q.set(t,i);return i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#Z.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#Q.clear();this.filterFactory.destroy(!0);(0,h.cleanupTextLayer)()}}cachedPageNumber(t){if(!isRefProxy(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#et.get(e)??null}get loadingParams(){const{disableAutoFetch:t,enableXfa:e}=this._params;return(0,s.shadow)(this,"loadingParams",{disableAutoFetch:t,enableXfa:e})}}const M=Symbol("INITIAL_DATA");class PDFObjects{#nt=Object.create(null);#rt(t){return this.#nt[t]||={...Promise.withResolvers(),data:M}}get(t,e=null){if(e){const i=this.#rt(t);i.promise.then((()=>e(i.data)));return null}const i=this.#nt[t];if(!i||i.data===M)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#nt[t];return!!e&&e.data!==M}resolve(t,e=null){const i=this.#rt(t);i.data=e;i.resolve()}clear(){for(const t in this.#nt){const{data:e}=this.#nt[t];e?.bitmap?.close()}this.#nt=Object.create(null)}*[Symbol.iterator](){for(const t in this.#nt){const{data:e}=this.#nt[t];e!==M&&(yield[t,e])}}}class RenderTask{#at=null;constructor(t){this.#at=t;this.onContinue=null}get promise(){return this.#at.capability.promise}cancel(t=0){this.#at.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#at.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#at;return t.form||t.canvas&&e?.size>0}}class InternalRenderTask{static#ot=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t;this.params=e;this.objs=i;this.commonObjs=s;this.annotationCanvasMap=n;this.operatorListIdx=null;this.operatorList=r;this._pageIndex=a;this.canvasFactory=o;this.filterFactory=l;this._pdfBug=d;this.pageColors=c;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window;this.cancelled=!1;this.capability=Promise.withResolvers();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#ot.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#ot.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:i,viewport:s,transform:n,background:r}=this.params;this.gfx=new l.CanvasGraphics(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:r});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1;this.cancelled=!0;this.gfx?.endDrawing();InternalRenderTask.#ot.delete(this._canvas);this.callback(t||new r.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){if(this.graphicsReady){this.stepper?.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();InternalRenderTask.#ot.delete(this._canvas);this.callback()}}}}}const P="4.2.67",R="49b388101";i()}catch(k){i(k)}}))},2583:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{BaseCMapReaderFactory:()=>BaseCMapReaderFactory,BaseCanvasFactory:()=>BaseCanvasFactory,BaseFilterFactory:()=>BaseFilterFactory,BaseSVGFactory:()=>BaseSVGFactory,BaseStandardFontDataFactory:()=>BaseStandardFontDataFactory});var i=e(4292);class BaseFilterFactory{constructor(){this.constructor===BaseFilterFactory&&(0,i.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class BaseCanvasFactory{constructor(){this.constructor===BaseCanvasFactory&&(0,i.unreachable)("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d")}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){(0,i.unreachable)("Abstract method `_createCanvas` called.")}}class BaseCMapReaderFactory{constructor({baseUrl:t=null,isCompressed:e=!0}){this.constructor===BaseCMapReaderFactory&&(0,i.unreachable)("Cannot initialize BaseCMapReaderFactory.");this.baseUrl=t;this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":""),s=this.isCompressed?i.CMapCompressionType.BINARY:i.CMapCompressionType.NONE;return this._fetchData(e,s).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}_fetchData(t,e){(0,i.unreachable)("Abstract method `_fetchData` called.")}}class BaseStandardFontDataFactory{constructor({baseUrl:t=null}){this.constructor===BaseStandardFontDataFactory&&(0,i.unreachable)("Cannot initialize BaseStandardFontDataFactory.");this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetchData(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}_fetchData(t){(0,i.unreachable)("Abstract method `_fetchData` called.")}}class BaseSVGFactory{constructor(){this.constructor===BaseSVGFactory&&(0,i.unreachable)("Cannot initialize BaseSVGFactory.")}create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");s.setAttribute("version","1.1");if(!i){s.setAttribute("width",`${t}px`);s.setAttribute("height",`${e}px`)}s.setAttribute("preserveAspectRatio","none");s.setAttribute("viewBox",`0 0 ${t} ${e}`);return s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){(0,i.unreachable)("Abstract method `_createSVG` called.")}}},4923:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{CanvasGraphics:()=>CanvasGraphics});e(4114),e(6573),e(8100),e(7936),e(7467),e(4732),e(9577),e(8992),e(4520),e(1454);var i=e(4292),s=e(5419);const n="Fill",r="Stroke",a="Shading";function applyBoundingBox(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s);t.clip(n)}class BaseShadingPattern{constructor(){this.constructor===BaseShadingPattern&&(0,i.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,i.unreachable)("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,a,o){let l;if(o===r||o===n){const n=e.current.getClippedPathBoundingBox(o,(0,s.getCurrentTransform)(t))||[0,0,0,0],r=Math.ceil(n[2]-n[0])||1,h=Math.ceil(n[3]-n[1])||1,d=e.cachedCanvases.getCanvas("pattern",r,h,!0),c=d.context;c.clearRect(0,0,c.canvas.width,c.canvas.height);c.beginPath();c.rect(0,0,c.canvas.width,c.canvas.height);c.translate(-n[0],-n[1]);a=i.Util.transform(a,[1,0,0,1,n[0],n[1]]);c.transform(...e.baseTransform);this.matrix&&c.transform(...this.matrix);applyBoundingBox(c,this._bbox);c.fillStyle=this._createGradient(c);c.fill();l=t.createPattern(d.canvas,"no-repeat");const u=new DOMMatrix(a);l.setTransform(u)}else{applyBoundingBox(t,this._bbox);l=this._createGradient(t)}return l}}function drawTriangle(t,e,i,s,n,r,a,o){const l=e.coords,h=e.colors,d=t.data,c=4*t.width;let u;if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=r;r=a;a=u}if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=a;a=o;o=u}if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=r;r=a;a=u}const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,f=(l[s]+e.offsetX)*e.scaleX,m=(l[s+1]+e.offsetY)*e.scaleY,b=(l[n]+e.offsetX)*e.scaleX,v=(l[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const y=h[r],A=h[r+1],E=h[r+2],w=h[a],x=h[a+1],_=h[a+2],S=h[o],T=h[o+1],C=h[o+2],M=Math.round(g),P=Math.round(v);let R,k,D,I,L,O,N,B;for(let t=M;t<=P;t++){if(t<m){const e=t<g?0:(g-t)/(g-m);R=p-(p-f)*e;k=y-(y-w)*e;D=A-(A-x)*e;I=E-(E-_)*e}else{let e;e=t>v?1:m===v?0:(m-t)/(m-v);R=f-(f-b)*e;k=w-(w-S)*e;D=x-(x-T)*e;I=_-(_-C)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v);L=p-(p-b)*e;O=y-(y-S)*e;N=A-(A-T)*e;B=E-(E-C)*e;const i=Math.round(Math.min(R,L)),s=Math.round(Math.max(R,L));let n=c*t+4*i;for(let t=i;t<=s;t++){e=(R-t)/(R-L);e<0?e=0:e>1&&(e=1);d[n++]=k-(k-O)*e|0;d[n++]=D-(D-N)*e|0;d[n++]=I-(I-B)*e|0;d[n++]=255}}}function drawFigure(t,e,i){const s=e.coords,n=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(r=0;r<l;r++){let e=r*o;for(let r=0;r<h;r++,e++){drawTriangle(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]);drawTriangle(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)drawTriangle(t,i,s[r],s[r+1],s[r+2],n[r],n[r+1],n[r+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[7];this._background=t[8];this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-s,a=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),h=r/o,d=a/l,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/h,scaleY:1/d},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p,!1),f=g.context,m=f.createImageData(o,l);if(e){const t=m.data;for(let i=0,s=t.length;i<s;i+=4){t[i]=e[0];t[i+1]=e[1];t[i+2]=e[2];t[i+3]=255}}for(const t of this._figures)drawFigure(m,t,c);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:s-2*h,offsetY:n-2*d,scaleX:h,scaleY:d}}getPattern(t,e,n,r){applyBoundingBox(t,this._bbox);let o;if(r===a)o=i.Util.singularValueDecompose2dScale((0,s.getCurrentTransform)(t));else{o=i.Util.singularValueDecompose2dScale(e.baseTransform);if(this.matrix){const t=i.Util.singularValueDecompose2dScale(this.matrix);o=[o[0]*t[0],o[1]*t[1]]}}const l=this._createMeshCanvas(o,r===a?null:this._background,e.cachedCanvases);if(r!==a){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(l.offsetX,l.offsetY);t.scale(l.scaleX,l.scaleY);return t.createPattern(l.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const o=1,l=2;class TilingPattern{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s,n){this.operatorList=t[2];this.matrix=t[3]||[1,0,0,1,0,0];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.ctx=i;this.canvasGraphicsFactory=s;this.baseTransform=n}createPatternCanvas(t){const e=this.operatorList,n=this.bbox,r=this.xstep,a=this.ystep,o=this.paintType,l=this.tilingType,h=this.color,d=this.canvasGraphicsFactory;(0,i.info)("TilingType: "+l);const c=n[0],u=n[1],p=n[2],g=n[3],f=i.Util.singularValueDecompose2dScale(this.matrix),m=i.Util.singularValueDecompose2dScale(this.baseTransform),b=[f[0]*m[0],f[1]*m[1]],v=this.getSizeAndScale(r,this.ctx.canvas.width,b[0]),y=this.getSizeAndScale(a,this.ctx.canvas.height,b[1]),A=t.cachedCanvases.getCanvas("pattern",v.size,y.size,!0),E=A.context,w=d.createCanvasGraphics(E);w.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(w,o,h);let x=c,_=u,S=p,T=g;if(c<0){x=0;S+=Math.abs(c)}if(u<0){_=0;T+=Math.abs(u)}E.translate(-v.scale*x,-y.scale*_);w.transform(v.scale,0,0,y.scale,0,0);E.save();this.clipBbox(w,x,_,S,T);w.baseTransform=(0,s.getCurrentTransform)(w.ctx);w.executeOperatorList(e);w.endDrawing();return{canvas:A.canvas,scaleX:v.scale,scaleY:y.scale,offsetX:x,offsetY:_}}getSizeAndScale(t,e,i){t=Math.abs(t);const s=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);n>=s?n=s:i=n/t;return{scale:i,size:n}}clipBbox(t,e,i,n,r){const a=n-e,o=r-i;t.ctx.rect(e,i,a,o);t.current.updateRectMinMax((0,s.getCurrentTransform)(t.ctx),[e,i,n,r]);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const n=t.ctx,r=t.current;switch(e){case o:const t=this.ctx;n.fillStyle=t.fillStyle;n.strokeStyle=t.strokeStyle;r.fillColor=t.fillStyle;r.strokeColor=t.strokeStyle;break;case l:const a=i.Util.makeHexColor(s[0],s[1],s[2]);n.fillStyle=a;n.strokeStyle=a;r.fillColor=a;r.strokeColor=a;break;default:throw new i.FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,s,n){let r=s;if(n!==a){r=i.Util.transform(r,e.baseTransform);this.matrix&&(r=i.Util.transform(r,this.matrix))}const o=this.createPatternCanvas(e);let l=new DOMMatrix(r);l=l.translate(o.offsetX,o.offsetY);l=l.scale(1/o.scaleX,1/o.scaleY);const h=t.createPattern(o.canvas,"repeat");h.setTransform(l);return h}}function convertBlackAndWhiteToRGBA({src:t,srcPos:e=0,dest:s,width:n,height:r,nonBlackColor:a=4294967295,inverseDecode:o=!1}){const l=i.FeatureTest.isLittleEndian?4278190080:255,[h,d]=o?[a,l]:[l,a],c=n>>3,u=7&n,p=t.length;s=new Uint32Array(s.buffer);let g=0;for(let i=0;i<r;i++){for(const i=e+c;e<i;e++){const i=e<p?t[e]:255;s[g++]=128&i?d:h;s[g++]=64&i?d:h;s[g++]=32&i?d:h;s[g++]=16&i?d:h;s[g++]=8&i?d:h;s[g++]=4&i?d:h;s[g++]=2&i?d:h;s[g++]=1&i?d:h}if(0===u)continue;const i=e<p?t[e++]:255;for(let t=0;t<u;t++)s[g++]=i&1<<7-t?d:h}return{srcPos:e,destPos:g}}const h=4096,d=16;class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,i){let s;if(void 0!==this.cache[t]){s=this.cache[t];this.canvasFactory.reset(s,e,i)}else{s=this.canvasFactory.create(e,i);this.cache[t]=s}return s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,i,n,r,a,o,l,h,d){const[c,u,p,g,f,m]=(0,s.getCurrentTransform)(t);if(0===u&&0===p){const s=o*c+f,b=Math.round(s),v=l*g+m,y=Math.round(v),A=(o+h)*c+f,E=Math.abs(Math.round(A)-b)||1,w=(l+d)*g+m,x=Math.abs(Math.round(w)-y)||1;t.setTransform(Math.sign(c),0,0,Math.sign(g),b,y);t.drawImage(e,i,n,r,a,0,0,E,x);t.setTransform(c,u,p,g,f,m);return[E,x]}if(0===c&&0===g){const s=l*p+f,b=Math.round(s),v=o*u+m,y=Math.round(v),A=(l+d)*p+f,E=Math.abs(Math.round(A)-b)||1,w=(o+h)*u+m,x=Math.abs(Math.round(w)-y)||1;t.setTransform(0,Math.sign(u),Math.sign(p),0,b,y);t.drawImage(e,i,n,r,a,0,0,x,E);t.setTransform(c,u,p,g,f,m);return[x,E]}t.drawImage(e,i,n,r,a,o,l,h,d);return[Math.hypot(c,u)*h,Math.hypot(p,g)*d]}class CanvasExtraState{constructor(t,e){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=i.IDENTITY_MATRIX;this.textMatrixScale=1;this.fontMatrix=i.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=i.TextRenderingMode.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.transferMaps="none";this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();return t}setCurrentPoint(t,e){this.x=t;this.y=e}updatePathMinMax(t,e,s){[e,s]=i.Util.applyTransform([e,s],t);this.minX=Math.min(this.minX,e);this.minY=Math.min(this.minY,s);this.maxX=Math.max(this.maxX,e);this.maxY=Math.max(this.maxY,s)}updateRectMinMax(t,e){const s=i.Util.applyTransform(e,t),n=i.Util.applyTransform(e.slice(2),t),r=i.Util.applyTransform([e[0],e[3]],t),a=i.Util.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,s[0],n[0],r[0],a[0]);this.minY=Math.min(this.minY,s[1],n[1],r[1],a[1]);this.maxX=Math.max(this.maxX,s[0],n[0],r[0],a[0]);this.maxY=Math.max(this.maxY,s[1],n[1],r[1],a[1])}updateScalingPathMinMax(t,e){i.Util.scaleMinMax(t,e);this.minX=Math.min(this.minX,e[0]);this.minY=Math.min(this.minY,e[1]);this.maxX=Math.max(this.maxX,e[2]);this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,s,n,r,a,o,l,h,d){const c=i.Util.bezierBoundingBox(e,s,n,r,a,o,l,h,d);d||this.updateRectMinMax(t,c)}getPathBoundingBox(t=n,e=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(t===r){e||(0,i.unreachable)("Stroke bounding box must include transform.");const t=i.Util.singularValueDecompose2dScale(e),n=t[0]*this.lineWidth/2,r=t[1]*this.lineWidth/2;s[0]-=n;s[1]-=r;s[2]+=n;s[3]+=r}return s}updateClipFromPath(){const t=i.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t;this.minX=1/0;this.minY=1/0;this.maxX=0;this.maxY=0}getClippedPathBoundingBox(t=n,e=null){return i.Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){if("undefined"!=typeof ImageData&&e instanceof ImageData){t.putImageData(e,0,0);return}const s=e.height,n=e.width,r=s%d,a=(s-r)/d,o=0===r?a:a+1,l=t.createImageData(n,d);let h,c=0;const u=e.data,p=l.data;let g,f,m,b;if(e.kind===i.ImageKind.GRAYSCALE_1BPP){const e=u.byteLength,s=new Uint32Array(p.buffer,0,p.byteLength>>2),b=s.length,v=n+7>>3,y=4294967295,A=i.FeatureTest.isLittleEndian?4278190080:255;for(g=0;g<o;g++){m=g<a?d:r;h=0;for(f=0;f<m;f++){const t=e-c;let i=0;const r=t>v?n:8*t-7,a=-8&r;let o=0,l=0;for(;i<a;i+=8){l=u[c++];s[h++]=128&l?y:A;s[h++]=64&l?y:A;s[h++]=32&l?y:A;s[h++]=16&l?y:A;s[h++]=8&l?y:A;s[h++]=4&l?y:A;s[h++]=2&l?y:A;s[h++]=1&l?y:A}for(;i<r;i++){if(0===o){l=u[c++];o=128}s[h++]=l&o?y:A;o>>=1}}for(;h<b;)s[h++]=0;t.putImageData(l,0,g*d)}}else if(e.kind===i.ImageKind.RGBA_32BPP){f=0;b=n*d*4;for(g=0;g<a;g++){p.set(u.subarray(c,c+b));c+=b;t.putImageData(l,0,f);f+=d}if(g<o){b=n*r*4;p.set(u.subarray(c,c+b));t.putImageData(l,0,f)}}else{if(e.kind!==i.ImageKind.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);m=d;b=n*m;for(g=0;g<o;g++){if(g>=a){m=r;b=n*m}h=0;for(f=b;f--;){p[h++]=u[c++];p[h++]=u[c++];p[h++]=u[c++];p[h++]=255}t.putImageData(l,0,g*d)}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const i=e.height,s=e.width,n=i%d,r=(i-n)/d,a=0===n?r:r+1,o=t.createImageData(s,d);let l=0;const h=e.data,c=o.data;for(let e=0;e<a;e++){const i=e<r?d:n;({srcPos:l}=convertBlackAndWhiteToRGBA({src:h,srcPos:l,dest:c,width:s,height:i,nonBlackColor:0}));t.putImageData(o,0,e*d)}}function copyCtxState(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t){t.strokeStyle=t.fillStyle="#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}if(!i.isNodeJS){const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function composeSMaskBackdrop(t,e,i,s){const n=t.length;for(let r=3;r<n;r+=4){const n=t[r];if(0===n){t[r-3]=e;t[r-2]=i;t[r-1]=s}else if(n<255){const a=255-n;t[r-3]=t[r-3]*n+e*a>>8;t[r-2]=t[r-2]*n+i*a>>8;t[r-1]=t[r-1]*n+s*a>>8}}}function composeSMaskAlpha(t,e,i){const s=t.length;for(let n=3;n<s;n+=4){const s=i?i[t[n]]:t[n];e[n]=e[n]*s*.00392156862745098|0}}function composeSMaskLuminosity(t,e,i){const s=t.length;for(let n=3;n<s;n+=4){const s=77*t[n-3]+152*t[n-2]+28*t[n-1];e[n]=i?e[n]*i[s>>8]>>8:e[n]*s>>16}}function composeSMask(t,e,i,s){const n=s[0],r=s[1],a=s[2]-n,o=s[3]-r;if(0!==a&&0!==o){!function genericComposeSMask(t,e,i,s,n,r,a,o,l,h,d){const c=!!r,u=c?r[0]:0,p=c?r[1]:0,g=c?r[2]:0,f="Luminosity"===n?composeSMaskLuminosity:composeSMaskAlpha,m=Math.min(s,Math.ceil(1048576/i));for(let n=0;n<s;n+=m){const r=Math.min(m,s-n),b=t.getImageData(o-h,n+(l-d),i,r),v=e.getImageData(o,n+l,i,r);c&&composeSMaskBackdrop(b.data,u,p,g);f(b.data,v.data,a);e.putImageData(v,o,n+l)}}(e.context,i,a,o,e.subtype,e.backdrop,e.transferMap,n,r,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(i.canvas,0,0);t.restore()}}function getImageSmoothingEnabled(t,e){if(e)return!0;const n=i.Util.singularValueDecompose2dScale(t);n[0]=Math.fround(n[0]);n[1]=Math.fround(n[1]);const r=Math.fround((globalThis.devicePixelRatio||1)*s.PixelsPerInch.PDF_TO_CSS_UNITS);return n[0]<=r&&n[1]<=r}const c=["butt","round","square"],u=["miter","round","bevel"],p={},g={};class CanvasGraphics{constructor(t,e,i,s,n,{optionalContentConfig:r,markedContentStack:a=null},o,l){this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=i;this.canvasFactory=s;this.filterFactory=n;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=a||[];this.optionalContentConfig=r;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=o;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.pageColors=l;this._cachedScaleForStroking=[-1,0];this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:n=null}){const r=this.ctx.canvas.width,a=this.ctx.canvas.height,o=this.ctx.fillStyle;this.ctx.fillStyle=n||"#ffffff";this.ctx.fillRect(0,0,r,a);this.ctx.fillStyle=o;if(i){const t=this.cachedCanvases.getCanvas("transparent",r,a);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...(0,s.getCurrentTransform)(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx);if(t){this.ctx.transform(...t);this.outputScaleX=t[0];this.outputScaleY=t[0]}this.ctx.transform(...e.transform);this.viewportScale=e.scale;this.baseTransform=(0,s.getCurrentTransform)(this.ctx)}executeOperatorList(t,e,s,n){const r=t.argsArray,a=t.fnArray;let o=e||0;const l=r.length;if(l===o)return o;const h=l-o>10&&"function"==typeof s,d=h?Date.now()+15:0;let c=0;const u=this.commonObjs,p=this.objs;let g;for(;;){if(void 0!==n&&o===n.nextBreakPoint){n.breakIt(o,s);return o}g=a[o];if(g!==i.OPS.dependency)this[g].apply(this,r[o]);else for(const t of r[o]){const e=t.startsWith("g_")?u:p;if(!e.has(t)){e.get(t,s);return o}}o++;if(o===l)return o;if(h&&++c>10){if(Date.now()>d){s();return o}c=0}}}#lt(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#lt();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.#ht()}#ht(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t;this.ctx.drawImage(this.ctx.canvas,0,0);this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width,s=t.height;let n,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=s,d="prescale1";for(;a>2&&l>1||o>2&&h>1;){let e=l,i=h;if(a>2&&l>1){e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2);a/=l/e}if(o>2&&h>1){i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2;o/=h/i}n=this.cachedCanvases.getCanvas(d,e,i);r=n.context;r.clearRect(0,0,e,i);r.drawImage(t,0,0,l,h,0,0,e,i);t=n.canvas;l=e;h=i;d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:r,height:a}=t,o=this.current.fillColor,l=this.current.patternFill,h=(0,s.getCurrentTransform)(e);let d,c,u,p;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;c=JSON.stringify(l?h:[h.slice(0,4),o]);d=this._cachedBitmapsMap.get(e);if(!d){d=new Map;this._cachedBitmapsMap.set(e,d)}const i=d.get(c);if(i&&!l){return{canvas:i,offsetX:Math.round(Math.min(h[0],h[2])+h[4]),offsetY:Math.round(Math.min(h[1],h[3])+h[5])}}u=i}if(!u){p=this.cachedCanvases.getCanvas("maskCanvas",r,a);putBinaryImageMask(p.context,t)}let g=i.Util.transform(h,[1/r,0,0,-1/a,0,0]);g=i.Util.transform(g,[1,0,0,1,0,-a]);const[f,m,b,v]=i.Util.getAxialAlignedBoundingBox([0,0,r,a],g),y=Math.round(b-f)||1,A=Math.round(v-m)||1,E=this.cachedCanvases.getCanvas("fillCanvas",y,A),w=E.context,x=f,_=m;w.translate(-x,-_);w.transform(...g);if(!u){u=this._scaleImage(p.canvas,(0,s.getCurrentTransformInverse)(w));u=u.img;d&&l&&d.set(c,u)}w.imageSmoothingEnabled=getImageSmoothingEnabled((0,s.getCurrentTransform)(w),t.interpolate);drawImageAtIntegerCoords(w,u,0,0,u.width,u.height,0,0,r,a);w.globalCompositeOperation="source-in";const S=i.Util.transform((0,s.getCurrentTransformInverse)(w),[1,0,0,1,-x,-_]);w.fillStyle=l?o.getPattern(e,this,S,n):o;w.fillRect(0,0,r,a);if(d&&!l){this.cachedCanvases.delete("fillCanvas");d.set(c,E.canvas)}return{canvas:E.canvas,offsetX:Math.round(x),offsetY:Math.round(_)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=c[t]}setLineJoin(t){this.ctx.lineJoin=u[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;if(void 0!==i.setLineDash){i.setLineDash(t);i.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i;this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,n=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;this.ctx=n.context;const r=this.ctx;r.setTransform(...(0,s.getCurrentTransform)(this.suspendedCtx));copyCtxState(this.suspendedCtx,r);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function ctxSave(){e.save();this.__originalSave()};t.restore=function ctxRestore(){e.restore();this.__originalRestore()};t.translate=function ctxTranslate(t,i){e.translate(t,i);this.__originalTranslate(t,i)};t.scale=function ctxScale(t,i){e.scale(t,i);this.__originalScale(t,i)};t.transform=function ctxTransform(t,i,s,n,r,a){e.transform(t,i,s,n,r,a);this.__originalTransform(t,i,s,n,r,a)};t.setTransform=function ctxSetTransform(t,i,s,n,r,a){e.setTransform(t,i,s,n,r,a);this.__originalSetTransform(t,i,s,n,r,a)};t.resetTransform=function ctxResetTransform(){e.resetTransform();this.__originalResetTransform()};t.rotate=function ctxRotate(t){e.rotate(t);this.__originalRotate(t)};t.clip=function ctxRotate(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,i){e.moveTo(t,i);this.__originalMoveTo(t,i)};t.lineTo=function(t,i){e.lineTo(t,i);this.__originalLineTo(t,i)};t.bezierCurveTo=function(t,i,s,n,r,a){e.bezierCurveTo(t,i,s,n,r,a);this.__originalBezierCurveTo(t,i,s,n,r,a)};t.rect=function(t,i,s,n){e.rect(t,i,s,n);this.__originalRect(t,i,s,n)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(r,this.suspendedCtx);this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask;composeSMask(this.suspendedCtx,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}save(){if(this.inSMaskMode){copyCtxState(this.ctx,this.suspendedCtx);this.suspendedCtx.save()}else this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode();if(0!==this.stateStack.length){this.current=this.stateStack.pop();if(this.inSMaskMode){this.suspendedCtx.restore();copyCtxState(this.suspendedCtx,this.ctx)}else this.ctx.restore();this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}}transform(t,e,i,s,n,r){this.ctx.transform(t,e,i,s,n,r);this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}constructPath(t,e,n){const r=this.ctx,a=this.current;let o,l,h=a.x,d=a.y;const c=(0,s.getCurrentTransform)(r),u=0===c[0]&&0===c[3]||0===c[1]&&0===c[2],p=u?n.slice(0):null;for(let s=0,n=0,g=t.length;s<g;s++)switch(0|t[s]){case i.OPS.rectangle:h=e[n++];d=e[n++];const t=e[n++],s=e[n++],g=h+t,f=d+s;r.moveTo(h,d);if(0===t||0===s)r.lineTo(g,f);else{r.lineTo(g,d);r.lineTo(g,f);r.lineTo(h,f)}u||a.updateRectMinMax(c,[h,d,g,f]);r.closePath();break;case i.OPS.moveTo:h=e[n++];d=e[n++];r.moveTo(h,d);u||a.updatePathMinMax(c,h,d);break;case i.OPS.lineTo:h=e[n++];d=e[n++];r.lineTo(h,d);u||a.updatePathMinMax(c,h,d);break;case i.OPS.curveTo:o=h;l=d;h=e[n+4];d=e[n+5];r.bezierCurveTo(e[n],e[n+1],e[n+2],e[n+3],h,d);a.updateCurvePathMinMax(c,o,l,e[n],e[n+1],e[n+2],e[n+3],h,d,p);n+=6;break;case i.OPS.curveTo2:o=h;l=d;r.bezierCurveTo(h,d,e[n],e[n+1],e[n+2],e[n+3]);a.updateCurvePathMinMax(c,o,l,h,d,e[n],e[n+1],e[n+2],e[n+3],p);h=e[n+2];d=e[n+3];n+=4;break;case i.OPS.curveTo3:o=h;l=d;h=e[n+2];d=e[n+3];r.bezierCurveTo(e[n],e[n+1],h,d,h,d);a.updateCurvePathMinMax(c,o,l,e[n],e[n+1],h,d,h,d,p);n+=4;break;case i.OPS.closePath:r.closePath()}u&&a.updateScalingPathMinMax(c,p);a.setCurrentPoint(h,d)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof i&&i?.getPattern){e.save();e.strokeStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),r);this.rescaleAndStroke(!1);e.restore()}else this.rescaleAndStroke(!0);t&&this.consumePath(this.current.getClippedPathBoundingBox());e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(t=!0){const e=this.ctx,i=this.current.fillColor;let r=!1;if(this.current.patternFill){e.save();e.fillStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),n);r=!0}const a=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==a)if(this.pendingEOFill){e.fill("evenodd");this.pendingEOFill=!1}else e.fill();r&&e.restore();t&&this.consumePath(a)}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=p}eoClip(){this.pendingClip=g}beginText(){this.current.textMatrix=i.IDENTITY_MATRIX;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save();e.beginPath();for(const i of t){e.setTransform(...i.transform);e.translate(i.x,i.y);i.addToPath(e,i.fontSize)}e.restore();e.clip();e.beginPath();delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),n=this.current;if(!s)throw new Error(`Can't find font for ${t}`);n.fontMatrix=s.fontMatrix||i.FONT_IDENTITY_MATRIX;0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||(0,i.warn)("Invalid font matrix for font "+t);if(e<0){e=-e;n.fontDirection=-1}else n.fontDirection=1;this.current.font=s;this.current.fontSize=e;if(s.isType3Font)return;const r=s.loadedName||"sans-serif",a=s.systemFontInfo?.css||`"${r}", ${s.fallbackName}`;let o="normal";s.black?o="900":s.bold&&(o="bold");const l=s.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100);this.current.fontSizeScale=e/h;this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t,e,i,s,n,r){this.current.textMatrix=[t,e,i,s,n,r];this.current.textMatrixScale=Math.hypot(t,e);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,n,r){const a=this.ctx,o=this.current,l=o.font,h=o.textRenderingMode,d=o.fontSize/o.fontSizeScale,c=h&i.TextRenderingMode.FILL_STROKE_MASK,u=!!(h&i.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!l.missingFile;let g;(l.disableFontFace||u||p)&&(g=l.getPathGenerator(this.commonObjs,t));if(l.disableFontFace||p){a.save();a.translate(e,n);a.beginPath();g(a,d);r&&a.setTransform(...r);c!==i.TextRenderingMode.FILL&&c!==i.TextRenderingMode.FILL_STROKE||a.fill();c!==i.TextRenderingMode.STROKE&&c!==i.TextRenderingMode.FILL_STROKE||a.stroke();a.restore()}else{c!==i.TextRenderingMode.FILL&&c!==i.TextRenderingMode.FILL_STROKE||a.fillText(t,e,n);c!==i.TextRenderingMode.STROKE&&c!==i.TextRenderingMode.FILL_STROKE||a.strokeText(t,e,n)}if(u){(this.pendingTextPaths||=[]).push({transform:(0,s.getCurrentTransform)(a),x:e,y:n,fontSize:d,addToPath:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){s=!0;break}return(0,i.shadow)(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,r=e.font;if(r.isType3Font)return this.showType3Text(t);const a=e.fontSize;if(0===a)return;const o=this.ctx,l=e.fontSizeScale,h=e.charSpacing,d=e.wordSpacing,c=e.fontDirection,u=e.textHScale*c,p=t.length,g=r.vertical,f=g?1:-1,m=r.defaultVMetrics,b=a*e.fontMatrix[0],v=e.textRenderingMode===i.TextRenderingMode.FILL&&!r.disableFontFace&&!e.patternFill;o.save();o.transform(...e.textMatrix);o.translate(e.x,e.y+e.textRise);c>0?o.scale(u,-1):o.scale(u,1);let y;if(e.patternFill){o.save();const t=e.fillColor.getPattern(o,this,(0,s.getCurrentTransformInverse)(o),n);y=(0,s.getCurrentTransform)(o);o.restore();o.fillStyle=t}let A=e.lineWidth;const E=e.textMatrixScale;if(0===E||0===A){const t=e.textRenderingMode&i.TextRenderingMode.FILL_STROKE_MASK;t!==i.TextRenderingMode.STROKE&&t!==i.TextRenderingMode.FILL_STROKE||(A=this.getSinglePixelWidth())}else A/=E;if(1!==l){o.scale(l,l);A/=l}o.lineWidth=A;if(r.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t){i.push(e.unicode);s+=e.width}o.fillText(i.join(""),0,0);e.x+=s*b*u;o.restore();this.compose();return}let w,x=0;for(w=0;w<p;++w){const e=t[w];if("number"==typeof e){x+=f*e*a/1e3;continue}let i=!1;const s=(e.isSpace?d:0)+h,n=e.fontChar,u=e.accent;let p,A,E=e.width;if(g){const t=e.vmetric||m,i=-(e.vmetric?t[1]:.5*E)*b,s=t[2]*b;E=t?-t[0]:E;p=i/l;A=(x+s)/l}else{p=x/l;A=0}if(r.remeasure&&E>0){const t=1e3*o.measureText(n).width/a*l;if(E<t&&this.isFontSubpixelAAEnabled){const e=E/t;i=!0;o.save();o.scale(e,1);p/=e}else E!==t&&(p+=(E-t)/2e3*a/l)}if(this.contentVisible&&(e.isInFont||r.missingFile))if(v&&!u)o.fillText(n,p,A);else{this.paintChar(n,p,A,y);if(u){const t=p+a*u.offset.x/l,e=A-a*u.offset.y/l;this.paintChar(u.fontChar,t,e,y)}}x+=g?E*b-s*c:E*b+s*c;i&&o.restore()}g?e.y-=x:e.x+=x*u;o.restore();this.compose()}showType3Text(t){const e=this.ctx,s=this.current,n=s.font,r=s.fontSize,a=s.fontDirection,o=n.vertical?1:-1,l=s.charSpacing,h=s.wordSpacing,d=s.textHScale*a,c=s.fontMatrix||i.FONT_IDENTITY_MATRIX,u=t.length;let p,g,f,m;if(!(s.textRenderingMode===i.TextRenderingMode.INVISIBLE)&&0!==r){this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null;e.save();e.transform(...s.textMatrix);e.translate(s.x,s.y);e.scale(d,a);for(p=0;p<u;++p){g=t[p];if("number"==typeof g){m=o*g*r/1e3;this.ctx.translate(m,0);s.x+=m*d;continue}const a=(g.isSpace?h:0)+l,u=n.charProcOperatorList[g.operatorListId];if(!u){(0,i.warn)(`Type3 character "${g.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=g;this.save();e.scale(r,r);e.transform(...c);this.executeOperatorList(u);this.restore()}f=i.Util.applyTransform([g.width,0],c)[0]*r+a;e.translate(f,0);s.x+=f*d}e.restore();this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,r){this.ctx.rect(i,s,n-i,r-s);this.ctx.clip();this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=t[1],n=this.baseTransform||(0,s.getCurrentTransform)(this.ctx),r={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new TilingPattern(t,i,this.ctx,r,n)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,s){const n=i.Util.makeHexColor(t,e,s);this.ctx.strokeStyle=n;this.current.strokeColor=n}setFillRGBColor(t,e,s){const n=i.Util.makeHexColor(t,e,s);this.ctx.fillStyle=n;this.current.fillColor=n;this.current.patternFill=!1}_getPattern(t,e=null){let i;if(this.cachedPatterns.has(t))i=this.cachedPatterns.get(t);else{i=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t));this.cachedPatterns.set(t,i)}e&&(i.matrix=e);return i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const n=this._getPattern(t);e.fillStyle=n.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),a);const r=(0,s.getCurrentTransformInverse)(e);if(r){const{width:t,height:s}=e.canvas,[n,a,o,l]=i.Util.getAxialAlignedBoundingBox([0,0,t,s],r);this.ctx.fillRect(n,a,o-n,l-a)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){(0,i.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,i.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);Array.isArray(t)&&6===t.length&&this.transform(...t);this.baseTransform=(0,s.getCurrentTransform)(this.ctx);if(e){const t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i);this.current.updateRectMinMax((0,s.getCurrentTransform)(this.ctx),e);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||(0,i.info)("TODO: Support non-isolated groups.");t.knockout&&(0,i.warn)("Knockout groups not supported.");const n=(0,s.getCurrentTransform)(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let r=i.Util.getAxialAlignedBoundingBox(t.bbox,(0,s.getCurrentTransform)(e));const a=[0,0,e.canvas.width,e.canvas.height];r=i.Util.intersect(r,a)||[0,0,0,0];const o=Math.floor(r[0]),l=Math.floor(r[1]);let d=Math.max(Math.ceil(r[2])-o,1),c=Math.max(Math.ceil(r[3])-l,1),u=1,p=1;if(d>h){u=d/h;d=h}if(c>h){p=c/h;c=h}this.current.startNewPathAndClipBox([0,0,d,c]);let g="groupAt"+this.groupLevel;t.smask&&(g+="_smask_"+this.smaskCounter++%2);const f=this.cachedCanvases.getCanvas(g,d,c),m=f.context;m.scale(1/u,1/p);m.translate(-o,-l);m.transform(...n);if(t.smask)this.smaskStack.push({canvas:f.canvas,context:m,offsetX:o,offsetY:l,scaleX:u,scaleY:p,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(o,l);e.scale(u,p);e.save()}copyCtxState(e,m);this.ctx=m;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,n=this.groupStack.pop();this.ctx=n;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=(0,s.getCurrentTransform)(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const n=i.Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(n)}}beginAnnotation(t,e,n,r,a){this.#lt();resetCtxToDefault(this.ctx);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(Array.isArray(e)&&4===e.length){const r=e[2]-e[0],o=e[3]-e[1];if(a&&this.annotationCanvasMap){(n=n.slice())[4]-=e[0];n[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=r;e[3]=o;const[a,l]=i.Util.singularValueDecompose2dScale((0,s.getCurrentTransform)(this.ctx)),{viewportScale:h}=this,d=Math.ceil(r*this.outputScaleX*h),c=Math.ceil(o*this.outputScaleY*h);this.annotationCanvas=this.canvasFactory.create(d,c);const{canvas:u,context:p}=this.annotationCanvas;this.annotationCanvasMap.set(t,u);this.annotationCanvas.savedCtx=this.ctx;this.ctx=p;this.ctx.save();this.ctx.setTransform(a,0,0,-l,0,o*l);resetCtxToDefault(this.ctx)}else{resetCtxToDefault(this.ctx);this.ctx.rect(e[0],e[1],r,o);this.ctx.clip();this.endPath()}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...n);this.transform(...r)}endAnnotation(){if(this.annotationCanvas){this.ctx.restore();this.#ht();this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this.processingType3;if(s){void 0===s.compiled&&(s.compiled=function compileType3Glyph(t){const{width:e,height:i}=t;if(e>1e3||i>1e3)return null;const s=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),n=e+1;let r,a,o,l=new Uint8Array(n*(i+1));const h=e+7&-8;let d=new Uint8Array(h*i),c=0;for(const e of t.data){let t=128;for(;t>0;){d[c++]=e&t?0:255;t>>=1}}let u=0;c=0;if(0!==d[c]){l[0]=1;++u}for(a=1;a<e;a++){if(d[c]!==d[c+1]){l[a]=d[c]?2:1;++u}c++}if(0!==d[c]){l[a]=2;++u}for(r=1;r<i;r++){c=r*h;o=r*n;if(d[c-h]!==d[c]){l[o]=d[c]?1:8;++u}let t=(d[c]?4:0)+(d[c-h]?8:0);for(a=1;a<e;a++){t=(t>>2)+(d[c+1]?4:0)+(d[c-h+1]?8:0);if(s[t]){l[o+a]=s[t];++u}c++}if(d[c-h]!==d[c]){l[o+a]=d[c]?2:4;++u}if(u>1e3)return null}c=h*(i-1);o=r*n;if(0!==d[c]){l[o]=8;++u}for(a=1;a<e;a++){if(d[c]!==d[c+1]){l[o+a]=d[c]?4:8;++u}c++}if(0!==d[c]){l[o+a]=4;++u}if(u>1e3)return null;const p=new Int32Array([0,n,-1,0,-n,0,0,0,1]),g=new Path2D;for(r=0;u&&r<=i;r++){let t=r*n;const i=t+e;for(;t<i&&!l[t];)t++;if(t===i)continue;g.moveTo(t%n,r);const s=t;let a=l[t];do{const e=p[a];do{t+=e}while(!l[t]);const i=l[t];if(5!==i&&10!==i){a=i;l[t]=0}else{a=i&51*a>>4;l[t]&=a>>2|a<<2}g.lineTo(t%n,t/n|0);l[t]||--u}while(s!==t);--r}d=null;l=null;return function(t){t.save();t.scale(1/e,-1/i);t.translate(0,-i);t.fill(g);t.beginPath();t.restore()}}(t));if(s.compiled){s.compiled(i);return}}const n=this._createMaskCanvas(t),r=n.canvas;i.save();i.setTransform(1,0,0,1,0,0);i.drawImage(r,n.offsetX,n.offsetY);i.restore();this.compose()}paintImageMaskXObjectRepeat(t,e,n=0,r=0,a,o){if(!this.contentVisible)return;t=this.getObject(t.data,t);const l=this.ctx;l.save();const h=(0,s.getCurrentTransform)(l);l.transform(e,n,r,a,0,0);const d=this._createMaskCanvas(t);l.setTransform(1,0,0,1,d.offsetX-h[4],d.offsetY-h[5]);for(let t=0,s=o.length;t<s;t+=2){const s=i.Util.transform(h,[e,n,r,a,o[t],o[t+1]]),[c,u]=i.Util.applyTransform([0,0],s);l.drawImage(d.canvas,c,u)}l.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,r=this.current.patternFill;for(const a of t){const{data:t,width:o,height:l,transform:h}=a,d=this.cachedCanvases.getCanvas("maskCanvas",o,l),c=d.context;c.save();putBinaryImageMask(c,this.getObject(t,a));c.globalCompositeOperation="source-in";c.fillStyle=r?i.getPattern(c,this,(0,s.getCurrentTransformInverse)(e),n):i;c.fillRect(0,0,o,l);c.restore();e.save();e.transform(...h);e.scale(1,-1);drawImageAtIntegerCoords(e,d.canvas,0,0,o,l,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,i.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,s,n){if(!this.contentVisible)return;const r=this.getObject(t);if(!r){(0,i.warn)("Dependent image isn't ready yet");return}const a=r.width,o=r.height,l=[];for(let t=0,i=n.length;t<i;t+=2)l.push({transform:[e,0,0,s,n[t],n[t+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(r,l)}applyTransferMapsToCanvas(t){if("none"!==this.current.transferMaps){t.filter=this.current.transferMaps;t.drawImage(t.canvas,0,0);t.filter="none"}return t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),r=n.context;r.filter=this.current.transferMaps;r.drawImage(e,0,0);r.filter="none";return n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,n=t.height,r=this.ctx;this.save();if(!i.isNodeJS){const{filter:t}=r;"none"!==t&&""!==t&&(r.filter="none")}r.scale(1/e,-1/n);let a;if(t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const i=this.cachedCanvases.getCanvas("inlineImage",e,n).context;putBinaryImageData(i,t);a=this.applyTransferMapsToCanvas(i)}const o=this._scaleImage(a,(0,s.getCurrentTransformInverse)(r));r.imageSmoothingEnabled=getImageSmoothingEnabled((0,s.getCurrentTransform)(r),t.interpolate);drawImageAtIntegerCoords(r,o.img,0,0,o.paintWidth,o.paintHeight,0,-n,e,n);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(n,t);s=this.applyTransferMapsToCanvas(n)}for(const t of e){i.save();i.transform(...t.transform);i.scale(1,-1);drawImageAtIntegerCoords(i,s,t.x,t.y,t.w,t.h,0,-1,1,1);i.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(t);const i=this.ctx;if(this.pendingClip){e||(this.pendingClip===g?i.clip("evenodd"):i.clip());this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox);i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=(0,s.getCurrentTransform)(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let r,a;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)r=a=1/i;else{const e=i*t;r=a=e<1?1/e:1}else if(0===t){r=1/i;a=1/s}else{const e=i*t,n=s*t;r=e<1?1/e:1;a=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),l=Math.hypot(e,i),h=Math.hypot(s,n);if(0===t){r=h/o;a=l/o}else{const e=t*o;r=h>e?h/e:1;a=l>e?l/e:1}}this._cachedScaleForStroking[0]=r;this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:i}=this.current,[s,n]=this.getScaleForStroking();e.lineWidth=i||1;if(1===s&&1===n){e.stroke();return}const r=e.getLineDash();t&&e.save();e.scale(s,n);if(r.length>0){const t=Math.max(s,n);e.setLineDash(r.map((e=>e/t)));e.lineDashOffset/=t}e.stroke();t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in i.OPS)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[i.OPS[t]]=CanvasGraphics.prototype[t])},5419:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{DOMCMapReaderFactory:()=>DOMCMapReaderFactory,DOMCanvasFactory:()=>DOMCanvasFactory,DOMFilterFactory:()=>DOMFilterFactory,DOMSVGFactory:()=>DOMSVGFactory,DOMStandardFontDataFactory:()=>DOMStandardFontDataFactory,PDFDateString:()=>PDFDateString,PageViewport:()=>PageViewport,PixelsPerInch:()=>PixelsPerInch,RenderingCancelledException:()=>RenderingCancelledException,StatTimer:()=>StatTimer,fetchData:()=>fetchData,getColorValues:()=>getColorValues,getCurrentTransform:()=>getCurrentTransform,getCurrentTransformInverse:()=>getCurrentTransformInverse,getFilenameFromUrl:()=>getFilenameFromUrl,getPdfFilenameFromUrl:()=>getPdfFilenameFromUrl,getRGB:()=>getRGB,getXfaPageViewport:()=>getXfaPageViewport,isDataScheme:()=>isDataScheme,isPdfFile:()=>isPdfFile,isValidFetchUrl:()=>isValidFetchUrl,noContextMenu:()=>noContextMenu,setLayerDimensions:()=>setLayerDimensions});e(4114),e(6573),e(8100),e(7936),e(7467),e(4732),e(9577),e(8992),e(4520),e(1454),e(4603),e(7566),e(8721);var i=e(2583),s=e(4292);const n="http://www.w3.org/2000/svg";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}class DOMFilterFactory extends i.BaseFilterFactory{#dt;#ct;#V;#ut;#pt;#gt=0;constructor({docId:t,ownerDocument:e=globalThis.document}={}){super();this.#V=t;this.#ut=e}get#ft(){return this.#dt||=new Map}get#mt(){return this.#pt||=new Map}get#bt(){if(!this.#ct){const t=this.#ut.createElement("div"),{style:e}=t;e.visibility="hidden";e.contain="strict";e.width=e.height=0;e.position="absolute";e.top=e.left=0;e.zIndex=-1;const i=this.#ut.createElementNS(n,"svg");i.setAttribute("width",0);i.setAttribute("height",0);this.#ct=this.#ut.createElementNS(n,"defs");t.append(i);i.append(this.#ct);this.#ut.body.append(t)}return this.#ct}addFilter(t){if(!t)return"none";let e,i,s,n,r=this.#ft.get(t);if(r)return r;if(1===t.length){const r=t[0],a=new Array(256);for(let t=0;t<256;t++)a[t]=r[t]/255;n=e=i=s=a.join(",")}else{const[r,a,o]=t,l=new Array(256),h=new Array(256),d=new Array(256);for(let t=0;t<256;t++){l[t]=r[t]/255;h[t]=a[t]/255;d[t]=o[t]/255}e=l.join(",");i=h.join(",");s=d.join(",");n=`${e}${i}${s}`}r=this.#ft.get(n);if(r){this.#ft.set(t,r);return r}const a=`g_${this.#V}_transfer_map_${this.#gt++}`,o=`url(#${a})`;this.#ft.set(t,o);this.#ft.set(n,o);const l=this.#vt(a);this.#yt(e,i,s,l);return o}addHCMFilter(t,e){const i=`${t}-${e}`,n="base";let r=this.#mt.get(n);if(r?.key===i)return r.url;if(r){r.filter?.remove();r.key=i;r.url="none";r.filter=null}else{r={key:i,url:"none",filter:null};this.#mt.set(n,r)}if(!t||!e)return r.url;const a=this.#At(t);t=s.Util.makeHexColor(...a);const o=this.#At(e);e=s.Util.makeHexColor(...o);this.#bt.style.color="";if("#000000"===t&&"#ffffff"===e||t===e)return r.url;const l=new Array(256);for(let t=0;t<=255;t++){const e=t/255;l[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const h=l.join(","),d=`g_${this.#V}_hcm_filter`,c=r.filter=this.#vt(d);this.#yt(h,h,h,c);this.#Et(c);const getSteps=(t,e)=>{const i=a[t]/255,s=o[t]/255,n=new Array(e+1);for(let t=0;t<=e;t++)n[t]=i+t/e*(s-i);return n.join(",")};this.#yt(getSteps(0,5),getSteps(1,5),getSteps(2,5),c);r.url=`url(#${d})`;return r.url}addHighlightHCMFilter(t,e,i,s,n){const r=`${e}-${i}-${s}-${n}`;let a=this.#mt.get(t);if(a?.key===r)return a.url;if(a){a.filter?.remove();a.key=r;a.url="none";a.filter=null}else{a={key:r,url:"none",filter:null};this.#mt.set(t,a)}if(!e||!i)return a.url;const[o,l]=[e,i].map(this.#At.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[c,u]=[s,n].map(this.#At.bind(this));d<h&&([h,d,c,u]=[d,h,u,c]);this.#bt.style.color="";const getSteps=(t,e,i)=>{const s=new Array(256),n=(d-h)/i,r=t/255,a=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(h+t*n),i=r+t*a;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},p=`g_${this.#V}_hcm_${t}_filter`,g=a.filter=this.#vt(p);this.#Et(g);this.#yt(getSteps(c[0],u[0],5),getSteps(c[1],u[1],5),getSteps(c[2],u[2],5),g);a.url=`url(#${p})`;return a.url}destroy(t=!1){if(!t||0===this.#mt.size){if(this.#ct){this.#ct.parentNode.parentNode.remove();this.#ct=null}if(this.#dt){this.#dt.clear();this.#dt=null}this.#gt=0}}#Et(t){const e=this.#ut.createElementNS(n,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0");t.append(e)}#vt(t){const e=this.#ut.createElementNS(n,"filter");e.setAttribute("color-interpolation-filters","sRGB");e.setAttribute("id",t);this.#bt.append(e);return e}#wt(t,e,i){const s=this.#ut.createElementNS(n,e);s.setAttribute("type","discrete");s.setAttribute("tableValues",i);t.append(s)}#yt(t,e,i,s){const r=this.#ut.createElementNS(n,"feComponentTransfer");s.append(r);this.#wt(r,"feFuncR",t);this.#wt(r,"feFuncG",e);this.#wt(r,"feFuncB",i)}#At(t){this.#bt.style.color=t;return getRGB(getComputedStyle(this.#bt).getPropertyValue("color"))}}class DOMCanvasFactory extends i.BaseCanvasFactory{constructor({ownerDocument:t=globalThis.document}={}){super();this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");i.width=t;i.height=e;return i}}async function fetchData(t,e="text"){if(isValidFetchUrl(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0);n.responseType=e;n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":i(n.response);return}i(n.responseText)}};n.send(null)}))}class DOMCMapReaderFactory extends i.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t,this.isCompressed?"arraybuffer":"text").then((t=>({cMapData:t instanceof ArrayBuffer?new Uint8Array(t):(0,s.stringToBytes)(t),compressionType:e})))}}class DOMStandardFontDataFactory extends i.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t,"arraybuffer").then((t=>new Uint8Array(t)))}}class DOMSVGFactory extends i.BaseSVGFactory{_createSVG(t){return document.createElementNS(n,t)}}class PageViewport{constructor({viewBox:t,scale:e,rotation:i,offsetX:s=0,offsetY:n=0,dontFlip:r=!1}){this.viewBox=t;this.scale=e;this.rotation=i;this.offsetX=s;this.offsetY=n;const a=(t[2]+t[0])/2,o=(t[3]+t[1])/2;let l,h,d,c,u,p,g,f;(i%=360)<0&&(i+=360);switch(i){case 180:l=-1;h=0;d=0;c=1;break;case 90:l=0;h=1;d=1;c=0;break;case 270:l=0;h=-1;d=-1;c=0;break;case 0:l=1;h=0;d=0;c=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(r){d=-d;c=-c}if(0===l){u=Math.abs(o-t[1])*e+s;p=Math.abs(a-t[0])*e+n;g=(t[3]-t[1])*e;f=(t[2]-t[0])*e}else{u=Math.abs(a-t[0])*e+s;p=Math.abs(o-t[1])*e+n;g=(t[2]-t[0])*e;f=(t[3]-t[1])*e}this.transform=[l*e,h*e,d*e,c*e,u-l*e*a-d*e*o,p-h*e*a-c*e*o];this.width=g;this.height=f}get rawDims(){const{viewBox:t}=this;return(0,s.shadow)(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){return s.Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=s.Util.applyTransform([t[0],t[1]],this.transform),i=s.Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return s.Util.applyInverseTransform([t,e],this.transform)}}class RenderingCancelledException extends s.BaseException{constructor(t,e=0){super(t,"RenderingCancelledException");this.extraDelay=e}}function isDataScheme(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function getFilenameFromUrl(t,e=!1){e||([t]=t.split(/[#?]/,1));return t.substring(t.lastIndexOf("/")+1)}function getPdfFilenameFromUrl(t,e="document.pdf"){if("string"!=typeof t)return e;if(isDataScheme(t)){(0,s.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,n=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let r=i.exec(n[1])||i.exec(n[2])||i.exec(n[3]);if(r){r=r[0];if(r.includes("%"))try{r=i.exec(decodeURIComponent(r))[0]}catch{}}return r||e}class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&(0,s.warn)(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||(0,s.warn)(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function isValidFetchUrl(t,e){try{const{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch{return!1}}function noContextMenu(t){t.preventDefault()}let r;class PDFDateString{static toDateObject(t){if(!t||"string"!=typeof t)return null;r||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=r.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const h=e[7]||"Z";let d=parseInt(e[8],10);d=d>=0&&d<=23?d:0;let c=parseInt(e[9],10)||0;c=c>=0&&c<=59?c:0;if("-"===h){a+=d;o+=c}else if("+"===h){a-=d;o-=c}return new Date(Date.UTC(i,s,n,a,o,l))}}function getXfaPageViewport(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,r=[0,0,parseInt(s),parseInt(n)];return new PageViewport({viewBox:r,scale:e,rotation:i})}function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);(0,s.warn)(`Not a valid color format: "${t}"`);return[0,0,0]}function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,getRGB(s))}e.remove()}function getCurrentTransform(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform();return[e,i,s,n,r,a]}function getCurrentTransformInverse(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform().invertSelf();return[e,i,s,n,r,a]}function setLayerDimensions(t,e,i=!1,n=!0){if(e instanceof PageViewport){const{pageWidth:n,pageHeight:r}=e.rawDims,{style:a}=t,o=s.FeatureTest.isCSSRoundSupported,l=`var(--scale-factor) * ${n}px`,h=`var(--scale-factor) * ${r}px`,d=o?`round(${l}, 1px)`:`calc(${l})`,c=o?`round(${h}, 1px)`:`calc(${h})`;if(i&&e.rotation%180!=0){a.width=c;a.height=d}else{a.width=d;a.height=c}}n&&t.setAttribute("data-main-rotation",e.rotation)}},4047:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{DrawLayer:()=>DrawLayer});var i=e(5419),s=e(4292);class DrawLayer{#y=null;#gt=0;#xt=new Map;#_t=new Map;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#y){if(this.#y!==t){if(this.#xt.size>0)for(const e of this.#xt.values()){e.remove();t.append(e)}this.#y=t}}else this.#y=t}static get _svgFactory(){return(0,s.shadow)(this,"_svgFactory",new i.DOMSVGFactory)}static#St(t,{x:e=0,y:i=0,width:s=1,height:n=1}={}){const{style:r}=t;r.top=100*i+"%";r.left=100*e+"%";r.width=100*s+"%";r.height=100*n+"%"}#Tt(t){const e=DrawLayer._svgFactory.create(1,1,!0);this.#y.append(e);e.setAttribute("aria-hidden",!0);DrawLayer.#St(e,t);return e}#Ct(t,e){const i=DrawLayer._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s);i.setAttribute("clipPathUnits","objectBoundingBox");const n=DrawLayer._svgFactory.createElement("use");i.append(n);n.setAttribute("href",`#${e}`);n.classList.add("clip");return s}highlight(t,e,i,s=!1){const n=this.#gt++,r=this.#Tt(t.box);r.classList.add("highlight");t.free&&r.classList.add("free");const a=DrawLayer._svgFactory.createElement("defs");r.append(a);const o=DrawLayer._svgFactory.createElement("path");a.append(o);const l=`path_p${this.pageIndex}_${n}`;o.setAttribute("id",l);o.setAttribute("d",t.toSVGPath());s&&this.#_t.set(n,o);const h=this.#Ct(a,l),d=DrawLayer._svgFactory.createElement("use");r.append(d);r.setAttribute("fill",e);r.setAttribute("fill-opacity",i);d.setAttribute("href",`#${l}`);this.#xt.set(n,r);return{id:n,clipPathId:`url(#${h})`}}highlightOutline(t){const e=this.#gt++,i=this.#Tt(t.box);i.classList.add("highlightOutline");const s=DrawLayer._svgFactory.createElement("defs");i.append(s);const n=DrawLayer._svgFactory.createElement("path");s.append(n);const r=`path_p${this.pageIndex}_${e}`;n.setAttribute("id",r);n.setAttribute("d",t.toSVGPath());n.setAttribute("vector-effect","non-scaling-stroke");let a;if(t.free){i.classList.add("free");const t=DrawLayer._svgFactory.createElement("mask");s.append(t);a=`mask_p${this.pageIndex}_${e}`;t.setAttribute("id",a);t.setAttribute("maskUnits","objectBoundingBox");const n=DrawLayer._svgFactory.createElement("rect");t.append(n);n.setAttribute("width","1");n.setAttribute("height","1");n.setAttribute("fill","white");const o=DrawLayer._svgFactory.createElement("use");t.append(o);o.setAttribute("href",`#${r}`);o.setAttribute("stroke","none");o.setAttribute("fill","black");o.setAttribute("fill-rule","nonzero");o.classList.add("mask")}const o=DrawLayer._svgFactory.createElement("use");i.append(o);o.setAttribute("href",`#${r}`);a&&o.setAttribute("mask",`url(#${a})`);const l=o.cloneNode();i.append(l);o.classList.add("mainOutline");l.classList.add("secondaryOutline");this.#xt.set(e,i);return e}finalizeLine(t,e){const i=this.#_t.get(t);this.#_t.delete(t);this.updateBox(t,e.box);i.setAttribute("d",e.toSVGPath())}updateLine(t,e){this.#xt.get(t).firstChild.firstChild.setAttribute("d",e.toSVGPath())}removeFreeHighlight(t){this.remove(t);this.#_t.delete(t)}updatePath(t,e){this.#_t.get(t).setAttribute("d",e.toSVGPath())}updateBox(t,e){DrawLayer.#St(this.#xt.get(t),e)}show(t,e){this.#xt.get(t).classList.toggle("hidden",!e)}rotate(t,e){this.#xt.get(t).setAttribute("data-main-rotation",e)}changeColor(t,e){this.#xt.get(t).setAttribute("fill",e)}changeOpacity(t,e){this.#xt.get(t).setAttribute("fill-opacity",e)}addClass(t,e){this.#xt.get(t).classList.add(e)}removeClass(t,e){this.#xt.get(t).classList.remove(e)}remove(t){if(null!==this.#y){this.#xt.get(t).remove();this.#xt.delete(t)}}destroy(){this.#y=null;for(const t of this.#xt.values())t.remove();this.#xt.clear()}}},9731:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{AnnotationEditorLayer:()=>AnnotationEditorLayer});e(1454),e(3375),e(9225),e(3972),e(9209),e(5714),e(7561),e(6197);var i=e(4292),s=e(310),n=(e(4114),e(8992),e(7550),e(7830)),r=e(6976);const a=/\r\n?|\n/g;class FreeTextEditor extends s.AnnotationEditor{#Mt=this.editorDivBlur.bind(this);#Pt=this.editorDivFocus.bind(this);#Rt=this.editorDivInput.bind(this);#kt=this.editorDivKeydown.bind(this);#Ft=this.editorDivPaste.bind(this);#g;#Dt="";#It=`${this.id}-editor`;#Lt;#Ot=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=FreeTextEditor.prototype,arrowChecker=t=>t.isEmpty(),e=n.AnnotationEditorUIManager.TRANSLATE_SMALL,s=n.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,i.shadow)(this,"_keyboardManager",new n.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[s,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,s],checker:arrowChecker}]]))}static _type="freetext";static _editorType=i.AnnotationEditorType.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"});this.#g=t.color||FreeTextEditor._defaultColor||s.AnnotationEditor._defaultLineColor;this.#Lt=t.fontSize||FreeTextEditor._defaultFontSize}static initialize(t,e){s.AnnotationEditor.initialize(t,e,{strings:["pdfjs-free-text-default-content"]});const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case i.AnnotationEditorParamsType.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case i.AnnotationEditorParamsType.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case i.AnnotationEditorParamsType.FREETEXT_SIZE:this.#Nt(e);break;case i.AnnotationEditorParamsType.FREETEXT_COLOR:this.#Bt(e)}}static get defaultPropertiesToUpdate(){return[[i.AnnotationEditorParamsType.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[i.AnnotationEditorParamsType.FREETEXT_COLOR,FreeTextEditor._defaultColor||s.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[i.AnnotationEditorParamsType.FREETEXT_SIZE,this.#Lt],[i.AnnotationEditorParamsType.FREETEXT_COLOR,this.#g]]}#Nt(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`;this.translate(0,-(t-this.#Lt)*this.parentScale);this.#Lt=t;this.#Ht()},e=this.#Lt;this.addCommands({cmd:setFontsize.bind(this,t),undo:setFontsize.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#Bt(t){const setColor=t=>{this.#g=this.editorDiv.style.color=t},e=this.#g;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+this.#Lt)*t]}rebuild(){if(this.parent){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}}enableEditMode(){if(!this.isInEditMode()){this.parent.setEditingState(!1);this.parent.updateToolbar(i.AnnotationEditorType.FREETEXT);super.enableEditMode();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this._isDraggable=!1;this.div.removeAttribute("aria-activedescendant");this.editorDiv.addEventListener("keydown",this.#kt);this.editorDiv.addEventListener("focus",this.#Pt);this.editorDiv.addEventListener("blur",this.#Mt);this.editorDiv.addEventListener("input",this.#Rt);this.editorDiv.addEventListener("paste",this.#Ft)}}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",this.#It);this._isDraggable=!0;this.editorDiv.removeEventListener("keydown",this.#kt);this.editorDiv.removeEventListener("focus",this.#Pt);this.editorDiv.removeEventListener("blur",this.#Mt);this.editorDiv.removeEventListener("input",this.#Rt);this.editorDiv.removeEventListener("paste",this.#Ft);this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freetextEditing")}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}}onceAdded(){if(!this.width){this.enableEditMode();this.editorDiv.focus();this._initialOptions?.isCentered&&this.center();this._initialOptions=null}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;if(this.parent){this.parent.setEditingState(!0);this.parent.div.classList.add("freetextEditing")}super.remove()}#Ut(){const t=[];this.editorDiv.normalize();for(const e of this.editorDiv.childNodes)t.push(FreeTextEditor.#zt(e));return t.join("\n")}#Ht(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden");e.style.display="hidden";t.div.append(this.div);i=e.getBoundingClientRect();e.remove();e.style.display=s;e.classList.toggle("hidden",n)}if(this.rotation%180==this.parentRotation%180){this.width=i.width/t;this.height=i.height/e}else{this.width=i.height/t;this.height=i.width/e}this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit();this.disableEditMode();const t=this.#Dt,e=this.#Dt=this.#Ut().trimEnd();if(t===e)return;const setText=t=>{this.#Dt=t;if(t){this.#jt();this._uiManager.rebuild(this);this.#Ht()}else this.remove()};this.addCommands({cmd:()=>{setText(e)},undo:()=>{setText(t)},mustExec:!1});this.#Ht()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode();this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enterInEditMode();t.preventDefault()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",this.#It);this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text");this.enableEditing();s.AnnotationEditor._l10nPromise.get("pdfjs-free-text-default-content").then((t=>this.editorDiv?.setAttribute("default-content",t)));this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;i.fontSize=`calc(${this.#Lt}px * var(--scale-factor))`;i.color=this.#g;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);(0,n.bindEvents)(this,this.div,["dblclick","keydown"]);if(this.width){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this.#Ot;let[r,a]=this.getInitialTranslation();[r,a]=this.pageTranslationToScreen(r,a);const[o,l]=this.pageDimensions,[h,d]=this.pageTranslation;let c,u;switch(this.rotation){case 0:c=t+(n[0]-h)/o;u=e+this.height-(n[1]-d)/l;break;case 90:c=t+(n[0]-h)/o;u=e-(n[1]-d)/l;[r,a]=[a,-r];break;case 180:c=t-this.width+(n[0]-h)/o;u=e-(n[1]-d)/l;[r,a]=[-r,-a];break;case 270:c=t+(n[0]-h-this.height*l)/o;u=e+(n[1]-d-this.width*o)/l;[r,a]=[-a,r]}this.setAt(c*i,u*s,r,a)}else this.setAt(t*i,e*s,this.width*i,this.height*s);this.#jt();this._isDraggable=!0;this.editorDiv.contentEditable=!1}else{this._isDraggable=!1;this.editorDiv.contentEditable=!0}return this.div}static#zt(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(a,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=FreeTextEditor.#Vt(e.getData("text")||"").replaceAll(a,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize();n.deleteFromDocument();const r=n.getRangeAt(0);if(!s.includes("\n")){r.insertNode(document.createTextNode(s));this.editorDiv.normalize();n.collapseToStart();return}const{startContainer:o,startOffset:l}=r,h=[],d=[];if(o.nodeType===Node.TEXT_NODE){const t=o.parentElement;d.push(o.nodeValue.slice(l).replaceAll(a,""));if(t!==this.editorDiv){let e=h;for(const i of this.editorDiv.childNodes)i!==t?e.push(FreeTextEditor.#zt(i)):e=d}h.push(o.nodeValue.slice(0,l).replaceAll(a,""))}else if(o===this.editorDiv){let t=h,e=0;for(const i of this.editorDiv.childNodes){e++===l&&(t=d);t.push(FreeTextEditor.#zt(i))}}this.#Dt=`${h.join("\n")}${s}${d.join("\n")}`;this.#jt();const c=new Range;let u=h.reduce(((t,e)=>t+e.length),0);for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(u<=e){c.setStart(t,u);c.setEnd(t,u);break}u-=e}n.removeAllRanges();n.addRange(c)}#jt(){this.editorDiv.replaceChildren();if(this.#Dt)for(const t of this.#Dt.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}}#Gt(){return this.#Dt.replaceAll(" "," ")}static#Vt(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static deserialize(t,e,s){let n=null;if(t instanceof r.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:s},rect:r,rotation:a,id:o},textContent:l,textPosition:h,parent:{page:{pageNumber:d}}}=t;if(!l||0===l.length)return null;n=t={annotationType:i.AnnotationEditorType.FREETEXT,color:Array.from(s),fontSize:e,value:l.join("\n"),position:h,pageIndex:d-1,rect:r.slice(0),rotation:a,id:o,deleted:!1}}const a=super.deserialize(t,e,s);a.#Lt=t.fontSize;a.#g=i.Util.makeHexColor(...t.color);a.#Dt=FreeTextEditor.#Vt(t.value);a.annotationElementId=t.id||null;a.#Ot=n;return a}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const e=FreeTextEditor._internalPadding*this.parentScale,n=this.getRect(e,e),r=s.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#g),a={annotationType:i.AnnotationEditorType.FREETEXT,color:r,fontSize:this.#Lt,value:this.#Gt(),pageIndex:this.pageIndex,rect:n,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t)return a;if(this.annotationElementId&&!this.#Wt(a))return null;a.id=this.annotationElementId;return a}#Wt(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this.#Ot;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#Lt}px * var(--scale-factor))`;i.color=this.#g;e.replaceChildren();for(const t of this.#Dt.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br"));e.append(i)}const s=FreeTextEditor._internalPadding*this.parentScale;t.updateEdited({rect:this.getRect(s,s)});return e}resetAnnotationElement(t){super.resetAnnotationElement(t);t.resetEdited()}}var o=e(4061),l=e(2259),h=e(5419);class HighlightEditor extends s.AnnotationEditor{#$t=null;#qt=0;#Kt;#Xt=null;#Yt=null;#Jt=null;#Qt=null;#Zt=0;#te=null;#ee=null;#gt=null;#ie=!1;#se=this.#ne.bind(this);#re=null;#ae;#oe=null;#le="";#he;#de="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _l10nPromise;static _type="highlight";static _editorType=i.AnnotationEditorType.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=HighlightEditor.prototype;return(0,i.shadow)(this,"_keyboardManager",new n.KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"});this.color=t.color||HighlightEditor._defaultColor;this.#he=t.thickness||HighlightEditor._defaultThickness;this.#ae=t.opacity||HighlightEditor._defaultOpacity;this.#Kt=t.boxes||null;this.#de=t.methodOfCreation||"";this.#le=t.text||"";this._isDraggable=!1;if(t.highlightId>-1){this.#ie=!0;this.#ce(t);this.#ue()}else{this.#$t=t.anchorNode;this.#qt=t.anchorOffset;this.#Qt=t.focusNode;this.#Zt=t.focusOffset;this.#pe();this.#ue();this.rotate(this.rotation)}}get telemetryInitialData(){return{action:"added",type:this.#ie?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#he,methodOfCreation:this.#de}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#pe(){const t=new o.Outliner(this.#Kt,.001);this.#ee=t.getOutlines();({x:this.x,y:this.y,width:this.width,height:this.height}=this.#ee.box);const e=new o.Outliner(this.#Kt,.0025,.001,"ltr"===this._uiManager.direction);this.#Jt=e.getOutlines();const{lastPoint:i}=this.#Jt.box;this.#re=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#ce({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#ee=t;this.#Jt=t.getNewOutline(this.#he/2****,.0025);if(e>=0){this.#gt=e;this.#Xt=i;this.parent.drawLayer.finalizeLine(e,t);this.#oe=this.parent.drawLayer.highlightOutline(this.#Jt)}else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateLine(this.#gt,t);this.parent.drawLayer.updateBox(this.#gt,HighlightEditor.#ge(this.#ee.box,(e-this.rotation+360)%360));this.parent.drawLayer.updateLine(this.#oe,this.#Jt);this.parent.drawLayer.updateBox(this.#oe,HighlightEditor.#ge(this.#Jt.box,e))}const{x:s,y:n,width:r,height:a}=t.box;switch(this.rotation){case 0:this.x=s;this.y=n;this.width=r;this.height=a;break;case 90:{const[t,e]=this.parentDimensions;this.x=n;this.y=1-s;this.width=r*e/t;this.height=a*t/e;break}case 180:this.x=1-s;this.y=1-n;this.width=r;this.height=a;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n;this.y=s;this.width=r*e/t;this.height=a*t/e;break}}const{lastPoint:o}=this.#Jt.box;this.#re=[(o[0]-s)/r,(o[1]-n)/a]}static initialize(t,e){s.AnnotationEditor.initialize(t,e);HighlightEditor._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case i.AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR:HighlightEditor._defaultColor=e;break;case i.AnnotationEditorParamsType.HIGHLIGHT_THICKNESS:HighlightEditor._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#re}updateParams(t,e){switch(t){case i.AnnotationEditorParamsType.HIGHLIGHT_COLOR:this.#Bt(e);break;case i.AnnotationEditorParamsType.HIGHLIGHT_THICKNESS:this.#fe(e)}}static get defaultPropertiesToUpdate(){return[[i.AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR,HighlightEditor._defaultColor],[i.AnnotationEditorParamsType.HIGHLIGHT_THICKNESS,HighlightEditor._defaultThickness]]}get propertiesToUpdate(){return[[i.AnnotationEditorParamsType.HIGHLIGHT_COLOR,this.color||HighlightEditor._defaultColor],[i.AnnotationEditorParamsType.HIGHLIGHT_THICKNESS,this.#he||HighlightEditor._defaultThickness],[i.AnnotationEditorParamsType.HIGHLIGHT_FREE,this.#ie]]}#Bt(t){const setColor=t=>{this.color=t;this.parent?.drawLayer.changeColor(this.#gt,t);this.#Yt?.updateColor(t)},e=this.color;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#fe(t){const e=this.#he,setThickness=t=>{this.#he=t;this.#me(t)};this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();if(!t)return null;if(this._uiManager.highlightColors){this.#Yt=new l.ColorPicker({editor:this});t.addColorPicker(this.#Yt)}return t}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#be())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#be())}onceAdded(){this.parent.addUndoableEditor(this);this.div.focus()}remove(){this.#ve();this._reportTelemetry({action:"deleted"});super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#ue();this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t)this.#ve();else if(t){this.#ue(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);this.show(this._isVisible);e&&this.select()}#me(t){if(!this.#ie)return;this.#ce({highlightOutlines:this.#ee.getNewOutline(t/2)});this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#ve(){if(null!==this.#gt&&this.parent){this.parent.drawLayer.remove(this.#gt);this.#gt=null;this.parent.drawLayer.remove(this.#oe);this.#oe=null}}#ue(t=this.parent){if(null===this.#gt){({id:this.#gt,clipPathId:this.#Xt}=t.drawLayer.highlight(this.#ee,this.color,this.#ae));this.#oe=t.drawLayer.highlightOutline(this.#Jt);this.#te&&(this.#te.style.clipPath=this.#Xt)}}static#ge({x:t,y:e,width:i,height:s},n){switch(n){case 90:return{x:1-e-s,y:t,width:s,height:i};case 180:return{x:1-t-i,y:1-e-s,width:i,height:s};case 270:return{x:e,y:1-t-i,width:s,height:i}}return{x:t,y:e,width:i,height:s}}rotate(t){const{drawLayer:e}=this.parent;let i;if(this.#ie){t=(t-this.rotation+360)%360;i=HighlightEditor.#ge(this.#ee.box,t)}else i=HighlightEditor.#ge(this,t);e.rotate(this.#gt,t);e.rotate(this.#oe,t);e.updateBox(this.#gt,i);e.updateBox(this.#oe,HighlightEditor.#ge(this.#Jt.box,t))}render(){if(this.div)return this.div;const t=super.render();if(this.#le){t.setAttribute("aria-label",this.#le);t.setAttribute("role","mark")}this.#ie?t.classList.add("free"):this.div.addEventListener("keydown",this.#se);const e=this.#te=document.createElement("div");t.append(e);e.setAttribute("aria-hidden","true");e.className="internal";e.style.clipPath=this.#Xt;const[i,s]=this.parentDimensions;this.setDims(this.width*i,this.height*s);(0,n.bindEvents)(this,this.#te,["pointerover","pointerleave"]);this.enableEditing();return t}pointerover(){this.parent.drawLayer.addClass(this.#oe,"hovered")}pointerleave(){this.parent.drawLayer.removeClass(this.#oe,"hovered")}#ne(t){HighlightEditor._keyboardManager.exec(this,t)}_moveCaret(t){this.parent.unselect(this);switch(t){case 0:case 2:this.#ye(!0);break;case 1:case 3:this.#ye(!1)}}#ye(t){if(!this.#$t)return;const e=window.getSelection();t?e.setPosition(this.#$t,this.#qt):e.setPosition(this.#Qt,this.#Zt)}select(){super.select();if(this.#oe){this.parent?.drawLayer.removeClass(this.#oe,"hovered");this.parent?.drawLayer.addClass(this.#oe,"selected")}}unselect(){super.unselect();if(this.#oe){this.parent?.drawLayer.removeClass(this.#oe,"selected");this.#ie||this.#ye(!1)}}get _mustFixPosition(){return!this.#ie}show(t=this._isVisible){super.show(t);if(this.parent){this.parent.drawLayer.show(this.#gt,t);this.parent.drawLayer.show(this.#oe,t)}}#be(){return this.#ie?this.rotation:0}#Ae(){if(this.#ie)return null;const[t,e]=this.pageDimensions,i=this.#Kt,s=new Array(8*i.length);let n=0;for(const{x:r,y:a,width:o,height:l}of i){const i=r*t,h=(1-a-l)*e;s[n]=s[n+4]=i;s[n+1]=s[n+3]=h;s[n+2]=s[n+6]=i+o*t;s[n+5]=s[n+7]=h+l*e;n+=8}return s}#Ee(t){return this.#ee.serialize(t,this.#be())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:r,y:a,width:l,height:d}=i.getBoundingClientRect(),pointerMove=e=>{this.#we(t,e)},c={capture:!0,passive:!1},pointerDown=t=>{t.preventDefault();t.stopPropagation()},pointerUpCallback=e=>{i.removeEventListener("pointermove",pointerMove);window.removeEventListener("blur",pointerUpCallback);window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("pointerdown",pointerDown,c);window.removeEventListener("contextmenu",h.noContextMenu);this.#xe(t,e)};window.addEventListener("blur",pointerUpCallback);window.addEventListener("pointerup",pointerUpCallback);window.addEventListener("pointerdown",pointerDown,c);window.addEventListener("contextmenu",h.noContextMenu);i.addEventListener("pointermove",pointerMove);this._freeHighlight=new o.FreeOutliner({x:s,y:n},[r,a,l,d],t.scale,this._defaultThickness/2,e,.001);({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.highlight(this._freeHighlight,this._defaultColor,this._defaultOpacity,!0))}static#we(t,e){this._freeHighlight.add(e)&&t.drawLayer.updatePath(this._freeHighlightId,this._freeHighlight)}static#xe(t,e){this._freeHighlight.isEmpty()?t.drawLayer.removeFreeHighlight(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"});this._freeHighlightId=-1;this._freeHighlight=null;this._freeHighlightClipId=""}static deserialize(t,e,s){const n=super.deserialize(t,e,s),{rect:[r,a,o,l],color:h,quadPoints:d}=t;n.color=i.Util.makeHexColor(...h);n.#ae=t.opacity;const[c,u]=n.pageDimensions;n.width=(o-r)/c;n.height=(l-a)/u;const p=n.#Kt=[];for(let t=0;t<d.length;t+=8)p.push({x:(d[4]-o)/c,y:(l-(1-d[t+5]))/u,width:(d[t+2]-d[t])/c,height:(d[t+5]-d[t+1])/u});n.#pe();return n}serialize(t=!1){if(this.isEmpty()||t)return null;const e=this.getRect(0,0),n=s.AnnotationEditor._colorManager.convert(this.color);return{annotationType:i.AnnotationEditorType.HIGHLIGHT,color:n,opacity:this.#ae,thickness:this.#he,quadPoints:this.#Ae(),outlines:this.#Ee(e),pageIndex:this.pageIndex,rect:e,rotation:this.#be(),structTreeParentId:this._structTreeParentId}}static canCreateNewEmptyEditor(){return!1}}class InkEditor extends s.AnnotationEditor{#_e=0;#Se=0;#Te=this.canvasPointermove.bind(this);#Ce=this.canvasPointerleave.bind(this);#Me=this.canvasPointerup.bind(this);#Pe=this.canvasPointerdown.bind(this);#Re=null;#ke=new Path2D;#Fe=!1;#De=!1;#Ie=!1;#Le=null;#Oe=0;#Ne=0;#Be=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _type="ink";static _editorType=i.AnnotationEditorType.INK;constructor(t){super({...t,name:"inkEditor"});this.color=t.color||null;this.thickness=t.thickness||null;this.opacity=t.opacity||null;this.paths=[];this.bezierPath2D=[];this.allRawPaths=[];this.currentPath=[];this.scaleFactor=1;this.translationX=this.translationY=0;this.x=0;this.y=0;this._willKeepAspectRatio=!0}static initialize(t,e){s.AnnotationEditor.initialize(t,e)}static updateDefaultParams(t,e){switch(t){case i.AnnotationEditorParamsType.INK_THICKNESS:InkEditor._defaultThickness=e;break;case i.AnnotationEditorParamsType.INK_COLOR:InkEditor._defaultColor=e;break;case i.AnnotationEditorParamsType.INK_OPACITY:InkEditor._defaultOpacity=e/100}}updateParams(t,e){switch(t){case i.AnnotationEditorParamsType.INK_THICKNESS:this.#fe(e);break;case i.AnnotationEditorParamsType.INK_COLOR:this.#Bt(e);break;case i.AnnotationEditorParamsType.INK_OPACITY:this.#He(e)}}static get defaultPropertiesToUpdate(){return[[i.AnnotationEditorParamsType.INK_THICKNESS,InkEditor._defaultThickness],[i.AnnotationEditorParamsType.INK_COLOR,InkEditor._defaultColor||s.AnnotationEditor._defaultLineColor],[i.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*InkEditor._defaultOpacity)]]}get propertiesToUpdate(){return[[i.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||InkEditor._defaultThickness],[i.AnnotationEditorParamsType.INK_COLOR,this.color||InkEditor._defaultColor||s.AnnotationEditor._defaultLineColor],[i.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??InkEditor._defaultOpacity))]]}#fe(t){const setThickness=t=>{this.thickness=t;this.#Ue()},e=this.thickness;this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#Bt(t){const setColor=t=>{this.color=t;this.#ze()},e=this.color;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#He(t){const setOpacity=t=>{this.opacity=t;this.#ze()};t/=100;const e=this.opacity;this.addCommands({cmd:setOpacity.bind(this,t),undo:setOpacity.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:i.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){if(!this.canvas){this.#je();this.#Ve()}if(!this.isAttachedToDOM){this.parent.add(this);this.#Ge()}this.#Ue()}}}remove(){if(null!==this.canvas){this.isEmpty()||this.commit();this.canvas.width=this.canvas.height=0;this.canvas.remove();this.canvas=null;if(this.#Re){clearTimeout(this.#Re);this.#Re=null}this.#Le.disconnect();this.#Le=null;super.remove()}}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this);super.setParent(t)}onScaleChanging(){const[t,e]=this.parentDimensions,i=this.width*t,s=this.height*e;this.setDimensions(i,s)}enableEditMode(){if(!this.#Fe&&null!==this.canvas){super.enableEditMode();this._isDraggable=!1;this.canvas.addEventListener("pointerdown",this.#Pe)}}disableEditMode(){if(this.isInEditMode()&&null!==this.canvas){super.disableEditMode();this._isDraggable=!this.isEmpty();this.div.classList.remove("editing");this.canvas.removeEventListener("pointerdown",this.#Pe)}}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#We(){const{parentRotation:t,parentDimensions:[e,i]}=this;switch(t){case 90:return[0,i,i,e];case 180:return[e,i,e,i];case 270:return[e,0,i,e];default:return[0,0,e,i]}}#$e(){const{ctx:t,color:e,opacity:i,thickness:s,parentScale:r,scaleFactor:a}=this;t.lineWidth=s*r/a;t.lineCap="round";t.lineJoin="round";t.miterLimit=10;t.strokeStyle=`${e}${(0,n.opacityToHex)(i)}`}#qe(t,e){this.canvas.addEventListener("contextmenu",h.noContextMenu);this.canvas.addEventListener("pointerleave",this.#Ce);this.canvas.addEventListener("pointermove",this.#Te);this.canvas.addEventListener("pointerup",this.#Me);this.canvas.removeEventListener("pointerdown",this.#Pe);this.isEditing=!0;if(!this.#Ie){this.#Ie=!0;this.#Ge();this.thickness||=InkEditor._defaultThickness;this.color||=InkEditor._defaultColor||s.AnnotationEditor._defaultLineColor;this.opacity??=InkEditor._defaultOpacity}this.currentPath.push([t,e]);this.#De=!1;this.#$e();this.#Be=()=>{this.#Ke();this.#Be&&window.requestAnimationFrame(this.#Be)};window.requestAnimationFrame(this.#Be)}#Xe(t,e){const[i,s]=this.currentPath.at(-1);if(this.currentPath.length>1&&t===i&&e===s)return;const n=this.currentPath;let r=this.#ke;n.push([t,e]);this.#De=!0;if(n.length<=2){r.moveTo(...n[0]);r.lineTo(t,e)}else{if(3===n.length){this.#ke=r=new Path2D;r.moveTo(...n[0])}this.#Ye(r,...n.at(-3),...n.at(-2),t,e)}}#Je(){if(0===this.currentPath.length)return;const t=this.currentPath.at(-1);this.#ke.lineTo(...t)}#Qe(t,e){this.#Be=null;t=Math.min(Math.max(t,0),this.canvas.width);e=Math.min(Math.max(e,0),this.canvas.height);this.#Xe(t,e);this.#Je();let i;if(1!==this.currentPath.length)i=this.#Ze();else{const s=[t,e];i=[[s,s.slice(),s.slice(),s]]}const s=this.#ke,n=this.currentPath;this.currentPath=[];this.#ke=new Path2D;this.addCommands({cmd:()=>{this.allRawPaths.push(n);this.paths.push(i);this.bezierPath2D.push(s);this._uiManager.rebuild(this)},undo:()=>{this.allRawPaths.pop();this.paths.pop();this.bezierPath2D.pop();if(0===this.paths.length)this.remove();else{if(!this.canvas){this.#je();this.#Ve()}this.#Ue()}},mustExec:!0})}#Ke(){if(!this.#De)return;this.#De=!1;const t=Math.ceil(this.thickness*this.parentScale),e=this.currentPath.slice(-3),i=e.map((t=>t[0])),s=e.map((t=>t[1])),{ctx:n}=(Math.min(...i),Math.max(...i),Math.min(...s),Math.max(...s),this);n.save();n.clearRect(0,0,this.canvas.width,this.canvas.height);for(const t of this.bezierPath2D)n.stroke(t);n.stroke(this.#ke);n.restore()}#Ye(t,e,i,s,n,r,a){const o=(e+s)/2,l=(i+n)/2,h=(s+r)/2,d=(n+a)/2;t.bezierCurveTo(o+2*(s-o)/3,l+2*(n-l)/3,h+2*(s-h)/3,d+2*(n-d)/3,h,d)}#Ze(){const t=this.currentPath;if(t.length<=2)return[[t[0],t[0],t.at(-1),t.at(-1)]];const e=[];let i,[s,n]=t[0];for(i=1;i<t.length-2;i++){const[r,a]=t[i],[o,l]=t[i+1],h=(r+o)/2,d=(a+l)/2,c=[s+2*(r-s)/3,n+2*(a-n)/3],u=[h+2*(r-h)/3,d+2*(a-d)/3];e.push([[s,n],c,u,[h,d]]);[s,n]=[h,d]}const[r,a]=t[i],[o,l]=t[i+1],h=[s+2*(r-s)/3,n+2*(a-n)/3],d=[o+2*(r-o)/3,l+2*(a-l)/3];e.push([[s,n],h,d,[o,l]]);return e}#ze(){if(this.isEmpty()){this.#ti();return}this.#$e();const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,t.width,t.height);this.#ti();for(const t of this.bezierPath2D)e.stroke(t)}commit(){if(!this.#Fe){super.commit();this.isEditing=!1;this.disableEditMode();this.setInForeground();this.#Fe=!0;this.div.classList.add("disabled");this.#Ue(!0);this.select();this.parent.addInkEditorIfNeeded(!0);this.moveInDOM();this.div.focus({preventScroll:!0})}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);this.enableEditMode()}}canvasPointerdown(t){if(0===t.button&&this.isInEditMode()&&!this.#Fe){this.setInForeground();t.preventDefault();this.div.contains(document.activeElement)||this.div.focus({preventScroll:!0});this.#qe(t.offsetX,t.offsetY)}}canvasPointermove(t){t.preventDefault();this.#Xe(t.offsetX,t.offsetY)}canvasPointerup(t){t.preventDefault();this.#ei(t)}canvasPointerleave(t){this.#ei(t)}#ei(t){this.canvas.removeEventListener("pointerleave",this.#Ce);this.canvas.removeEventListener("pointermove",this.#Te);this.canvas.removeEventListener("pointerup",this.#Me);this.canvas.addEventListener("pointerdown",this.#Pe);this.#Re&&clearTimeout(this.#Re);this.#Re=setTimeout((()=>{this.#Re=null;this.canvas.removeEventListener("contextmenu",h.noContextMenu)}),10);this.#Qe(t.offsetX,t.offsetY);this.addToAnnotationStorage();this.setInBackground()}#je(){this.canvas=document.createElement("canvas");this.canvas.width=this.canvas.height=0;this.canvas.className="inkEditorCanvas";this.canvas.setAttribute("data-l10n-id","pdfjs-ink-canvas");this.div.append(this.canvas);this.ctx=this.canvas.getContext("2d")}#Ve(){this.#Le=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)}));this.#Le.observe(this.div)}get isResizable(){return!this.isEmpty()&&this.#Fe}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.div.setAttribute("data-l10n-id","pdfjs-ink");const[i,s,n,r]=this.#We();this.setAt(i,s,0,0);this.setDims(n,r);this.#je();if(this.width){const[i,s]=this.parentDimensions;this.setAspectRatio(this.width*i,this.height*s);this.setAt(t*i,e*s,this.width*i,this.height*s);this.#Ie=!0;this.#Ge();this.setDims(this.width*i,this.height*s);this.#ze();this.div.classList.add("disabled")}else{this.div.classList.add("editing");this.enableEditMode()}this.#Ve();return this.div}#Ge(){if(!this.#Ie)return;const[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t);this.canvas.height=Math.ceil(this.height*e);this.#ti()}setDimensions(t,e){const i=Math.round(t),s=Math.round(e);if(this.#Oe===i&&this.#Ne===s)return;this.#Oe=i;this.#Ne=s;this.canvas.style.visibility="hidden";const[n,r]=this.parentDimensions;this.width=t/n;this.height=e/r;this.fixAndSetPosition();this.#Fe&&this.#ii(t,e);this.#Ge();this.#ze();this.canvas.style.visibility="visible";this.fixDims()}#ii(t,e){const i=this.#si(),s=(t-i)/this.#Se,n=(e-i)/this.#_e;this.scaleFactor=Math.min(s,n)}#ti(){const t=this.#si()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static#ni(t){const e=new Path2D;for(let i=0,s=t.length;i<s;i++){const[s,n,r,a]=t[i];0===i&&e.moveTo(...s);e.bezierCurveTo(n[0],n[1],r[0],r[1],a[0],a[1])}return e}static#ri(t,e,i){const[s,n,r,a]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2){t[e]+=s;t[e+1]=a-t[e+1]}break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]+s;t[e+1]=i+n}break;case 180:for(let e=0,i=t.length;e<i;e+=2){t[e]=r-t[e];t[e+1]+=n}break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=r-t[e+1];t[e+1]=a-i}break;default:throw new Error("Invalid rotation")}return t}static#ai(t,e,i){const[s,n,r,a]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2){t[e]-=s;t[e+1]=a-t[e+1]}break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]-n;t[e+1]=i-s}break;case 180:for(let e=0,i=t.length;e<i;e+=2){t[e]=r-t[e];t[e+1]-=n}break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=a-t[e+1];t[e+1]=r-i}break;default:throw new Error("Invalid rotation")}return t}#oi(t,e,i,s){const n=[],r=this.thickness/2,a=t*e+r,o=t*i+r;for(const e of this.paths){const i=[],r=[];for(let s=0,n=e.length;s<n;s++){const[l,h,d,c]=e[s];if(l[0]===c[0]&&l[1]===c[1]&&1===n){const e=t*l[0]+a,s=t*l[1]+o;i.push(e,s);r.push(e,s);break}const u=t*l[0]+a,p=t*l[1]+o,g=t*h[0]+a,f=t*h[1]+o,m=t*d[0]+a,b=t*d[1]+o,v=t*c[0]+a,y=t*c[1]+o;if(0===s){i.push(u,p);r.push(u,p)}i.push(g,f,m,b,v,y);r.push(g,f);s===n-1&&r.push(v,y)}n.push({bezier:InkEditor.#ri(i,s,this.rotation),points:InkEditor.#ri(r,s,this.rotation)})}return n}#li(){let t=1/0,e=-1/0,s=1/0,n=-1/0;for(const r of this.paths)for(const[a,o,l,h]of r){const r=i.Util.bezierBoundingBox(...a,...o,...l,...h);t=Math.min(t,r[0]);s=Math.min(s,r[1]);e=Math.max(e,r[2]);n=Math.max(n,r[3])}return[t,s,e,n]}#si(){return this.#Fe?Math.ceil(this.thickness*this.parentScale):0}#Ue(t=!1){if(this.isEmpty())return;if(!this.#Fe){this.#ze();return}const e=this.#li(),i=this.#si();this.#Se=Math.max(s.AnnotationEditor.MIN_SIZE,e[2]-e[0]);this.#_e=Math.max(s.AnnotationEditor.MIN_SIZE,e[3]-e[1]);const n=Math.ceil(i+this.#Se*this.scaleFactor),r=Math.ceil(i+this.#_e*this.scaleFactor),[a,o]=this.parentDimensions;this.width=n/a;this.height=r/o;this.setAspectRatio(n,r);const l=this.translationX,h=this.translationY;this.translationX=-e[0];this.translationY=-e[1];this.#Ge();this.#ze();this.#Oe=n;this.#Ne=r;this.setDims(n,r);const d=t?i/this.scaleFactor/2:0;this.translate(l-this.translationX-d,h-this.translationY-d)}static deserialize(t,e,n){if(t instanceof r.InkAnnotationElement)return null;const a=super.deserialize(t,e,n);a.thickness=t.thickness;a.color=i.Util.makeHexColor(...t.color);a.opacity=t.opacity;const[o,l]=a.pageDimensions,h=a.width*o,d=a.height*l,c=a.parentScale,u=t.thickness/2;a.#Fe=!0;a.#Oe=Math.round(h);a.#Ne=Math.round(d);const{paths:p,rect:g,rotation:f}=t;for(let{bezier:t}of p){t=InkEditor.#ai(t,g,f);const e=[];a.paths.push(e);let i=c*(t[0]-u),s=c*(t[1]-u);for(let n=2,r=t.length;n<r;n+=6){const r=c*(t[n]-u),a=c*(t[n+1]-u),o=c*(t[n+2]-u),l=c*(t[n+3]-u),h=c*(t[n+4]-u),d=c*(t[n+5]-u);e.push([[i,s],[r,a],[o,l],[h,d]]);i=h;s=d}const n=this.#ni(e);a.bezierPath2D.push(n)}const m=a.#li();a.#Se=Math.max(s.AnnotationEditor.MIN_SIZE,m[2]-m[0]);a.#_e=Math.max(s.AnnotationEditor.MIN_SIZE,m[3]-m[1]);a.#ii(h,d);return a}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=s.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:i.AnnotationEditorType.INK,color:e,thickness:this.thickness,opacity:this.opacity,paths:this.#oi(this.scaleFactor/this.parentScale,this.translationX,this.translationY,t),pageIndex:this.pageIndex,rect:t,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}e(4520),e(4979);class StampEditor extends s.AnnotationEditor{#hi=null;#di=null;#ci=null;#ui=null;#pi=null;#gi="";#fi=null;#Le=null;#mi=null;#bi=!1;#vi=!1;static _type="stamp";static _editorType=i.AnnotationEditorType.STAMP;constructor(t){super({...t,name:"stampEditor"});this.#ui=t.bitmapUrl;this.#pi=t.bitmapFile}static initialize(t,e){s.AnnotationEditor.initialize(t,e)}static get supportedTypes(){return(0,i.shadow)(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map((t=>`image/${t}`)))}static get supportedTypesStr(){return(0,i.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(i.AnnotationEditorType.STAMP,{bitmapFile:t.getAsFile()})}#yi(t,e=!1){if(t){this.#hi=t.bitmap;if(!e){this.#di=t.id;this.#bi=t.isSvg}t.file&&(this.#gi=t.file.name);this.#je()}else this.remove()}#Ai(){this.#ci=null;this._uiManager.enableWaiting(!1);this.#fi&&this.div.focus()}#Ei(){if(this.#di){this._uiManager.enableWaiting(!0);this._uiManager.imageManager.getFromId(this.#di).then((t=>this.#yi(t,!0))).finally((()=>this.#Ai()));return}if(this.#ui){const t=this.#ui;this.#ui=null;this._uiManager.enableWaiting(!0);this.#ci=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#yi(t))).finally((()=>this.#Ai()));return}if(this.#pi){const t=this.#pi;this.#pi=null;this._uiManager.enableWaiting(!0);this.#ci=this._uiManager.imageManager.getFromFile(t).then((t=>this.#yi(t))).finally((()=>this.#Ai()));return}const t=document.createElement("input");t.type="file";t.accept=StampEditor.supportedTypesStr;this.#ci=new Promise((e=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this.#yi(e)}else this.remove();e()}));t.addEventListener("cancel",(()=>{this.remove();e()}))})).finally((()=>this.#Ai()));t.click()}remove(){if(this.#di){this.#hi=null;this._uiManager.imageManager.deleteId(this.#di);this.#fi?.remove();this.#fi=null;this.#Le?.disconnect();this.#Le=null;if(this.#mi){clearTimeout(this.#mi);this.#mi=null}}super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#di&&null===this.#fi&&this.#Ei();this.isAttachedToDOM||this.parent.add(this)}}else this.#di&&this.#Ei()}onceAdded(){this._isDraggable=!0;this.div.focus()}isEmpty(){return!(this.#ci||this.#hi||this.#ui||this.#pi||this.#di)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.div.hidden=!0;this.addAltTextButton();this.#hi?this.#je():this.#Ei();if(this.width){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s)}return this.div}#je(){const{div:t}=this;let{width:e,height:i}=this.#hi;const[s,n]=this.pageDimensions,r=.75;if(this.width){e=this.width*s;i=this.height*n}else if(e>r*s||i>r*n){const t=Math.min(r*s/e,r*n/i);e*=t;i*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/s,i*o/n);this._uiManager.enableWaiting(!1);const l=this.#fi=document.createElement("canvas");t.append(l);t.hidden=!1;this.#wi(e,i);this.#Ve();if(!this.#vi){this.parent.addUndoableEditor(this);this.#vi=!0}this._reportTelemetry({action:"inserted_image"});this.#gi&&l.setAttribute("aria-label",this.#gi)}#xi(t,e){const[i,s]=this.parentDimensions;this.width=t/i;this.height=e/s;this.setDims(t,e);this._initialOptions?.isCentered?this.center():this.fixAndSetPosition();this._initialOptions=null;null!==this.#mi&&clearTimeout(this.#mi);this.#mi=setTimeout((()=>{this.#mi=null;this.#wi(t,e)}),200)}#_i(t,e){const{width:i,height:s}=this.#hi;let n=i,r=s,a=this.#hi;for(;n>2*t||r>2*e;){const i=n,s=r;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2));r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const o=new OffscreenCanvas(n,r);o.getContext("2d").drawImage(a,0,0,i,s,0,0,n,r);a=o.transferToImageBitmap()}return a}#wi(t,e){t=Math.ceil(t);e=Math.ceil(e);const i=this.#fi;if(!i||i.width===t&&i.height===e)return;i.width=t;i.height=e;const s=this.#bi?this.#hi:this.#_i(t,e);if(this._uiManager.hasMLManager&&!this.hasAltText()){const i=new OffscreenCanvas(t,e);i.getContext("2d").drawImage(s,0,0,s.width,s.height,0,0,t,e);i.convertToBlob().then((t=>{const e=new FileReader;e.onload=()=>{const t=e.result;this._uiManager.mlGuess({service:"image-to-text",request:{imageData:t}}).then((t=>{const e=t?.output||"";this.parent&&e&&!this.hasAltText()&&(this.altTextData={altText:e,decorative:!1})}))};e.readAsDataURL(t)}))}const n=i.getContext("2d");n.filter=this._uiManager.hcmFilter;n.drawImage(s,0,0,s.width,s.height,0,0,t,e)}getImageForAltText(){return this.#fi}#Si(t){if(t){if(this.#bi){const t=this._uiManager.imageManager.getSvgUrl(this.#di);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#hi);t.getContext("2d").drawImage(this.#hi,0,0);return t.toDataURL()}if(this.#bi){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*h.PixelsPerInch.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*h.PixelsPerInch.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);n.getContext("2d").drawImage(this.#hi,0,0,this.#hi.width,this.#hi.height,0,0,i,s);return n.transferToImageBitmap()}return structuredClone(this.#hi)}#Ve(){this.#Le=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.#xi(e.width,e.height)}));this.#Le.observe(this.div)}static deserialize(t,e,i){if(t instanceof r.StampAnnotationElement)return null;const s=super.deserialize(t,e,i),{rect:n,bitmapUrl:a,bitmapId:o,isSvg:l,accessibilityData:h}=t;o&&i.imageManager.isValidId(o)?s.#di=o:s.#ui=a;s.#bi=l;const[d,c]=s.pageDimensions;s.width=(n[2]-n[0])/d;s.height=(n[3]-n[1])/c;h&&(s.altTextData=h);return s}serialize(t=!1,e=null){if(this.isEmpty())return null;const s={annotationType:i.AnnotationEditorType.STAMP,bitmapId:this.#di,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#bi,structTreeParentId:this._structTreeParentId};if(t){s.bitmapUrl=this.#Si(!0);s.accessibilityData=this.altTextData;return s}const{decorative:n,altText:r}=this.altTextData;!n&&r&&(s.accessibilityData={type:"Figure",alt:r});if(null===e)return s;e.stamps||=new Map;const a=this.#bi?(s.rect[2]-s.rect[0])*(s.rect[3]-s.rect[1]):null;if(e.stamps.has(this.#di)){if(this.#bi){const t=e.stamps.get(this.#di);if(a>t.area){t.area=a;t.serialized.bitmap.close();t.serialized.bitmap=this.#Si(!1)}}}else{e.stamps.set(this.#di,{area:a,serialized:s});s.bitmap=this.#Si(!1)}return s}}class AnnotationEditorLayer{#I;#Ti=!1;#Ci=null;#Mi=null;#Pi=null;#Ri=null;#ki=null;#Fi=new Map;#Di=!1;#Ii=!1;#Li=!1;#Oi=null;#Ni;static _initialized=!1;static#Bi=new Map([FreeTextEditor,InkEditor,StampEditor,HighlightEditor].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,accessibilityManager:s,annotationLayer:n,drawLayer:r,textLayer:a,viewport:o,l10n:l}){const h=[...AnnotationEditorLayer.#Bi.values()];if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;for(const e of h)e.initialize(l,t)}t.registerEditorTypes(h);this.#Ni=t;this.pageIndex=e;this.div=i;this.#I=s;this.#Ci=n;this.viewport=o;this.#Oi=a;this.drawLayer=r;this.#Ni.addLayer(this)}get isEmpty(){return 0===this.#Fi.size}get isInvisible(){return this.isEmpty&&this.#Ni.getMode()===i.AnnotationEditorType.NONE}updateToolbar(t){this.#Ni.updateToolbar(t)}updateMode(t=this.#Ni.getMode()){this.#Hi();switch(t){case i.AnnotationEditorType.NONE:this.disableTextSelection();this.togglePointerEvents(!1);this.toggleAnnotationLayerPointerEvents(!0);this.disableClick();return;case i.AnnotationEditorType.INK:this.addInkEditorIfNeeded(!1);this.disableTextSelection();this.togglePointerEvents(!0);this.disableClick();break;case i.AnnotationEditorType.HIGHLIGHT:this.enableTextSelection();this.togglePointerEvents(!1);this.disableClick();break;default:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of AnnotationEditorLayer.#Bi.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#Oi?.div}addInkEditorIfNeeded(t){if(this.#Ni.getMode()!==i.AnnotationEditorType.INK)return;if(!t)for(const t of this.#Fi.values())if(t.isEmpty()){t.setInBackground();return}this.createAndAddNewEditor({offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(t){this.#Ni.setEditingState(t)}addCommands(t){this.#Ni.addCommands(t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#Ci?.div.classList.toggle("disabled",!t)}enable(){this.div.tabIndex=0;this.togglePointerEvents(!0);const t=new Set;for(const e of this.#Fi.values()){e.enableEditing();e.show(!0);if(e.annotationElementId){this.#Ni.removeChangedExistingAnnotation(e);t.add(e.annotationElementId)}}if(!this.#Ci)return;const e=this.#Ci.getEditableAnnotations();for(const i of e){i.hide();if(this.#Ni.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=this.deserialize(i);if(e){this.addOrRebuild(e);e.enableEditing()}}}disable(){this.#Li=!0;this.div.tabIndex=-1;this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of this.#Fi.values()){i.disableEditing();if(i.annotationElementId)if(null===i.serialize()){e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show();i.remove()}else t.set(i.annotationElementId,i)}if(this.#Ci){const i=this.#Ci.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#Ni.isDeletedAnnotationElement(i))continue;let n=e.get(i);if(n){n.resetAnnotationElement(s);n.show(!1);s.show()}else{n=t.get(i);if(n){this.#Ni.addChangedExistingAnnotation(n);n.renderAnnotationElement(s);n.show(!1)}s.show()}}}this.#Hi();this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of AnnotationEditorLayer.#Bi.values())i.remove(`${t._type}Editing`);this.disableTextSelection();this.toggleAnnotationLayerPointerEvents(!0);this.#Li=!1}getEditableAnnotation(t){return this.#Ci?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#Ni.getActive()!==t&&this.#Ni.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1;if(this.#Oi?.div&&!this.#Ri){this.#Ri=this.#Ui.bind(this);this.#Oi.div.addEventListener("pointerdown",this.#Ri);this.#Oi.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0;if(this.#Oi?.div&&this.#Ri){this.#Oi.div.removeEventListener("pointerdown",this.#Ri);this.#Ri=null;this.#Oi.div.classList.remove("highlighting")}}#Ui(t){this.#Ni.unselectAll();if(t.target===this.#Oi.div){const{isMac:e}=i.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#Ni.showAllEditors("highlight",!0,!0);this.#Oi.div.classList.add("free");HighlightEditor.startHighlighting(this,"ltr"===this.#Ni.direction,t);this.#Oi.div.addEventListener("pointerup",(()=>{this.#Oi.div.classList.remove("free")}),{once:!0});t.preventDefault()}}enableClick(){if(!this.#Pi){this.#Pi=this.pointerdown.bind(this);this.#Mi=this.pointerup.bind(this);this.div.addEventListener("pointerdown",this.#Pi);this.div.addEventListener("pointerup",this.#Mi)}}disableClick(){if(this.#Pi){this.div.removeEventListener("pointerdown",this.#Pi);this.div.removeEventListener("pointerup",this.#Mi);this.#Pi=null;this.#Mi=null}}attach(t){this.#Fi.set(t.id,t);const{annotationElementId:e}=t;e&&this.#Ni.isDeletedAnnotationElement(e)&&this.#Ni.removeDeletedAnnotationElement(t)}detach(t){this.#Fi.delete(t.id);this.#I?.removePointerInTextLayer(t.contentDiv);!this.#Li&&t.annotationElementId&&this.#Ni.addDeletedAnnotationElement(t)}remove(t){this.detach(t);this.#Ni.removeEditor(t);t.div.remove();t.isAttachedToDOM=!1;this.#Ii||this.addInkEditorIfNeeded(!1)}changeParent(t){if(t.parent!==this){if(t.parent&&t.annotationElementId){this.#Ni.addDeletedAnnotationElement(t.annotationElementId);s.AnnotationEditor.deleteAnnotationElement(t);t.annotationElementId=null}this.attach(t);t.parent?.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){if(t.parent!==this||!t.isAttachedToDOM){this.changeParent(t);this.#Ni.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}t.fixAndSetPosition();t.onceAdded();this.#Ni.addToAnnotationStorage(t);t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;if(t.div.contains(e)&&!this.#ki){t._focusEventsAllowed=!1;this.#ki=setTimeout((()=>{this.#ki=null;if(t.div.contains(document.activeElement))t._focusEventsAllowed=!0;else{t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0});e.focus()}}),0)}t._structTreeParentId=this.#I?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){if(t.needsToBeRebuilt()){t.parent||=this;t.rebuild();t.show()}else this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#Ni.getId()}get#zi(){return AnnotationEditorLayer.#Bi.get(this.#Ni.getMode())}#ji(t){const e=this.#zi;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#zi?.canCreateNewEmptyEditor()}pasteEditor(t,e){this.#Ni.updateToolbar(t);this.#Ni.updateMode(t);const{offsetX:i,offsetY:s}=this.#Vi(),n=this.getNextId(),r=this.#ji({parent:this,id:n,x:i,y:s,uiManager:this.#Ni,isCentered:!0,...e});r&&this.add(r)}deserialize(t){return AnnotationEditorLayer.#Bi.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#Ni)||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#ji({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#Ni,isCentered:e,...i});n&&this.add(n);return n}#Vi(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),r=Math.max(0,e),a=(n+Math.min(window.innerWidth,t+i))/2-t,o=(r+Math.min(window.innerHeight,e+s))/2-e,[l,h]=this.viewport.rotation%180==0?[a,o]:[o,a];return{offsetX:l,offsetY:h}}addNewEditor(){this.createAndAddNewEditor(this.#Vi(),!0)}setSelected(t){this.#Ni.setSelected(t)}toggleSelected(t){this.#Ni.toggleSelected(t)}isSelected(t){return this.#Ni.isSelected(t)}unselect(t){this.#Ni.unselect(t)}pointerup(t){const{isMac:e}=i.FeatureTest.platform;if(!(0!==t.button||t.ctrlKey&&e)&&t.target===this.div&&this.#Di){this.#Di=!1;this.#Ti?this.#Ni.getMode()!==i.AnnotationEditorType.STAMP?this.createAndAddNewEditor(t,!1):this.#Ni.unselectAll():this.#Ti=!0}}pointerdown(t){this.#Ni.getMode()===i.AnnotationEditorType.HIGHLIGHT&&this.enableTextSelection();if(this.#Di){this.#Di=!1;return}const{isMac:e}=i.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#Di=!0;const s=this.#Ni.getActive();this.#Ti=!s||s.isEmpty()}findNewParent(t,e,i){const s=this.#Ni.findParent(e,i);if(null===s||s===this)return!1;s.changeParent(t);return!0}destroy(){if(this.#Ni.getActive()?.parent===this){this.#Ni.commitOrRemove();this.#Ni.setActiveEditor(null)}if(this.#ki){clearTimeout(this.#ki);this.#ki=null}for(const t of this.#Fi.values()){this.#I?.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;this.#Fi.clear();this.#Ni.removeLayer(this)}#Hi(){this.#Ii=!0;for(const t of this.#Fi.values())t.isEmpty()&&t.remove();this.#Ii=!1}render({viewport:t}){this.viewport=t;(0,h.setLayerDimensions)(this.div,t);for(const t of this.#Ni.getEditors(this.pageIndex)){this.add(t);t.rebuild()}this.updateMode()}update({viewport:t}){this.#Ni.commitOrRemove();this.#Hi();const e=this.viewport.rotation,i=t.rotation;this.viewport=t;(0,h.setLayerDimensions)(this.div,{rotation:i});if(e!==i)for(const t of this.#Fi.values())t.rotate(i);this.addInkEditorIfNeeded(!1)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#Ni.viewParameters.realScale}}},2259:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{ColorPicker:()=>ColorPicker});var i=e(4292),s=e(7830),n=e(5419);class ColorPicker{#a=this.#o.bind(this);#Gi=this.#Wi.bind(this);#$i=null;#qi=null;#Ki;#Xi=null;#Yi=!1;#Ji=!1;#Qi=null;#Zi;#Ni=null;#ts;static get _keyboardManager(){return(0,i.shadow)(this,"_keyboardManager",new s.KeyboardManager([[["Escape","mac+Escape"],ColorPicker.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ColorPicker.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ColorPicker.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ColorPicker.prototype._moveToPrevious],[["Home","mac+Home"],ColorPicker.prototype._moveToBeginning],[["End","mac+End"],ColorPicker.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){if(t){this.#Ji=!1;this.#ts=i.AnnotationEditorParamsType.HIGHLIGHT_COLOR;this.#Qi=t}else{this.#Ji=!0;this.#ts=i.AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR}this.#Ni=t?._uiManager||e;this.#Zi=this.#Ni._eventBus;this.#Ki=t?.color||this.#Ni?.highlightColors.values().next().value||"#FFFF98"}renderButton(){const t=this.#$i=document.createElement("button");t.className="colorPicker";t.tabIndex="0";t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button");t.setAttribute("aria-haspopup",!0);t.addEventListener("click",this.#es.bind(this));t.addEventListener("keydown",this.#a);const e=this.#qi=document.createElement("span");e.className="swatch";e.setAttribute("aria-hidden",!0);e.style.backgroundColor=this.#Ki;t.append(e);return t}renderMainDropdown(){const t=this.#Xi=this.#is();t.setAttribute("aria-orientation","horizontal");t.setAttribute("aria-labelledby","highlightColorPickerLabel");return t}#is(){const t=document.createElement("div");t.addEventListener("contextmenu",n.noContextMenu);t.className="dropdown";t.role="listbox";t.setAttribute("aria-multiselectable",!1);t.setAttribute("aria-orientation","vertical");t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[e,i]of this.#Ni.highlightColors){const s=document.createElement("button");s.tabIndex="0";s.role="option";s.setAttribute("data-color",i);s.title=e;s.setAttribute("data-l10n-id",`pdfjs-editor-colorpicker-${e}`);const n=document.createElement("span");s.append(n);n.className="swatch";n.style.backgroundColor=i;s.setAttribute("aria-selected",i===this.#Ki);s.addEventListener("click",this.#ss.bind(this,i));t.append(s)}t.addEventListener("keydown",this.#a);return t}#ss(t,e){e.stopPropagation();this.#Zi.dispatch("switchannotationeditorparams",{source:this,type:this.#ts,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#$i){this.#es(t);return}const e=t.target.getAttribute("data-color");e&&this.#ss(e,t)}_moveToNext(t){this.#ns?t.target!==this.#$i?t.target.nextSibling?.focus():this.#Xi.firstChild?.focus():this.#es(t)}_moveToPrevious(t){if(t.target!==this.#Xi?.firstChild&&t.target!==this.#$i){this.#ns||this.#es(t);t.target.previousSibling?.focus()}else this.#ns&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#ns?this.#Xi.firstChild?.focus():this.#es(t)}_moveToEnd(t){this.#ns?this.#Xi.lastChild?.focus():this.#es(t)}#o(t){ColorPicker._keyboardManager.exec(this,t)}#es(t){if(this.#ns){this.hideDropdown();return}this.#Yi=0===t.detail;window.addEventListener("pointerdown",this.#Gi);if(this.#Xi){this.#Xi.classList.remove("hidden");return}const e=this.#Xi=this.#is();this.#$i.append(e)}#Wi(t){this.#Xi?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#Xi?.classList.add("hidden");window.removeEventListener("pointerdown",this.#Gi)}get#ns(){return this.#Xi&&!this.#Xi.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#Ji)if(this.#ns){this.hideDropdown();this.#$i.focus({preventScroll:!0,focusVisible:this.#Yi})}else this.#Qi?.unselect()}updateColor(t){this.#qi&&(this.#qi.style.backgroundColor=t);if(!this.#Xi)return;const e=this.#Ni.highlightColors.values();for(const i of this.#Xi.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#$i?.remove();this.#$i=null;this.#qi=null;this.#Xi?.remove();this.#Xi=null}}},310:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{AnnotationEditor:()=>AnnotationEditor});var i=e(7830),s=e(4292),n=e(5419);class AltText{#rs="";#as=!1;#os=null;#ls=null;#hs=null;#ds=!1;#Qi=null;static _l10nPromise=null;constructor(t){this.#Qi=t}static initialize(t){AltText._l10nPromise||=t}async render(){const t=this.#os=document.createElement("button");t.className="altText";const e=await AltText._l10nPromise.get("pdfjs-editor-alt-text-button-label");t.textContent=e;t.setAttribute("aria-label",e);t.tabIndex="0";t.addEventListener("contextmenu",n.noContextMenu);t.addEventListener("pointerdown",(t=>t.stopPropagation()));const onClick=t=>{t.preventDefault();this.#Qi._uiManager.editAltText(this.#Qi)};t.addEventListener("click",onClick,{capture:!0});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){this.#ds=!0;onClick(e)}}));await this.#cs();return t}finish(){if(this.#os){this.#os.focus({focusVisible:this.#ds});this.#ds=!1}}isEmpty(){return!this.#rs&&!this.#as}get data(){return{altText:this.#rs,decorative:this.#as}}set data({altText:t,decorative:e}){if(this.#rs!==t||this.#as!==e){this.#rs=t;this.#as=e;this.#cs()}}toggle(t=!1){if(this.#os){if(!t&&this.#hs){clearTimeout(this.#hs);this.#hs=null}this.#os.disabled=!t}}destroy(){this.#os?.remove();this.#os=null;this.#ls=null}async#cs(){const t=this.#os;if(!t)return;if(!this.#rs&&!this.#as){t.classList.remove("done");this.#ls?.remove();return}t.classList.add("done");AltText._l10nPromise.get("pdfjs-editor-alt-text-edit-button-label").then((e=>{t.setAttribute("aria-label",e)}));let e=this.#ls;if(!e){this.#ls=e=document.createElement("span");e.className="tooltip";e.setAttribute("role","tooltip");const i=e.id=`alt-text-tooltip-${this.#Qi.id}`;t.setAttribute("aria-describedby",i);const s=100;t.addEventListener("mouseenter",(()=>{this.#hs=setTimeout((()=>{this.#hs=null;this.#ls.classList.add("show");this.#Qi._reportTelemetry({action:"alt_text_tooltip"})}),s)}));t.addEventListener("mouseleave",(()=>{if(this.#hs){clearTimeout(this.#hs);this.#hs=null}this.#ls?.classList.remove("show")}))}e.innerText=this.#as?await AltText._l10nPromise.get("pdfjs-editor-alt-text-decorative-tooltip"):this.#rs;e.parentNode||t.append(e);const i=this.#Qi.getImageForAltText();i?.setAttribute("aria-describedby",e.id)}}var r=e(4362);class AnnotationEditor{#us=null;#rs=null;#ps=!1;#gs=!1;#fs=null;#ms=null;#bs=this.focusin.bind(this);#vs=this.focusout.bind(this);#ys=null;#As="";#Es=!1;#ws=null;#xs=!1;#_s=!1;#Ss=!1;#Ts=null;#Cs=0;#Ms=0;#Ps=null;_initialOptions=Object.create(null);_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;_l10nPromise=null;#Rs=!1;#ks=AnnotationEditor._zIndex++;static _borderLineWidth=-1;static _colorManager=new i.ColorManager;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=AnnotationEditor.prototype._resizeWithKeyboard,e=i.AnnotationEditorUIManager.TRANSLATE_SMALL,n=i.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,s.shadow)(this,"_resizerKeyboardManager",new i.KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-n,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[n,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-n]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,n]}],[["Escape","mac+Escape"],AnnotationEditor.prototype._stopResizingWithKeyboard]]))}constructor(t){this.constructor===AnnotationEditor&&(0,s.unreachable)("Cannot initialize AnnotationEditor.");this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;this.annotationElementId=null;this._willKeepAspectRatio=!1;this._initialOptions.isCentered=t.isCentered;this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:n,pageX:r,pageY:a}}=this.parent.viewport;this.rotation=e;this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360;this.pageDimensions=[i,n];this.pageTranslation=[r,a];const[o,l]=this.parentDimensions;this.x=t.x/o;this.y=t.y/l;this.isAttachedToDOM=!1;this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,s.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new FakeEditor({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId;e.deleted=!0;e._uiManager.addToAnnotationStorage(e)}static initialize(t,e,i){AnnotationEditor._l10nPromise||=new Map(["pdfjs-editor-alt-text-button-label","pdfjs-editor-alt-text-edit-button-label","pdfjs-editor-alt-text-decorative-tooltip","pdfjs-editor-resizer-label-topLeft","pdfjs-editor-resizer-label-topMiddle","pdfjs-editor-resizer-label-topRight","pdfjs-editor-resizer-label-middleRight","pdfjs-editor-resizer-label-bottomRight","pdfjs-editor-resizer-label-bottomMiddle","pdfjs-editor-resizer-label-bottomLeft","pdfjs-editor-resizer-label-middleLeft"].map((e=>[e,t.get(e.replaceAll(/([A-Z])/g,(t=>`-${t.toLowerCase()}`)))])));if(i?.strings)for(const e of i.strings)AnnotationEditor._l10nPromise.set(e,t.get(e));if(-1!==AnnotationEditor._borderLineWidth)return;const s=getComputedStyle(document.documentElement);AnnotationEditor._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){(0,s.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#Rs}set _isDraggable(t){this.#Rs=t;this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t);this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2;this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t);this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2;this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#ks}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}else this.#Fs();this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#Es?this.#Es=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;if(!e?.closest(`#${this.id}`)){t.preventDefault();this.parent?.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,r]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s);this.x=(t+i)/n;this.y=(e+s)/r;this.fixAndSetPosition()}#Ds([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s);this.x+=i/t;this.y+=s/e;this.fixAndSetPosition()}translate(t,e){this.#Ds(this.parentDimensions,t,e)}translateInPage(t,e){this.#ws||=[this.x,this.y];this.#Ds(this.pageDimensions,t,e);this.div.scrollIntoView({block:"nearest"})}drag(t,e){this.#ws||=[this.x,this.y];const[i,s]=this.parentDimensions;this.x+=t/i;this.y+=e/s;if(this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();if(this.parent.findNewParent(this,t,e)){this.x-=Math.floor(this.x);this.y-=Math.floor(this.y)}}let{x:n,y:r}=this;const[a,o]=this.getBaseTranslation();n+=a;r+=o;this.div.style.left=`${(100*n).toFixed(2)}%`;this.div.style.top=`${(100*r).toFixed(2)}%`;this.div.scrollIntoView({block:"nearest"})}get _hasBeenMoved(){return!!this.#ws&&(this.#ws[0]!==this.x||this.#ws[1]!==this.y)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=AnnotationEditor,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const[e,i]=this.pageDimensions;let{x:s,y:n,width:r,height:a}=this;r*=e;a*=i;s*=e;n*=i;if(this._mustFixPosition)switch(t){case 0:s=Math.max(0,Math.min(e-r,s));n=Math.max(0,Math.min(i-a,n));break;case 90:s=Math.max(0,Math.min(e-a,s));n=Math.min(i,Math.max(r,n));break;case 180:s=Math.min(e,Math.max(r,s));n=Math.min(i,Math.max(a,n));break;case 270:s=Math.min(e,Math.max(a,s));n=Math.max(0,Math.min(i-r,n))}this.x=s/=e;this.y=n/=i;const[o,l]=this.getBaseTranslation();s+=o;n+=l;const{style:h}=this.div;h.left=`${(100*s).toFixed(2)}%`;h.top=`${(100*n).toFixed(2)}%`;this.moveInDOM()}static#Is(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return AnnotationEditor.#Is(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return AnnotationEditor.#Is(t,e,360-this.parentRotation)}#Ls(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this,n=e*t,r=i*t;return s.FeatureTest.isCSSRoundSupported?[Math.round(n),Math.round(r)]:[n,r]}setDims(t,e){const[i,s]=this.parentDimensions;this.div.style.width=`${(100*t/i).toFixed(2)}%`;this.#gs||(this.div.style.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#gs&&e.endsWith("%");if(s&&n)return;const[r,a]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`);this.#gs||n||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#Os(){if(this.#fs)return;this.#fs=document.createElement("div");this.#fs.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"];for(const e of t){const t=document.createElement("div");this.#fs.append(t);t.classList.add("resizer",e);t.setAttribute("data-resizer-name",e);t.addEventListener("pointerdown",this.#Ns.bind(this,e));t.addEventListener("contextmenu",n.noContextMenu);t.tabIndex=-1}this.div.prepend(this.#fs)}#Ns(t,e){e.preventDefault();const{isMac:i}=s.FeatureTest.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#rs?.toggle(!1);const r=this.#Bs.bind(this,t),a=this._isDraggable;this._isDraggable=!1;const o={passive:!0,capture:!0};this.parent.togglePointerEvents(!1);window.addEventListener("pointermove",r,o);window.addEventListener("contextmenu",n.noContextMenu);const l=this.x,h=this.y,d=this.width,c=this.height,u=this.parent.div.style.cursor,p=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const pointerUpCallback=()=>{this.parent.togglePointerEvents(!0);this.#rs?.toggle(!0);this._isDraggable=a;window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("blur",pointerUpCallback);window.removeEventListener("pointermove",r,o);window.removeEventListener("contextmenu",n.noContextMenu);this.parent.div.style.cursor=u;this.div.style.cursor=p;this.#Hs(l,h,d,c)};window.addEventListener("pointerup",pointerUpCallback);window.addEventListener("blur",pointerUpCallback)}#Hs(t,e,i,s){const n=this.x,r=this.y,a=this.width,o=this.height;n===t&&r===e&&a===i&&o===s||this.addCommands({cmd:()=>{this.width=a;this.height=o;this.x=n;this.y=r;const[t,e]=this.parentDimensions;this.setDims(t*a,e*o);this.fixAndSetPosition()},undo:()=>{this.width=i;this.height=s;this.x=t;this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*i,r*s);this.fixAndSetPosition()},mustExec:!0})}#Bs(t,e){const[i,s]=this.parentDimensions,n=this.x,r=this.y,a=this.width,o=this.height,l=AnnotationEditor.MIN_SIZE/i,h=AnnotationEditor.MIN_SIZE/s,round=t=>Math.round(1e4*t)/1e4,d=this.#Ls(this.rotation),transf=(t,e)=>[d[0]*t+d[2]*e,d[1]*t+d[3]*e],c=this.#Ls(360-this.rotation);let u,p,g=!1,f=!1;switch(t){case"topLeft":g=!0;u=(t,e)=>[0,0];p=(t,e)=>[t,e];break;case"topMiddle":u=(t,e)=>[t/2,0];p=(t,e)=>[t/2,e];break;case"topRight":g=!0;u=(t,e)=>[t,0];p=(t,e)=>[0,e];break;case"middleRight":f=!0;u=(t,e)=>[t,e/2];p=(t,e)=>[0,e/2];break;case"bottomRight":g=!0;u=(t,e)=>[t,e];p=(t,e)=>[0,0];break;case"bottomMiddle":u=(t,e)=>[t/2,e];p=(t,e)=>[t/2,0];break;case"bottomLeft":g=!0;u=(t,e)=>[0,e];p=(t,e)=>[t,0];break;case"middleLeft":f=!0;u=(t,e)=>[0,e/2];p=(t,e)=>[t,e/2]}const m=u(a,o),b=p(a,o);let v=transf(...b);const y=round(n+v[0]),A=round(r+v[1]);let E=1,w=1,[x,_]=this.screenToPageTranslation(e.movementX,e.movementY);[x,_]=(S=x/i,T=_/s,[c[0]*S+c[2]*T,c[1]*S+c[3]*T]);var S,T;if(g){const t=Math.hypot(a,o);E=w=Math.max(Math.min(Math.hypot(b[0]-m[0]-x,b[1]-m[1]-_)/t,1/a,1/o),l/a,h/o)}else f?E=Math.max(l,Math.min(1,Math.abs(b[0]-m[0]-x)))/a:w=Math.max(h,Math.min(1,Math.abs(b[1]-m[1]-_)))/o;const C=round(a*E),M=round(o*w);v=transf(...p(C,M));const P=y-v[0],R=A-v[1];this.width=C;this.height=M;this.x=P;this.y=R;this.setDims(i*C,s*M);this.fixAndSetPosition()}altTextFinish(){this.#rs?.finish()}async addEditToolbar(){if(this.#ys||this.#_s)return this.#ys;this.#ys=new r.EditorToolbar(this);this.div.append(this.#ys.render());this.#rs&&this.#ys.addAltTextButton(await this.#rs.render());return this.#ys}removeEditToolbar(){if(this.#ys){this.#ys.remove();this.#ys=null;this.#rs?.destroy()}}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){if(!this.#rs){AltText.initialize(AnnotationEditor._l10nPromise);this.#rs=new AltText(this);await this.addEditToolbar()}}get altTextData(){return this.#rs?.data}set altTextData(t){this.#rs&&(this.#rs.data=t)}hasAltText(){return!this.#rs?.isEmpty()}render(){this.div=document.createElement("div");this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360);this.div.className=this.name;this.div.setAttribute("id",this.id);this.div.tabIndex=this.#ps?-1:0;this._isVisible||this.div.classList.add("hidden");this.setInForeground();this.div.addEventListener("focusin",this.#bs);this.div.addEventListener("focusout",this.#vs);const[t,e]=this.parentDimensions;if(this.parentRotation%180!=0){this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`;this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`}const[s,n]=this.getInitialTranslation();this.translate(s,n);(0,i.bindEvents)(this,this.div,["pointerdown"]);return this.div}pointerdown(t){const{isMac:e}=s.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{this.#Es=!0;this._isDraggable?this.#Us(t):this.#zs(t)}}#zs(t){const{isMac:e}=s.FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#Us(t){const e=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let i,s;if(e){this.div.classList.add("moving");i={passive:!0,capture:!0};this.#Cs=t.clientX;this.#Ms=t.clientY;s=t=>{const{clientX:e,clientY:i}=t,[s,n]=this.screenToPageTranslation(e-this.#Cs,i-this.#Ms);this.#Cs=e;this.#Ms=i;this._uiManager.dragSelectedEditors(s,n)};window.addEventListener("pointermove",s,i)}const pointerUpCallback=()=>{window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("blur",pointerUpCallback);if(e){this.div.classList.remove("moving");window.removeEventListener("pointermove",s,i)}this.#Es=!1;this._uiManager.endDragSession()||this.#zs(t)};window.addEventListener("pointerup",pointerUpCallback);window.addEventListener("blur",pointerUpCallback)}moveInDOM(){this.#Ts&&clearTimeout(this.#Ts);this.#Ts=setTimeout((()=>{this.#Ts=null;this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this);this.x=e;this.y=i;this.fixAndSetPosition()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,r]=this.pageDimensions,[a,o]=this.pageTranslation,l=t/s,h=e/s,d=this.x*n,c=this.y*r,u=this.width*n,p=this.height*r;switch(i){case 0:return[d+l+a,r-c-h-p+o,d+l+u+a,r-c-h+o];case 90:return[d+h+a,r-c+l+o,d+h+p+a,r-c+l+u+o];case 180:return[d-l-u+a,r-c+h+o,d-l+a,r-c+h+p+o];case 270:return[d-h-p+a,r-c-l-u+o,d-h+a,r-c-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,r]=t,a=n-i,o=r-s;switch(this.rotation){case 0:return[i,e-r,a,o];case 90:return[i,e-s,o,a];case 180:return[n,e-s,a,o];case 270:return[n,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#_s=!0}disableEditMode(){this.#_s=!1}isInEditMode(){return this.#_s}shouldGetKeyboardEvents(){return this.#Ss}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){this.div?.addEventListener("focusin",this.#bs);this.div?.addEventListener("focusout",this.#vs)}rotate(t){}serialize(t=!1,e=null){(0,s.unreachable)("An editor must be serializable")}static deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation;const[n,r]=s.pageDimensions,[a,o,l,h]=s.getRectInCurrentCoords(t.rect,r);s.x=a/n;s.y=o/r;s.width=l/n;s.height=h/r;return s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){this.div.removeEventListener("focusin",this.#bs);this.div.removeEventListener("focusout",this.#vs);this.isEmpty()||this.commit();this.parent?this.parent.remove(this):this._uiManager.removeEditor(this);if(this.#Ts){clearTimeout(this.#Ts);this.#Ts=null}this.#Fs();this.removeEditToolbar();if(this.#Ps){for(const t of this.#Ps.values())clearTimeout(t);this.#Ps=null}this.parent=null}get isResizable(){return!1}makeResizable(){if(this.isResizable){this.#Os();this.#fs.classList.remove("hidden");(0,i.bindEvents)(this,this.div,["keydown"])}}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this);this.#ms={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#fs.children;if(!this.#us){this.#us=Array.from(e);const t=this.#js.bind(this),i=this.#Vs.bind(this);for(const e of this.#us){const s=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton");e.addEventListener("keydown",t);e.addEventListener("blur",i);e.addEventListener("focus",this.#Gs.bind(this,s));AnnotationEditor._l10nPromise.get(`pdfjs-editor-resizer-label-${s}`).then((t=>e.setAttribute("aria-label",t)))}}const i=this.#us[0];let s=0;for(const t of e){if(t===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#us.length/4);if(n!==s){if(n<s)for(let t=0;t<s-n;t++)this.#fs.append(this.#fs.firstChild);else if(n>s)for(let t=0;t<n-s;t++)this.#fs.firstChild.before(this.#fs.lastChild);let t=0;for(const i of e){const e=this.#us[t++].getAttribute("data-resizer-name");AnnotationEditor._l10nPromise.get(`pdfjs-editor-resizer-label-${e}`).then((t=>i.setAttribute("aria-label",t)))}}this.#Ws(0);this.#Ss=!0;this.#fs.firstChild.focus({focusVisible:!0});t.preventDefault();t.stopImmediatePropagation()}#js(t){AnnotationEditor._resizerKeyboardManager.exec(this,t)}#Vs(t){this.#Ss&&t.relatedTarget?.parentNode!==this.#fs&&this.#Fs()}#Gs(t){this.#As=this.#Ss?t:""}#Ws(t){if(this.#us)for(const e of this.#us)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#Ss&&this.#Bs(this.#As,{movementX:t,movementY:e})}#Fs(){this.#Ss=!1;this.#Ws(-1);if(this.#ms){const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#ms;this.#Hs(t,e,i,s);this.#ms=null}}_stopResizingWithKeyboard(){this.#Fs();this.div.focus()}select(){this.makeResizable();this.div?.classList.add("selectedEditor");this.#ys?this.#ys?.show():this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this.#ys?.show()}))}unselect(){this.#fs?.classList.add("hidden");this.div?.classList.remove("selectedEditor");this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0});this.#ys?.hide()}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return this.#xs}set isEditing(t){this.#xs=t;if(this.parent)if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#gs=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i;s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#Ps||=new Map;const{action:e}=t;let i=this.#Ps.get(e);i&&clearTimeout(i);i=setTimeout((()=>{this._reportTelemetry(t);this.#Ps.delete(e);0===this.#Ps.size&&(this.#Ps=null)}),AnnotationEditor._telemetryTimeout);this.#Ps.set(e,i)}else{t.type||=this.editorType;this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}}show(t=this._isVisible){this.div.classList.toggle("hidden",!t);this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0);this.#ps=!1}disable(){this.div&&(this.div.tabIndex=-1);this.#ps=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.before(e)}}else{e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.container.prepend(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class FakeEditor extends AnnotationEditor{constructor(t){super(t);this.annotationElementId=t.annotationElementId;this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},4061:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{FreeOutliner:()=>FreeOutliner,Outliner:()=>Outliner});e(4114),e(6573),e(8100),e(7936),e(7467),e(4732),e(9577),e(3375),e(9225),e(3972),e(9209),e(5714),e(7561),e(6197);var i=e(4292);class Outliner{#$s;#qs=[];#Ks=[];constructor(t,e=0,i=0,s=!0){let n=1/0,r=-1/0,a=1/0,o=-1/0;const l=10**-4;for(const{x:i,y:s,width:h,height:d}of t){const t=Math.floor((i-e)/l)*l,c=Math.ceil((i+h+e)/l)*l,u=Math.floor((s-e)/l)*l,p=Math.ceil((s+d+e)/l)*l,g=[t,u,p,!0],f=[c,u,p,!1];this.#qs.push(g,f);n=Math.min(n,t);r=Math.max(r,c);a=Math.min(a,u);o=Math.max(o,p)}const h=r-n+2*i,d=o-a+2*i,c=n-i,u=a-i,p=this.#qs.at(s?-1:-2),g=[p[0],p[2]];for(const t of this.#qs){const[e,i,s]=t;t[0]=(e-c)/h;t[1]=(i-u)/d;t[2]=(s-u)/d}this.#$s={x:c,y:u,width:h,height:d,lastPoint:g}}getOutlines(){this.#qs.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#qs)if(e[3]){t.push(...this.#Xs(e));this.#Ys(e)}else{this.#Js(e);t.push(...this.#Xs(e))}return this.#Qs(t)}#Qs(t){const e=[],i=new Set;for(const i of t){const[t,s,n]=i;e.push([t,s,i],[t,n,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,s=e.length;t<s;t+=2){const s=e[t][2],n=e[t+1][2];s.push(n);n.push(s);i.add(s);i.add(n)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,r,a,o,l]=t;i.delete(t);let h=e,d=r;n=[e,a];s.push(n);for(;;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t);[e,r,a,o,l]=t;if(h!==e){n.push(h,d,e,d===r?r:a);h=e}d=d===r?a:r}n.push(h,d)}return new HighlightOutline(s,this.#$s)}#Zs(t){const e=this.#Ks;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,r=e[n][0];if(r===t)return n;r<t?i=n+1:s=n-1}return s+1}#Ys([,t,e]){const i=this.#Zs(t);this.#Ks.splice(i,0,[t,e])}#Js([,t,e]){const i=this.#Zs(t);for(let s=i;s<this.#Ks.length;s++){const[i,n]=this.#Ks[s];if(i!==t)break;if(i===t&&n===e){this.#Ks.splice(s,1);return}}for(let s=i-1;s>=0;s--){const[i,n]=this.#Ks[s];if(i!==t)break;if(i===t&&n===e){this.#Ks.splice(s,1);return}}}#Xs(t){const[e,i,s]=t,n=[[e,i,s]],r=this.#Zs(s);for(let t=0;t<r;t++){const[i,s]=this.#Ks[t];for(let t=0,r=n.length;t<r;t++){const[,a,o]=n[t];if(!(s<=a||o<=i))if(a>=i)if(o>s)n[t][1]=s;else{if(1===r)return[];n.splice(t,1);t--;r--}else{n[t][2]=i;o>s&&n.push([e,s,o])}}}return n}}class Outline{toSVGPath(){throw new Error("Abstract method `toSVGPath` must be implemented.")}get box(){throw new Error("Abstract getter `box` must be implemented.")}serialize(t,e){throw new Error("Abstract method `serialize` must be implemented.")}get free(){return this instanceof FreeHighlightOutline}}class HighlightOutline extends Outline{#$s;#tn;constructor(t,e){super();this.#tn=t;this.#$s=e}toSVGPath(){const t=[];for(const e of this.#tn){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const r=e[n],a=e[n+1];if(r===i){t.push(`V${a}`);s=a}else if(a===s){t.push(`H${r}`);i=r}}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const r=[],a=i-t,o=s-e;for(const e of this.#tn){const i=new Array(e.length);for(let n=0;n<e.length;n+=2){i[n]=t+e[n]*a;i[n+1]=s-e[n+1]*o}r.push(i)}return r}get box(){return this.#$s}}class FreeOutliner{#$s;#en=[];#in;#sn;#nn=[];#rn=new Float64Array(18);#an;#on;#ln;#hn;#dn;#he;#cn=[];static#un=8;static#pn=2;static#gn=FreeOutliner.#un+FreeOutliner.#pn;constructor({x:t,y:e},i,s,n,r,a=0){this.#$s=i;this.#he=n*s;this.#sn=r;this.#rn.set([NaN,NaN,NaN,NaN,t,e],6);this.#in=a;this.#hn=FreeOutliner.#un*s;this.#ln=FreeOutliner.#gn*s;this.#dn=s;this.#cn.push(t,e)}get free(){return!0}isEmpty(){return isNaN(this.#rn[8])}#fn(){const t=this.#rn.subarray(4,6),e=this.#rn.subarray(16,18),[i,s,n,r]=this.#$s;return[(this.#an+(t[0]-e[0])/2-i)/n,(this.#on+(t[1]-e[1])/2-s)/r,(this.#an+(e[0]-t[0])/2-i)/n,(this.#on+(e[1]-t[1])/2-s)/r]}add({x:t,y:e}){this.#an=t;this.#on=e;const[i,s,n,r]=this.#$s;let[a,o,l,h]=this.#rn.subarray(8,12);const d=t-l,c=e-h,u=Math.hypot(d,c);if(u<this.#ln)return!1;const p=u-this.#hn,g=p/u,f=g*d,m=g*c;let b=a,v=o;a=l;o=h;l+=f;h+=m;this.#cn?.push(t,e);const y=f/p,A=-m/p*this.#he,E=y*this.#he;this.#rn.set(this.#rn.subarray(2,8),0);this.#rn.set([l+A,h+E],4);this.#rn.set(this.#rn.subarray(14,18),12);this.#rn.set([l-A,h-E],16);if(isNaN(this.#rn[6])){if(0===this.#nn.length){this.#rn.set([a+A,o+E],2);this.#nn.push(NaN,NaN,NaN,NaN,(a+A-i)/n,(o+E-s)/r);this.#rn.set([a-A,o-E],14);this.#en.push(NaN,NaN,NaN,NaN,(a-A-i)/n,(o-E-s)/r)}this.#rn.set([b,v,a,o,l,h],6);return!this.isEmpty()}this.#rn.set([b,v,a,o,l,h],6);if(Math.abs(Math.atan2(v-o,b-a)-Math.atan2(m,f))<Math.PI/2){[a,o,l,h]=this.#rn.subarray(2,6);this.#nn.push(NaN,NaN,NaN,NaN,((a+l)/2-i)/n,((o+h)/2-s)/r);[a,o,b,v]=this.#rn.subarray(14,18);this.#en.push(NaN,NaN,NaN,NaN,((b+a)/2-i)/n,((v+o)/2-s)/r);return!0}[b,v,a,o,l,h]=this.#rn.subarray(0,6);this.#nn.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+l)/6-i)/n,((5*o+h)/6-s)/r,((a+l)/2-i)/n,((o+h)/2-s)/r);[l,h,a,o,b,v]=this.#rn.subarray(12,18);this.#en.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+l)/6-i)/n,((5*o+h)/6-s)/r,((a+l)/2-i)/n,((o+h)/2-s)/r);return!0}toSVGPath(){if(this.isEmpty())return"";const t=this.#nn,e=this.#en,i=this.#rn.subarray(4,6),s=this.#rn.subarray(16,18),[n,r,a,o]=this.#$s,[l,h,d,c]=this.#fn();if(isNaN(this.#rn[6])&&!this.isEmpty())return`M${(this.#rn[2]-n)/a} ${(this.#rn[3]-r)/o} L${(this.#rn[4]-n)/a} ${(this.#rn[5]-r)/o} L${l} ${h} L${d} ${c} L${(this.#rn[16]-n)/a} ${(this.#rn[17]-r)/o} L${(this.#rn[14]-n)/a} ${(this.#rn[15]-r)/o} Z`;const u=[];u.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?u.push(`L${t[e+4]} ${t[e+5]}`):u.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);u.push(`L${(i[0]-n)/a} ${(i[1]-r)/o} L${l} ${h} L${d} ${c} L${(s[0]-n)/a} ${(s[1]-r)/o}`);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?u.push(`L${e[t+4]} ${e[t+5]}`):u.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);u.push(`L${e[4]} ${e[5]} Z`);return u.join(" ")}getOutlines(){const t=this.#nn,e=this.#en,i=this.#rn,s=i.subarray(4,6),n=i.subarray(16,18),[r,a,o,l]=this.#$s,h=new Float64Array((this.#cn?.length??0)+2);for(let t=0,e=h.length-2;t<e;t+=2){h[t]=(this.#cn[t]-r)/o;h[t+1]=(this.#cn[t+1]-a)/l}h[h.length-2]=(this.#an-r)/o;h[h.length-1]=(this.#on-a)/l;const[d,c,u,p]=this.#fn();if(isNaN(i[6])&&!this.isEmpty()){const t=new Float64Array(36);t.set([NaN,NaN,NaN,NaN,(i[2]-r)/o,(i[3]-a)/l,NaN,NaN,NaN,NaN,(i[4]-r)/o,(i[5]-a)/l,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(i[16]-r)/o,(i[17]-a)/l,NaN,NaN,NaN,NaN,(i[14]-r)/o,(i[15]-a)/l],0);return new FreeHighlightOutline(t,h,this.#$s,this.#dn,this.#in,this.#sn)}const g=new Float64Array(this.#nn.length+24+this.#en.length);let f=t.length;for(let e=0;e<f;e+=2)if(isNaN(t[e]))g[e]=g[e+1]=NaN;else{g[e]=t[e];g[e+1]=t[e+1]}g.set([NaN,NaN,NaN,NaN,(s[0]-r)/o,(s[1]-a)/l,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(n[0]-r)/o,(n[1]-a)/l],f);f+=24;for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)if(isNaN(e[t+i])){g[f]=g[f+1]=NaN;f+=2}else{g[f]=e[t+i];g[f+1]=e[t+i+1];f+=2}g.set([NaN,NaN,NaN,NaN,e[4],e[5]],f);return new FreeHighlightOutline(g,h,this.#$s,this.#dn,this.#in,this.#sn)}}class FreeHighlightOutline extends Outline{#$s;#mn=null;#in;#sn;#cn;#dn;#bn;constructor(t,e,i,s,n,r){super();this.#bn=t;this.#cn=e;this.#$s=i;this.#dn=s;this.#in=n;this.#sn=r;this.#vn(r);const{x:a,y:o,width:l,height:h}=this.#mn;for(let e=0,i=t.length;e<i;e+=2){t[e]=(t[e]-a)/l;t[e+1]=(t[e+1]-o)/h}for(let t=0,i=e.length;t<i;t+=2){e[t]=(e[t]-a)/l;e[t+1]=(e[t+1]-o)/h}}toSVGPath(){const t=[`M${this.#bn[4]} ${this.#bn[5]}`];for(let e=6,i=this.#bn.length;e<i;e+=6)isNaN(this.#bn[e])?t.push(`L${this.#bn[e+4]} ${this.#bn[e+5]}`):t.push(`C${this.#bn[e]} ${this.#bn[e+1]} ${this.#bn[e+2]} ${this.#bn[e+3]} ${this.#bn[e+4]} ${this.#bn[e+5]}`);t.push("Z");return t.join(" ")}serialize([t,e,i,s],n){const r=i-t,a=s-e;let o,l;switch(n){case 0:o=this.#yn(this.#bn,t,s,r,-a);l=this.#yn(this.#cn,t,s,r,-a);break;case 90:o=this.#An(this.#bn,t,e,r,a);l=this.#An(this.#cn,t,e,r,a);break;case 180:o=this.#yn(this.#bn,i,e,-r,a);l=this.#yn(this.#cn,i,e,-r,a);break;case 270:o=this.#An(this.#bn,i,s,-r,-a);l=this.#An(this.#cn,i,s,-r,-a)}return{outline:Array.from(o),points:[Array.from(l)]}}#yn(t,e,i,s,n){const r=new Float64Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a]*s;r[a+1]=i+t[a+1]*n}return r}#An(t,e,i,s,n){const r=new Float64Array(t.length);for(let a=0,o=t.length;a<o;a+=2){r[a]=e+t[a+1]*s;r[a+1]=i+t[a]*n}return r}#vn(t){const e=this.#bn;let s=e[4],n=e[5],r=s,a=n,o=s,l=n,h=s,d=n;const c=t?Math.max:Math.min;for(let t=6,u=e.length;t<u;t+=6){if(isNaN(e[t])){r=Math.min(r,e[t+4]);a=Math.min(a,e[t+5]);o=Math.max(o,e[t+4]);l=Math.max(l,e[t+5]);if(d<e[t+5]){h=e[t+4];d=e[t+5]}else d===e[t+5]&&(h=c(h,e[t+4]))}else{const u=i.Util.bezierBoundingBox(s,n,...e.slice(t,t+6));r=Math.min(r,u[0]);a=Math.min(a,u[1]);o=Math.max(o,u[2]);l=Math.max(l,u[3]);if(d<u[3]){h=u[2];d=u[3]}else d===u[3]&&(h=c(h,u[2]))}s=e[t+4];n=e[t+5]}const u=r-this.#in,p=a-this.#in,g=o-r+2*this.#in,f=l-a+2*this.#in;this.#mn={x:u,y:p,width:g,height:f,lastPoint:[h,d]}}get box(){return this.#mn}getNewOutline(t,e){const{x:i,y:s,width:n,height:r}=this.#mn,[a,o,l,h]=this.#$s,d=n*l,c=r*h,u=i*l+a,p=s*h+o,g=new FreeOutliner({x:this.#cn[0]*d+u,y:this.#cn[1]*c+p},this.#$s,this.#dn,t,this.#sn,e??this.#in);for(let t=2;t<this.#cn.length;t+=2)g.add({x:this.#cn[t]*d+u,y:this.#cn[t+1]*c+p});return g.getOutlines()}}},4362:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{EditorToolbar:()=>EditorToolbar,HighlightToolbar:()=>HighlightToolbar});var i=e(5419);class EditorToolbar{#En=null;#Yt=null;#Qi;#wn=null;constructor(t){this.#Qi=t}render(){const t=this.#En=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");t.addEventListener("contextmenu",i.noContextMenu);t.addEventListener("pointerdown",EditorToolbar.#Wi);const e=this.#wn=document.createElement("div");e.className="buttons";t.append(e);const s=this.#Qi.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#Qi._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%";e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}this.#xn();return t}static#Wi(t){t.stopPropagation()}#_n(t){this.#Qi._focusEventsAllowed=!1;t.preventDefault();t.stopPropagation()}#Sn(t){this.#Qi._focusEventsAllowed=!0;t.preventDefault();t.stopPropagation()}#Tn(t){t.addEventListener("focusin",this.#_n.bind(this),{capture:!0});t.addEventListener("focusout",this.#Sn.bind(this),{capture:!0});t.addEventListener("contextmenu",i.noContextMenu)}hide(){this.#En.classList.add("hidden");this.#Yt?.hideDropdown()}show(){this.#En.classList.remove("hidden")}#xn(){const t=document.createElement("button");t.className="delete";t.tabIndex=0;t.setAttribute("data-l10n-id",`pdfjs-editor-remove-${this.#Qi.editorType}-button`);this.#Tn(t);t.addEventListener("click",(t=>{this.#Qi._uiManager.delete()}));this.#wn.append(t)}get#Cn(){const t=document.createElement("div");t.className="divider";return t}addAltTextButton(t){this.#Tn(t);this.#wn.prepend(t,this.#Cn)}addColorPicker(t){this.#Yt=t;const e=t.renderButton();this.#Tn(e);this.#wn.prepend(e,this.#Cn)}remove(){this.#En.remove();this.#Yt?.destroy();this.#Yt=null}}class HighlightToolbar{#wn=null;#En=null;#Ni;constructor(t){this.#Ni=t}#Mn(){const t=this.#En=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");t.addEventListener("contextmenu",i.noContextMenu);const e=this.#wn=document.createElement("div");e.className="buttons";t.append(e);this.#Pn();return t}#Rn(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const r=n.x+(e?n.width:0);if(t>i){s=r;i=t}else e?r>s&&(s=r):r<s&&(s=r)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#Rn(e,i),{style:r}=this.#En||=this.#Mn();t.append(this.#En);r.insetInlineEnd=100*s+"%";r.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#En.remove()}#Pn(){const t=document.createElement("button");t.className="highlightButton";t.tabIndex=0;t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e);e.className="visuallyHidden";e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");t.addEventListener("contextmenu",i.noContextMenu);t.addEventListener("click",(()=>{this.#Ni.highlightSelection("floating_button")}));this.#wn.append(t)}}},7830:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{AnnotationEditorUIManager:()=>AnnotationEditorUIManager,ColorManager:()=>ColorManager,KeyboardManager:()=>KeyboardManager,bindEvents:()=>bindEvents,opacityToHex:()=>opacityToHex});e(4114),e(6573),e(8100),e(7936),e(7467),e(4732),e(9577),e(8992),e(3215),e(1454),e(7550),e(8335),e(3375),e(9225),e(3972),e(9209),e(5714),e(7561),e(6197);var i=e(4292),s=e(5419),n=e(4362);function bindEvents(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}function opacityToHex(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")}class IdManager{#gt=0;constructor(){}get id(){return`${i.AnnotationEditorPrefix}${this.#gt++}`}}class ImageManager{#kn=(0,i.getUuid)();#gt=0;#ft=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d"),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';const s=e.decode().then((()=>{t.drawImage(e,0,0,1,1,0,0,1,3);return 0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]}));return(0,i.shadow)(this,"_isSVGFittingCanvas",s)}async#Fn(t,e){this.#ft||=new Map;let i=this.#ft.get(t);if(null===i)return null;if(i?.bitmap){i.refCounter+=1;return i}try{i||={bitmap:null,id:`image_${this.#kn}_${this.#gt++}`,refCounter:0,isSvg:!1};let t;if("string"==typeof e){i.url=e;t=await(0,s.fetchData)(e,"blob")}else t=i.file=e;if("image/svg+xml"===t.type){const e=ImageManager._isSVGFittingCanvas,s=new FileReader,n=new Image,r=new Promise(((t,r)=>{n.onload=()=>{i.bitmap=n;i.isSvg=!0;t()};s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t};n.onerror=s.onerror=r}));s.readAsDataURL(t);await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){console.error(t);i=null}this.#ft.set(t,i);i&&this.#ft.set(i.id,i);return i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#Fn(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#Fn(t,t)}async getFromId(t){this.#ft||=new Map;const e=this.#ft.get(t);if(!e)return null;if(e.bitmap){e.refCounter+=1;return e}return e.file?this.getFromFile(e.file):this.getFromUrl(e.url)}getSvgUrl(t){const e=this.#ft.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#ft||=new Map;const e=this.#ft.get(t);if(e){e.refCounter-=1;0===e.refCounter&&(e.bitmap=null)}}isValidId(t){return t.startsWith(`image_${this.#kn}_`)}}class CommandManager{#Dn=[];#In=!1;#Ln;#On=-1;constructor(t=128){this.#Ln=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){s&&t();if(this.#In)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#On){this.#Dn.length>0&&(this.#Dn.length=0);this.#On=0;this.#Dn.push(o);return}if(r&&this.#Dn[this.#On].type===n){a&&(o.undo=this.#Dn[this.#On].undo);this.#Dn[this.#On]=o;return}const l=this.#On+1;if(l===this.#Ln)this.#Dn.splice(0,1);else{this.#On=l;l<this.#Dn.length&&this.#Dn.splice(l)}this.#Dn.push(o)}undo(){if(-1===this.#On)return;this.#In=!0;const{undo:t,post:e}=this.#Dn[this.#On];t();e?.();this.#In=!1;this.#On-=1}redo(){if(this.#On<this.#Dn.length-1){this.#On+=1;this.#In=!0;const{cmd:t,post:e}=this.#Dn[this.#On];t();e?.();this.#In=!1}}hasSomethingToUndo(){return-1!==this.#On}hasSomethingToRedo(){return this.#On<this.#Dn.length-1}destroy(){this.#Dn=null}}class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=i.FeatureTest.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");if(e&&i){this.callbacks.set(t.slice(4),{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}else if(!e&&!i){this.callbacks.set(t,{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}}}#Nn(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#Nn(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:r=[],checker:a=null}}=i;if(!a||a(t,e)){s.bind(t,...r,e)();if(!n){e.stopPropagation();e.preventDefault()}}}}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);(0,s.getColorValues)(t);return(0,i.shadow)(this,"_colors",t)}convert(t){const e=(0,s.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?i.Util.makeHexColor(...e):t}}class AnnotationEditorUIManager{#Bn=null;#Hn=new Map;#Un=new Map;#zn=null;#jn=null;#Vn=null;#Gn=new CommandManager;#Wn=0;#$n=new Set;#qn=null;#Bi=null;#Kn=new Set;#Xn=!1;#Yn=null;#Jn=null;#Qn=null;#Zn=!1;#tr=null;#er=new IdManager;#ir=!1;#sr=!1;#nr=null;#rr=null;#ar=null;#or=i.AnnotationEditorType.NONE;#lr=new Set;#hr=null;#dr=null;#cr=null;#ur=this.blur.bind(this);#pr=this.focus.bind(this);#gr=this.copy.bind(this);#fr=this.cut.bind(this);#mr=this.paste.bind(this);#se=this.keydown.bind(this);#br=this.keyup.bind(this);#vr=this.onEditingAction.bind(this);#yr=this.onPageChanging.bind(this);#Ar=this.onScaleChanging.bind(this);#Er=this.#wr.bind(this);#xr=this.onRotationChanging.bind(this);#_r={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#Sr=[0,0];#Tr=null;#f=null;#Cr=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=AnnotationEditorUIManager.prototype,arrowChecker=t=>t.#f.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),textInputChecker=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},e=this.TRANSLATE_SMALL,s=this.TRANSLATE_BIG;return(0,i.shadow)(this,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:textInputChecker}],[["ctrl+z","mac+meta+z"],t.undo,{checker:textInputChecker}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:textInputChecker}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:textInputChecker}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#f.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#f.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:arrowChecker}]]))}constructor(t,e,i,n,r,a,o,l,h){this.#f=t;this.#Cr=e;this.#zn=i;this._eventBus=n;this._eventBus._on("editingaction",this.#vr);this._eventBus._on("pagechanging",this.#yr);this._eventBus._on("scalechanging",this.#Ar);this._eventBus._on("rotationchanging",this.#xr);this.#Mr();this.#Pr();this.#jn=r.annotationStorage;this.#Yn=r.filterFactory;this.#dr=a;this.#Qn=o||null;this.#Xn=l;this.#ar=h||null;this.viewParameters={realScale:s.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0};this.isShiftKeyDown=!1}destroy(){this.#Rr();this.#kr();this._eventBus._off("editingaction",this.#vr);this._eventBus._off("pagechanging",this.#yr);this._eventBus._off("scalechanging",this.#Ar);this._eventBus._off("rotationchanging",this.#xr);for(const t of this.#Un.values())t.destroy();this.#Un.clear();this.#Hn.clear();this.#Kn.clear();this.#Bn=null;this.#lr.clear();this.#Gn.destroy();this.#zn?.destroy();this.#tr?.hide();this.#tr=null;if(this.#Jn){clearTimeout(this.#Jn);this.#Jn=null}if(this.#Tr){clearTimeout(this.#Tr);this.#Tr=null}this.#Fr()}async mlGuess(t){return this.#ar?.guess(t)||null}get hasMLManager(){return!!this.#ar}get hcmFilter(){return(0,i.shadow)(this,"hcmFilter",this.#dr?this.#Yn.addHCMFilter(this.#dr.foreground,this.#dr.background):"none")}get direction(){return(0,i.shadow)(this,"direction",getComputedStyle(this.#f).direction)}get highlightColors(){return(0,i.shadow)(this,"highlightColors",this.#Qn?new Map(this.#Qn.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return(0,i.shadow)(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setMainHighlightColorPicker(t){this.#rr=t}editAltText(t){this.#zn?.editAltText(this,t)}onPageChanging({pageNumber:t}){this.#Wn=t-1}focusMainContainer(){this.#f.focus()}findParent(t,e){for(const i of this.#Un.values()){const{x:s,y:n,width:r,height:a}=i.div.getBoundingClientRect();if(t>=s&&t<=s+r&&e>=n&&e<=n+a)return i}return null}disableUserSelect(t=!1){this.#Cr.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#Kn.add(t)}removeShouldRescale(t){this.#Kn.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove();this.viewParameters.realScale=t*s.PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of this.#Kn)t.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove();this.viewParameters.rotation=t}#Dr({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:n,focusNode:r,focusOffset:a}=e,o=e.toString(),l=this.#Dr(e).closest(".textLayer"),h=this.getSelectionBoxes(l);if(h){e.empty();if(this.#or===i.AnnotationEditorType.NONE){this._eventBus.dispatch("showannotationeditorui",{source:this,mode:i.AnnotationEditorType.HIGHLIGHT});this.showAllEditors("highlight",!0,!0)}for(const e of this.#Un.values())if(e.hasTextLayer(l)){e.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:h,anchorNode:s,anchorOffset:n,focusNode:r,focusOffset:a,text:o});break}}}#Ir(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#Dr(t).closest(".textLayer"),i=this.getSelectionBoxes(e);if(i){this.#tr||=new n.HighlightToolbar(this);this.#tr.show(e,i,"ltr"===this.direction)}}addToAnnotationStorage(t){t.isEmpty()||!this.#jn||this.#jn.has(t.id)||this.#jn.setValue(t.id,t)}#wr(){const t=document.getSelection();if(!t||t.isCollapsed){if(this.#hr){this.#tr?.hide();this.#hr=null;this.#Lr({hasSelectedText:!1})}return}const{anchorNode:e}=t;if(e===this.#hr)return;if(this.#Dr(t).closest(".textLayer")){this.#tr?.hide();this.#hr=e;this.#Lr({hasSelectedText:!0});if(this.#or===i.AnnotationEditorType.HIGHLIGHT||this.#or===i.AnnotationEditorType.NONE){this.#or===i.AnnotationEditorType.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0);this.#Zn=this.isShiftKeyDown;if(!this.isShiftKeyDown){const pointerup=t=>{if("pointerup"!==t.type||0===t.button){window.removeEventListener("pointerup",pointerup);window.removeEventListener("blur",pointerup);"pointerup"===t.type&&this.#Or("main_toolbar")}};window.addEventListener("pointerup",pointerup);window.addEventListener("blur",pointerup)}}}else if(this.#hr){this.#tr?.hide();this.#hr=null;this.#Lr({hasSelectedText:!1})}}#Or(t=""){this.#or===i.AnnotationEditorType.HIGHLIGHT?this.highlightSelection(t):this.#Xn&&this.#Ir()}#Mr(){document.addEventListener("selectionchange",this.#Er)}#Fr(){document.removeEventListener("selectionchange",this.#Er)}#Nr(){window.addEventListener("focus",this.#pr);window.addEventListener("blur",this.#ur)}#kr(){window.removeEventListener("focus",this.#pr);window.removeEventListener("blur",this.#ur)}blur(){this.isShiftKeyDown=!1;if(this.#Zn){this.#Zn=!1;this.#Or("main_toolbar")}if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#lr)if(e.div.contains(t)){this.#nr=[e,t];e._focusEventsAllowed=!1;break}}focus(){if(!this.#nr)return;const[t,e]=this.#nr;this.#nr=null;e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0});e.focus()}#Pr(){window.addEventListener("keydown",this.#se);window.addEventListener("keyup",this.#br)}#Rr(){window.removeEventListener("keydown",this.#se);window.removeEventListener("keyup",this.#br)}#Br(){document.addEventListener("copy",this.#gr);document.addEventListener("cut",this.#fr);document.addEventListener("paste",this.#mr)}#Hr(){document.removeEventListener("copy",this.#gr);document.removeEventListener("cut",this.#fr);document.removeEventListener("paste",this.#mr)}addEditListeners(){this.#Pr();this.#Br()}removeEditListeners(){this.#Rr();this.#Hr()}copy(t){t.preventDefault();this.#Bn?.commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of this.#lr){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#Bi)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(t){(0,i.warn)(`paste: "${t.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const n=this.currentLayer;try{const t=[];for(const e of s){const i=n.deserialize(e);if(!i)return;t.push(i)}const cmd=()=>{for(const e of t)this.#Ur(e);this.#zr(t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd,undo,mustExec:!0})}catch(t){(0,i.warn)(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0);this.#or===i.AnnotationEditorType.NONE||this.isEditorHandlingKeyboard||AnnotationEditorUIManager._keyboardManager.exec(this,t)}keyup(t){if(this.isShiftKeyDown&&"Shift"===t.key){this.isShiftKeyDown=!1;if(this.#Zn){this.#Zn=!1;this.#Or("main_toolbar")}}}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#Lr(t){if(Object.entries(t).some((([t,e])=>this.#_r[t]!==e))){this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#_r,t)});this.#or===i.AnnotationEditorType.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#jr([[i.AnnotationEditorParamsType.HIGHLIGHT_FREE,!0]])}}#jr(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#Nr();this.#Br();this.#Lr({isEditing:this.#or!==i.AnnotationEditorType.NONE,isEmpty:this.#Vr(),hasSomethingToUndo:this.#Gn.hasSomethingToUndo(),hasSomethingToRedo:this.#Gn.hasSomethingToRedo(),hasSelectedEditor:!1})}else{this.#kr();this.#Hr();this.#Lr({isEditing:!1});this.disableUserSelect(!1)}}registerEditorTypes(t){if(!this.#Bi){this.#Bi=t;for(const t of this.#Bi)this.#jr(t.defaultPropertiesToUpdate)}}getId(){return this.#er.id}get currentLayer(){return this.#Un.get(this.#Wn)}getLayer(t){return this.#Un.get(t)}get currentPageIndex(){return this.#Wn}addLayer(t){this.#Un.set(t.pageIndex,t);this.#ir?t.enable():t.disable()}removeLayer(t){this.#Un.delete(t.pageIndex)}updateMode(t,e=null,s=!1){if(this.#or!==t){this.#or=t;if(t!==i.AnnotationEditorType.NONE){this.setEditingState(!0);this.#Gr();this.unselectAll();for(const e of this.#Un.values())e.updateMode(t);if(e||!s){if(e)for(const t of this.#Hn.values())if(t.annotationElementId===e){this.setSelected(t);t.enterInEditMode();break}}else this.addNewEditorFromKeyboard()}else{this.setEditingState(!1);this.#Wr()}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==this.#or&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#Bi){switch(t){case i.AnnotationEditorParamsType.CREATE:this.currentLayer.addNewEditor();return;case i.AnnotationEditorParamsType.HIGHLIGHT_DEFAULT_COLOR:this.#rr?.updateColor(e);break;case i.AnnotationEditorParamsType.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}});(this.#cr||=new Map).set(t,e);this.showAllEditors("highlight",e)}for(const i of this.#lr)i.updateParams(t,e);for(const i of this.#Bi)i.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){for(const i of this.#Hn.values())i.editorType===t&&i.show(e);(this.#cr?.get(i.AnnotationEditorParamsType.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#jr([[i.AnnotationEditorParamsType.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#sr!==t){this.#sr=t;for(const e of this.#Un.values()){t?e.disableClick():e.enableClick();e.div.classList.toggle("waiting",t)}}}#Gr(){if(!this.#ir){this.#ir=!0;for(const t of this.#Un.values())t.enable();for(const t of this.#Hn.values())t.enable()}}#Wr(){this.unselectAll();if(this.#ir){this.#ir=!1;for(const t of this.#Un.values())t.disable();for(const t of this.#Hn.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#Hn.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#Hn.get(t)}addEditor(t){this.#Hn.set(t.id,t)}removeEditor(t){if(t.div.contains(document.activeElement)){this.#Jn&&clearTimeout(this.#Jn);this.#Jn=setTimeout((()=>{this.focusMainContainer();this.#Jn=null}),0)}this.#Hn.delete(t.id);this.unselect(t);t.annotationElementId&&this.#$n.has(t.annotationElementId)||this.#jn?.remove(t.id)}addDeletedAnnotationElement(t){this.#$n.add(t.annotationElementId);this.addChangedExistingAnnotation(t);t.deleted=!0}isDeletedAnnotationElement(t){return this.#$n.has(t)}removeDeletedAnnotationElement(t){this.#$n.delete(t.annotationElementId);this.removeChangedExistingAnnotation(t);t.deleted=!1}#Ur(t){const e=this.#Un.get(t.pageIndex);if(e)e.addOrRebuild(t);else{this.addEditor(t);this.addToAnnotationStorage(t)}}setActiveEditor(t){if(this.#Bn!==t){this.#Bn=t;t&&this.#jr(t.propertiesToUpdate)}}get#$r(){let t=null;for(t of this.#lr);return t}updateUI(t){this.#$r===t&&this.#jr(t.propertiesToUpdate)}toggleSelected(t){if(this.#lr.has(t)){this.#lr.delete(t);t.unselect();this.#Lr({hasSelectedEditor:this.hasSelection})}else{this.#lr.add(t);t.select();this.#jr(t.propertiesToUpdate);this.#Lr({hasSelectedEditor:!0})}}setSelected(t){for(const e of this.#lr)e!==t&&e.unselect();this.#lr.clear();this.#lr.add(t);t.select();this.#jr(t.propertiesToUpdate);this.#Lr({hasSelectedEditor:!0})}isSelected(t){return this.#lr.has(t)}get firstSelectedEditor(){return this.#lr.values().next().value}unselect(t){t.unselect();this.#lr.delete(t);this.#Lr({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#lr.size}get isEnterHandled(){return 1===this.#lr.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#Gn.undo();this.#Lr({hasSomethingToUndo:this.#Gn.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#Vr()})}redo(){this.#Gn.redo();this.#Lr({hasSomethingToUndo:!0,hasSomethingToRedo:this.#Gn.hasSomethingToRedo(),isEmpty:this.#Vr()})}addCommands(t){this.#Gn.add(t);this.#Lr({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#Vr()})}#Vr(){if(0===this.#Hn.size)return!0;if(1===this.#Hn.size)for(const t of this.#Hn.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();if(!this.hasSelection)return;const t=[...this.#lr];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)this.#Ur(e)},mustExec:!0})}commitOrRemove(){this.#Bn?.commitOrRemove()}hasSomethingToControl(){return this.#Bn||this.hasSelection}#zr(t){for(const t of this.#lr)t.unselect();this.#lr.clear();for(const e of t)if(!e.isEmpty()){this.#lr.add(e);e.select()}this.#Lr({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#lr)t.commit();this.#zr(this.#Hn.values())}unselectAll(){if(this.#Bn){this.#Bn.commitOrRemove();if(this.#or!==i.AnnotationEditorType.NONE)return}if(this.hasSelection){for(const t of this.#lr)t.unselect();this.#lr.clear();this.#Lr({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){i||this.commitOrRemove();if(!this.hasSelection)return;this.#Sr[0]+=t;this.#Sr[1]+=e;const[s,n]=this.#Sr,r=[...this.#lr];this.#Tr&&clearTimeout(this.#Tr);this.#Tr=setTimeout((()=>{this.#Tr=null;this.#Sr[0]=this.#Sr[1]=0;this.addCommands({cmd:()=>{for(const t of r)this.#Hn.has(t.id)&&t.translateInPage(s,n)},undo:()=>{for(const t of r)this.#Hn.has(t.id)&&t.translateInPage(-s,-n)},mustExec:!1})}),1e3);for(const i of r)i.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0);this.#qn=new Map;for(const t of this.#lr)this.#qn.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#qn)return!1;this.disableUserSelect(!1);const t=this.#qn;this.#qn=null;let e=!1;for(const[{x:i,y:s,pageIndex:n},r]of t){r.newX=i;r.newY=s;r.newPageIndex=n;e||=i!==r.savedX||s!==r.savedY||n!==r.savedPageIndex}if(!e)return!1;const move=(t,e,i,s)=>{if(this.#Hn.has(t.id)){const n=this.#Un.get(s);if(n)t._setParentAndPosition(n,e,i);else{t.pageIndex=s;t.x=e;t.y=i}}};this.addCommands({cmd:()=>{for(const[e,{newX:i,newY:s,newPageIndex:n}]of t)move(e,i,s,n)},undo:()=>{for(const[e,{savedX:i,savedY:s,savedPageIndex:n}]of t)move(e,i,s,n)},mustExec:!0});return!0}dragSelectedEditors(t,e){if(this.#qn)for(const i of this.#qn.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);if(e){e.changeParent(t);e.addOrRebuild(t)}else{this.addEditor(t);this.addToAnnotationStorage(t);t.rebuild()}}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#lr.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#Bn===t}getActive(){return this.#Bn}getMode(){return this.#or}get imageManager(){return(0,i.shadow)(this,"imageManager",new ImageManager)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,s=e.rangeCount;i<s;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(t,e,a,o)=>({x:(e-s)/r,y:1-(t+a-i)/n,width:o/r,height:a/n});break;case"180":a=(t,e,a,o)=>({x:1-(t+a-i)/n,y:1-(e+o-s)/r,width:a/n,height:o/r});break;case"270":a=(t,e,a,o)=>({x:1-(e+o-s)/r,y:(t-i)/n,width:o/r,height:a/n});break;default:a=(t,e,a,o)=>({x:(t-i)/n,y:(e-s)/r,width:a/n,height:o/r})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:s,height:n}of i.getClientRects())0!==s&&0!==n&&o.push(a(t,e,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#Vn||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#Vn?.delete(t)}renderAnnotationElement(t){const e=this.#Vn?.get(t.data.id);if(!e)return;const s=this.#jn.getRawValue(e);s&&(this.#or!==i.AnnotationEditorType.NONE||s.hasBeenModified)&&s.renderAnnotationElement(t)}}},4094:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{PDFFetchStream:()=>PDFFetchStream});e(4114),e(6573),e(8100),e(7936),e(4628),e(7467),e(4732),e(9577);var i=e(4292),s=e(6490);function createFetchOptions(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function createHeaders(t){const e=new Headers;for(const i in t){const s=t[i];void 0!==s&&e.append(i,s)}return e}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;(0,i.warn)(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}class PDFFetchStream{constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._headers=createHeaders(this._stream.httpHeaders);const n=e.url;fetch(n,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,s.validateResponseStatus)(t.status))throw(0,s.createResponseStatusError)(t.status,n);this._reader=t.body.getReader();this._headersCapability.resolve();const getResponseHeader=e=>t.headers.get(e),{allowRangeRequests:e,suggestedLength:r}=(0,s.validateRangeRequestCapabilities)({getResponseHeader,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=e;this._contentLength=r||this._contentLength;this._filename=(0,s.extractFilenameFromHeader)(getResponseHeader);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new i.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,i){this._stream=t;this._reader=null;this._loaded=0;const n=t.source;this._withCredentials=n.withCredentials||!1;this._readCapability=Promise.withResolvers();this._isStreamingSupported=!n.disableStream;this._abortController=new AbortController;this._headers=createHeaders(this._stream.httpHeaders);this._headers.append("Range",`bytes=${e}-${i-1}`);const r=n.url;fetch(r,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,s.validateResponseStatus)(t.status))throw(0,s.createResponseStatusError)(t.status,r);this._readCapability.resolve();this._reader=t.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}},10:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{FontFaceObject:()=>FontFaceObject,FontLoader:()=>FontLoader});e(4114),e(3375),e(9225),e(3972),e(9209),e(5714),e(7561),e(6197),e(4979);var i=e(4292);class FontLoader{#qr=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t;this.nativeFontFaces=new Set;this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t);this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t);this._document.fonts.delete(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear();this.#qr.clear();if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(t&&!this.#qr.has(t.loadedName)){(0,i.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set.");if(this.isFontLoadingAPISupported){const{loadedName:s,src:n,style:r}=t,a=new FontFace(s,n,r);this.addNativeFontFace(a);try{await a.load();this.#qr.add(s);e?.(t)}catch{(0,i.warn)(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`);this.removeNativeFontFace(a)}}else(0,i.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;t.attached=!0;if(t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(s){(0,i.warn)(`Failed to load font '${e.family}': '${s}'.`);t.disableFontFace=!0;throw s}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return(0,i.shadow)(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){let t=!1;(i.isNodeJS||"undefined"!=typeof navigator&&"string"==typeof navigator?.userAgent&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0);return(0,i.shadow)(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,s={done:!1,complete:function completeRequest(){(0,i.assert)(!s.done,"completeRequest() cannot be called twice.");s.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(s);return s}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,i.shadow)(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let s,n;const r=this._document.createElement("canvas");r.width=1;r.height=1;const a=r.getContext("2d");let o=0;const l=`lt${Date.now()}${this.loadTestFontId++}`;let h=this._loadTestFont;h=spliceString(h,976,l.length,l);const d=1482184792;let c=int32(h,16);for(s=0,n=l.length-3;s<n;s+=4)c=c-d+int32(l,s)|0;s<l.length&&(c=c-d+int32(l+"XXX",s)|0);h=spliceString(h,16,4,(0,i.string32)(c));const u=`@font-face {font-family:"${l}";src:${`url(data:font/opentype;base64,${btoa(h)});`}}`;this.insertRule(u);const p=this._document.createElement("div");p.style.visibility="hidden";p.style.width=p.style.height="10px";p.style.position="absolute";p.style.top=p.style.left="0px";for(const e of[t.loadedName,l]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;p.append(t)}this._document.body.append(p);!function isFontReady(t,e){if(++o>30){(0,i.warn)("Load test font never loaded.");e();return}a.font="30px "+t;a.fillText(".",0,20);a.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(l,(()=>{p.remove();e.complete()}))}}class FontFaceObject{constructor(t,{disableFontFace:e=!1,ignoreErrors:i=!1,inspectFont:s=null}){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.disableFontFace=!0===e;this.ignoreErrors=!0===i;this._inspectFont=s}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this._inspectFont?.(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=(0,i.bytesToString)(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let s;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);s=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else s=`@font-face {font-family:"${this.loadedName}";src:${e}}`;this._inspectFont?.(this,e);return s}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let s;try{s=t.get(this.loadedName+"_path_"+e)}catch(t){if(!this.ignoreErrors)throw t;(0,i.warn)(`getPathGenerator - ignoring character: "${t}".`)}if(!Array.isArray(s)||0===s.length)return this.compiledGlyphs[e]=function(t,e){};const n=[];for(let t=0,e=s.length;t<e;)switch(s[t++]){case i.FontRenderOps.BEZIER_CURVE_TO:{const[e,i,r,a,o,l]=s.slice(t,t+6);n.push((t=>t.bezierCurveTo(e,i,r,a,o,l)));t+=6}break;case i.FontRenderOps.MOVE_TO:{const[e,i]=s.slice(t,t+2);n.push((t=>t.moveTo(e,i)));t+=2}break;case i.FontRenderOps.LINE_TO:{const[e,i]=s.slice(t,t+2);n.push((t=>t.lineTo(e,i)));t+=2}break;case i.FontRenderOps.QUADRATIC_CURVE_TO:{const[e,i,r,a]=s.slice(t,t+4);n.push((t=>t.quadraticCurveTo(e,i,r,a)));t+=4}break;case i.FontRenderOps.RESTORE:n.push((t=>t.restore()));break;case i.FontRenderOps.SAVE:n.push((t=>t.save()));break;case i.FontRenderOps.SCALE:(0,i.assert)(2===n.length,"Scale command is only valid at the third position.");break;case i.FontRenderOps.TRANSFORM:{const[e,i,r,a,o,l]=s.slice(t,t+6);n.push((t=>t.transform(e,i,r,a,o,l)));t+=6}break;case i.FontRenderOps.TRANSLATE:{const[e,i]=s.slice(t,t+2);n.push((t=>t.translate(e,i)));t+=2}}return this.compiledGlyphs[e]=function glyphDrawer(t,e){n[0](t);n[1](t);t.scale(e,-e);for(let e=2,i=n.length;e<i;e++)n[e](t)}}}},6062:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{Metadata:()=>Metadata});var i=e(4292);class Metadata{#Kr;#Xr;constructor({parsedData:t,rawData:e}){this.#Kr=t;this.#Xr=e}getRaw(){return this.#Xr}get(t){return this.#Kr.get(t)??null}getAll(){return(0,i.objectFromMap)(this.#Kr)}has(t){return this.#Kr.has(t)}}},7457:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{PDFNetworkStream:()=>PDFNetworkStream});e(4114),e(4628);var i=e(4292),s=e(6490);class NetworkManager{constructor(t,e={}){this.url=t;this.isHttp=/^https?:/i.test(t);this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null);this.withCredentials=e.withCredentials||!1;this.currXhrId=0;this.pendingRequests=Object.create(null)}requestRange(t,e,i){const s={begin:t,end:e};for(const t in i)s[t]=i[t];return this.request(s)}requestFull(t){return this.request(t)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const t in this.httpHeaders){const i=this.httpHeaders[t];void 0!==i&&e.setRequestHeader(t,i)}if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);s.expectedStatus=206}else s.expectedStatus=200;e.responseType="arraybuffer";t.onError&&(e.onerror=function(i){t.onError(e.status)});e.onreadystatechange=this.onStateChange.bind(this,i);e.onprogress=this.onProgress.bind(this,i);s.onHeadersReceived=t.onHeadersReceived;s.onDone=t.onDone;s.onError=t.onError;s.onProgress=t.onProgress;e.send(null);return i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const n=s.xhr;if(n.readyState>=2&&s.onHeadersReceived){s.onHeadersReceived();delete s.onHeadersReceived}if(4!==n.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===n.status&&this.isHttp){s.onError?.(n.status);return}const r=n.status||200;if(!(200===r&&206===s.expectedStatus)&&r!==s.expectedStatus){s.onError?.(n.status);return}const a=function getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:(0,i.stringToBytes)(e).buffer}(n);if(206===r){const t=n.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);s.onDone({begin:parseInt(e[1],10),chunk:a})}else a?s.onDone({begin:0,chunk:a}):s.onError?.(n.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials});this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const i=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);i.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;const i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url;this._fullRequestId=t.requestFull(i);this._headersReceivedCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),getResponseHeader=t=>e.getResponseHeader(t),{allowRangeRequests:i,suggestedLength:n}=(0,s.validateRangeRequestCapabilities)({getResponseHeader,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});i&&(this._isRangeSupported=!0);this._contentLength=n||this._contentLength;this._filename=(0,s.extractFilenameFromHeader)(getResponseHeader);this._isRangeSupported&&this._manager.abortRequest(t);this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=(0,s.createResponseStatusError)(t,this._url);this._headersReceivedCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersReceivedCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,i){this._manager=t;const s={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url;this._requestId=t.requestRange(e,i,s);this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError=(0,s.createResponseStatusError)(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}},6490:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{createResponseStatusError:()=>createResponseStatusError,extractFilenameFromHeader:()=>extractFilenameFromHeader,validateRangeRequestCapabilities:()=>validateRangeRequestCapabilities,validateResponseStatus:()=>validateResponseStatus});var i=e(4292);e(4114),e(4979);var s=e(5419);function validateRangeRequestCapabilities({getResponseHeader:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t("Content-Length"),10);if(!Number.isInteger(r))return n;n.suggestedLength=r;if(r<=2*i)return n;if(s||!e)return n;if("bytes"!==t("Accept-Ranges"))return n;if("identity"!==(t("Content-Encoding")||"identity"))return n;n.allowRangeRequests=!0;return n}function extractFilenameFromHeader(t){const e=t("Content-Disposition");if(e){let t=function getFilenameFromContentDispositionHeader(t){let e=!0,s=toParamRegExp("filename\\*","i").exec(t);if(s){s=s[1];let t=rfc2616unquote(s);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}s=function rfc2231getparam(t){const e=[];let i;const s=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=s.exec(t));){let[,t,s,n]=i;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[s,n]}const n=[];for(let t=0;t<e.length&&t in e;++t){let[i,s]=e[t];s=rfc2616unquote(s);if(i){s=unescape(s);0===t&&(s=rfc5987decode(s))}n.push(s)}return n.join("")}(t);if(s)return fixupEncoding(rfc2047decode(s));s=toParamRegExp("filename","i").exec(t);if(s){s=s[1];let t=rfc2616unquote(s);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,s){if(t){if(!/^[\x00-\xFF]+$/.test(s))return s;try{const n=new TextDecoder(t,{fatal:!0}),r=(0,i.stringToBytes)(s);s=n.decode(r);e=!1}catch{}}return s}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');if(-1!==i){e[t]=e[t].slice(0,i);e.length=t+1}e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");return-1===e?t:textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return textdecode(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return textdecode(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if((0,s.isPdfFile)(t))return t}return null}function createResponseStatusError(t,e){return 404===t||0===t&&e.startsWith("file:")?new i.MissingPDFException('Missing PDF "'+e+'".'):new i.UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)}function validateResponseStatus(t){return 200===t||206===t}},4786:(t,__webpack_exports__,e)=>{e.a(t,(async(t,i)=>{try{e.d(__webpack_exports__,{PDFNodeStream:()=>PDFNodeStream});e(4114),e(6573),e(8100),e(7936),e(4628),e(7467),e(4732),e(9577);var s=e(4292),n=e(6490);let r,a,o,l;if(s.isNodeJS){r=await import("fs");a=await import("http");o=await import("https");l=await import("url")}const h=/^file:\/\/\/[a-zA-Z]:\//;function parseUrl(t){const e=l.parse(t);if("file:"===e.protocol||e.host)return e;if(/^[a-z]:[/\\]/i.test(t))return l.parse(`file:///${t}`);e.host||(e.protocol="file:");return e}class PDFNodeStream{constructor(t){this.source=t;this.url=parseUrl(t.url);this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol;this.isFsUrl="file:"===this.url.protocol;this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,s.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=this.isFsUrl?new PDFNodeStreamFsFullReader(this):new PDFNodeStreamFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=this.isFsUrl?new PDFNodeStreamFsRangeReader(this,t,e):new PDFNodeStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class BaseFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=Promise.withResolvers();this._headersCapability=Promise.withResolvers()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new s.AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class BaseRangeReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=Promise.withResolvers();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}function createRequestOptions(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class PDFNodeStreamFullReader extends BaseFullReader{constructor(t){super(t);const handleResponse=e=>{if(404===e.statusCode){const t=new s.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t;this._headersCapability.reject(t);return}this._headersCapability.resolve();this._setReadableStream(e);const getResponseHeader=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:i,suggestedLength:r}=(0,n.validateRangeRequestCapabilities)({getResponseHeader,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=i;this._contentLength=r||this._contentLength;this._filename=(0,n.extractFilenameFromHeader)(getResponseHeader)};this._request=null;"http:"===this._url.protocol?this._request=a.request(createRequestOptions(this._url,t.httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,t.httpHeaders),handleResponse);this._request.on("error",(t=>{this._storedError=t;this._headersCapability.reject(t)}));this._request.end()}}class PDFNodeStreamRangeReader extends BaseRangeReader{constructor(t,e,i){super(t);this._httpHeaders={};for(const e in t.httpHeaders){const i=t.httpHeaders[e];void 0!==i&&(this._httpHeaders[e]=i)}this._httpHeaders.Range=`bytes=${e}-${i-1}`;const handleResponse=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new s.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t}};this._request=null;"http:"===this._url.protocol?this._request=a.request(createRequestOptions(this._url,this._httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,this._httpHeaders),handleResponse);this._request.on("error",(t=>{this._storedError=t}));this._request.end()}}class PDFNodeStreamFsFullReader extends BaseFullReader{constructor(t){super(t);let e=decodeURIComponent(this._url.path);h.test(this._url.href)&&(e=e.replace(/^\//,""));r.promises.lstat(e).then((t=>{this._contentLength=t.size;this._setReadableStream(r.createReadStream(e));this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=new s.MissingPDFException(`Missing PDF "${e}".`));this._storedError=t;this._headersCapability.reject(t)}))}}class PDFNodeStreamFsRangeReader extends BaseRangeReader{constructor(t,e,i){super(t);let s=decodeURIComponent(this._url.path);h.test(this._url.href)&&(s=s.replace(/^\//,""));this._setReadableStream(r.createReadStream(s,{start:e,end:i-1}))}}i()}catch(d){i(d)}}),1)},1573:(t,__webpack_exports__,e)=>{e.a(t,(async(t,i)=>{try{e.d(__webpack_exports__,{NodeCMapReaderFactory:()=>NodeCMapReaderFactory,NodeCanvasFactory:()=>NodeCanvasFactory,NodeFilterFactory:()=>NodeFilterFactory,NodeStandardFontDataFactory:()=>NodeStandardFontDataFactory});e(6573),e(8100),e(7936),e(7467),e(4732),e(9577);var s=e(2583),n=e(4292);let t,r,a;if(n.isNodeJS){t=await import("fs");try{r=await import("canvas")}catch{}try{a=await import("path2d")}catch{}}!function checkDOMMatrix(){if(globalThis.DOMMatrix||!n.isNodeJS)return;const t=r?.DOMMatrix;t?globalThis.DOMMatrix=t:(0,n.warn)("Cannot polyfill `DOMMatrix`, rendering may be broken.")}();!function checkPath2D(){if(globalThis.Path2D||!n.isNodeJS)return;const t=r?.CanvasRenderingContext2D,e=a?.applyPath2DToCanvasRenderingContext,i=a?.Path2D;if(t&&e&&i){e(t);globalThis.Path2D=i}else(0,n.warn)("Cannot polyfill `Path2D`, rendering may be broken.")}();const fetchData=function(e){return t.promises.readFile(e).then((t=>new Uint8Array(t)))};class NodeFilterFactory extends s.BaseFilterFactory{}class NodeCanvasFactory extends s.BaseCanvasFactory{_createCanvas(t,e){return r.createCanvas(t,e)}}class NodeCMapReaderFactory extends s.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t).then((t=>({cMapData:t,compressionType:e})))}}class NodeStandardFontDataFactory extends s.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t)}}i()}catch(t){i(t)}}),1)},5626:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{OptionalContentConfig:()=>OptionalContentConfig});var i=e(4292),s=e(7651);const n=Symbol("INTERNAL");class OptionalContentGroup{#Yr=!1;#Jr=!1;#Qr=!1;#Zr=!0;constructor(t,{name:e,intent:s,usage:n}){this.#Yr=!!(t&i.RenderingIntentFlag.DISPLAY);this.#Jr=!!(t&i.RenderingIntentFlag.PRINT);this.name=e;this.intent=s;this.usage=n}get visible(){if(this.#Qr)return this.#Zr;if(!this.#Zr)return!1;const{print:t,view:e}=this.usage;return this.#Yr?"OFF"!==e?.viewState:!this.#Jr||"OFF"!==t?.printState}_setVisible(t,e,s=!1){t!==n&&(0,i.unreachable)("Internal method `_setVisible` called.");this.#Qr=s;this.#Zr=e}}class OptionalContentConfig{#ta=null;#ea=new Map;#ia=null;#sa=null;constructor(t,e=i.RenderingIntentFlag.DISPLAY){this.renderingIntent=e;this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#sa=t.order;for(const i of t.groups)this.#ea.set(i.id,new OptionalContentGroup(e,i));if("OFF"===t.baseState)for(const t of this.#ea.values())t._setVisible(n,!1);for(const e of t.on)this.#ea.get(e)._setVisible(n,!0);for(const e of t.off)this.#ea.get(e)._setVisible(n,!1);this.#ia=this.getHash()}}#na(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let n=1;n<e;n++){const e=t[n];let r;if(Array.isArray(e))r=this.#na(e);else{if(!this.#ea.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}r=this.#ea.get(e).visible}switch(s){case"And":if(!r)return!1;break;case"Or":if(r)return!0;break;case"Not":return!r;default:return!0}}return"And"===s}isVisible(t){if(0===this.#ea.size)return!0;if(!t){(0,i.info)("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#ea.has(t.id)){(0,i.warn)(`Optional content group not found: ${t.id}`);return!0}return this.#ea.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#na(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#ea.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(this.#ea.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#ea.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(!this.#ea.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#ea.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(!this.#ea.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#ea.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(this.#ea.get(e).visible)return!1}return!0}(0,i.warn)(`Unknown optional content policy ${t.policy}.`);return!0}(0,i.warn)(`Unknown group type ${t.type}.`);return!0}setVisibility(t,e=!0){const s=this.#ea.get(t);if(s){s._setVisible(n,!!e,!0);this.#ta=null}else(0,i.warn)(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const e of t){switch(e){case"ON":case"OFF":case"Toggle":i=e;continue}const t=this.#ea.get(e);if(t)switch(i){case"ON":t._setVisible(n,!0);break;case"OFF":t._setVisible(n,!1);break;case"Toggle":t._setVisible(n,!t.visible)}}this.#ta=null}get hasInitialVisibility(){return null===this.#ia||this.getHash()===this.#ia}getOrder(){return this.#ea.size?this.#sa?this.#sa.slice():[...this.#ea.keys()]:null}getGroups(){return this.#ea.size>0?(0,i.objectFromMap)(this.#ea):null}getGroup(t){return this.#ea.get(t)||null}getHash(){if(null!==this.#ta)return this.#ta;const t=new s.MurmurHash3_64;for(const[e,i]of this.#ea)t.update(`${e}:${i.visible}`);return this.#ta=t.hexdigest()}}},6814:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{cleanupTextLayer:()=>cleanupTextLayer,renderTextLayer:()=>renderTextLayer,updateTextLayer:()=>updateTextLayer});e(4114),e(4628);var i=e(4292),s=e(5419);const n=30,r=.8,a=new Map;let o=null;function getCtx(){if(!o){const t=document.createElement("canvas");t.className="hiddenCanvasElement";document.body.append(t);o=t.getContext("2d",{alpha:!1})}return o}function cleanupTextLayer(){o?.canvas.remove();o=null}function appendText(t,e,s){const o=document.createElement("span"),l={angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,fontSize:0};t._textDivs.push(o);const h=i.Util.transform(t._transform,e.transform);let d=Math.atan2(h[1],h[0]);const c=s[e.fontName];c.vertical&&(d+=Math.PI/2);const u=t._fontInspectorEnabled&&c.fontSubstitution||c.fontFamily,p=Math.hypot(h[2],h[3]),g=p*function getAscent(t){const e=a.get(t);if(e)return e;const i=getCtx(),s=i.font;i.canvas.width=i.canvas.height=n;i.font=`${n}px ${t}`;const o=i.measureText("");let l=o.fontBoundingBoxAscent,h=Math.abs(o.fontBoundingBoxDescent);if(l){const e=l/(l+h);a.set(t,e);i.canvas.width=i.canvas.height=0;i.font=s;return e}i.strokeStyle="red";i.clearRect(0,0,n,n);i.strokeText("g",0,0);let d=i.getImageData(0,0,n,n).data;h=0;for(let t=d.length-1-3;t>=0;t-=4)if(d[t]>0){h=Math.ceil(t/4/n);break}i.clearRect(0,0,n,n);i.strokeText("A",0,n);d=i.getImageData(0,0,n,n).data;l=0;for(let t=0,e=d.length;t<e;t+=4)if(d[t]>0){l=n-Math.floor(t/4/n);break}i.canvas.width=i.canvas.height=0;i.font=s;if(l){const e=l/(l+h);a.set(t,e);return e}a.set(t,r);return r}(u);let f,m;if(0===d){f=h[4];m=h[5]-g}else{f=h[4]+g*Math.sin(d);m=h[5]-g*Math.cos(d)}const b="calc(var(--scale-factor)*",v=o.style;if(t._container===t._rootContainer){v.left=`${(100*f/t._pageWidth).toFixed(2)}%`;v.top=`${(100*m/t._pageHeight).toFixed(2)}%`}else{v.left=`${b}${f.toFixed(2)}px)`;v.top=`${b}${m.toFixed(2)}px)`}v.fontSize=`${b}${p.toFixed(2)}px)`;v.fontFamily=u;l.fontSize=p;o.setAttribute("role","presentation");o.textContent=e.str;o.dir=e.dir;t._fontInspectorEnabled&&(o.dataset.fontName=c.fontSubstitutionLoadedName||e.fontName);0!==d&&(l.angle=d*(180/Math.PI));let y=!1;if(e.str.length>1)y=!0;else if(" "!==e.str&&e.transform[0]!==e.transform[3]){const t=Math.abs(e.transform[0]),i=Math.abs(e.transform[3]);t!==i&&Math.max(t,i)/Math.min(t,i)>1.5&&(y=!0)}y&&(l.canvasWidth=c.vertical?e.height:e.width);t._textDivProperties.set(o,l);t._isReadableStream&&t._layoutText(o)}function layout(t){const{div:e,scale:i,properties:s,ctx:n,prevFontSize:r,prevFontFamily:a}=t,{style:o}=e;let l="";if(0!==s.canvasWidth&&s.hasText){const{fontFamily:h}=o,{canvasWidth:d,fontSize:c}=s;if(r!==c||a!==h){n.font=`${c*i}px ${h}`;t.prevFontSize=c;t.prevFontFamily=h}const{width:u}=n.measureText(e.textContent);u>0&&(l=`scaleX(${d*i/u})`)}0!==s.angle&&(l=`rotate(${s.angle}deg) ${l}`);l.length>0&&(o.transform=l)}class TextLayerRenderTask{constructor({textContentSource:t,container:e,viewport:i,textDivs:n,textDivProperties:r,textContentItemsStr:a}){this._textContentSource=t;this._isReadableStream=t instanceof ReadableStream;this._container=this._rootContainer=e;this._textDivs=n||[];this._textContentItemsStr=a||[];this._fontInspectorEnabled=!!globalThis.FontInspector?.enabled;this._reader=null;this._textDivProperties=r||new WeakMap;this._canceled=!1;this._capability=Promise.withResolvers();this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:i.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:getCtx()};const{pageWidth:o,pageHeight:l,pageX:h,pageY:d}=i.rawDims;this._transform=[1,0,0,-1,-h,d+l];this._pageWidth=o;this._pageHeight=l;(0,s.setLayerDimensions)(e,i);this._capability.promise.finally((()=>{this._layoutTextParams=null})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0;if(this._reader){this._reader.cancel(new i.AbortException("TextLayer task cancelled.")).catch((()=>{}));this._reader=null}this._capability.reject(new i.AbortException("TextLayer task cancelled."))}_processItems(t,e){for(const i of t)if(void 0!==i.str){this._textContentItemsStr.push(i.str);appendText(this,i,e)}else if("beginMarkedContentProps"===i.type||"beginMarkedContent"===i.type){const t=this._container;this._container=document.createElement("span");this._container.classList.add("markedContent");null!==i.id&&this._container.setAttribute("id",`${i.id}`);t.append(this._container)}else"endMarkedContent"===i.type&&(this._container=this._container.parentNode)}_layoutText(t){const e=this._layoutTextParams.properties=this._textDivProperties.get(t);this._layoutTextParams.div=t;layout(this._layoutTextParams);e.hasText&&this._container.append(t);if(e.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this._container.append(t)}}_render(){const{promise:t,resolve:e,reject:i}=Promise.withResolvers();let s=Object.create(null);if(this._isReadableStream){const pump=()=>{this._reader.read().then((({value:t,done:i})=>{if(i)e();else{Object.assign(s,t.styles);this._processItems(t.items,s);pump()}}),i)};this._reader=this._textContentSource.getReader();pump()}else{if(!this._textContentSource)throw new Error('No "textContentSource" parameter specified.');{const{items:t,styles:i}=this._textContentSource;this._processItems(t,i);e()}}t.then((()=>{s=null;!function render(t){if(t._canceled)return;const e=t._textDivs,i=t._capability;if(e.length>1e5)i.resolve();else{if(!t._isReadableStream)for(const i of e)t._layoutText(i);i.resolve()}}(this)}),this._capability.reject)}}function renderTextLayer(t){const e=new TextLayerRenderTask(t);e._render();return e}function updateTextLayer({container:t,viewport:e,textDivs:i,textDivProperties:n,mustRotate:r=!0,mustRescale:a=!0}){r&&(0,s.setLayerDimensions)(t,{rotation:e.rotation});if(a){const t=getCtx(),s={prevFontSize:null,prevFontFamily:null,div:null,scale:e.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:t};for(const t of i){s.properties=n.get(t);s.div=t;layout(s)}}}},585:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{PDFDataTransportStream:()=>PDFDataTransportStream});e(4114),e(6573),e(8100),e(7936),e(4628),e(7467),e(4732),e(9577),e(8992),e(7550);var i=e(4292),s=e(5419);class PDFDataTransportStream{constructor(t,{disableRange:e=!1,disableStream:s=!1}){(0,i.assert)(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:n,initialData:r,progressiveDone:a,contentDispositionFilename:o}=t;this._queuedChunks=[];this._progressiveDone=a;this._contentDispositionFilename=o;if(r?.length>0){const t=r instanceof Uint8Array&&r.byteLength===r.buffer.byteLength?r.buffer:new Uint8Array(r).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t;this._isStreamingSupported=!s;this._isRangeSupported=!e;this._contentLength=n;this._fullRequestReader=null;this._rangeReaders=[];t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));t.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const e=this._rangeReaders.some((function(e){if(e._begin!==t)return!1;e._enqueue(s);return!0}));(0,i.assert)(e,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class PDFDataTransportStreamReader{constructor(t,e,i=!1,n=null){this._stream=t;this._done=i||!1;this._filename=(0,s.isPdfFile)(n)?n:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,i){this._stream=t;this._begin=e;this._end=i;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}},6164:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{GlobalWorkerOptions:()=>GlobalWorkerOptions});class GlobalWorkerOptions{static#ra=null;static#aa="";static get workerPort(){return this.#ra}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#ra=t}static get workerSrc(){return this.#aa}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#aa=t}}},8284:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{XfaLayer:()=>XfaLayer});e(4114);var i=e(2050);class XfaLayer{static setupStorage(t,e,i,s,n){const r=s.getValue(e,{value:null});switch(i.name){case"textarea":null!==r.value&&(t.textContent=r.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked");if("print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==r.value&&t.setAttribute("value",r.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value){t.setAttribute("value",r.value);for(const t of i.children)t.attributes.value===r.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;"radio"===r.type&&(r.name=`${r.name}-${s}`);for(const[e,i]of Object.entries(r))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!a||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}a&&n.addLinkAttributes(t,r.href,r.newWindow);i&&r.dataId&&this.setupStorage(t,r.dataId,e,i)}static render(t){const e=t.annotationStorage,s=t.linkService,n=t.xfaHtml,r=t.intent||"display",a=document.createElement(n.name);n.attributes&&this.setAttributes({html:a,element:n,intent:r,linkService:s});const o="richText"!==r,l=t.div;l.append(a);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=e}o&&l.setAttribute("class","xfaLayer xfaFont");const h=[];if(0===n.children.length){if(n.value){const t=document.createTextNode(n.value);a.append(t);o&&i.XfaText.shouldBuildText(n.name)&&h.push(t)}return{textDivs:h}}const d=[[n,-1,a]];for(;d.length>0;){const[t,n,a]=d.at(-1);if(n+1===t.children.length){d.pop();continue}const l=t.children[++d.at(-1)[1]];if(null===l)continue;const{name:c}=l;if("#text"===c){const t=document.createTextNode(l.value);h.push(t);a.append(t);continue}const u=l?.attributes?.xmlns?document.createElementNS(l.attributes.xmlns,c):document.createElement(c);a.append(u);l.attributes&&this.setAttributes({html:u,element:l,storage:e,intent:r,linkService:s});if(l.children?.length>0)d.push([l,-1,u]);else if(l.value){const t=document.createTextNode(l.value);o&&i.XfaText.shouldBuildText(c)&&h.push(t);u.append(t)}}for(const t of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:h}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}},2050:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{XfaText:()=>XfaText});e(4114);class XfaText{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let i=null;const s=t.name;if("#text"===s)i=t.value;else{if(!XfaText.shouldBuildText(s))return;t?.attributes?.textContent?i=t.attributes.textContent:t.value&&(i=t.value)}null!==i&&e.push({str:i});if(t.children)for(const e of t.children)walk(e)}(t);return i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}},1228:(t,__webpack_exports__,e)=>{e.a(t,(async(t,i)=>{try{e.d(__webpack_exports__,{AbortException:()=>s.AbortException,AnnotationEditorLayer:()=>o.AnnotationEditorLayer,AnnotationEditorParamsType:()=>s.AnnotationEditorParamsType,AnnotationEditorType:()=>s.AnnotationEditorType,AnnotationEditorUIManager:()=>l.AnnotationEditorUIManager,AnnotationLayer:()=>h.AnnotationLayer,AnnotationMode:()=>s.AnnotationMode,CMapCompressionType:()=>s.CMapCompressionType,ColorPicker:()=>d.ColorPicker,DOMSVGFactory:()=>r.DOMSVGFactory,DrawLayer:()=>c.DrawLayer,FeatureTest:()=>s.FeatureTest,GlobalWorkerOptions:()=>u.GlobalWorkerOptions,ImageKind:()=>s.ImageKind,InvalidPDFException:()=>s.InvalidPDFException,MissingPDFException:()=>s.MissingPDFException,OPS:()=>s.OPS,Outliner:()=>p.Outliner,PDFDataRangeTransport:()=>n.PDFDataRangeTransport,PDFDateString:()=>r.PDFDateString,PDFWorker:()=>n.PDFWorker,PasswordResponses:()=>s.PasswordResponses,PermissionFlag:()=>s.PermissionFlag,PixelsPerInch:()=>r.PixelsPerInch,RenderingCancelledException:()=>r.RenderingCancelledException,UnexpectedResponseException:()=>s.UnexpectedResponseException,Util:()=>s.Util,VerbosityLevel:()=>s.VerbosityLevel,XfaLayer:()=>g.XfaLayer,build:()=>n.build,createValidAbsoluteUrl:()=>s.createValidAbsoluteUrl,fetchData:()=>r.fetchData,getDocument:()=>n.getDocument,getFilenameFromUrl:()=>r.getFilenameFromUrl,getPdfFilenameFromUrl:()=>r.getPdfFilenameFromUrl,getXfaPageViewport:()=>r.getXfaPageViewport,isDataScheme:()=>r.isDataScheme,isPdfFile:()=>r.isPdfFile,noContextMenu:()=>r.noContextMenu,normalizeUnicode:()=>s.normalizeUnicode,renderTextLayer:()=>a.renderTextLayer,setLayerDimensions:()=>r.setLayerDimensions,shadow:()=>s.shadow,updateTextLayer:()=>a.updateTextLayer,version:()=>n.version});var s=e(4292),n=e(3831),r=e(5419),a=e(6814),o=e(9731),l=e(7830),h=e(6976),d=e(2259),c=e(4047),u=e(6164),p=e(4061),g=e(8284),f=t([n]);n=(f.then?(await f)():f)[0];i()}catch(t){i(t)}}))},5178:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{MessageHandler:()=>MessageHandler});e(4628);var i=e(4292);const s=1,n=2,r=1,a=2,o=3,l=4,h=5,d=6,c=7,u=8;function wrapReason(t){t instanceof Error||"object"==typeof t&&null!==t||(0,i.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new i.AbortException(t.message);case"MissingPDFException":return new i.MissingPDFException(t.message);case"PasswordException":return new i.PasswordException(t.message,t.code);case"UnexpectedResponseException":return new i.UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new i.UnknownErrorException(t.message,t.details);default:return new i.UnknownErrorException(t.message,t.toString())}}class MessageHandler{constructor(t,e,i){this.sourceName=t;this.targetName=e;this.comObj=i;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream){this.#oa(e);return}if(e.callback){const t=e.callbackId,i=this.callbackCapabilities[t];if(!i)throw new Error(`Cannot resolve callback ${t}`);delete this.callbackCapabilities[t];if(e.callback===s)i.resolve(e.data);else{if(e.callback!==n)throw new Error("Unexpected callback case");i.reject(wrapReason(e.reason))}return}const r=this.actionHandler[e.action];if(!r)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,a=e.sourceName;new Promise((function(t){t(r(e.data))})).then((function(n){i.postMessage({sourceName:t,targetName:a,callback:s,callbackId:e.callbackId,data:n})}),(function(s){i.postMessage({sourceName:t,targetName:a,callback:n,callbackId:e.callbackId,reason:wrapReason(s)})}))}else e.streamId?this.#la(e):r(e.data)};i.addEventListener("message",this._onComObjOnMessage)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){n.reject(t)}return n.promise}sendWithStream(t,e,s,n){const a=this.streamId++,o=this.sourceName,l=this.targetName,h=this.comObj;return new ReadableStream({start:i=>{const s=Promise.withResolvers();this.streamControllers[a]={controller:i,startCall:s,pullCall:null,cancelCall:null,isClosed:!1};h.postMessage({sourceName:o,targetName:l,action:t,streamId:a,data:e,desiredSize:i.desiredSize},n);return s.promise},pull:t=>{const e=Promise.withResolvers();this.streamControllers[a].pullCall=e;h.postMessage({sourceName:o,targetName:l,stream:d,streamId:a,desiredSize:t.desiredSize});return e.promise},cancel:t=>{(0,i.assert)(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();this.streamControllers[a].cancelCall=e;this.streamControllers[a].isClosed=!0;h.postMessage({sourceName:o,targetName:l,stream:r,streamId:a,reason:wrapReason(t)});return e.promise}},s)}#la(t){const e=t.streamId,s=this.sourceName,n=t.sourceName,r=this.comObj,a=this,d=this.actionHandler[t.action],c={enqueue(t,i=1,a){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=i;if(o>0&&this.desiredSize<=0){this.sinkCapability=Promise.withResolvers();this.ready=this.sinkCapability.promise}r.postMessage({sourceName:s,targetName:n,stream:l,streamId:e,chunk:t},a)},close(){if(!this.isCancelled){this.isCancelled=!0;r.postMessage({sourceName:s,targetName:n,stream:o,streamId:e});delete a.streamSinks[e]}},error(t){(0,i.assert)(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;r.postMessage({sourceName:s,targetName:n,stream:h,streamId:e,reason:wrapReason(t)})}},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};c.sinkCapability.resolve();c.ready=c.sinkCapability.promise;this.streamSinks[e]=c;new Promise((function(e){e(d(t.data,c))})).then((function(){r.postMessage({sourceName:s,targetName:n,stream:u,streamId:e,success:!0})}),(function(t){r.postMessage({sourceName:s,targetName:n,stream:u,streamId:e,reason:wrapReason(t)})}))}#oa(t){const e=t.streamId,s=this.sourceName,n=t.sourceName,p=this.comObj,g=this.streamControllers[e],f=this.streamSinks[e];switch(t.stream){case u:t.success?g.startCall.resolve():g.startCall.reject(wrapReason(t.reason));break;case c:t.success?g.pullCall.resolve():g.pullCall.reject(wrapReason(t.reason));break;case d:if(!f){p.postMessage({sourceName:s,targetName:n,stream:c,streamId:e,success:!0});break}f.desiredSize<=0&&t.desiredSize>0&&f.sinkCapability.resolve();f.desiredSize=t.desiredSize;new Promise((function(t){t(f.onPull?.())})).then((function(){p.postMessage({sourceName:s,targetName:n,stream:c,streamId:e,success:!0})}),(function(t){p.postMessage({sourceName:s,targetName:n,stream:c,streamId:e,reason:wrapReason(t)})}));break;case l:(0,i.assert)(g,"enqueue should have stream controller");if(g.isClosed)break;g.controller.enqueue(t.chunk);break;case o:(0,i.assert)(g,"close should have stream controller");if(g.isClosed)break;g.isClosed=!0;g.controller.close();this.#ha(g,e);break;case h:(0,i.assert)(g,"error should have stream controller");g.controller.error(wrapReason(t.reason));this.#ha(g,e);break;case a:t.success?g.cancelCall.resolve():g.cancelCall.reject(wrapReason(t.reason));this.#ha(g,e);break;case r:if(!f)break;new Promise((function(e){e(f.onCancel?.(wrapReason(t.reason)))})).then((function(){p.postMessage({sourceName:s,targetName:n,stream:a,streamId:e,success:!0})}),(function(t){p.postMessage({sourceName:s,targetName:n,stream:a,streamId:e,reason:wrapReason(t)})}));f.sinkCapability.reject(wrapReason(t.reason));f.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#ha(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]);delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},7651:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{MurmurHash3_64:()=>MurmurHash3_64});e(6573),e(8100),e(7936),e(7467),e(4732),e(9577);const i=3285377520,s=4294901760,n=65535;class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:i;this.h2=t?4294967295&t:i}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length);i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);if(n<=255)e[i++]=n;else{e[i++]=n>>>8;e[i++]=255&n}}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice();i=e.byteLength}const r=i>>2,a=i-4*r,o=new Uint32Array(e.buffer,0,r);let l=0,h=0,d=this.h1,c=this.h2;const u=3432918353,p=461845907,g=11601,f=13715;for(let t=0;t<r;t++)if(1&t){l=o[t];l=l*u&s|l*g&n;l=l<<15|l>>>17;l=l*p&s|l*f&n;d^=l;d=d<<13|d>>>19;d=5*d+3864292196}else{h=o[t];h=h*u&s|h*g&n;h=h<<15|h>>>17;h=h*p&s|h*f&n;c^=h;c=c<<13|c>>>19;c=5*c+3864292196}l=0;switch(a){case 3:l^=e[4*r+2]<<16;case 2:l^=e[4*r+1]<<8;case 1:l^=e[4*r];l=l*u&s|l*g&n;l=l<<15|l>>>17;l=l*p&s|l*f&n;1&r?d^=l:c^=l}this.h1=d;this.h2=c}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&s|36045*t&n;e=4283543511*e&s|(2950163797*(e<<16|t>>>16)&s)>>>16;t^=e>>>1;t=444984403*t&s|60499*t&n;e=3301882366*e&s|(3120437893*(e<<16|t>>>16)&s)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}},4292:(t,__webpack_exports__,e)=>{e.d(__webpack_exports__,{AbortException:()=>AbortException,AnnotationBorderStyleType:()=>m,AnnotationEditorParamsType:()=>c,AnnotationEditorPrefix:()=>h,AnnotationEditorType:()=>d,AnnotationMode:()=>l,AnnotationPrefix:()=>T,AnnotationType:()=>f,BaseException:()=>w,CMapCompressionType:()=>v,FONT_IDENTITY_MATRIX:()=>n,FeatureTest:()=>FeatureTest,FontRenderOps:()=>C,FormatError:()=>FormatError,IDENTITY_MATRIX:()=>s,ImageKind:()=>g,InvalidPDFException:()=>InvalidPDFException,LINE_FACTOR:()=>a,MAX_IMAGE_SIZE_TO_CACHE:()=>r,MissingPDFException:()=>MissingPDFException,OPS:()=>y,PasswordException:()=>PasswordException,PasswordResponses:()=>A,PermissionFlag:()=>u,RenderingIntentFlag:()=>o,TextRenderingMode:()=>p,UnexpectedResponseException:()=>UnexpectedResponseException,UnknownErrorException:()=>UnknownErrorException,Util:()=>Util,VerbosityLevel:()=>b,assert:()=>assert,bytesToString:()=>bytesToString,createValidAbsoluteUrl:()=>createValidAbsoluteUrl,getUuid:()=>getUuid,getVerbosityLevel:()=>getVerbosityLevel,info:()=>info,isNodeJS:()=>i,normalizeUnicode:()=>normalizeUnicode,objectFromMap:()=>objectFromMap,setVerbosityLevel:()=>setVerbosityLevel,shadow:()=>shadow,string32:()=>string32,stringToBytes:()=>stringToBytes,unreachable:()=>unreachable,warn:()=>warn});e(4114),e(6573),e(8100),e(7936),e(7467),e(4732),e(9577),e(4603),e(7566),e(8721);const i=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),s=[1,0,0,1,0,0],n=[.001,0,0,.001,0,0],r=1e7,a=1.35,o={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256},l={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},h="pdfjs_internal_editor_",d={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},c={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35},u={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},p={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},g={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},f={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},m={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},b={ERRORS:0,WARNINGS:1,INFOS:5},v={NONE:0,BINARY:1},y={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},A={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let E=b.WARNINGS;function setVerbosityLevel(t){Number.isInteger(t)&&(E=t)}function getVerbosityLevel(){return E}function info(t){E>=b.INFOS&&console.log(`Info: ${t}`)}function warn(t){E>=b.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function assert(t,e){t||unreachable(e)}function createValidAbsoluteUrl(t,e=null,i=null){if(!t)return null;try{if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=function stringToUTF8String(t){return decodeURIComponent(escape(t))}(t)}catch{}}const s=e?new URL(t,e):new URL(t);if(function _isValidProtocol(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s))return s}catch{}return null}function shadow(t,e,i,s=!1){Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1});return i}const w=function BaseExceptionClosure(){function BaseException(t,e){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class PasswordException extends w{constructor(t,e){super(t,"PasswordException");this.code=e}}class UnknownErrorException extends w{constructor(t,e){super(t,"UnknownErrorException");this.details=e}}class InvalidPDFException extends w{constructor(t){super(t,"InvalidPDFException")}}class MissingPDFException extends w{constructor(t){super(t,"MissingPDFException")}}class UnexpectedResponseException extends w{constructor(t,e){super(t,"UnexpectedResponseException");this.status=e}}class FormatError extends w{constructor(t){super(t,"FormatError")}}class AbortException extends w{constructor(t){super(t,"AbortException")}}function bytesToString(t){"object"==typeof t&&void 0!==t?.length||unreachable("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const r=Math.min(n+i,e),a=t.subarray(n,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)}function objectFromMap(t){const e=Object.create(null);for(const[i,s]of t)e[i]=s;return e}class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"!=typeof navigator&&"string"==typeof navigator?.platform?shadow(this,"platform",{isMac:navigator.platform.includes("Mac")}):shadow(this,"platform",{isMac:!1})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const x=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,i){return`#${x[t]}${x[e]}${x[i]}`}static scaleMinMax(t,e){let i;if(t[0]){if(t[0]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[0];e[2]*=t[0];if(t[3]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[3];e[3]*=t[3]}else{i=e[0];e[0]=e[1];e[1]=i;i=e[2];e[2]=e[3];e[3]=i;if(t[1]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[1];e[3]*=t[1];if(t[2]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[2];e[2]*=t[2]}e[0]+=t[4];e[1]+=t[5];e[2]+=t[4];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const i=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i]}static getAxialAlignedBoundingBox(t,e){const i=this.applyTransform(t,e),s=this.applyTransform(t.slice(2,4),e),n=this.applyTransform([t[0],t[3]],e),r=this.applyTransform([t[2],t[1]],e);return[Math.min(i[0],s[0],n[0],r[0]),Math.min(i[1],s[1],n[1],r[1]),Math.max(i[0],s[0],n[0],r[0]),Math.max(i[1],s[1],n[1],r[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],s=t[0]*e[1]+t[1]*e[3],n=t[2]*e[0]+t[3]*e[2],r=t[2]*e[1]+t[3]*e[3],a=(i+r)/2,o=Math.sqrt((i+r)**2-4*(i*r-n*s))/2,l=a+o||1,h=a-o||1;return[Math.sqrt(l),Math.sqrt(h)]}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>r?null:[i,n,s,r]}static#da(t,e,i,s,n,r,a,o,l,h){if(l<=0||l>=1)return;const d=1-l,c=l*l,u=c*l,p=d*(d*(d*t+3*l*e)+3*c*i)+u*s,g=d*(d*(d*n+3*l*r)+3*c*a)+u*o;h[0]=Math.min(h[0],p);h[1]=Math.min(h[1],g);h[2]=Math.max(h[2],p);h[3]=Math.max(h[3],g)}static#ca(t,e,i,s,n,r,a,o,l,h,d,c){if(Math.abs(l)<1e-12){Math.abs(h)>=1e-12&&this.#da(t,e,i,s,n,r,a,o,-d/h,c);return}const u=h**2-4*d*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#da(t,e,i,s,n,r,a,o,(-h+p)/g,c);this.#da(t,e,i,s,n,r,a,o,(-h-p)/g,c)}static bezierBoundingBox(t,e,i,s,n,r,a,o,l){if(l){l[0]=Math.min(l[0],t,a);l[1]=Math.min(l[1],e,o);l[2]=Math.max(l[2],t,a);l[3]=Math.max(l[3],e,o)}else l=[Math.min(t,a),Math.min(e,o),Math.max(t,a),Math.max(e,o)];this.#ca(t,i,n,a,e,s,r,o,3*(3*(i-n)-t+a),6*(t-2*i+n),3*(i-t),l);this.#ca(t,i,n,a,e,s,r,o,3*(3*(s-r)-e+o),6*(e-2*s+r),3*(s-e),l);return l}}let _=null,S=null;function normalizeUnicode(t){if(!_){_=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;S=new Map([["ﬅ","ſt"]])}return t.replaceAll(_,((t,e,i)=>e?e.normalize("NFKC"):S.get(i)))}function getUuid(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(t);else for(let e=0;e<32;e++)t[e]=Math.floor(255*Math.random());return bytesToString(t)}const T="pdfjs_internal_id_",C={BEZIER_CURVE_TO:0,MOVE_TO:1,LINE_TO:2,QUADRATIC_CURVE_TO:3,RESTORE:4,SAVE:5,SCALE:6,TRANSFORM:7,TRANSLATE:8}}},r={};function __webpack_require__(t){var e=r[t];if(void 0!==e)return e.exports;var i=r[t]={exports:{}};n[t].call(i.exports,i,i.exports,__webpack_require__);return i.exports}t="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",e="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",i="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",s=t=>{if(t&&t.d<1){t.d=1;t.forEach((t=>t.r--));t.forEach((t=>t.r--?t.r++:t()))}},__webpack_require__.a=(n,r,a)=>{var o;a&&((o=[]).d=-1);var l,h,d,c=new Set,u=n.exports,p=new Promise(((t,e)=>{d=e;h=t}));p[e]=u;p[t]=t=>(o&&t(o),c.forEach(t),p.catch((t=>{})));n.exports=p;r((n=>{l=(n=>n.map((n=>{if(null!==n&&"object"==typeof n){if(n[t])return n;if(n.then){var r=[];r.d=0;n.then((t=>{a[e]=t;s(r)}),(t=>{a[i]=t;s(r)}));var a={};a[t]=t=>t(r);return a}}var o={};o[t]=t=>{};o[e]=n;return o})))(n);var r,getResult=()=>l.map((t=>{if(t[i])throw t[i];return t[e]})),a=new Promise((e=>{(r=()=>e(getResult)).r=0;var fnQueue=t=>t!==o&&!c.has(t)&&(c.add(t),t&&!t.d&&(r.r++,t.push(r)));l.map((e=>e[t](fnQueue)))}));return r.r?a:getResult()}),(t=>(t?d(p[i]=t):h(u),s(o))));o&&o.d<0&&(o.d=0)};__webpack_require__.d=(t,e)=>{for(var i in e)__webpack_require__.o(e,i)&&!__webpack_require__.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})};__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var __webpack_exports__=__webpack_require__(1228),a=(__webpack_exports__=globalThis.pdfjsLib=await (globalThis.pdfjsLibPromise=__webpack_exports__)).AbortException,o=__webpack_exports__.AnnotationEditorLayer,l=__webpack_exports__.AnnotationEditorParamsType,h=__webpack_exports__.AnnotationEditorType,d=__webpack_exports__.AnnotationEditorUIManager,c=__webpack_exports__.AnnotationLayer,u=__webpack_exports__.AnnotationMode,p=__webpack_exports__.CMapCompressionType,g=__webpack_exports__.ColorPicker,f=__webpack_exports__.DOMSVGFactory,m=__webpack_exports__.DrawLayer,b=__webpack_exports__.FeatureTest,v=__webpack_exports__.GlobalWorkerOptions,y=__webpack_exports__.ImageKind,A=__webpack_exports__.InvalidPDFException,E=__webpack_exports__.MissingPDFException,w=__webpack_exports__.OPS,x=__webpack_exports__.Outliner,_=__webpack_exports__.PDFDataRangeTransport,S=__webpack_exports__.PDFDateString,T=__webpack_exports__.PDFWorker,C=__webpack_exports__.PasswordResponses,M=__webpack_exports__.PermissionFlag,P=__webpack_exports__.PixelsPerInch,R=__webpack_exports__.RenderingCancelledException,k=__webpack_exports__.UnexpectedResponseException,D=__webpack_exports__.Util,I=__webpack_exports__.VerbosityLevel,L=__webpack_exports__.XfaLayer,O=__webpack_exports__.build,N=__webpack_exports__.createValidAbsoluteUrl,B=__webpack_exports__.fetchData,H=__webpack_exports__.getDocument,U=__webpack_exports__.getFilenameFromUrl,z=__webpack_exports__.getPdfFilenameFromUrl,j=__webpack_exports__.getXfaPageViewport,V=__webpack_exports__.isDataScheme,G=__webpack_exports__.isPdfFile,W=__webpack_exports__.noContextMenu,$=__webpack_exports__.normalizeUnicode,q=__webpack_exports__.renderTextLayer,K=__webpack_exports__.setLayerDimensions,X=__webpack_exports__.shadow,Y=__webpack_exports__.updateTextLayer,J=__webpack_exports__.version;export{a as AbortException,o as AnnotationEditorLayer,l as AnnotationEditorParamsType,h as AnnotationEditorType,d as AnnotationEditorUIManager,c as AnnotationLayer,u as AnnotationMode,p as CMapCompressionType,g as ColorPicker,f as DOMSVGFactory,m as DrawLayer,b as FeatureTest,v as GlobalWorkerOptions,y as ImageKind,A as InvalidPDFException,E as MissingPDFException,w as OPS,x as Outliner,_ as PDFDataRangeTransport,S as PDFDateString,T as PDFWorker,C as PasswordResponses,M as PermissionFlag,P as PixelsPerInch,R as RenderingCancelledException,k as UnexpectedResponseException,D as Util,I as VerbosityLevel,L as XfaLayer,O as build,N as createValidAbsoluteUrl,B as fetchData,H as getDocument,U as getFilenameFromUrl,z as getPdfFilenameFromUrl,j as getXfaPageViewport,V as isDataScheme,G as isPdfFile,W as noContextMenu,$ as normalizeUnicode,q as renderTextLayer,K as setLayerDimensions,X as shadow,Y as updateTextLayer,J as version};