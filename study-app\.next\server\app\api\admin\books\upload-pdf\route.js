const CHUNK_PUBLIC_PATH = "server/app/api/admin/books/upload-pdf/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_11d1d4f6._.js");
runtime.loadChunk("server/chunks/node_modules_pdf2json_dist_pdfparser_7e246206.js");
runtime.loadChunk("server/chunks/node_modules_bcryptjs_umd_index_6ff40003.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__431f243a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/books/upload-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/books/upload-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/books/upload-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
