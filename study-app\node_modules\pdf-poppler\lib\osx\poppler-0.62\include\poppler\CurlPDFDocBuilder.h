//========================================================================
//
// CurlPDFDocBuilder.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright 2010 Hib Eris <<EMAIL>>
// Copyright 2010 Albert Astals Cid <<EMAIL>>
//
//========================================================================

#ifndef CURLPDFDOCBUILDER_H
#define CURLPDFDOCBUILDER_H

#include "PDFDocBuilder.h"

//------------------------------------------------------------------------
// CurlPDFDocBuilder
//
// The CurlPDFDocBuilder implements a PDFDocBuilder for 'http(s)://'.
//------------------------------------------------------------------------

class CurlPDFDocBuilder : public PDFDocBuilder {

public:

  PDFDoc *buildPDFDoc(const GooString &uri, GooString *ownerPassword = NULL,
    GooString *userPassword = NULL, void *guiDataA = NULL) override;
  GBool supports(const GooString &uri) override;

};

#endif /* CURLPDFDOCBUILDER_H */
