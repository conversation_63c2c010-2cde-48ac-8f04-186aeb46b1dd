import db from './database';

// Types
export interface Class {
  id: number;
  name: string;
  description?: string;
  created_at: string;
}

export interface Subject {
  id: number;
  class_id: number;
  name: string;
  description?: string;
  created_at: string;
  class_name?: string;
}

export interface Book {
  id: number;
  title: string;
  class_id: number;
  subject_id: number;
  description?: string;
  cover_image_path?: string;
  total_pages: number;
  status: 'draft' | 'processing' | 'completed';
  created_at: string;
  updated_at: string;
  // Joined fields
  class_name?: string;
  subject_name?: string;
}

export interface BookWithStats extends Book {
  uploaded_pages: number;
  processed_pages: number;
  total_questions: number;
}

export interface Question {
  id: number;
  class_id: number;
  subject_id: number;
  chapter?: string;
  type: 'mcq' | 'true_false' | 'fill_blank' | 'short_answer' | 'long_answer';
  content: string;
  options?: string; // JSON string
  correct_answer?: string;
  marks: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  created_at: string;
  updated_at: string;
  class_name?: string;
  subject_name?: string;
}

export interface Test {
  id: number;
  title: string;
  class_id: number;
  subject_id: number;
  chapters?: string; // JSON string
  time_min: number;
  total_marks: number;
  instructions?: string;
  is_active: boolean;
  created_by: number;
  created_at: string;
  class_name?: string;
  subject_name?: string;
  creator_name?: string;
}

// Classes CRUD operations
export const ClassModel = {
  getAll(): Class[] {
    return db.prepare('SELECT * FROM classes ORDER BY name').all() as Class[];
  },

  getById(id: number): Class | null {
    return db.prepare('SELECT * FROM classes WHERE id = ?').get(id) as Class || null;
  },

  create(name: string, description?: string): Class {
    const result = db.prepare(`
      INSERT INTO classes (name, description)
      VALUES (?, ?)
    `).run(name, description || null);

    return this.getById(result.lastInsertRowid as number)!;
  },

  update(id: number, name: string, description?: string): boolean {
    try {
      db.prepare(`
        UPDATE classes 
        SET name = ?, description = ?
        WHERE id = ?
      `).run(name, description || null, id);
      return true;
    } catch (error) {
      console.error('Update class error:', error);
      return false;
    }
  },

  delete(id: number): boolean {
    try {
      db.prepare('DELETE FROM classes WHERE id = ?').run(id);
      return true;
    } catch (error) {
      console.error('Delete class error:', error);
      return false;
    }
  }
};

// Subjects CRUD operations
export const SubjectModel = {
  getAll(): Subject[] {
    return db.prepare(`
      SELECT s.*, c.name as class_name
      FROM subjects s
      JOIN classes c ON s.class_id = c.id
      ORDER BY c.name, s.name
    `).all() as Subject[];
  },

  getByClassId(classId: number): Subject[] {
    return db.prepare(`
      SELECT s.*, c.name as class_name
      FROM subjects s
      JOIN classes c ON s.class_id = c.id
      WHERE s.class_id = ?
      ORDER BY s.name
    `).all(classId) as Subject[];
  },

  getById(id: number): Subject | null {
    return db.prepare(`
      SELECT s.*, c.name as class_name
      FROM subjects s
      JOIN classes c ON s.class_id = c.id
      WHERE s.id = ?
    `).get(id) as Subject || null;
  },

  create(classId: number, name: string, description?: string): Subject {
    const result = db.prepare(`
      INSERT INTO subjects (class_id, name, description)
      VALUES (?, ?, ?)
    `).run(classId, name, description || null);

    return this.getById(result.lastInsertRowid as number)!;
  },

  update(id: number, name: string, description?: string): boolean {
    try {
      db.prepare(`
        UPDATE subjects 
        SET name = ?, description = ?
        WHERE id = ?
      `).run(name, description || null, id);
      return true;
    } catch (error) {
      console.error('Update subject error:', error);
      return false;
    }
  },

  delete(id: number): boolean {
    try {
      db.prepare('DELETE FROM subjects WHERE id = ?').run(id);
      return true;
    } catch (error) {
      console.error('Delete subject error:', error);
      return false;
    }
  }
};

// Questions CRUD operations
export const QuestionModel = {
  getAll(filters?: { classId?: number; subjectId?: number; type?: string; chapter?: string }): Question[] {
    let query = `
      SELECT q.*, c.name as class_name, s.name as subject_name
      FROM questions q
      JOIN classes c ON q.class_id = c.id
      JOIN subjects s ON q.subject_id = s.id
    `;
    
    const conditions: string[] = [];
    const params: any[] = [];
    
    if (filters?.classId) {
      conditions.push('q.class_id = ?');
      params.push(filters.classId);
    }
    
    if (filters?.subjectId) {
      conditions.push('q.subject_id = ?');
      params.push(filters.subjectId);
    }
    
    if (filters?.type) {
      conditions.push('q.type = ?');
      params.push(filters.type);
    }
    
    if (filters?.chapter) {
      conditions.push('q.chapter = ?');
      params.push(filters.chapter);
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }
    
    query += ' ORDER BY q.created_at DESC';
    
    return db.prepare(query).all(...params) as Question[];
  },

  getById(id: number): Question | null {
    return db.prepare(`
      SELECT q.*, c.name as class_name, s.name as subject_name
      FROM questions q
      JOIN classes c ON q.class_id = c.id
      JOIN subjects s ON q.subject_id = s.id
      WHERE q.id = ?
    `).get(id) as Question || null;
  },

  create(questionData: Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>): Question {
    const result = db.prepare(`
      INSERT INTO questions (
        class_id, subject_id, chapter, type, content, options, 
        correct_answer, marks, difficulty
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      questionData.class_id,
      questionData.subject_id,
      questionData.chapter || null,
      questionData.type,
      questionData.content,
      questionData.options || null,
      questionData.correct_answer || null,
      questionData.marks,
      questionData.difficulty || null
    );

    return this.getById(result.lastInsertRowid as number)!;
  },

  update(id: number, questionData: Partial<Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>>): boolean {
    try {
      const fields = Object.keys(questionData).filter(key => questionData[key as keyof typeof questionData] !== undefined);
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = fields.map(field => questionData[field as keyof typeof questionData]);
      
      db.prepare(`
        UPDATE questions 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(...values, id);
      
      return true;
    } catch (error) {
      console.error('Update question error:', error);
      return false;
    }
  },

  delete(id: number): boolean {
    try {
      db.prepare('DELETE FROM questions WHERE id = ?').run(id);
      return true;
    } catch (error) {
      console.error('Delete question error:', error);
      return false;
    }
  },

  getChapters(classId?: number, subjectId?: number): string[] {
    let query = 'SELECT DISTINCT chapter FROM questions WHERE chapter IS NOT NULL';
    const params: any[] = [];

    if (classId) {
      query += ' AND class_id = ?';
      params.push(classId);
    }

    if (subjectId) {
      query += ' AND subject_id = ?';
      params.push(subjectId);
    }

    query += ' ORDER BY chapter';

    const results = db.prepare(query).all(...params) as { chapter: string }[];
    return results.map(r => r.chapter);
  },

  // Get available chapters from page types
  getChaptersFromPageTypes(classId?: number, subjectId?: number): string[] {
    let query = `
      SELECT DISTINCT page_type
      FROM images
      WHERE page_type LIKE 'chapter-%'
    `;
    const params: any[] = [];

    if (classId) {
      query += ' AND class_id = ?';
      params.push(classId);
    }

    if (subjectId) {
      query += ' AND subject_id = ?';
      params.push(subjectId);
    }

    query += ' ORDER BY page_type';

    const results = db.prepare(query).all(...params) as { page_type: string }[];
    return results.map(r => r.page_type.replace('chapter-', 'Chapter '));
  }
};

// Book Model
export const BookModel = {
  getAll(): BookWithStats[] {
    try {
      // Check if books table exists
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='books'").get();
      if (!tableExists) {
        console.log('Books table does not exist yet, returning empty array');
        return [];
      }

      const books = db.prepare(`
        SELECT
          b.*,
          c.name as class_name,
          s.name as subject_name,
          COUNT(DISTINCT i.id) as uploaded_pages,
          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
          0 as total_questions
        FROM books b
        LEFT JOIN classes c ON b.class_id = c.id
        LEFT JOIN subjects s ON b.subject_id = s.id
        LEFT JOIN images i ON b.id = i.book_id
        LEFT JOIN ocr_text ot ON i.id = ot.image_id
        GROUP BY b.id
        ORDER BY b.updated_at DESC
      `).all() as BookWithStats[];

      return books;
    } catch (error) {
      console.error('Error fetching books:', error);
      return [];
    }
  },

  getById(id: number): BookWithStats | null {
    try {
      // Check if books table exists
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='books'").get();
      if (!tableExists) {
        return null;
      }

      const book = db.prepare(`
        SELECT
          b.*,
          c.name as class_name,
          s.name as subject_name,
          COUNT(DISTINCT i.id) as uploaded_pages,
          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
          0 as total_questions
        FROM books b
        LEFT JOIN classes c ON b.class_id = c.id
        LEFT JOIN subjects s ON b.subject_id = s.id
        LEFT JOIN images i ON b.id = i.book_id
        LEFT JOIN ocr_text ot ON i.id = ot.image_id
        WHERE b.id = ?
        GROUP BY b.id
      `).get(id) as BookWithStats | undefined;

      return book || null;
    } catch (error) {
      console.error('Error fetching book by id:', error);
      return null;
    }
  },

  create(data: {
    title: string;
    class_id: number;
    subject_id: number;
    description?: string;
  }): number {
    const result = db.prepare(`
      INSERT INTO books (title, class_id, subject_id, description)
      VALUES (?, ?, ?, ?)
    `).run(data.title, data.class_id, data.subject_id, data.description || null);

    return result.lastInsertRowid as number;
  },

  update(id: number, data: Partial<Book>): boolean {
    const fields = [];
    const values = [];

    if (data.title !== undefined) {
      fields.push('title = ?');
      values.push(data.title);
    }
    if (data.description !== undefined) {
      fields.push('description = ?');
      values.push(data.description);
    }
    if (data.status !== undefined) {
      fields.push('status = ?');
      values.push(data.status);
    }

    if (fields.length === 0) return false;

    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const result = db.prepare(`
      UPDATE books SET ${fields.join(', ')} WHERE id = ?
    `).run(...values);

    return result.changes > 0;
  },

  delete(id: number): boolean {
    const result = db.prepare('DELETE FROM books WHERE id = ?').run(id);
    return result.changes > 0;
  },

  getImages(bookId: number) {
    return db.prepare(`
      SELECT
        i.*,
        CASE WHEN ot.id IS NOT NULL THEN 1 ELSE 0 END as has_ocr,
        CASE WHEN ot.processed = 1 THEN 1 ELSE 0 END as ocr_processed
      FROM images i
      LEFT JOIN ocr_text ot ON i.id = ot.image_id
      WHERE i.book_id = ?
      ORDER BY i.page_number ASC, i.upload_order ASC
    `).all(bookId);
  }
};
