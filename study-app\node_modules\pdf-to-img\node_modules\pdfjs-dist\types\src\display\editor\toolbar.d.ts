export class EditorToolbar {
    static "__#3@#pointerDown"(e: any): void;
    constructor(editor: any);
    render(): HTMLDivElement;
    hide(): void;
    show(): void;
    addAltTextButton(button: any): void;
    addColorPicker(colorPicker: any): void;
    remove(): void;
    #private;
}
export class HighlightToolbar {
    constructor(uiManager: any);
    show(parent: any, boxes: any, isLTR: any): void;
    hide(): void;
    #private;
}
