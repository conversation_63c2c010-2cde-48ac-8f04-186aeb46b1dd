import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { SubjectModel } from '@/lib/models';

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { name, description } = await request.json();
    const subjectId = parseInt(params.id);

    if (!name) {
      return NextResponse.json({ error: 'Subject name is required' }, { status: 400 });
    }

    const success = SubjectModel.update(subjectId, name, description);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to update subject' }, { status: 500 });
    }

    const updatedSubject = SubjectModel.getById(subjectId);
    return NextResponse.json(updatedSubject);
  } catch (error) {
    console.error('Update subject error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const subjectId = parseInt(params.id);
    const success = SubjectModel.delete(subjectId);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to delete subject' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Delete subject error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
