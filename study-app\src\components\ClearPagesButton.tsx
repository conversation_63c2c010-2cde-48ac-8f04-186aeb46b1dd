'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface ClearPagesButtonProps {
  bookId: number;
}

export default function ClearPagesButton({ bookId }: ClearPagesButtonProps) {
  const router = useRouter();
  const [isClearing, setIsClearing] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const handleClear = async () => {
    if (!showConfirm) {
      setShowConfirm(true);
      return;
    }

    setIsClearing(true);
    try {
      const response = await fetch(`/api/admin/books/${bookId}/clear-pages`, {
        method: 'DELETE',
      });

      if (response.ok) {
        router.refresh();
        setShowConfirm(false);
      } else {
        alert('Failed to clear pages');
      }
    } catch (error) {
      alert('Error clearing pages');
    } finally {
      setIsClearing(false);
    }
  };

  const handleCancel = () => {
    setShowConfirm(false);
  };

  if (showConfirm) {
    return (
      <div className="flex space-x-2">
        <button
          onClick={handleClear}
          disabled={isClearing}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
        >
          {isClearing ? 'Clearing...' : 'Confirm Clear'}
        </button>
        <button
          onClick={handleCancel}
          disabled={isClearing}
          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </button>
      </div>
    );
  }

  return (
    <button
      onClick={handleClear}
      className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md shadow-sm text-red-700 bg-white hover:bg-red-50"
    >
      Clear All Pages
    </button>
  );
}
