import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { ClassModel } from '@/lib/models';

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { name, description } = await request.json();
    const classId = parseInt(params.id);

    if (!name) {
      return NextResponse.json({ error: 'Class name is required' }, { status: 400 });
    }

    const success = ClassModel.update(classId, name, description);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to update class' }, { status: 500 });
    }

    const updatedClass = ClassModel.getById(classId);
    return NextResponse.json(updatedClass);
  } catch (error) {
    console.error('Update class error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const classId = parseInt(params.id);
    const success = ClassModel.delete(classId);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to delete class' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Delete class error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
