{"version": 3, "sources": ["../../src/telemetry/post-telemetry-payload.test.ts"], "sourcesContent": ["import { postNextTelemetryPayload } from './post-telemetry-payload'\n\ndescribe('postNextTelemetryPayload', () => {\n  let originalFetch: typeof fetch\n\n  beforeEach(() => {\n    originalFetch = global.fetch\n  })\n\n  afterEach(() => {\n    global.fetch = originalFetch\n  })\n\n  it('sends telemetry payload successfully', async () => {\n    const mockFetch = jest.fn().mockResolvedValue({\n      ok: true,\n    })\n    global.fetch = mockFetch\n\n    const payload = {\n      meta: { version: '1.0' },\n      context: {\n        anonymousId: 'test-id',\n        projectId: 'test-project',\n        sessionId: 'test-session',\n      },\n      events: [\n        {\n          eventName: 'test-event',\n          fields: { foo: 'bar' },\n        },\n      ],\n    }\n\n    await postNextTelemetryPayload(payload)\n\n    expect(mockFetch).toHaveBeenCalledWith(\n      'https://telemetry.nextjs.org/api/v1/record',\n      {\n        method: 'POST',\n        body: JSON.stringify(payload),\n        headers: { 'content-type': 'application/json' },\n        signal: expect.any(AbortSignal),\n      }\n    )\n  })\n\n  it('retries on failure', async () => {\n    const mockFetch = jest\n      .fn()\n      .mockRejectedValueOnce(new Error('Network error'))\n      .mockResolvedValueOnce({ ok: true })\n    global.fetch = mockFetch\n\n    const payload = {\n      meta: {},\n      context: {\n        anonymousId: 'test-id',\n        projectId: 'test-project',\n        sessionId: 'test-session',\n      },\n      events: [],\n    }\n\n    await postNextTelemetryPayload(payload)\n\n    expect(mockFetch).toHaveBeenCalledTimes(2)\n  })\n\n  it('swallows errors after retries exhausted', async () => {\n    const mockFetch = jest.fn().mockRejectedValue(new Error('Network error'))\n    global.fetch = mockFetch\n\n    const payload = {\n      meta: {},\n      context: {\n        anonymousId: 'test-id',\n        projectId: 'test-project',\n        sessionId: 'test-session',\n      },\n      events: [],\n    }\n\n    // Should not throw\n    await postNextTelemetryPayload(payload)\n\n    expect(mockFetch).toHaveBeenCalledTimes(2) // Initial try + 1 retry\n  })\n})\n"], "names": ["describe", "originalFetch", "beforeEach", "global", "fetch", "after<PERSON>ach", "it", "mockFetch", "jest", "fn", "mockResolvedValue", "ok", "payload", "meta", "version", "context", "anonymousId", "projectId", "sessionId", "events", "eventName", "fields", "foo", "postNextTelemetryPayload", "expect", "toHaveBeenCalledWith", "method", "body", "JSON", "stringify", "headers", "signal", "any", "AbortSignal", "mockRejectedValueOnce", "Error", "mockResolvedValueOnce", "toHaveBeenCalledTimes", "mockRejectedValue"], "mappings": ";;;;sCAAyC;AAEzCA,SAAS,4BAA4B;IACnC,IAAIC;IAEJC,WAAW;QACTD,gBAAgBE,OAAOC,KAAK;IAC9B;IAEAC,UAAU;QACRF,OAAOC,KAAK,GAAGH;IACjB;IAEAK,GAAG,wCAAwC;QACzC,MAAMC,YAAYC,KAAKC,EAAE,GAAGC,iBAAiB,CAAC;YAC5CC,IAAI;QACN;QACAR,OAAOC,KAAK,GAAGG;QAEf,MAAMK,UAAU;YACdC,MAAM;gBAAEC,SAAS;YAAM;YACvBC,SAAS;gBACPC,aAAa;gBACbC,WAAW;gBACXC,WAAW;YACb;YACAC,QAAQ;gBACN;oBACEC,WAAW;oBACXC,QAAQ;wBAAEC,KAAK;oBAAM;gBACvB;aACD;QACH;QAEA,MAAMC,IAAAA,8CAAwB,EAACX;QAE/BY,OAAOjB,WAAWkB,oBAAoB,CACpC,8CACA;YACEC,QAAQ;YACRC,MAAMC,KAAKC,SAAS,CAACjB;YACrBkB,SAAS;gBAAE,gBAAgB;YAAmB;YAC9CC,QAAQP,OAAOQ,GAAG,CAACC;QACrB;IAEJ;IAEA3B,GAAG,sBAAsB;QACvB,MAAMC,YAAYC,KACfC,EAAE,GACFyB,qBAAqB,CAAC,IAAIC,MAAM,kBAChCC,qBAAqB,CAAC;YAAEzB,IAAI;QAAK;QACpCR,OAAOC,KAAK,GAAGG;QAEf,MAAMK,UAAU;YACdC,MAAM,CAAC;YACPE,SAAS;gBACPC,aAAa;gBACbC,WAAW;gBACXC,WAAW;YACb;YACAC,QAAQ,EAAE;QACZ;QAEA,MAAMI,IAAAA,8CAAwB,EAACX;QAE/BY,OAAOjB,WAAW8B,qBAAqB,CAAC;IAC1C;IAEA/B,GAAG,2CAA2C;QAC5C,MAAMC,YAAYC,KAAKC,EAAE,GAAG6B,iBAAiB,CAAC,IAAIH,MAAM;QACxDhC,OAAOC,KAAK,GAAGG;QAEf,MAAMK,UAAU;YACdC,MAAM,CAAC;YACPE,SAAS;gBACPC,aAAa;gBACbC,WAAW;gBACXC,WAAW;YACb;YACAC,QAAQ,EAAE;QACZ;QAEA,mBAAmB;QACnB,MAAMI,IAAAA,8CAAwB,EAACX;QAE/BY,OAAOjB,WAAW8B,qBAAqB,CAAC,GAAG,wBAAwB;;IACrE;AACF"}