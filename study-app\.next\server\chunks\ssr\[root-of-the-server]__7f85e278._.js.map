{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Upload Images', href: '/admin/upload', icon: '📤' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user.name}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAiB,MAAM;YAAiB,MAAM;QAAK;QAC3D;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,KAAK,IAAI;;;;;;;8DAC3D,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/QuestionsManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Class, Subject, Question } from '@/lib/models';\n\ninterface QuestionsManagerProps {\n  classes: Class[];\n  subjects: Subject[];\n  initialQuestions: Question[];\n  initialOCRText?: string;\n  imageId?: number;\n}\n\nexport default function QuestionsManager({ \n  classes, \n  subjects, \n  initialQuestions, \n  initialOCRText = '',\n  imageId \n}: QuestionsManagerProps) {\n  const [questions, setQuestions] = useState(initialQuestions);\n  const [ocrText, setOcrText] = useState(initialOCRText);\n  const [showQuestionForm, setShowQuestionForm] = useState(false);\n  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);\n  const [selectedClassId, setSelectedClassId] = useState<number | null>(null);\n  const [selectedSubjectId, setSelectedSubjectId] = useState<number | null>(null);\n  const [selectedType, setSelectedType] = useState<string>('');\n\n  // Form state\n  const [formData, setFormData] = useState({\n    class_id: 0,\n    subject_id: 0,\n    chapter: '',\n    type: 'mcq' as 'mcq' | 'true_false' | 'fill_blank' | 'short_answer' | 'long_answer',\n    content: '',\n    options: '',\n    correct_answer: '',\n    marks: 1,\n    difficulty: 'medium' as 'easy' | 'medium' | 'hard'\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const filteredSubjects = selectedClassId \n    ? subjects.filter(s => s.class_id === selectedClassId)\n    : subjects;\n\n  const filteredQuestions = questions.filter(q => {\n    if (selectedClassId && q.class_id !== selectedClassId) return false;\n    if (selectedSubjectId && q.subject_id !== selectedSubjectId) return false;\n    if (selectedType && q.type !== selectedType) return false;\n    return true;\n  });\n\n  const resetForm = () => {\n    setFormData({\n      class_id: 0,\n      subject_id: 0,\n      chapter: '',\n      type: 'mcq',\n      content: '',\n      options: '',\n      correct_answer: '',\n      marks: 1,\n      difficulty: 'medium'\n    });\n    setEditingQuestion(null);\n    setShowQuestionForm(false);\n    setError('');\n  };\n\n  const parseOCRQuestions = () => {\n    if (!ocrText.trim()) {\n      setError('No OCR text available to parse');\n      return;\n    }\n\n    try {\n      const response = fetch('/api/admin/questions/parse', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ ocrText, imageId }),\n      });\n\n      // For now, let's implement a simple client-side parser\n      const lines = ocrText.split('\\n').map(line => line.trim()).filter(line => line.length > 0);\n      const parsedQuestions: any[] = [];\n      \n      let currentQuestion = '';\n      let currentOptions: string[] = [];\n      let questionType = 'short_answer';\n      \n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i];\n        \n        // Detect question patterns\n        if (line.match(/^\\d+[\\.\\)]\\s*/) || line.match(/^Q\\d*[\\.\\)]\\s*/i)) {\n          // Save previous question if exists\n          if (currentQuestion) {\n            parsedQuestions.push({\n              type: questionType,\n              content: currentQuestion,\n              options: currentOptions.length > 0 ? JSON.stringify(currentOptions) : '',\n              marks: 1,\n              difficulty: 'medium'\n            });\n          }\n          \n          // Start new question\n          currentQuestion = line.replace(/^\\d+[\\.\\)]\\s*/, '').replace(/^Q\\d*[\\.\\)]\\s*/i, '');\n          currentOptions = [];\n          \n          // Detect question type\n          if (currentQuestion.toLowerCase().includes('true') && currentQuestion.toLowerCase().includes('false')) {\n            questionType = 'true_false';\n          } else if (currentQuestion.includes('_____') || currentQuestion.includes('____')) {\n            questionType = 'fill_blank';\n          } else {\n            questionType = 'short_answer';\n          }\n        }\n        // Detect MCQ options\n        else if (line.match(/^[a-d][\\.\\)]\\s*/i) || line.match(/^\\([a-d]\\)\\s*/i)) {\n          if (currentQuestion) {\n            questionType = 'mcq';\n            const option = line.replace(/^[a-d][\\.\\)]\\s*/i, '').replace(/^\\([a-d]\\)\\s*/i, '');\n            currentOptions.push(option);\n          }\n        }\n        // Continue current question\n        else if (currentQuestion && !line.match(/^[a-d][\\.\\)]\\s*/i)) {\n          currentQuestion += ' ' + line;\n        }\n      }\n      \n      // Save last question\n      if (currentQuestion) {\n        parsedQuestions.push({\n          type: questionType,\n          content: currentQuestion,\n          options: currentOptions.length > 0 ? JSON.stringify(currentOptions) : '',\n          marks: 1,\n          difficulty: 'medium'\n        });\n      }\n\n      // Show parsed questions for review\n      if (parsedQuestions.length > 0) {\n        alert(`Found ${parsedQuestions.length} questions. You can now review and save them individually.`);\n        // You could set these to a state for bulk editing\n      } else {\n        setError('No questions found in the OCR text');\n      }\n    } catch (error) {\n      setError('Failed to parse questions from OCR text');\n    }\n  };\n\n  const handleCreateQuestion = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/admin/questions', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setQuestions([data.question, ...questions]);\n        resetForm();\n      } else {\n        setError(data.error || 'Failed to create question');\n      }\n    } catch (error) {\n      setError('An error occurred while creating the question');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUpdateQuestion = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!editingQuestion) return;\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch(`/api/admin/questions/${editingQuestion.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setQuestions(questions.map(q => q.id === editingQuestion.id ? data.question : q));\n        resetForm();\n      } else {\n        setError(data.error || 'Failed to update question');\n      }\n    } catch (error) {\n      setError('An error occurred while updating the question');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteQuestion = async (questionId: number) => {\n    if (!confirm('Are you sure you want to delete this question?')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/admin/questions/${questionId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setQuestions(questions.filter(q => q.id !== questionId));\n      } else {\n        const data = await response.json();\n        setError(data.error || 'Failed to delete question');\n      }\n    } catch (error) {\n      setError('An error occurred while deleting the question');\n    }\n  };\n\n  const startEditQuestion = (question: Question) => {\n    setEditingQuestion(question);\n    setFormData({\n      class_id: question.class_id,\n      subject_id: question.subject_id,\n      chapter: question.chapter || '',\n      type: question.type,\n      content: question.content,\n      options: question.options || '',\n      correct_answer: question.correct_answer || '',\n      marks: question.marks,\n      difficulty: question.difficulty || 'medium'\n    });\n    setShowQuestionForm(true);\n  };\n\n  const questionTypes = [\n    { value: 'mcq', label: 'Multiple Choice (MCQ)' },\n    { value: 'true_false', label: 'True/False' },\n    { value: 'fill_blank', label: 'Fill in the Blanks' },\n    { value: 'short_answer', label: 'Short Answer' },\n    { value: 'long_answer', label: 'Long Answer' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* OCR Text Section */}\n      {ocrText && (\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                OCR Text {imageId && `(Image ID: ${imageId})`}\n              </h3>\n              <button\n                onClick={parseOCRQuestions}\n                className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Parse Questions\n              </button>\n            </div>\n            \n            <div className=\"bg-gray-50 p-4 rounded-lg max-h-64 overflow-y-auto\">\n              <pre className=\"text-sm text-gray-700 whitespace-pre-wrap\">{ocrText}</pre>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Filters and Add Question */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n              Questions ({filteredQuestions.length})\n            </h3>\n            <button\n              onClick={() => setShowQuestionForm(true)}\n              className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n            >\n              Add Question\n            </button>\n          </div>\n\n          {/* Filters */}\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4 mb-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Class</label>\n              <select\n                value={selectedClassId || ''}\n                onChange={(e) => {\n                  setSelectedClassId(e.target.value ? Number(e.target.value) : null);\n                  setSelectedSubjectId(null);\n                }}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n              >\n                <option value=\"\">All Classes</option>\n                {classes.map((cls) => (\n                  <option key={cls.id} value={cls.id}>{cls.name}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Subject</label>\n              <select\n                value={selectedSubjectId || ''}\n                onChange={(e) => setSelectedSubjectId(e.target.value ? Number(e.target.value) : null)}\n                disabled={!selectedClassId}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100\"\n              >\n                <option value=\"\">All Subjects</option>\n                {filteredSubjects.map((subject) => (\n                  <option key={subject.id} value={subject.id}>{subject.name}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Type</label>\n              <select\n                value={selectedType}\n                onChange={(e) => setSelectedType(e.target.value)}\n                className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n              >\n                <option value=\"\">All Types</option>\n                {questionTypes.map((type) => (\n                  <option key={type.value} value={type.value}>{type.label}</option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"flex items-end\">\n              <button\n                onClick={() => {\n                  setSelectedClassId(null);\n                  setSelectedSubjectId(null);\n                  setSelectedType('');\n                }}\n                className=\"w-full bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Clear Filters\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Form */}\n      {showQuestionForm && (\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n              {editingQuestion ? 'Edit Question' : 'Add New Question'}\n            </h3>\n\n            {error && (\n              <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n                {error}\n              </div>\n            )}\n\n            <form onSubmit={editingQuestion ? handleUpdateQuestion : handleCreateQuestion}>\n              <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Class</label>\n                  <select\n                    value={formData.class_id}\n                    onChange={(e) => {\n                      setFormData({ ...formData, class_id: Number(e.target.value), subject_id: 0 });\n                    }}\n                    required\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  >\n                    <option value={0}>Select a class</option>\n                    {classes.map((cls) => (\n                      <option key={cls.id} value={cls.id}>{cls.name}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Subject</label>\n                  <select\n                    value={formData.subject_id}\n                    onChange={(e) => setFormData({ ...formData, subject_id: Number(e.target.value) })}\n                    required\n                    disabled={!formData.class_id}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100\"\n                  >\n                    <option value={0}>Select a subject</option>\n                    {subjects.filter(s => s.class_id === formData.class_id).map((subject) => (\n                      <option key={subject.id} value={subject.id}>{subject.name}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Chapter/Topic</label>\n                  <input\n                    type=\"text\"\n                    value={formData.chapter}\n                    onChange={(e) => setFormData({ ...formData, chapter: e.target.value })}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"e.g., Chapter 1, Algebra\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Question Type</label>\n                  <select\n                    value={formData.type}\n                    onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  >\n                    {questionTypes.map((type) => (\n                      <option key={type.value} value={type.value}>{type.label}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Marks</label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    value={formData.marks}\n                    onChange={(e) => setFormData({ ...formData, marks: Number(e.target.value) })}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Difficulty</label>\n                  <select\n                    value={formData.difficulty}\n                    onChange={(e) => setFormData({ ...formData, difficulty: e.target.value as any })}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  >\n                    <option value=\"easy\">Easy</option>\n                    <option value=\"medium\">Medium</option>\n                    <option value=\"hard\">Hard</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700\">Question Content</label>\n                <textarea\n                  value={formData.content}\n                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}\n                  required\n                  rows={3}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Enter the question text...\"\n                />\n              </div>\n\n              {(formData.type === 'mcq') && (\n                <div className=\"mt-4\">\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    Options (JSON format: [\"Option A\", \"Option B\", \"Option C\", \"Option D\"])\n                  </label>\n                  <textarea\n                    value={formData.options}\n                    onChange={(e) => setFormData({ ...formData, options: e.target.value })}\n                    rows={2}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder='[\"Option A\", \"Option B\", \"Option C\", \"Option D\"]'\n                  />\n                </div>\n              )}\n\n              {(formData.type === 'mcq' || formData.type === 'true_false' || formData.type === 'fill_blank') && (\n                <div className=\"mt-4\">\n                  <label className=\"block text-sm font-medium text-gray-700\">Correct Answer</label>\n                  <input\n                    type=\"text\"\n                    value={formData.correct_answer}\n                    onChange={(e) => setFormData({ ...formData, correct_answer: e.target.value })}\n                    className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder={formData.type === 'true_false' ? 'true or false' : 'Enter correct answer'}\n                  />\n                </div>\n              )}\n\n              <div className=\"mt-6 flex space-x-3\">\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50\"\n                >\n                  {loading ? 'Saving...' : (editingQuestion ? 'Update Question' : 'Create Question')}\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={resetForm}\n                  className=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Questions List */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"space-y-4\">\n            {filteredQuestions.map((question) => (\n              <div key={question.id} className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {questionTypes.find(t => t.value === question.type)?.label}\n                      </span>\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                        {question.marks} mark{question.marks !== 1 ? 's' : ''}\n                      </span>\n                      {question.difficulty && (\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          question.difficulty === 'easy' ? 'bg-green-100 text-green-800' :\n                          question.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-red-100 text-red-800'\n                        }`}>\n                          {question.difficulty}\n                        </span>\n                      )}\n                    </div>\n\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-2\">\n                      {question.content}\n                    </h4>\n\n                    <div className=\"text-xs text-gray-500 space-y-1\">\n                      <p>{question.class_name} - {question.subject_name}</p>\n                      {question.chapter && <p>Chapter: {question.chapter}</p>}\n                    </div>\n\n                    {/* Show options for MCQ */}\n                    {question.type === 'mcq' && question.options && (\n                      <div className=\"mt-2\">\n                        <p className=\"text-xs font-medium text-gray-700 mb-1\">Options:</p>\n                        <div className=\"text-xs text-gray-600\">\n                          {(() => {\n                            try {\n                              const options = JSON.parse(question.options);\n                              return options.map((option: string, index: number) => (\n                                <div key={index} className=\"ml-2\">\n                                  {String.fromCharCode(65 + index)}. {option}\n                                </div>\n                              ));\n                            } catch {\n                              return <div className=\"ml-2 text-red-500\">Invalid options format</div>;\n                            }\n                          })()}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Show correct answer */}\n                    {question.correct_answer && (\n                      <div className=\"mt-2\">\n                        <p className=\"text-xs font-medium text-gray-700\">\n                          Correct Answer: <span className=\"text-green-600\">{question.correct_answer}</span>\n                        </p>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex space-x-2 ml-4\">\n                    <button\n                      onClick={() => startEditQuestion(question)}\n                      className=\"text-indigo-600 hover:text-indigo-900 text-sm\"\n                    >\n                      Edit\n                    </button>\n                    <button\n                      onClick={() => handleDeleteQuestion(question.id)}\n                      className=\"text-red-600 hover:text-red-900 text-sm\"\n                    >\n                      Delete\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n\n            {filteredQuestions.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 text-6xl mb-4\">❓</div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No questions found</h3>\n                <p className=\"text-gray-500\">\n                  {selectedClassId || selectedSubjectId || selectedType\n                    ? 'No questions match the selected filters.'\n                    : 'Create your first question or upload images with OCR to get started.'\n                  }\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS,iBAAiB,EACvC,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,iBAAiB,EAAE,EACnB,OAAO,EACe;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,YAAY;QACZ,SAAS;QACT,MAAM;QACN,SAAS;QACT,SAAS;QACT,gBAAgB;QAChB,OAAO;QACP,YAAY;IACd;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,mBAAmB,kBACrB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,mBACpC;IAEJ,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,IAAI,mBAAmB,EAAE,QAAQ,KAAK,iBAAiB,OAAO;QAC9D,IAAI,qBAAqB,EAAE,UAAU,KAAK,mBAAmB,OAAO;QACpE,IAAI,gBAAgB,EAAE,IAAI,KAAK,cAAc,OAAO;QACpD,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,UAAU;YACV,YAAY;YACZ,SAAS;YACT,MAAM;YACN,SAAS;YACT,SAAS;YACT,gBAAgB;YAChB,OAAO;YACP,YAAY;QACd;QACA,mBAAmB;QACnB,oBAAoB;QACpB,SAAS;IACX;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,8BAA8B;gBACnD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAS;gBAAQ;YAC1C;YAEA,uDAAuD;YACvD,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YACxF,MAAM,kBAAyB,EAAE;YAEjC,IAAI,kBAAkB;YACtB,IAAI,iBAA2B,EAAE;YACjC,IAAI,eAAe;YAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,2BAA2B;gBAC3B,IAAI,KAAK,KAAK,CAAC,oBAAoB,KAAK,KAAK,CAAC,oBAAoB;oBAChE,mCAAmC;oBACnC,IAAI,iBAAiB;wBACnB,gBAAgB,IAAI,CAAC;4BACnB,MAAM;4BACN,SAAS;4BACT,SAAS,eAAe,MAAM,GAAG,IAAI,KAAK,SAAS,CAAC,kBAAkB;4BACtE,OAAO;4BACP,YAAY;wBACd;oBACF;oBAEA,qBAAqB;oBACrB,kBAAkB,KAAK,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,mBAAmB;oBAC/E,iBAAiB,EAAE;oBAEnB,uBAAuB;oBACvB,IAAI,gBAAgB,WAAW,GAAG,QAAQ,CAAC,WAAW,gBAAgB,WAAW,GAAG,QAAQ,CAAC,UAAU;wBACrG,eAAe;oBACjB,OAAO,IAAI,gBAAgB,QAAQ,CAAC,YAAY,gBAAgB,QAAQ,CAAC,SAAS;wBAChF,eAAe;oBACjB,OAAO;wBACL,eAAe;oBACjB;gBACF,OAEK,IAAI,KAAK,KAAK,CAAC,uBAAuB,KAAK,KAAK,CAAC,mBAAmB;oBACvE,IAAI,iBAAiB;wBACnB,eAAe;wBACf,MAAM,SAAS,KAAK,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,kBAAkB;wBAC9E,eAAe,IAAI,CAAC;oBACtB;gBACF,OAEK,IAAI,mBAAmB,CAAC,KAAK,KAAK,CAAC,qBAAqB;oBAC3D,mBAAmB,MAAM;gBAC3B;YACF;YAEA,qBAAqB;YACrB,IAAI,iBAAiB;gBACnB,gBAAgB,IAAI,CAAC;oBACnB,MAAM;oBACN,SAAS;oBACT,SAAS,eAAe,MAAM,GAAG,IAAI,KAAK,SAAS,CAAC,kBAAkB;oBACtE,OAAO;oBACP,YAAY;gBACd;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,MAAM,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,0DAA0D,CAAC;YACjG,kDAAkD;YACpD,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa;oBAAC,KAAK,QAAQ;uBAAK;iBAAU;gBAC1C;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAEhB,IAAI,CAAC,iBAAiB;QAEtB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,EAAE,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,EAAE,GAAG,KAAK,QAAQ,GAAG;gBAC9E;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,mDAAmD;YAC9D;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,YAAY,EAAE;gBACjE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC9C,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,YAAY;YACV,UAAU,SAAS,QAAQ;YAC3B,YAAY,SAAS,UAAU;YAC/B,SAAS,SAAS,OAAO,IAAI;YAC7B,MAAM,SAAS,IAAI;YACnB,SAAS,SAAS,OAAO;YACzB,SAAS,SAAS,OAAO,IAAI;YAC7B,gBAAgB,SAAS,cAAc,IAAI;YAC3C,OAAO,SAAS,KAAK;YACrB,YAAY,SAAS,UAAU,IAAI;QACrC;QACA,oBAAoB;IACtB;IAEA,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;QAAwB;QAC/C;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAc,OAAO;QAAqB;QACnD;YAAE,OAAO;YAAgB,OAAO;QAAe;QAC/C;YAAE,OAAO;YAAe,OAAO;QAAc;KAC9C;IAED,qBACE,8OAAC;QAAI,WAAU;;YAEZ,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA8C;wCAChD,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;;;;;;;8CAE/C,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA6C;;;;;;;;;;;;;;;;;;;;;;0BAOpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA8C;wCAC9C,kBAAkB,MAAM;wCAAC;;;;;;;8CAEvC,8OAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACC,OAAO,mBAAmB;4CAC1B,UAAU,CAAC;gDACT,mBAAmB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC7D,qBAAqB;4CACvB;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;wDAAoB,OAAO,IAAI,EAAE;kEAAG,IAAI,IAAI;uDAAhC,IAAI,EAAE;;;;;;;;;;;;;;;;;8CAKzB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACC,OAAO,qBAAqB;4CAC5B,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;4CAChF,UAAU,CAAC;4CACX,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;wDAAwB,OAAO,QAAQ,EAAE;kEAAG,QAAQ,IAAI;uDAA5C,QAAQ,EAAE;;;;;;;;;;;;;;;;;8CAK7B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wDAAwB,OAAO,KAAK,KAAK;kEAAG,KAAK,KAAK;uDAA1C,KAAK,KAAK;;;;;;;;;;;;;;;;;8CAK7B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;4CACP,mBAAmB;4CACnB,qBAAqB;4CACrB,gBAAgB;wCAClB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,kBAAkB,kBAAkB;;;;;;wBAGtC,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BAAK,UAAU,kBAAkB,uBAAuB;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC;wDACT,YAAY;4DAAE,GAAG,QAAQ;4DAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAG,YAAY;wDAAE;oDAC7E;oDACA,QAAQ;oDACR,WAAU;;sEAEV,8OAAC;4DAAO,OAAO;sEAAG;;;;;;wDACjB,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;gEAAoB,OAAO,IAAI,EAAE;0EAAG,IAAI,IAAI;+DAAhC,IAAI,EAAE;;;;;;;;;;;;;;;;;sDAKzB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE;oDAC/E,QAAQ;oDACR,UAAU,CAAC,SAAS,QAAQ;oDAC5B,WAAU;;sEAEV,8OAAC;4DAAO,OAAO;sEAAG;;;;;;wDACjB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,QAAQ,EAAE,GAAG,CAAC,CAAC,wBAC3D,8OAAC;gEAAwB,OAAO,QAAQ,EAAE;0EAAG,QAAQ,IAAI;+DAA5C,QAAQ,EAAE;;;;;;;;;;;;;;;;;sDAK7B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAQ;oDACxE,WAAU;8DAET,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;4DAAwB,OAAO,KAAK,KAAK;sEAAG,KAAK,KAAK;2DAA1C,KAAK,KAAK;;;;;;;;;;;;;;;;sDAK7B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,OAAO,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE;oDAC1E,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAC3D,8OAAC;oDACC,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAQ;oDAC9E,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAK3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACC,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpE,QAAQ;4CACR,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;gCAId,SAAS,IAAI,KAAK,uBAClB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAG3D,8OAAC;4CACC,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpE,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;gCAKjB,CAAC,SAAS,IAAI,KAAK,SAAS,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,YAAY,mBAC3F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC3E,WAAU;4CACV,aAAa,SAAS,IAAI,KAAK,eAAe,kBAAkB;;;;;;;;;;;;8CAKtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,UAAU,cAAe,kBAAkB,oBAAoB;;;;;;sDAElE,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oCAAsB,WAAU;8CAC/B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,IAAI,GAAG;;;;;;0EAEvD,8OAAC;gEAAK,WAAU;;oEACb,SAAS,KAAK;oEAAC;oEAAM,SAAS,KAAK,KAAK,IAAI,MAAM;;;;;;;4DAEpD,SAAS,UAAU,kBAClB,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,SAAS,UAAU,KAAK,SAAS,gCACjC,SAAS,UAAU,KAAK,WAAW,kCACnC,2BACA;0EACC,SAAS,UAAU;;;;;;;;;;;;kEAK1B,8OAAC;wDAAG,WAAU;kEACX,SAAS,OAAO;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAG,SAAS,UAAU;oEAAC;oEAAI,SAAS,YAAY;;;;;;;4DAChD,SAAS,OAAO,kBAAI,8OAAC;;oEAAE;oEAAU,SAAS,OAAO;;;;;;;;;;;;;oDAInD,SAAS,IAAI,KAAK,SAAS,SAAS,OAAO,kBAC1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAyC;;;;;;0EACtD,8OAAC;gEAAI,WAAU;0EACZ,CAAC;oEACA,IAAI;wEACF,MAAM,UAAU,KAAK,KAAK,CAAC,SAAS,OAAO;wEAC3C,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAgB,sBAClC,8OAAC;gFAAgB,WAAU;;oFACxB,OAAO,YAAY,CAAC,KAAK;oFAAO;oFAAG;;+EAD5B;;;;;oEAId,EAAE,OAAM;wEACN,qBAAO,8OAAC;4EAAI,WAAU;sFAAoB;;;;;;oEAC5C;gEACF,CAAC;;;;;;;;;;;;oDAMN,SAAS,cAAc,kBACtB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;gEAAoC;8EAC/B,8OAAC;oEAAK,WAAU;8EAAkB,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;0DAMjF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,kBAAkB;wDACjC,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,qBAAqB,SAAS,EAAE;wDAC/C,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCAvEG,SAAS,EAAE;;;;;4BA+EtB,kBAAkB,MAAM,KAAK,mBAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDACV,mBAAmB,qBAAqB,eACrC,6CACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB", "debugId": null}}]}