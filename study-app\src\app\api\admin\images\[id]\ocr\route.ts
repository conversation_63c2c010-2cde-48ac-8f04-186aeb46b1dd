import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { processOCR } from '@/lib/upload';
import db from '@/lib/database';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const imageId = parseInt(id);

    if (isNaN(imageId)) {
      return NextResponse.json({ error: 'Invalid image ID' }, { status: 400 });
    }

    console.log(`Starting OCR processing for image ID: ${imageId}`);
    
    // Process OCR
    const ocrText = await processOCR(imageId);
    
    return NextResponse.json({ 
      success: true, 
      text: ocrText,
      message: 'OCR processing completed successfully'
    });
  } catch (error) {
    console.error('OCR API error:', error);
    return NextResponse.json({ 
      error: 'Failed to process OCR',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const imageId = parseInt(id);

    // Get image info and OCR text
    const imageInfo = db.prepare(`
      SELECT
        i.id,
        i.original_name,
        i.page_type,
        i.upload_order,
        i.book_id,
        b.title as book_title,
        ocr.content,
        ocr.processed,
        ocr.created_at
      FROM images i
      LEFT JOIN books b ON i.book_id = b.id
      LEFT JOIN ocr_text ocr ON i.id = ocr.image_id
      WHERE i.id = ?
    `).get(imageId) as any;

    if (!imageInfo) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      image: {
        id: imageInfo.id,
        name: imageInfo.original_name,
        pageType: imageInfo.page_type,
        pageNumber: imageInfo.upload_order,
        bookId: imageInfo.book_id,
        bookTitle: imageInfo.book_title
      },
      ocr: imageInfo.content ? {
        text: imageInfo.content,
        processed: imageInfo.processed,
        processedAt: imageInfo.created_at,
        wordCount: imageInfo.content.split(/\s+/).length,
        characterCount: imageInfo.content.length
      } : null
    });

  } catch (error) {
    console.error('OCR retrieval error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
