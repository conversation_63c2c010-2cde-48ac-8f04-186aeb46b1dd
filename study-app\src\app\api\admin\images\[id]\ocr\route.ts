import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { processOCR } from '@/lib/upload';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const imageId = parseInt(id);

    if (isNaN(imageId)) {
      return NextResponse.json({ error: 'Invalid image ID' }, { status: 400 });
    }

    console.log(`Starting OCR processing for image ID: ${imageId}`);
    
    // Process OCR
    const ocrText = await processOCR(imageId);
    
    return NextResponse.json({ 
      success: true, 
      text: ocrText,
      message: 'OCR processing completed successfully'
    });
  } catch (error) {
    console.error('OCR API error:', error);
    return NextResponse.json({ 
      error: 'Failed to process OCR',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
