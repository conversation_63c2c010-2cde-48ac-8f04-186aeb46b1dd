{
	"compileOnSave": false,
	"compilerOptions": {
		"forceConsistentCasingInFileNames": true,
		"rootDir": ".",
		"outDir": "./bin",
		"baseUrl": ".",
		"sourceMap": true,
		"declaration": false,
		"declarationMap": false,
		"moduleResolution": "node",
		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,
		"allowSyntheticDefaultImports": true,
		"importHelpers": true,
		"strict": true,
		"target": "es2022",
		"module": "esnext",
		"lib": ["es2022"],
		"skipLibCheck": true,
		"skipDefaultLibCheck": true,
		"esModuleInterop": true,
		"resolveJsonModule": true,
		"allowJs": true,
		"checkJs": true,
	},
	"include": [
		"./src/**/*.ts",
		"./*.d.ts",
		"./lib/**/*.js",
		"./pdfparser.js"
	],
	"exclude": [
		"./lib/pdfjs-code.js"
	]
}
