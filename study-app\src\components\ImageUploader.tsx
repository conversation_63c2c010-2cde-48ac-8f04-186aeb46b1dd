'use client';

import { useState, useRef } from 'react';
import { Class, Subject } from '@/lib/models';

interface ImageUploaderProps {
  classes: Class[];
  subjects: Subject[];
  initialImages: any[];
}

export default function ImageUploader({ classes, subjects, initialImages }: ImageUploaderProps) {
  const [images, setImages] = useState(initialImages);
  const [selectedClassId, setSelectedClassId] = useState<number | null>(null);
  const [selectedSubjectId, setSelectedSubjectId] = useState<number | null>(null);
  const [uploading, setUploading] = useState(false);
  const [processing, setProcessing] = useState<number[]>([]);
  const [error, setError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const filteredSubjects = selectedClassId 
    ? subjects.filter(s => s.class_id === selectedClassId)
    : [];

  const filteredImages = images.filter(img => {
    if (selectedClassId && img.class_id !== selectedClassId) return false;
    if (selectedSubjectId && img.subject_id !== selectedSubjectId) return false;
    return true;
  });

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    if (!selectedClassId || !selectedSubjectId) {
      setError('Please select both class and subject before uploading');
      return;
    }

    setUploading(true);
    setError('');

    try {
      for (const file of Array.from(files)) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          setError(`${file.name} is not an image file`);
          continue;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setError(`${file.name} is too large (max 10MB)`);
          continue;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('classId', selectedClassId.toString());
        formData.append('subjectId', selectedSubjectId.toString());

        const response = await fetch('/api/admin/upload', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          setImages(prev => [result.image, ...prev]);
          
          // Start OCR processing
          processOCR(result.image.id);
        } else {
          const errorData = await response.json();
          setError(errorData.error || `Failed to upload ${file.name}`);
        }
      }
    } catch (error) {
      setError('Upload failed. Please try again.');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const processOCR = async (imageId: number) => {
    setProcessing(prev => [...prev, imageId]);

    try {
      const response = await fetch('/api/admin/ocr', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageId }),
      });

      if (response.ok) {
        const result = await response.json();
        setImages(prev => prev.map(img => 
          img.id === imageId 
            ? { ...img, ocr_content: result.text, processed: true }
            : img
        ));
      } else {
        console.error('OCR processing failed for image', imageId);
      }
    } catch (error) {
      console.error('OCR processing error:', error);
    } finally {
      setProcessing(prev => prev.filter(id => id !== imageId));
    }
  };

  const handleDeleteImage = async (imageId: number) => {
    if (!confirm('Are you sure you want to delete this image? This will also delete any extracted questions.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/images/${imageId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setImages(prev => prev.filter(img => img.id !== imageId));
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete image');
      }
    } catch (error) {
      setError('Delete failed. Please try again.');
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-6">
      {/* Upload Form */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Upload New Images
          </h3>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Class
              </label>
              <select
                value={selectedClassId || ''}
                onChange={(e) => {
                  setSelectedClassId(e.target.value ? Number(e.target.value) : null);
                  setSelectedSubjectId(null);
                }}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">Select a class</option>
                {classes.map((cls) => (
                  <option key={cls.id} value={cls.id}>{cls.name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Subject
              </label>
              <select
                value={selectedSubjectId || ''}
                onChange={(e) => setSelectedSubjectId(e.target.value ? Number(e.target.value) : null)}
                disabled={!selectedClassId}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
              >
                <option value="">Select a subject</option>
                {filteredSubjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>{subject.name}</option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={triggerFileInput}
                disabled={!selectedClassId || !selectedSubjectId || uploading}
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploading ? 'Uploading...' : 'Select Images'}
              </button>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          <div className="mt-4 text-sm text-gray-600">
            <p>• Supported formats: JPG, PNG, GIF, WebP</p>
            <p>• Maximum file size: 10MB per image</p>
            <p>• Multiple images can be selected at once</p>
          </div>
        </div>
      </div>

      {/* Images List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Uploaded Images ({filteredImages.length})
            </h3>
            
            <div className="flex space-x-3">
              <select
                value={selectedClassId || ''}
                onChange={(e) => {
                  setSelectedClassId(e.target.value ? Number(e.target.value) : null);
                  setSelectedSubjectId(null);
                }}
                className="border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">All Classes</option>
                {classes.map((cls) => (
                  <option key={cls.id} value={cls.id}>{cls.name}</option>
                ))}
              </select>
              
              {selectedClassId && (
                <select
                  value={selectedSubjectId || ''}
                  onChange={(e) => setSelectedSubjectId(e.target.value ? Number(e.target.value) : null)}
                  className="border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">All Subjects</option>
                  {filteredSubjects.map((subject) => (
                    <option key={subject.id} value={subject.id}>{subject.name}</option>
                  ))}
                </select>
              )}
            </div>
          </div>

          {/* Images Grid */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {filteredImages.map((image) => (
              <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="aspect-w-16 aspect-h-9 bg-gray-100">
                  <img
                    src={`/api/admin/images/${image.id}/preview`}
                    alt={image.original_name}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                    }}
                  />
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {image.original_name}
                    </h4>
                    <button
                      onClick={() => handleDeleteImage(image.id)}
                      className="text-red-600 hover:text-red-900 text-sm"
                    >
                      Delete
                    </button>
                  </div>

                  <div className="text-xs text-gray-500 space-y-1">
                    <p>{image.class_name} - {image.subject_name}</p>
                    <p>Uploaded: {new Date(image.uploaded_at).toLocaleDateString()}</p>
                  </div>

                  {/* OCR Status */}
                  <div className="mt-3">
                    {processing.includes(image.id) ? (
                      <div className="flex items-center text-yellow-600">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
                        <span className="text-xs">Processing OCR...</span>
                      </div>
                    ) : image.ocr_content ? (
                      <div className="text-green-600 text-xs flex items-center">
                        <span className="mr-1">✓</span>
                        OCR Complete
                      </div>
                    ) : (
                      <div className="text-gray-500 text-xs">
                        OCR Pending
                      </div>
                    )}
                  </div>

                  {/* OCR Text Preview */}
                  {image.ocr_content && (
                    <div className="mt-3">
                      <details className="text-xs">
                        <summary className="cursor-pointer text-indigo-600 hover:text-indigo-800">
                          View OCR Text
                        </summary>
                        <div className="mt-2 p-2 bg-gray-50 rounded text-gray-700 max-h-32 overflow-y-auto">
                          {image.ocr_content.substring(0, 200)}
                          {image.ocr_content.length > 200 && '...'}
                        </div>
                      </details>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="mt-3 flex space-x-2">
                    {image.ocr_content && (
                      <button
                        onClick={() => window.open(`/admin/questions?imageId=${image.id}`, '_blank')}
                        className="text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded hover:bg-indigo-200"
                      >
                        Extract Questions
                      </button>
                    )}

                    {!image.ocr_content && !processing.includes(image.id) && (
                      <button
                        onClick={() => processOCR(image.id)}
                        className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded hover:bg-yellow-200"
                      >
                        Retry OCR
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredImages.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📷</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No images uploaded</h3>
              <p className="text-gray-500">
                {selectedClassId || selectedSubjectId
                  ? 'No images found for the selected filters.'
                  : 'Upload your first textbook image to get started.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
