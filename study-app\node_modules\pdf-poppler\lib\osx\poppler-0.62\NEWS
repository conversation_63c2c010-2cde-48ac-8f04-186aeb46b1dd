Release 0.62.0
        core:
         * Stop supporting lcms1, you really want to use lcms2 :)
         * Stop supporting openjpeg1, you really want to use openjpeg2 :)
         * Open files that state 8 bits as third field of W. Bug #103469
         * GfxLabColorSpace::parse: Fix crash in broken documents. Bug #103582
         * Fix leak if parseDA fails
         * Include glibc.h where needed
         * Document the meaning of the 'type' integer of a shading
         * Fix UTF test fail
         * INSTALL: add debug options

        qt5:
         * Add API to let the rendering process callback to get a partial rendering. Bug #103372

        qt4:
         * Remove the Qt4 frontend

        utils:
         * Support unicode on windows console
         * pdfsig: install man page
         * sort encoding list

        glib:
         * demo: fix warning

Release 0.61.1
        core:
         * CairoOutputDev: don't overflow y * stride when accessing image data

        cpp:
         * Fix for corrupted image files on Windows. Bug #102494

        build system:
         * Fix incorrect paths in .pc files. Bug #103578
         * add the custom buildtests target only once. Bug #103003

Release 0.61.0
        core:
         * Fix crashes in broken files
         * Cleanup unused functions from GlobalParams
         * Tweak LZWStream::processNextCode error handling. Bug #103174
         * Warning fixes
         * Remove t1lib code

        qt5:
         * Clean up the remaining Splash code in Arthur backend. Bug #103117
         * ArthurOutputDev: Properly implement saveState/restoreState. Bug #103118
         * Fix leak in ArthurOutputDev::updateFont. Bug #103508

        build system:
         * Use GNUInstallDirs. Bug #103211
         * mingw: Install pkg-config files
         * mingw: change library names to include the soversion. Bug #103157
         * Fix installing a .cc file as header
         * Use -pthread flag instead of -lpthread

Release 0.60.1
        qt5:
         * ArthurOutputDev: Add missing 'return' in error paths

        build system:
         * FindLIBOPENJPEG.cmake: Add CheckCXXSourceCompiles

Release 0.60.0
        core:
         * Enable libcurl support by default
         * PSOutputDev: Fix wrong text generation. Bug #102760
         * Added methods to get and set the font size of text fields. Bug #101692
         * CairoOutputDev: Do not extend the pattern in drawImageMaskRegular
         * CairoOutputDev: do not use the custom downscaling for rendering images when using cairo >= 1.14
         * Fix build with old clang
         * Fix various crashes in broken files
         * Fix some warnings
         * Add some constness to the basic classes
         * Remove unused functions from GlobalParams

        qt5:
         * Added methods to get and set the font size of text fields. Bug #101692
         * Add whether renderToImage shows annotations
         * ArthurOutputDev: Replace Splash font rendering by Qt font rendering
         * ArthurOutputDev: Implement the drawSoftMaskedImage method
         * ArthurOutputDev: Fix several small bugs related to dash pattern handling
         * Fix two minor typos

        build system:
         * cmake is now the default build system
         * autotools based build system has been removed

         utils:
         * pdfinfo: don't truncate dest name

Release 0.59.0
        core:
         * Fix infinite recursion in NameTree parsing in broken files

        utils:
         * pdfunite: Fix API porting error that caused abort in some cases
         * pdfinfo: Fix crashes and memory leaks when using -dests
         * pdfinfo: use GooString.append instead of sprintf/strcat
         * pdfimages: Fix warning when compiling with cygwin

        build system:
         * Fix cygwin 32-bit compile
         * cmake tweaks


Release 0.58.0
        core:
         * CairoOutputDev: cairo 1.14 now has high quality downscaling
         * Signature related improvements. Bug #99271
         * Tweak which cmap we use. Bug #101855
         * Memory leak fixes
         * Substantial rework of the internals
         * win32: call ANSI functions directly. Bug #100312
         * Add some documentation

        qt5:
         * Expose signature information.
         * ArthurOutputDev: initialize the image with the paper color. Bug #102129
         * Fix copy'n'paste bugs: Qt4 -> Qt5
         * ArthurOutputDev: Properly set the QPainter transformation
         * ArthurOutputDev: Use Qt::SvgMiterJoin instead of Qt::MiterJoin. Bug #102356

        utils:
         * pdfinfo: add -dests option to print named destinations. Bug #97262
         * pdftocairo: add -jpegopt for setting jpeg compression parameters. Bug #45727
         * pdftoppm: add -jpegopt for setting jpeg compression parameters. Bug #45727
         * pdfimages: support listing/extracting inline images. Bug #25625

        build system:
         * cmake: Various Windows fixes
         * cmake: Use -std=c++11 instead of -std=gnu++11

        cpp:
         * Fix page.text() not taking page orientation into account. Bug #94517

Release 0.57.0
        core:
         * Fix parsing of Type 1 fonts with newlines in encoding sequences. Bug #101728
         * Fix crash in broken documents

        utils:
         * pdfunite: Fix crash with broken documents. Bug #101208
         * pdftohtml: skip control characters Bug #101770
         * pdfseparate: minor improvement to the documentation. Bug #101800

        build system:
         * cmake: Set RUNPATH for poppler shared libs. Bug #101945
         * configure: fix --disable-FEATURE actually enabling the feature

Release 0.56.0
        core:
         * FormFieldButton::setState() shouldn't check the field is readOnly
         * Fix crashes on multiple broken files

        utils:
         * pdfunite: Fix crash with broken documents. Bugs #101153 #101149

Release 0.55.0
        core:
         * Fix abort in files with broken Decode arrays. KDE bug #379835
         * Fix memory leak (and probably logic bug) parsing broken XRef entries. Bug #100775
         * Fix memory leak when reconstructing broken files. Bug #100776
         * Minor optimization
         * Fix regression in GfxIndexedColorSpace::mapColorToBase. Bug #100931
         * Fix memory leak in error condition

        cpp:
         * Return nullptr if the page at index can't be fetched. Bug #100981

        build system:
         * Fail by default if libjpeg is not available
         * Fail by default if libopenjpeg2/1 is not available

Release 0.54.0
        core:
         * Make XRef reconstruction a bit better. Bug #100509

        glib:
         * Expose movie play mode. Bug #99625
         * demo: Show play mode in movie properties view

        qt5:
         * Compile with -DQT_NO_CAST_FROM_BYTEARRAY. Bug #100311

        utils:
         * pdfimages: don't fail listing if inline image data contains 'EI'. Bug #100737

Release 0.53.0
        core:
         * Form support improvements
         * SplashOutputDev: Fix memory leak when rendering images with colormap and matte color
         * Minor fix in GlobalParams documentation

        qt5:
         * Expose form calculate order
         * Expose Form additional actions

        utils:
         * pdfimages: support 16bpc png and tiff images. Bug #99988
         * pdftohtml: fix small memory leak when constructing some filenames
         * pdfinfo: fix leak when printing JS

        build sytem:
         * Compile in C++11 mode

Release 0.52.0
        core:
         * Fix assert on reading some OCGs. Bug #99768
         * Properly initialize some RichMedia variables in corner cases. Bug #99767

        qt4:
         * optcontent structure was leaking the headers items. Bug #99449
         * Cleanup objects in tests to fix memory leaks. Bug #99449

        qt5:
         * optcontent structure was leaking the headers items. Bug #99449
         * Cleanup objects in tests to fix memory leaks. Bug #99449

        utils:
         * pdftocairo.1: Fix typo

Release 0.51.0
        core:
         * Check for error from NSS in SignatureHandler construct. Bug #99363
         * Add Form[Field|Widget]::setPartialName
         * Fix memory leak in PDFDoc::markAnnotations

        qt5:
         * Implement digital signature support. Bug #94378
         * Add Poppler::FormField::setName
         * Fix segfault/assert if LinkDestination is constructed with invalid input string. Bug #99357

        utils:
         * pdfunite: add fields to AcroForm dict. Bug #99141

Release 0.50.0
        core:
         * PSOutputDev: Fix PS conversion for some files. Bug #63963
         * Fix Outline parsing on broken documents. Bug #98732
         * Fix PDFDoc::saveIncrementalUpdate()'s detection of document being modified. Bug #96561
         * SplashOutputDev: Read softmask into memstrean in case of matte. Bug #97803
         * Bail out if Hints nBitsNumObjects or nBitsDiffGroupLength are greater than 32. Bug #94941
         * CairoOutputDev: initialize CairoOutputDev::antialias. Bug #98983
         * Fix crash when loading some thumbnails. Bug #97870

        utils:
         * pdftoppm: Fix -tiff -gray/-mono incorrect output.
         * pdftops: add -passlevel1customcolor. Bug #97193


        build system:
         * Default to libopenjpeg2 instead of libopenjpeg1

        qt:
         * Support OCG state change links

        glib:
         * Use g_slice_new0 for PopplerActionLayer. Bug #98786

Release 0.49.0
        core:
         * Merge type3 glyph handling from xpdf 3.04. Bug #96667
         * Continue rendering in case of 'Singular matrix in shading pattern fill. Bug #98623
         * Fix memory leak in parametrized gouraudTriangleShadedFill
         * Fix crash on broken files
         * PDFDoc::setDocInfoStringEntry(): treat value consisting of just the unicode marker as an empty string
         * Fix UBSAN warning
         * Misc compile fixes

        utils:
         * pdfseparate: remove extra '%' in error message
        
        build system:
         * configure: Fix typo in disable nss help string

Release 0.48.0
        core:
         * Fix crashes and memory leaks in invalid files.
         * Small memory usage improvements.
         * TextOutputDev: Remove null characters from PDF text. Bug #97144
         * TextOutputDev: Break words on all whitespace characters. Bug #97399
         * Fix UTF16 decoding of document outline title. Bug #97156
         * Add functions for named destination name in name-tree/dict

        glib:
         * Increase glib requirement to 2.41

Release 0.47.0
        core:
         * Fix abort on documents where the docinfo obj is not a dict. Bug #97134
         * Check for XRefEntry existing before using it. Bug #97005
         * Fix memory leak on PDFDoc::setDocInfoStringEntry() with empty string
         * Don't presume that DocInfo is a dictionary in XRef::createDocInfoIfNoneExists()

        build system:
         * configure: Work with non gnu greps

Release 0.46.0
        core:
         * cairo: fix bug in setAntialias()
         * cairo: Fix tiling patterns with BBox with non-zero x,y
         * cairo: try finding glyphs in substitute fonts by unicode value. Bug #96994
         * Added XRef modification flag
         * Added DocInfo setters & getters
         * Be less strict when parsing FitH Link destinations. Bug #96661

        utils:
         * pdftocairo: revert the use of groups for blending into white page
         * pdftocairo: Use fprintf for printing errors
         * pdfinfo: Don't print pdf info when printing metadata, javascript, or structure. Bug #96801

        glib:
         * Added document property setters & simplified getters
         * make document metatag gobject properties writeable

        cpp:
         * pass len to GooString constructor in detail::ustring_to_unicode_GooString(). Bug #96426
         * Added functions to save a document
         * Added document property setters & getters

        qt4:
         * Added document property setters & simplified getters

        qt5:
         * Added document property setters & simplified getters

        build system:
         * configure: Don't use -fPIC on cygwin
         * configure: Work with non gnu greps

Release 0.45.0
        core:
         * SplashOutputDev: Fix iccTransform + splashModeXBGR8
         * Fix memory leaks
         * Fix crash in broken files. Bug #95567. Bug #96027
         * Emulate some non portable glibc functions when not available

        utils:
         * pdftohtml: Fix crash in broken files. Bug #95563
         * pdfinfo: convert dates to local time zone
         * pdfinfo: add -isodates for printing dates in ISO-8601 format
         * pdfinfo: Fix memory leaks

        glib:
         * return date in UTC instead of local time. Bug #94173

        cpp:
         * switched from detail::convert_date() to core's dateStringToTime()

Release 0.44.0
        core:
         * Fix Compile in 32bit linux. Bug #95492
         * Splash: type 3 chars. restore the current position also in output device. Bug #95344
         * Splash: Improve rendering of some dotted lines. Bug #84693
         * Refactor GooString::Set(). Bug #94201
         * Fix typo in GfxPatchMeshShading::parse
         * Fix memory leak in PSOutputDev::filterPSLabel
         * Fix memory leak in SignatureHandler::getDefaultFirefoxCertDB_Linux
         * Fix potential crash in SplashOutputDev::doUpdateFont
         * Fix potential crash in TextPage::coalesce
         * Remove call that does nothing

        utils:
         * pdftocairo: add -antialias option. Bug #94977

Release 0.43.0
        core:
         * Implement sanity check for linearization usage. Bug #92482
         * Add SymbolMT as an alias for the Symbol font. Bug #93168
         * Fix some blank files. Bug #94756
         * cairo: fix fillToStrokePathClip crash and rendering. Bug #62905
         * cairo: Check if PDF knows the width of 'm' in case of substituted font. Bug #94054
         * cairo: save mask state and don't extend image mask. bug #94234
         * SplashOuputDev: Compile with C++11 compilers that don't define isinfinite. Bug #94761
         * typo fixes

        utils:
         * pdftocairo: Calculate rotation before scaling. Bug #94655

        qt4:
         * Fix crash on certain PDF form item activation actions. Bug #94873

        qt5:
         * Fix crash on certain PDF form item activation actions. Bug #94873

Release 0.42.0
        core:
         * Add the support for version 5 + revision 6 documents. Bug #85368
         * Add initial support for Signature handling
         * Initialize gamut mapping multipliers in ::copy() functions. Bug #90697
         * Implement jpx streams support with depth < 8
         * Handle SMaskInData = 0 for JPX encoded images. Bug #93468
         * Fix rendering of some broken PDF files. Bug #92508
         * PSOutputDev: Support for LZW encoding
         * PSOutputDev: Add support for Flate compression in Level 3 output.
         * SplashOuputDev: Implement function shading. Bug #94441
         * SplashOuputDev: Improve rendering of some non embedded fonts. Bug #94054
         * SplashOuputDev: Fall back to Gfx implementation of tiling pattern if repetition rate is small. Bug #90596
         * SplashOuputDev: Implementation of Matte entries in softmasks of softmasked images. Bug #22473
         * SplashOuputDev: assure line width > 0 in case of text stroke. Bug #94038
         * TextOuputDev: Cache result of inner loop in visitDepthFirst. Bug #77087
         * Avoid attempting a tiling pattern fill with a singular transform matrix

        utils:
         * pdfinfo: Add option to show document structure
         * pdfsig: New command that gives information about signature

        qt4:
         * Fix bug in links to remote documents getting the page number wrong sometimes

        qt5:
         * Fix bug in links to remote documents getting the page number wrong sometimes

Release 0.41.0
        core:
         * CairoOutputDev: add missing font types (fontCIDType0COT and fontTrueTypeOT). Bug #93559
         * SplashOutputDev: Adjust limit check and check in addition bitmap pointer. Bug #94053

        utils:
         * pdfseparate: Refine resource detection
         * pdfinfo: fix man page

Release 0.40.0
        core:
         * CairoOutputDev: Use shape mask with soft mask. Bug #91931
         * TextOutputDev: Handle right-to-left text in search
         * TextOutputDev: Fix finding Arabic Presentation Forms ligatures
         * Fix crash in invalid file. Bug #93476
         * Regression test improvements

        utils:
         * pdftocairo: fix writing to stdout out with image output
         * pdftocairo: document that -singlefile appends file type. Bug #86254
         * pdftocairo: ensure surface flushed before accessing image data
         * pdftocairo: check for invalid use of options. Bug #92195
         * pdfunite: Fix typo in manual

        build system:
         * Improve cmake build system

Release 0.39.0
        core:
         * Ignore the alternateSpace and tintTransform. Bug #92381
         * CairoOutputDev: Scale radial pattern. Bug #22098
         * CairoOutputDev: Implement function shading using mesh gradients. Bug #88394
         * Regression test improvements
         * Fix typos in error messages

        build system:
         * Visual Studio 2015 now supports snprintf. Bug #93116

        utils:
         * pdftops: fix %%PageBoundingBox. Bug #87161
         * pdftocairo: Fix double free when both user and owner passwords are given

        glib:
         * Add duration_real to PopplerPageTransition. Bug #92040
         * Remove enum PopplerOrientation from API. Bug #93229
         * documentation improvements
         * glib-demo improvements

Release 0.38.0
        core:
         * Splash: Multiply opacity in case of pattern colorspace. Bug #92592
         * Small form improvements on non ascii character rendering
         * Clarify README

        build system:
         * Clarify internal DCT and JPX are only provided as deprecated fallbacks

        utils:
         * pdftocairo: fix fit to page transformation

Release 0.37.0
        core:
         * CairoOutputDev: Use mask for even-odd fill. Bug #84527
         * SplashOuputDev: Protect calls to set/getAA with the proper #if guards. Bug #92006
         * SplashOuputDev: Try to use an external font if the internal one is invalid
         * PageTransition D is a number not an int. Bug #92040
         * Catalog::getNumPages(): validate page count
         * Catalog::cachePageTree(): recover from out of memory condition
         * Fix crashes in malformed documents

        build system:
         * configure: fix openjpeg detection

Release 0.36.0
        core:
         * Patch to support RichMedia annotations
         * Splash: Fix wrong memory access. Bug #91686
         * Cairo: fix size of transparency group surface. Bug #66229
         * Fix bounds check in Linearization::getPageFirst. Bug #91200
         * File Saving improvements
         * Add premultiplied alpha channel to SplashBitmap
         * Fix for xref table creation. Bug #90790
         * Fix JBIG2Decode infinite loop and stack overflow. Bug #91186
         * Minor optimization in text extraction

        qt4:
         * Basic support for RichMedia annotations
         * Change default image format
         * Minor optimizations

        qt5:
         * Basic support for RichMedia annotations
         * Change default image format
         * Minor optimizations

        cpp:
         * Fix utf8/utf16 conversion. Bug #91644

        build system:
         * Do not hardcode -fPIC in Makefile.am
         * cmake: Allow configuring SHARE_INSTALL_DIR. Bug #90293

        utils:
         * pdfunite: Insert embedded files in result pdf. Bug #90066
         * pdftotext: Add -bbox-layout option. Bug #89941

Release 0.35.0
        core:
         * Fix assert in broken file. Bug #91344
         * Adjust memory layout computation of GooString
         * Make SplashBitmap XBGR transfer alpha channel
         * Splash: Fix wrong writes on non rgb outputs. Bug #90570
         * Splash: remove ifndef in Windows code
         * GlobalParamsWin bugfixes. Bug #91053

        qt4:
         * Switch default image format
         * Add IgnorePaperColor render flag

        qt5:
         * Improve efficiency of Poppler::Page::renderToImage
         * Switch default image format
         * Add IgnorePaperColor render flag

        build system:
         * Allow configuring SPLASH_CMYK support
         * Add configure --enable-build-type. Bug #90796

        glib:
         * Explicitly link against pthread
         * Deprecation fixes

        utils:
         * pdftocairo: Fix cast to pointer from integer of different size on win64

Release 0.34.0
        core:
         * Splash: Fix crash in PDF with nested softmasks. Bug #91240
         * Splash: Speed up of rendering icc based images. Bug #90171
         * PSOutputDev: Embed Type1 fonts to PostScript files correctly. Bug #19747
         * Fix pedantic memory leak

        glib:
         * update new symbols section

        build system:
         * cmake: Make sure ENABLE_LIBOPENJPEG is either 0 or 1

Release 0.33.0
        core:
         * Fix regression in pdftops parameter passing. Bug #89827
         * Combine base characters and diacritical marks. Bug #87215
         * Use width from W array for WMode positioning. Bug #89621
         * Fixed adding annotation of Subtype Popup to pdf page. Bug #89136
         * CairoOutputDev: Fix memory leak in CairoFreeTypeFont::create
         * SplashOutputDev: memset on error to have reproducible outputs

        qt4:
         * Fix PDF Text String -> QString conversion. KDE Bug #344849

        qt5:
         * Fix PDF Text String -> QString conversion. KDE Bug #344849

        glib:
         * Add poppler_annot_markup_set_popup_rectangle()
         * Fix segfault when creating PopplerAction. Bug #90093

        utils:
         * pdftohtml: Set exit status adecuately. Bug #83609

        build system:
         * configure: Fix invalid shell comparaison in libtiff test

Release 0.32.0
        core:
         * Annotations: Fix rendering of empty BG/BC arrays
         * Splash: Fix wrong colour shown when GouraudTriangleShFill uses a DeviceN colorspace. Bug #89182
         * Splash: Fix use of uninitialized variable in Splash::pipeRun
         * Remove unnecesary check for font validity. Bug #88939
         * Small optimization in GooString::appendfv(). Bug #89096
         * Fix crashes in malformed files

        utils:
         * pdftops: Make colorpsace optimization an option instead of default
         * pdfseparate: use always an unique instance for PDFDoc for savePageAs

        build system:
         * cmake: If extra-cmake-modules is around include the Sanitizers module

Release 0.31.0
        core:
         * CairoOutputDev: support embedding JBIG2 image data
         * Accept malformed documents whose root is a Page instead of a Pages. Bug #88172
         * Fix crash on broken documents
         * JPEG2000Stream: Inline doGetChar and doLookChar
         * GlobalParams cleaning

        utils:
         * pdftops: Add rasterization option. Bug #85934

        qt4:
         * Expose whole-words search option

        qt5:
         * Expose whole-words search option

Release 0.30.0
        core:
         * Openjpeg2 support (openjpeg 1 is preferred). Bug #58906
         * Fix potential memory corruption on TextSelectionDumper. Bug #84555
         * Check for invalid matrix in annotation. Bug #84990
         * Open some not conforming files. Bug #85919
         * PSOutputDev: Accept a list of pages indeces instead of first, last. Bug #84833
         * Fix memory leak on error condition

        cpp:
         * New API to set debug output function

        build system:
         * configure: Improve support with older clang versions. Bug #76963

        utils:
         * pdfunite: Support output intents, optional content and acroform

Release 0.29.0
        core:
         * Use correct LAB byte array for lcms input. Bug #86388
         * Write correct size in trailer dict. Bug #86063
         * Use Default colorspaces if present instead of Device colorspaces
         * Solve blend mode problem in CYMK and DeviceN for separable blend modes
         * Compilation/warning fixes on SunOS
         * Regression test improvements

        glib:
         * demo: Compilation fixlets

        build system:
         * cofigure: print "no" instead of "auto" if lcms not found

Release 0.28.0
        core:
         * Fix rendering of file with a wrong embedded font. Bug #84270
         * Use alt colorspace to get CMYK values for an ICC based CMYK colorspace. Bug #79019
         * Map Standard/Expert encoding ligatures to AGLFN names. Bug #80093
         * Make Attribute::getName() work when UTF-16BE is used. Bug #84722
         * Fix memory leak in Dict::remove. Bug #84607
         * Fix crashes in broken files
         * SplashOutputDev: Improve Overprintmode and shadings. Bug #80998
         * CairoOutputDev: fix crash when no group color space. Bug #85137
         * CairoOutputDev: Don't render text when text matrix is not invertable. Bug #78042
         * CairoOutputDev: Only embed mime data for gray/rgb/cmyk colorspaces. Bug #80719
         * CairoOutputDev: Only embed mime data if image decode map is identity
         * cairo: Use matrix to determine pattern size. Bug #33364
         * Fix compile warnings
         * regression test improvements

        glib:
         * Fix use of uninitialized members in PopplerInputStream. Bug #82630
         * Documentation improvements
         * Do not dist gir_DATA
         * Remove use of GTK deprecated functions. Bug #82384. Bug #82385
         * Build introspection linking to the uninstalled libraries. Big #84526

        qt4:
         * Add a new Page::annotations() that let's you specify subtypes

        qt5:
         * Add a new Page::annotations() that let's you specify subtypes

        utils:
         * pdfseparate: additonal handling for annotations. Bug #77549
         * pdfdetach: fix crash when getPage() returns null. Bug #85145
         * pdftocairo: Add support for printing to a Windows printer. Bug #79936

        build system:
         * Move automake version check from autogen.sh to configure.ac. Bug #79797
         * Makefile.am cleanups. Bug #79411
         * Use poppler-data pkg-config
         * Make autogen.sh work with variables with spaces
         * Don't use -fPIC on mingw
         * Fix build with --disable-utils. Bug #84448

Release 0.26.4
        core:
         * CairoOutputDev: Make sure we always push a transparency group in setSoftMaskFromImageMask(). Bug #81624
         * Fix a crash when adding Annotation without contents
         * Improve non-latin characters in inline notes. Bug #65956
         * Don't check for inlineImg twice. Bug #82059
         * printf() -> error()

        glib:
         * Return NULL in poppler_annot_get_contents also for empty strings
         * Fix a memory leak when getting text layout and attributes

Release 0.26.3
        qt5:
         * autoconf: Improve moc-qt5 detection
         * Fix compilation with MinGW

        glib:
         * Fix typo in api docs
         * use C90-style comments in public headers

        core:
         * Error out instead of exiting if allInter grows too much. Bug #78714

        qt4:
         * Update required version to Qt 4.7.0

        build system:
         * Include stdio.h from poppler-config.h

        misc:
         * Update .gitignore files

Release 0.26.2
        core:
         * Make sure we have an xref before using. KDE Bug #335413

        build system:
         * autoconf: Fix typo in configure.ac

        utils:
         * pdftohtml: exit with 0 with -v and -h

Release 0.26.1
        core:
         * Use field value V for radio buttons. Bug #75979
         * Fix extraction of text in some files. Bug #78145
         * Only add annotations of the current page when splitting. Bug #77549

        build system:
         * autoconf: Fix libopenjpeg 1.5 detection on some systems. Bug #78389

        glib:
         * Fix multiple definition of PopplerTextSpan

Release 0.26.0
        qt4:
         * Fix mismatched boolean logic in TextAnnotation::setInplaceIntent

        qt5:
         * Fix mismatched boolean logic in TextAnnotation::setInplaceIntent

        core:
         * Very small code cleanup

        cpp:
         * Very small code cleanup

Release 0.25.3
        core:
         * Fix crashes on broken files
         * Avoid MinGW/Cygwin warnings due to redefinition of NOMINMAX
         * Fix some small memory leaks

        qt5:
         * Fix some kinds of OCG models
         * Cleanup some deprecated methods

        glib:
         * Fix the first coord of the quadrilateral in create_poppler_quads_from_annot_quads(). Bug #76504

        utils:
         * pdftohtml: Fix typo in manpage

        qt4:
         * Fix some kinds of OCG models

Release 0.25.2
        core:
         * Tagged-PDF support
         * Open some broken files. Bug #75232
         * Fix crashes on broken files
         * Fix regression parsing some broken files. KDE Bug #329600
         * Improve compilation under Win 8 with Visual Studio 2012. Bug #73111
         * PSOutputDev: Ensure paper size takes into account rotation. Bug #72312
         * PSOutputDev: Fix DocumentMedia/Page/Media/PageBBox DSC comments
         * PSOutputDev: Use crop box as page size
         * PSOutputDev: Remove origpagesizes mode and make -origpagesizes an alias for -paper match
         * PSOutputDev: Only change paper size when different to previous size
         * PSOutputDev: Ensure there is always a page size in the output
         * PSOutputDev: Fix regression when creating level1 PS. Bug #75241 
         * CairoOutputDev: Clip to crop box. Gnome Bug #649886
         * Splash: Blend usage in PDF with spot colors casue random output. Bug #74883
         * Splash: Fix off by one that caused crash in a file. Bug #76387
         * Make sure number of least objects in hints table is valid. Bug #74741
         * Limit numeric parsing of character names. Bug #38456

        glib:
         * Tagged-PDF support
         * Annotation improvements
         * Install error callback. Bug #73269
         * Fix gobject-introspection warnings
         * demo: Fix performance in text markup annotations
         * Increase gtk3 dependency

        qt4:
         * Improve naming of internal export/import macros
         * Add GCC visibility export attributes
         * Expose document-supplied text direction

        qt5:
         * Improve naming of internal export/import macros
         * Add GCC visibility export attributes
         * Expose document-supplied text direction

        utils: 
         * pdftocairo: Ensure page size and crop box works the same as pdftops
         * Fix TIFF writting in Windows. Bug #75969 

        buildsystem:
         * Learn about automake 1.14
         * Do not define -ansi. Bug #72499
         * cmake: Install JpegWriter.h depending on libjpeg
         * cmake: Use c99 for the c compiler

Release 0.25.1
        core:
         * GooString format: Added some tests + improved documentation
         * GooString format: fixed bug with printing LLONG_MIN
         * regression test improvements

        qt4:
         * Arthur backend font rendering improvements
         * test program to save to file

        qt5:
         * Arthur backend font rendering improvements
         * Improve detection of Qt5 moc. Bug #72744
         * test program to save to file

        utils:
         * pdfunite: Work even if there's a single file given
         * pdfunite: do not lose fonts when merging some files

Release 0.25.0
        core:
         * Annotation improvements
         * Tagged PDF work
         * Improve speed on some files using ICC color space
         * Use ICC profile in OutputIntents. Bug #34053
         * Limit use of ZapfDingbats character names. Bug #60243
         * Splash: correction for knockout transparency groups
         * regression test improvements

        utils:
         * pdftoppm: Added thinlinemode option setting
         * pdfinfo: Indicate if pdf contains javascript
         * pdfinfo: Add option to print out javascript
         * pdfimages: Print size, ratio, and ppi
         * pdfimages: More image output format support
         * pdfseparate: allow zero-padded pagespecs

        glib:
         * Annotation improvements
         * Add API to get text, text layout and text attributes for a given area
         * demo improvements

Release 0.24.5
        core:
         * Fix crash due to wrong formatting of error message. KDE Bug #328511

Release 0.24.4
        core:
         * Fix regression in broken endstream detection. Bug #70854
         * Catalog: sort entries of NameTrees to make sure lookup works. Bug #26049
         * Don't infinite loop if reading from GooFile::read fails. Bug #71835

        utils:
         * pdftotext: Do not close stdout. Bug #71639
         * pdftotext: Silence warning for may be used uninitialized variable. Bug #71640
         * pdftotext: Escape the text of the xml headers
         * Warn the user if he provides a wrong range

        qt4:
         * Fix typo in xml API. Bug #71643

        qt5:
         * Fix typo in xml API. Bug #71643

Release 0.24.3
        core:
         * PSOutputDev: Fix PFB font embedding. Bug #69717
         * CairoOutputDev: Do not set an invalid matrix in drawImage(). Bug #70085 

        qt4:
         * Don't crash if getXRef()->copy() fails

        qt5:
         * Don't crash if getXRef()->copy() fails

        utils:
         * pdfseparate: Allow only one %d in the filename. Bug #69434
        
Release 0.24.2
        core:
         * Windows: Fix CreateFile fails with ERROR_SHARING_VIOLATION. Bug #69597

        utils:
         * pdfseparate: improve the path building
         * pdftocairo: check file opening failure in beginDocument()
        
Release 0.24.1
        core:
         * SplashOutputDev: use getRGBLine images if available. Bug #66928
         * SplashOutputDev: Don't copy bitmap if we don't need to.
         * PSOutputDev: Fix regression in -eps -level1sep rendering. Bug #68321
         * Fix crash in malformed file 1026.asan.0.42.pdf
         * use copyString instead of strdup where memory is freed with gfree. Bug #67666

        utils:
         * pdfdetach: don't mention xpdfrc
         * pdftotext: Fix -bbox with stdin as input. Bug #45163
         * pdftohtml: Fix jpeg image export. Bug #48270
         * pdfimages: Fix typos in man page

        glib:
         * demo: Remove GTK_DISABLE_DEPRECATED compilation flag

        qt4:
         * Fix small typo in documentation

        qt5:
         * Fix small typo in documentation

Release 0.24.0
        core:
         * TextOutputDev: Do not draw ligatures more than once when selected. Bug #9001
         * PSOutputDev: Make some pdftops conversions much faster
         * PSOutputDev: Initialize t3FillColorOnly
         * SplashOutputDev: Fallback to 1x1 bitmap if we fail to create the real size

Release 0.23.4
        core:
         * TextOutputDev: clip the selected text rendering to the selection box. Bug #66983
         * CairoImageOutputDev: Fix the bounding box of saved images

        build system:
         * Improve linking against pthreads

Release 0.23.3
        core:
         * Annotation improvements
         * Fix crashes on malformed files
         * TextSelectionPainter: Draw glyphs after selection background
         * TextOutputDev: add a method to TextPage to get the selection as a list of words

        qt5:
         * Initial Qt5 port

        qt4:
         * Windows compile fixes
         * Demo: Allow the choose the page rotation

        build system:
         * Fix mingw build
         * Minor autotools fixes

Release 0.23.2
        core:
         * SplashOutputDev: Speed-up some tiling on a 10x factor
         * Improve caching of lcms2 ICC color profiles
         * Put some private classes in an anonymous namespace

        qt4:
         * Add a thread stresser tool

        build system:
         * Fix mingw build

Release 0.23.1
        core:
         * XRef stream writing: Write 32-bit offsets when possible
         * Fix splashModeBGR8 rendering (Bug #64381)

        glib:
         * Do not use deprecated gtk_scrolled_window_add_with_viewport() (Bug #64683)

        build system:
         * Fix Large file support when using cmake

Release 0.23.0
        core:
         * Make rendering thread safe
         * Large file support
         * Implement Crypt filter (Bug #62800)
         * Fix endstream detection (Bug #62985)
         * CairoOutputDev: support uncolored tiling patterns (Bug #59179)
         * SplashOutputDev: Introduce Thin Line mode support (Bug #37347)

        qt4:
         * Expose Thin Line mode support

Release 0.22.4
        core:
         * Always consider a softmask transfer function (Bug #63587)
         * Fix crash on malformed files (Bug #63190)
         * Splash: Fix compilation with fixed point mode enabled

        utils:
         * Fix crash on some files (Bug #63909)

        qt4:
         * Fix name decoding of some attachments (KDE Bug #307786)

        build system:
         * Fix compilation with mingw-w64 compiler

Release 0.22.3
        core:
         * Check order bounding box values in tiling pattern (Bug #62369)
         * CairoImageOutputDev: Don't change image interpolation when printing (Bug #62418)
         * TextOutputDev: Set text matrix when painting selection (Bug #61042)
         * Only write the file once when saving (Bug #62739)
         * Fix for complete rewrites in repaired files
         * Fixlet regarding spec interpretation for Link Zoom value
         * Fix typos in man pages
         * Fix compile when not using libjpeg

        glib:
         * Always start from the beginning when starting a new search on a page (Bug #59972)

        qt4:
         * Fix crash in files with LinkRendition (KDE Bug #317710)

        build system:
         * Small cmake improvements

Release 0.22.2
        core:
         * Correct rendering of underline and strike out annotations (Bug #61518)
         * Workaround broken jpeg stream definitions (Bug #61994)
         * SplashOutputDev: Restore CTM on early exits (Bug #61413)
         * SplashOutputDev: Make sure we don't try to paint in x < 0 (KDE Bug #315432)
         * Fix latin page labels. (Bug #61034)
         * Fix compilation with jpeglib9
         * Fix minor valgrind warning

        utils:
         * pdfimages: Fix extraction of some images (Bug #61168)

        build system:
         * Fix the build with automake-1.13

Release 0.22.1
        core:
         * Fix crash in some pdf files when extracting text (Bug #59561)
         * Fix crashes in wrongly formed files
         * Fix wrong warning when opening some files (Bug #58966)

        build system:
         * Improve autoconf jpeglib.h detection (Bug #59186)

Release 0.22.0
        core:
         * Fix crash in invalid files that define a <= 0 bits per image value
         * Fix a few issues in JPX decoding when not using OpenJPEG
         * TextOutputDev: Use page size for max value in TextPage::visitSelection
         * Fix typo in error message

        utils:
         * Fix pdfunite regression (Bug #58569)
         * Demo fixes and improvements

        misc:
         * pdf-inspector improvements

Release 0.21.4
        core:
         * SplashOutputDev: Fix crash when rendering in monochrome mode
         * SplashOutputDev: Fix line widths in monochrome mode (Bug #57294)
         * PSOutputDev: Fix crop on EPS conversion (Bug #30692)
         * TextOutputDev: Fix minor logic mistake
         * Fix assert on some malformed files (Bug #58257)
         * Move #include "jpeglib.h" into .cc file (Bug #57687)
         * Filter text that may end up being written to the shell
         * Fix windows compile warnings

        glib:
         * Add poppler_annot_set_flags (Bug #58015)
         * Demo fixes and improvements

        qt4:
         * Fix check_lexer on 32-bit systems

Release 0.21.3
        core:
         * Splash: Implement bilinear image scaling (Bug #22138)
         * CairoOutputDev: Update fill and stroke color in startPage (Bug #54526)
         * Fix GooString::insert()
         * Allow large chars in TextPage
         * Fix crash on ActualText::end
         * Don't use memcpy to copy classes
         * Fix warnings

        glib:
         * Check if words end with spaces (Bug #54504)
         * Ensure text is only computed on first render
         * Fix warnings while generating introspection file
         * Fix returns tag in PopplerAttachmentSaveFunc api doc
         * Minor demo fixes

Release 0.21.2
        core:
         * CairoOutputDev: make drawImage work with images > 32767 in width/height (Bug #56858)
         * CairoOutputDev: Fix soft mask when image resolution != smask resolution (Bug #57070)
         * CairoOutputDev: Fix crash in CairoImageOutputDev with setSoftMaskFromImageMask (Bug #57067)
         * Remove a check on fonts that we don't need (Bug #56753)
         * Misc code cleanups

        utils:
         * pdftocairo: Add tiff output support (Bug #57006)
         * pdfunite: Fix -v (Bug #56817)
         * Misc code cleanups

Release 0.21.1
        core:
         * Annotation improvements
         * Form improvements
         * CairoImageOutputDev: Support parameterized Gouraud shading (Bug #56463)
         * UTF validation fixes
         * Do not call drawing routines if we don't need non text (Bug #54617)
         * Fix Memory leak in CharCodeToUnicode (Bug #54702)

        qt4:
         * Make LinkRendition properties available (Bug #55378)
         * Accessors for FormWidgetChoice::editChoice
         * Implement overprint

Release 0.21.0
        core:
         * Support the modification of files with Encrypt
         * Annotation improvements
         * Form improvements
         * Splash: Implement DeviceN support
         * Splash: Avoid bogus memory error for tilingPattern
         * TextOutputDev: Allow multiple fonts in a TextWord
         * Kill the concept of base dir
         * PSOutputDev: Always write HiResBoundingBox (Bug #53159)
         * Convert UTF-16 to UCS-4 when reading toUnicode cmap
         * GooString formatting: add support for uppercase hexadecimal
         * Use error() instead of fprintf(stderr, ...) in Annot::layoutText
         * poppler-config.h: remove WITH_FONTCONFIGURATION_* macros

        glib:
         * Annotation improvements
         * Add poppler_page_remove_annot()
         * Add poppler_document_new_from_stream
         * Add poppler_document_new_from_gfile
         * Add poppler_page_find_text_with_options (Bug #2951)
         * Demo improvements
         * Port tests and demo to GTK+3

        qt4:
         * Add accessor methods for movie poster information
         * Make 'additional actions' available in Annotation API (Bug #53589)
         * Add whole-page search method to Poppler::Page
         * Small changes in tests

        utils:
         * pdftohtml: Make the output more xhtml compliant
         * pdftohtml: Add -fontfullname. (Bug #49872)
         * pdftohtml: Do not invoke gs anymore

        build system:
         * Add the possibility of using lcms1 even if lcms2 is installed
         * Remove extra fontconfig CFLAGS and LIBS

Release 0.20.5
        core:
         * Fix crashes in malformed documents
         * Fix parsing of very big numbers
         * Splash: Do not render invalid font outlines (Bug #55573)
         * Check for NaN in TextPage::addChar

        build system:
         * Fix build using mingw64 with winpthread
         * autotools: Fix compilation when lcms is on non standard locations (Bug #55326)
         * Support automake-1.12 (Bug #55541)

        glib:
         * Chain up finalize to the parent class (Bug #55521)

Release 0.20.4
        core:
         * Improvements regarding embedded file handling. (KDE Bug #306008)
         * Fix opening some broken files (Bug #14303)
         * Fix memory leaks
         * Fix crashes in various broken files
         * Refine warning to only complain when really needed
         * Remove function declared but not implemented
         * Remove execution permissions from a header file

        qt4:
         * Improvements regarding embedded file handling. (KDE Bug #306008)

Release 0.20.3
        core:
         * If NULL, NULL fails as password try EMPTY, EMPTY before failing (Bug #3498)
         * SplashOutputDev: Fix bogus memory allocation size in Splash::arbitraryTransformImage (Bug #49523)
         * SplashOutputDev: Fix segfault when scaleImage returns NULL (Bug #52488)
         * SplashOutputDev: Blend mode enhancements for CMYK
         * PSOutputDev: Fix conversion when creating multiple strips (Bug #51982)
         * PSOutputDev: Fix Bitmaps in level2sep or level3sep (Bug #52384)
         * PSOutputDev: Fix DeviceN images with alternate Lab colorspace in level 3 PostScript (Bug #51822)
         * PSOutputDev: Make sure xScale and yScale are always initialized (Bug #52215)
         * Unify poppler-config.h includes in core "installed" headers (Bug #52193)
         * Replace c++ style includes with c style ones (Bug #52426)

        utils:
         * pdfseparate: Return 0 on success
        
Release 0.20.2
        core:
         * Fix compilation on Windows
         * Copy resources content defined in the pages dict on save (Bug #51369)
         * PSOutputDev: Correct %%DocumentCustomColors (Bug #51479)
         * PSOutputDev: Fix handling of DeviceN images in level 3 PostScript (Bug #51548)
         * Fix crash in malformed documents
        
        qt4:
         * Do not hang on malformed /Annots objects (Bug #51361)

Release 0.20.1
        core:
         * Remove unnecesary transparency group handling in splash (Bug #13487)
         * Include substitute font name in system font cache (Bug #49826)
         * Fix logic on SplashBitmap::writeImgFile
         * PSOutputDev: use setoverprintmode only if rip knows it
         * Fix crash in malformed documents
        
        qt4:
         * Make TextAnnotation constructor public
         * Fix saving of default TextAnnotation to xml
         * Keep page rotation into account when normalizing annotation coords
        
        glib:
         * Fix memory leak when document fails to load
         * Make sure password is always converted to latin1
         * Fix typo in documentation

        build system:
         * Distribute cmake/modules/FindLCMS2.cmake (Bug #49818)

        utils:
         * pdftohtml: Determine if font is bold or italic based on FontDescriptor (Bug #49758)
         * pdfseparate: Syntax fixes in the man page

Release 0.20.0
        core:
         * Reconstruct xref table if xref needed but missing (Bug #40719)
         * Fix getFullyQualifiedName with unicode field names (Bug #49256)
         * SplashOutputDev: Fix rendering of knockout groups (Bug #12185)
         * SplashOutputDev: Fix cmyk transfer bug (Bug #49341)
         * Fix crashes in broken documents
         * Bring back the Outputdev::begin/endMarkedContent virtuals
         * Build fixes
        
        qt4:
         * Convert propertly unicode encoded field qualified names
        
        glib:
         * glib: Use delete[] to free array allocated with new[] (Bug #48447)

Release 0.19.4
        core:
         * Annotation improvements
         * More compatible file writing
         * SplashOutputDev: Fix slow rendering of pdf with a lot of image masks in pattern colorspace
         * Fix crashes in broken documents
         * Fix spurious warning messages
        
        utils:
         * pdftotext: Add missing section heading to man page
         * pdftohtml: Fix crash when the destination file does not exist
        
        build system:
         * autoconf: Do not append "-ansi" to CXXFLAG, if "-std=XXX" is already specified.
         * autoconf: Do not clear FREETYPE_CFLAGS, FREETYPE_LIBS before PKG_CHECK_MODULES()
         * autoconf: Copying graphics library CFLAGS to cpp frontend Makefile.am

Release 0.19.3
        core:
         * Annotation improvements
         * CairoOutputDev: Fix regression caused by mesh gradients
         * CairoOutputDev: Use correct userfont font bbox (Bug #48399)
         * CairoOutputDev: Fix paintTransparencyGroup when both mask and fill opacity are required (Bug #48453)
         * CairoOutputDev: Ensure 0 width lines with stroke_adjust are aligned
         * CairoOutputDev: Only align stroke coords for horizontal and vertical lines (Bug #48318)
         * CairoOutputDev: Fix stroke pattern with transparency group (Bug #48468)
         * Fix crash in JBIG2Stream decoding
         * Fix memory leak when looking for a substitute font
         * Fix page labels to not have a null character at the end
         * Fix Splash CMYK merge error
         * ttc<->ttf fallback is expected for CJK font list in for Windows (Bug #48046)
        
        qt4:
         * Annotations can now be modified
         * Annotations can now be added
         * Annotations can now be removed

        utils:
         * pdftohtml: Add producer and version to xml output
         * pdftohtml: Fix the mask inversion for PNG
         
Release 0.19.2
        core:
         * Annotation improvements
         * CairoOutputDev: update cairo mesh pattern to 1.12 api
         * CairoOutputDev: fix some transparency issues (Bug #47739)
         * CairoOutputDev: Fix regression in some shadings
         * TextOutputDev: Don't add newline to last line extracted by TextSelectionDumper (Bug #45955)
         * CJK font improvements
         * Improve font matching for non embedded fonts
         * Fix regression regarding forceRasterize in PSOutputDev
         * Fix typos glyph names in truetype 'post' table standard mac ordering
        
        build system:
         * minor cmake fixes
         * misc autoconf fixes
         * POPPLER_VERSION is now wrapped in quotes

        utils:
         * pdftohtml: extract mask images even if they are not JPEG (Bug #47186)
         * pdftohtml: Flip images if they need to (Bug #32340)
         
Release 0.19.1
        core:
         * Improve CJK suport in PSOutputDev
         * CJK substitute implementation on WIndows platforms
         * Do not crash on malformed files with 0 bits in the color map of an image
         * Fix regression in some PSOutputDev array sizing
         * Improvements to Annotation editing
         * Fix logic error in Rendition parsing code (Bug #47063)
         * Minor API changes to SplashOutputDev (Bug #46622)
         * Fix mismatch in some functions declarations
         * Update poppler copyright year
        
        utils:
         * pdftops: Fix -passfonts regression. (Bug #46744)
         * pdffonts: List the encoding of each font. (Bug #46888)
         * pdftohtml: Add possibilty of controlling word breaks percentage. (Bug #47022)
         
        qt4:
         * Support for LinkMovie object (Bug #40561)
         * Support for Media Rendition
         
        glib:
         * Add poppler_fonts_iter_get_encoding
         * Improvements to the demo

Release 0.19.0
        core:
         * Merge Xpdf 3.03
         * Add support for lcms2
         * SplashOutputDev: Implement Overprint
         * PSOutputDev: Implement Overprint
         * Expand glyph name ligatures such as "ff", "ffi" etc to normal form (Bug #7002)
         * Use an Identity CharCodeToUnicode for Adobe-Identity and Adobe-UCS collections (Bug #35468)
         * CairoOutputDev: Avoid setting huge clip area when printing (Bug #44002)
         * CairoOutputDev: Fix test for rotation (Bug #14619)
         * CairoOutputDev: Don't read inline image streams twice (Bug #45668)
         * CairoOutputDev: set mask matrix before drawing an image with a mask (Bug #40828)
         * Update glyph names to Unicode values mapping (Bug #13131)
         * Only use Hints table when there are no parse errors (Bug #46459)
         * Expose POPPLER_VERSION in poppler-config.h
        
        utils:
         * pdftohtml: Output images in -xml mode if no -i option is specified
         * pdftohtml: Get rid of static data members; merge duplicated jpeg dumping code
         * pdftohtml: Be more consistent generating the outlines
         * pdftohtml: Generate outlines in pdftohtml in -xml mode (Bug #56993)
         * pdftohtml: Combine UTF16 surrogate pairs (Bug #46521)
         * pdfinfo: Report page rotation
         * pdfinfo: Decode utf-16 surrogate pairs
         * pdftoppm: Allow one of -scale-to-[xy] = -1 to mean the aspect ratio is to be preserved (Bug #43393)
         * pdftocairo: Allow one of -scale-to-[xy] = -1 to mean the aspect ratio is to be preserved
         * pdffonts: Add -subst option to list the substitute font name and filename
         * pdfseparate: Produce PDF/X conformant pdf pages if the original PDF was PDF/X conformant
         * pdfimages: Add -list option to list all images (Bug #46066)
         * Improve various manpages
         
        glib:
         * Add poppler_fonts_iter_get_substitute_name
         * Demo improvements
         * Update gtk-doc makefile and m4 file
         * Fix typos in documentation
        
        qt4:
         * Add the option of PSConverter creating EPS
         * Form support improvements
        
        build system:
         * autotools: Print the cairo version required if not found (Bug #44619)
         * autotools: Print the glib version required if not found
         * autotools: Use pkgconfig to check for libopenjpeg (Bug #21789)
         * autotools: Replace openjpeg compile test with a version test
         * Add a configuration option for the test data dir

Release 0.18.4
        core:
         * CairoOutputDev: Restore temporary clip used in CairoOutputDev::fill when painting a mask
         * CairoOutputDev: Ensure paintTransparencyGroup uses same ctm as beginTransparencyGroup. Bug #29968
         * CairoOutputDev: Use fabs when comparing the transformed line width. Bug #43441
         * CairoOutputDev: Remove unused variable in CairoFontEngine.cc. Bug #45442
         * SplashOutputDev: Do not use 50Kb of stack in SplashXPath::addCurve. Bug #44905
         * JpegWriter: set image parameters after jpeg_set_defaults(). Bug #45224
         * OpenJPEG decoder: Set OPJ_DPARAMETERS_IGNORE_PCLR_CMAP_CDEF_FLAG if you have it. Bug #43414
         * Lexer: convert integer to real when overflow occurs. Bug #45605

        glib:
         * Various minor introspection and documentation improvements. Bug #44790
         * Fix return values. Bug #45440. Bug #45441
         * gtk-doc improvements. Bug #45549
         * Introspection improvements. Bug #45455
        
        utils:
         * HtmlOutputDev: Proper unicode support when dumping PDF outline. Bug #45572
         * HtmlOutputDev: Fix leaks. Bug #45805
         * HtmlOutputDev: Close li tags in generated outlines. Bug #45807
         * man pages: fix minor issues with hypens and %

        build system:
         * automake: Link to lcms if needed
         * automake: Fix build for builddir != srcdir. Bug #45434
         * automake: Improve moc detection when cross compiling
         * Fix build with latest mingw-w64 headers. Bug #45407
        
        qt4:
         * remove non-existing 'qt' include dirs
        
Release 0.18.3
        core:
         * Do not fail if we are trying to save a file with Encrypt that has not been modified. KDE Bug #288045
         * Include .otf fonts when finding substitute fonts. Bug #44412
         * Fix stack overflow in PDFDoc::markObject(). Bug #44660
         * Include strings.h as we use memcpy. Bug #43558

        utils:
         * pdfunite: Properly initialize globalParams. Bug #44659
         * pdfseparate: Properly initialize globalParams
         * Fix iniliazialization of GooString arguments

        build system:
         * autoconf: Check for cairo-ft and other cairo backends. Bug #43969
        
Release 0.18.2
        core:
         * Fix leak in GooString when resizing to a smaller string
         * Fix crash if failing to parse the colorspace. Bug #42793
         * Make GfxColorSpace::parse accept dicts

        qt4:
         * Use PDFDoc(wchar_t *, ...) on Windows. Bug #35378
         * Add missing include
         * Minor fixes in documentation

        utils:
         * pdftocairo: Fix crash when rendering only odd/even pages in a printing format
        
        build system:
         * Fix pkg-config files
        
Release 0.18.1
        core:
         * PSOutputDev: Output PS that does not confuse libspectre
         * PSOutputDev: Fix tiling pattern fill matrix. Bug #41374
         * PSOutputDev: Emit non repeating patterns just once
         * PSOutputDev: Fix uncolored tiling patterns. Bug #41462
         * CairoOutputDev: Fix crash when using poppler_page_get_image()
         * CairoOutputDev: Fix various setSoftMask bugs. Bug #41005
        
        utils:
         * pdftocairo: Flush/close files one we are done using them
         * pdftocairo: Compile in Windows
        
        build system:
         * CMake: Fix typo in option description
         * CMake: Correctly include fontconfig include dir
         * Remove poppler-cairo dependency from poppler-glib pkg-config file
        
        qt4:
         * Minor fixes in documentation

Release 0.18.0
        core:
         * Fix small memory leak when dealing with marked content
         * Remove DCTStream::getRawStream since Stream::getNextStream does the same
        
        utils:
         * Rename pdfmerge to pdfunite
         * Rename pdfextract to pdfseparate
         * pdfseparate: Complain if %d is not present and it should
         * Add pdfseparate and pdfunite man pages
        
        build system:
         * Minor cleanup in regarding removed qt code

Release 0.17.4 (0.18 RC)
        core:
         * SplashOutputDev: Compile when defining USE_FIXEDPOINT
         * PNGWriter: Compile with libpng >= 1.5.0
        
Release 0.17.3 (0.18 Beta 3)
        core:
         * PSOutputDev: Use Patterns for tiling fill when PS level >= 2
         * PSOutputDev: Avoid using /PatternType if only one instance of the pattern is used
         * PSOutputDev: Add poppler version as comment in the file
         * CairoOutputDev: Set mime data for soft masked images (Bug #40192)
         * CairoOutputDev: Assume printer pixel size is 1/600" when stroking 0 width lines (Bug #39067)
         * CairoOutputDev: Use cairo_show_text_glyphs() when printing
         * CairoOutputDev: Fix stroke patterns (Bug #11719)
         * CairoOutputDev: Fix unique id mime data
         * CairoOutputDev: fix stroking of very thin lines
         * CairoOutputDev: align strokes when Stroke Adjust is true and line width <= 1 (Bug #4536)
         * TextOutputDev: Add TextFontInfo::matches()
         * Improve PNGWriter
         * Rework writing of PDF files

        utils:
         * Introduce pdftocairo - utility for creating png/jpeg/ps/eps/pdf/svg using CairoOutputDev
         * Introduce pdfextract - utility to extract PDF pages
         * Introduce pdfmerge - utility to merge PDF files
         * Fix compilation warning
         * pdftohtml: Support text rotation (Bug #38586)
         * Update SEE ALSO section of man pages

        glib:
         * Add poppler_page_get_text_attributes()
         * Add text attributes information to text demo

        qt4:
         * Add a way to get the fully qualified name of a FormField
         * Minor documentation improvements
        
Release 0.17.2 (0.18 Beta 2)
        core:
         * EmbeddedFile improvements
         * don't gmalloc(-1) upon ftell failure
         * Fix missing content in some pages (Bug #39637)
         * Improve selection of CJK fonts (Bug #36474)
         * SplashOutputDev: Implement overprint
         * SplashOutputDev: Render dots for 0 length dashed lines (Bug #34150)
         * SplashOutputDev: Fix bad memory access when not using antialias (Bug #37189)
         * PSOutputDev: Make level2sep and level3sep write cmyk instead of rgb
         * PSOutputDev: Make level1sep, level2sep and level3sep write gray instead of rgb for gray images
         * Fix numerical overflow in libopenjpeg JPXStream (Bug #39361)
         * Fix crash on truncated JPEG/DCT stream (Bug #36693)
         * Make sure the dict is a page dict (Bugs #35925 #39072)
         * Fix calculation of startXRefPos
         * Handle missing startxref properly (Bug #38209)
         * Parse the "Medium" modifier when asking fontconfig for a font
         * Header cleanup
         * Include cleanup
         * Define cleanup

        glib:
         * Add missing permissions flags to PopplerPermissions
         * Add missing permission flags to info demo
         * Update gtk-doc.make
         * Add poppler_document_get_n_attachments()
        
        utils:
         * pdftohtml: Fix encoding of PDF document metadata (Bug #37900)
         * pdftohtml: Fix vertical spacing issues  (Bug #38019)
         * pdftotext: Fix -htmlmeta to correctly output U+2019 in PDF metadata (Bug #37900)
         * pdftoppm: Implement overprint

        qt4:
         * Rework EmbeddedFile internals
         * Fix possible crash in test
        
Release 0.17.1 (0.18 Beta 1)
        core:
         * Rework the way form fields tree is built
         * Cleanup unused parameters/variables

        glib:
         * Add JavaScript actions
         * demo: Show javascript actions in actions view
        
        qt4:
         * tests: Turn some assignments to bool into QVERIFY checks

Release 0.17.0 (0.18 Alpha)
        core:
         * Splash: Implement tiling patterns
         * Splash: Support slight hinting
         * Splash: Radial shading improvements
         * Splash: General speed improvements
         * Arthur: Add Hinting API
         * Cairo: Implement Type 4,5,6,7 shadings using cairo mesh gradients
         * Cairo: Use the new cairo unique id to set the surface id when printing
         * PS: Add PS level1 non standard binary output option
         * PS: Allow setting the rasterization resolution
         * Form support improvements
         * Annotation support improvements
         * General speed improvements
         * Add support for handling ViewerPreferences
         * Remove abiword output device

        utils:
         * pdftoppm: Add -singlefile option (Bug #32025)
         * pdftoppm: Add TIFF output format support (Bug #32027)
         * pdftops: Add PS level1 non standard binary output option
         * pdftops: Allow setting the rasterization resolution
         * pdftoabw has been removed
        
        glib:
         * Add poppler_form_field_get_action() (Bug 33174)
         * Remove GDK API
         * Remove test-poppler-glib
         * demo: Add a tooltip with current selected character in text demo
         * demo: show the activation action of form fields if there's one

        cpp:
         * Add TIFF output possibility
         * Add PNM output possibility

        qt4:
         * Support slight hinting
         * Form support improvements
        
        qt3:
         * The Qt3 frontend has been removed
        
        tests:
         * Merge splash and cairo tests into a single gtk-test tool

Release 0.16.4
        core:
         * Small improvements in Annot parsing

        glib:
         * Add g_return macros to make sure index is correct in form field choice methods
         * Fix a crash when a choice form field has no items selected in glib-demo

        utils:
         * Small fixes to the pdftohtml manpage
         * Fix copyright years

        qt4:
         * Fix caption of push button fields

Release 0.16.3
        core:
         * Increase precision in PS output device
         * Workaround bug when converting pdf to ps with level1 (Bug #31926)
         * Fix crash in Splash output device in some broken pdf
         * Fix infinite loop in some broken files
         * Fix rendering of some substituted fonts (Bug #34522)
         * Do not ask Freetype for 0x0 fonts in Splash output device (Bug #34602)
         * Don't assume y1 > y3 for points of a highlight annotation (Gnome Bug #643028)
         * Handle fontCIDType2OT when creating freetype font in Cairo output device (Gnome Bug #643273)
         * Fix crash in some pdf that use ICC color space (Bug #34357)

        glib:
         * Don't use an uninitialized local variable in demo
         * Add some introspection markers

        qt4:
         * Fix crash regression in unicodeToQString (again)

        utils:
         * pdftotext: Do not crash when using -bbox

Release 0.16.2
	core:
	 * Fix text extraction for some files

	qt4:
	 * Fix crash regression in unicodeToQString

Release 0.16.1
	core:
	 * Fix colorspace issues in the Cairo backend (Bug #32746)
	 * Remove declaration of function without implementation
	 * Do not crash in case jpeg_create_decompress fails (Bug #32890)
	 * Fix variable access mismatch (Bug #33063)
	 * Fix converting some pdf to ps with -level1sep (Bug #32365)
	 * Fix line selection, dont check y for Line selections
	 * Include zlib header in PNGWriter.cc
	 * Fix leak in Splash backend when doing axial shaded fills
	 * Fix label to index conversion on multiple prefixes

	glib:
	 * Use NULL instead of FALSE for functions returning a pointer
	 * Fix memory leak in poppler_page_get_text_layout() for pages with no text

	qt4:
	 * Fix unicodeToQString() to correctly decode the Unicode sequence

Release 0.16.0

	core:
	 * Improve the correctness of radial shadings (Bug #32349)
	 * Adapt the zlib-based FlateStream code to API changes  (Bug #32065)
	 * Make PreScanOutputDev be less agressive when deciding to rasterize (Bug #30107)
	 * Fix some warnings in newer gcc in Splash backend
	 * Fix the preliminary bbox/clip calculation in Splash backend
	 * Use A1 instead of A8 for imagemask in the Cairo backend
	 * Windows compile fixes

	utils:
	 * Do not return 99 (or 1) with -h, -v and -printenc (Bug #32149)
	 * Misc style improvements to pdftohtml code
	 * pdftohtml: Remove the -resolution flag introduced in 0.15.0 and fix the
	              existing -zoom flag

	build system:
	 * Add more warning flags to the default gcc builds
	 * Enable GObject introspection support in the cmake build system

	qt4:
	 * Windows compile fixes

Release 0.15.3 (0.16 RC)

	core:
	 * Improve rendering of radial shadings
	 * Open a broken file (Bug #31861)
	 * Correct parsing of linearization table (Bug #31627)
	 * Find fonts inside patterns (Bug #31948)
	 * [win32] Simplify strtok_r implementation
	 * Use a std::vector<char> instead of a var-length-array of chars
	 * Fix crashes in broken files
	 * Use sets instead of arrays for looking for duplicate fonts

	cpp:
	 * Include correction

	utils:
	 * pdffonts: Remove duplicated code

Release 0.15.2 (0.16 Beta 2)

	core:
	 * Improve shadings and antialias in the Splash backend (Bug #30436)
	 * Linearization improvements
	 * Small improvements to the Arthur backend
	 * Fix calculation of the size of some pages (Bug #30784)
	 * Fix crashes in broken documents

	qt4:
	 * Add Page::renderToPainter() method
	 * Add setDebugErrorFunction() method

	cpp:
	 * Add the hability to render pages to an image

	utils:
	 * Add -p flag to pdfimages

	build system:
	 * Remove -ansi flag for cywin and mingw

Release 0.15.1 (0.16 Beta 1)

	core:
	 * Consider render value when colorizing text (Bug #2807)
	 * Improve rendering of Shading Type 6 and 7
	 * Improve dict lookup speed for big dicts
	 * Fix multiple crashes in malformed PDF files
	 * Fix memory leak in in malformed PDF files
	 * Fix memory leak in the Catalog names
	 * Fix uninitialized uses on DCTScanInfo
	 * Fix a crash when drawing square/circle annots without a border (Bug #30580)
	 * Only clip boxes to mediabox if we are at the page level (Bug #30784)
	 * Do not omit the notdef glyph in the Splash backend
	 * Fix a crash when redering documents with invalid type 3 fonts in the Cairo backend
	 * Form improvements
	 * Add a method to get the PDF file identifier

	glib:
	 * Add more printing options to the API
	 * Add a method to get the PDF file identifier
	 * Add accessor for all PopplerDocument properties
	 * Form improvements
	 * Documentation improvements
	 * Improvements to the demo

	qt4:
	 * Add a callback to know which page has been printed
	 * Add a method to get the PDF file identifier
	 * Optimize GooString to QString conversion
	 * Some more autotests
	 * Update Doxyfile (enables .qch file for assistant)

	build system:
	 * Require Cairo 1.10

	utils:
	 * pdftohtml: Add -s option to generate a single HTML page
	 * pdftotext: Add -bbox option

	cpp:
	 * Add the possibility of loading a document from raw data
	 * Add a method to get the PDF file identifier
	 * Improve Unicode to ustring conversion
	 * Documentation improvements
	 * Update Doxyfile

Release 0.15.0 (0.16 Alpha)

	core:
	 * Remove exception support
	 * Improve creation of Annotations
	 * Fix failure to parse PDF with damaged internal structure. (Bugs #29189 #3870)
	 * Add a way to access the raw text of a page
	 * Speed improvements when reading multiple characters from a given Stream
	 * Speed improvements in the Splash backend
	 * Speed improvement in gray color space calculations
	 * Speed improvement in ICC color space calculations
	 * Speed improvement when reading some fonts
	 * Make GBool a bool instead of an int

	glib:
	 * Add GObject introspection support
	 * Improve creation of Annotations
	 * Add a way to get the coordinates of each character of a page
	 * Add a way to get the page label
	 * Documentation improvements
	 * Support password protected documents in the demo
	 * Support for selection in the demo
	 * Support for adding annotationss in the demo
	 * Misc improvements in the internals
	
	qt4:
	 * Add a way to access the raw text of a page
	 * Recognize "Print" as named action
	 * Documentation improvements
	
	build system:
	 * Add option for autogen.sh to skip configure
	 * Nicer autogen.sh output
	 * Improvements when build the glib frontend with CMake
	
	utils:
	 * pdftohtml: Use splash instead of external gs invocation to render the background
	 * pdftohtml: Let the user specify the resolution of the background. (Bug #29551)
	
	cpp:
	 * Add a way to access the raw text of a page

Release 0.14.3

	core:
	 * Tell Windows we are writing/reading binary data from stdout/stdio (Bug #29329)
	 * Fix crash when parsing some Movie elements (KDE Bug #249586)

Release 0.14.2

	core:
	 * Fix rendering of some documents involving tilingPatternFill in the cairo output device
	 * Improve rendering of some annotations
	 * Handle ColorTransform in DCT streams when using libjpeg (Bug #28873)
	 * Fix crash in the ps output device in some files (KDE Bug #246269)
	 * Fix crash in some malformed files (Bug #28842)
	
	build system:
	 * Improve build on windows
	 * Add uninstalled .pc file support when using autoconf
	
	glib:
	 * Fix a crash when a layer doesn't have a name (Bug #28842)
	
	utils:
	 * Fix padding of names in pdftoppm

Release 0.14.1

	core:
	 * Add ObjectStream caching, makes opening some files ten times faster (Bug #26759)
	 * Fix crash when writing to negative coordinates (Bug #28480)
	 * Check objects are the type we want them to be when parsing GfxICCBasedColorSpace
	 * Optimize Splash::compositeBackground
	 * Optimize color space calculations by using sqrt instead of pow 0.5
	 * Fix crash in JBIG2Stream with malformed documents
	
	build system:
	 * Make sure we ship two needed cmake files
	 * Do not distribute glib/poppler-features.h and poppler/poppler-config.h
	 * Improve compilation with Sun Studio
	 * Fix linking of the cpp frontend when using autotools
	
	glib:
	 * Fix links/annots area for some documents (Bug #28588)
	 * Fix poppler_page_find_tex() when called more than once (Bug #27927)
	
	utils:
	 * Add -cropbox to pdftoppm manual

Release 0.14.0

	core:
	 * Fix crash when parsing pdf with broken JBIG2Stream (Bug #28170)
	 * Do not follow loops blindly when parsing XRef (Bug #28172)
	 * Allow quality & progressive mode to be utilised in JpegWriter
	 * Fix potential assert in Lexer code (KDE bug #240208)
	 * Fix opening of files whose /P is stored as unsigned integer
	 * Do not exit() when trying to allocate memory for the XRef fails
	
	cpp:
	 * Minor bugfixes
	 * Documentation improvements
	
	build system:
	 * Fix build in mingw32 when using autotools
	 * Preserve compiler flags when using cmake

Release 0.13.4 (0.14 RC 1)

	core:
	 * Include standard float.h instead of unportable values.h
	 * Fix first color stop offset of linear gradients. Bug #27837
	 * Fix compilation if JPEG nor PNG is used
	 * Use fabs for doubles instead of abs
	 * Use strtok_r instead strtok
	 * Adjust bbox for line annots when y1 = y2
	 * Some fixes and regressions in the cairo output device
	 * Better check of overlapping of table cells when selecting text
	
	cpp:
	 * Make the pkg-config files really work
	 * Fix in/out buffer sizes in some functions

Release 0.13.3 (0.14 Beta 2)

	core:
	 * Fix roll optimization in the PS function interpreter
	 * Correctly parse numbers with '+' sign. Gnome bug #614549
	 * Add support for cached files
	 * Add support for reading a cached file from stdin
	 * Add HTTP support using libcurl, disabled by default
	 * Add some const correctnes to GooString
	 * Rework DCTStream error handling. Bug #26280
	 * Use current fill_opacity when drawing soft masked images in Cairo backend. Gnome bug #614915
	 * Use the topleft of the Rect of text annots to draw
	 * Fix saving update docs that have a compressed xref table. Bug #27450
	 * Parse varius part of the document catalog on demand
	 * Implement colorizing image masks with pattern colorspace in Cairo backend
	 * Fix a crash when rendering 0x0 images in Cairo backend
	 * Check pattern status after setting matrix when rendering images
	 * Improve text selection/extraction order. Bug #3188
	 * Fix pattern size when bbox is not at 0,0
	 * Improve colorizing text and masks in pattern colorspace. Bug #27482
	 * Silence some Illegal entry in bfrange block in ToUnicode CMap. Bug #27728

	utils:
	 * Add the -o[dd] and -e[ven] options to pdftoppm
	 * Allow read from stdin using the new cached files feature
	 * Fix crash in pdftohtml when output filename was shorter than 5 characters

	glib:
	 * Use existing cairo api when rendering to a pixbuf
	 * Compile with -DGSEAL_ENABLE. Bug #27579

Release 0.13.2 (0.14 Beta 1)

	core:
	 * Improve Movie support
	 * Fix experimental Arthur backend to compile when if Splash backend is disable
	 * Fix usage of some streams in the Cairo backend
	 * Small improvements in the experimental Arthur backend
	 * Minor annotation improvements
	 * Rework LinkRendition to follow the spec
	 * Add support for Set-OCG-State actions
	 * Correctly initialize the grayscale softmask color in the Splash backend
	 * Correctly initialize actualText in TextOutputDev when initialization fails
	 * Various MSVC fixes

	glib:
	 * Add support for Movie objects
	 * Add support for Screen annotations
	 * Add support for rendition actions
	 * Add support for OCG State actions
	 * Improvements to the demo
	
	qt4:
	 * Always compile the experimental Arthur backend
	 * Minor speed improvement in QPainter usage
	 * Add a search overload that takes doubles instead of QRectF
	
	cpp:
	 * Fix iconv usage
	 * use gmtime() when gmtime_r() is not available
	 * Fix building in autotools in windows
	 * {from,to}_utf_8() -> {from,to}_utf8()
	
	build system:
	 * Multiple CMake build system fixes
	 * Fix of some DIST targets in autotools
	 * Make finding of Qt3 in autotools use pkg-config

Release 0.13.1 (0.14 Alpha 2)

	core:
	 * New C++ frontend to interface with Poppler using only STL
	 * Use the right matrix for the mask in drawMaskedImage in Cairo output device. Bug #16906
	 * Fix downscaling images when document is rotated in Cairo output device. Bug #26264
	 * GooVector rewrite, old version had "unknown" origins/license
	 * Fix use after free in a error condition
	 * Improve handling of broken commands. Bug #24575
	 * Fix potential use after free in Cairo output device.
	 * Fix regression in painting. Bug #26243
	 * Improve handling of FontConfig. Bug #26544
	 * Only assume the OC is not visible if it exists and is set to no. Bug #26532
	 * Fix a potential crash in Splash font handling on out of memory conditions
	 * Implement writeImgFile for splashModeXBGR8
	 * Several speed increases (around 40% in some documents) in the Splash output device
	 * Improve printing on the Cairo output device
	 * Do not use '\' character in PostScript names
	 * Omit writing of embedded fonts into temporary files in the Cairo output device. Bug #26694
	 * Improve filtering of some images in the Cairo output device. Bugs #25268, #9860

	utils:
	 * pdftoppm: Only swap w with h if rotation is 90 or 270
	
	build system:
	 * Add POPPLER_WITH_GDK in cmake build system. Bug #26247
	 * Fix typo: "MULTITHREAD" -> "MULTITHREADED in cmake build system
	 * Wrap #include <jpeglib.h> in extern "C" to fix build. Bug #26351
	 * Add the Win32-specific ENABLE_RELOCATABLE option to cmake build system
	 * Reflect that poppler-glib needs cairo now in cmake build system
	 * Use pkgconfig to detect libpng on autotools build system
	 * Detect the need for nanosleep in solaris in cmake build system. Bug #26650

Release 0.13.0 (0.14 Alpha)

	core:
	 * Improvements to Annotation rendering. Bug #23108
	 * Do not give an error when opening files without pages. Bug #24720
	 * Try to read streams without Length
	 * Do not crop the transformation matrix at an arbitrary value. Bug #25763
	 * Make poppler (optionally) relocatable on Windows
	 * Use a small object cache in GfxResources to cache GState objects
	 * Reduce the number of redundant pattern creations in the Cairo output device
	 * Use colToDbl() to avoid rounding error in the Cairo output device
	 * Fix problems with mask handling in the Cairo output device. Bug #8474
	 * Use a better scale down implementation in the Cairo output device
	 * Various optimizations to the Splash output device
	 * Add the possibility to use floats instead of doubles in the Splash output device. Bug #25578
	 * Write out fixed-content portion of Type 1 fonts in the PS output device
	 
	build system:
	 * Improvements to the CMake build system
	 * Enable AM_SILENT_RULES by default in autotools
	 * Require glib 2.18
	 * Require GTK+ 2.14
	 * Make fontconfig optional with mingw compiler
	 * Remove makefile.vc
	
	glib:
	 * Add support for file attachment annotations
	 * Improvements to the demo
	 * Use TextOutputDev to get TextPage when we haven't rendered the page
	 * Remove support for the Splash output device
	
	utils:
	 * pdftoppm can now write to jpeg
	 * pdftoppm embeds the correct resolution in png and jpeg files
	 
	qt4:
	 * Minor improvements to the tests

Release 0.12.3

	core:
	 * Be more lenient with /Decode key on images. Bug #17439
	 * Correctly initialize fileName in LinkGoToR. Bug #25221
	 * Improve the reconstruction of the XRef for broken files
	 * [Cairo backend] Do not crash on malformed files. Bug #24575
	 * Accept Fontname if FontName is not present. KDE bug #217013
	 * Make PSOutputDev code a bit more resilient
	 * Fix writing of null objects. Bug #25465
	 * [Cairo backend] Fix crash in some documents. GNOME bug #603934
	 * Correctly initialize profileCommands in Gfx constructor
	
	build system:
	 * Check for openjpeg in the C++ part as it uses bool in the header. Bug #25103
	
Release 0.12.2

	core:
	 * Fix a memory leak when converting to PostScript
	 * Fix crash when reading a font fails. Bug #24525
	 * Make the ICC cache per page instead of global. Bug #24686
	 * Do not accept negative interval lengths in the page labels tree. Bug #24721
	 * Do not crash on files Aspect of Movie objects are reals instead of integers. Bug #24733
	 * Do not render patterns when using CairoImageOutputDev
	 * Allow Transitions dictionary to be a Ref
	 * Do not crash if jpeg_start_decompress fails. KDE bug #214317
	
	glib:
	 * Fix CVE-2009-3607
	
	qt4:
	 * Use '.' in the annotations XML instead of the decimal separator of the current locale

Release 0.12.1

	core:
	 * Fix compilation on some compilers
	 * Only initialize the font list once in Windows32/MSVC
	 * Do not crash on fonts without CharCodeToUnicode. Bug #24036
	 * Fix regression due to not setting LC_NUMERIC anymore
	 * Improve realibility for Streams with broken Length. Bug #6841
	 * Write the Info into the trailer dict if there is one. Bug #24091
	 * Do not crash when saving files that come from a stream without name. Bug #24090
	 * Improve relability of the save function
	 * Fix the Length value if it was wrong when saving
	 * Fix includes for those using internal headers
	 * Rework how hinting is used in the splash backend. It is disabled by default now
	 * fix constructor of DCTStream when using internal decoder
	 * Security fixes based xpdf 3.02pl4
	
	qt4:
	 * Add the possibility of setting wheter to use or not font hinting
	 * Add a way for converters to return more exact errors they had when converting
	 * Check the document is not locked when converting to PS
	
	build system:
	 * Compile on Cygwin
	 * Use _WIN32 instead of WIN32. Bug #24259
	 * Add the possibility to pass LIB_SUFFIX when using CMake

Release 0.12.0

	core:
	 * Fix printf format security warnings
	 * Improve rendering of radial shadings. Bug #20238
	 * Better fallback when there's a font type mismatch. Bug #17252
	 * Do not crash on attachments without data stream. Bug #10386
	 * Fix infinite loop in JBIG2Decoder. Bug #23025
	
	build system:
	 * Minimizes pkg-config dependencies for Qt frontends
	 * Add automake 1.11 support
	 * Use the newest automake found and not the oldest
	 * Support AM_SILENT_RULES when using automake 1.11
	
	utils:
	 * Add common options to pdftoabw

Release 0.11.3 (0.12 RC 1)

	core:
	 * Optimization in the Cairo renderer for some fonts
	 * Do not apply masks when fill color space mode is csPattern in the Cairo renderer. Bug #22216
	 * Check for overflow when parsing integers. Bug #23078
	 * Do not save the font file twice for FreeType fonts in the Cairo renderer. Bug #20491
	 * Use current fill_opacity when drawing images in the Cairo renderer
	 * Fix alpha rendering in some files in the Splash renderer. Bug #22143, #22152
	 * Implement tiling patterns in the Cairo renderer
	 * When converting a cm matrix to PS write 4 significant digits for numbers < 1 not 4 decimals. Bug #23332
	 * Fix changing of locale, now poppler no longer changes LC_NUMERIC to "C"
	 * Return PDF version as two integers instead of as a double
	
	Qt4:
	 * Addition of the Color Management API
	 * Small fix to documentation
	 * Fix backwards text search
	
	utils:
	 * Add the -png flag to pdftoppm to output to PNG

Release 0.11.2 (0.12 Beta 2)

	core:
	 * Make DecryptStream return sane values for getPos(). Bug #19706
	 * Fix bug when printing pdf with multiple page sizes in duplex mode
	 * Initilize AnnotColot properly when the Array is not correct
	 * Fix crash on some files with forms. Bug #22485
	 * Fix crash in files with invalid embedded files. Bug #22551
	 * Improve FileSpec attribute parsing
	 * Cairo output device improvements. Bugs #10942, #18017, #14160
	 * Implement blend modes in cairo backend
	 * Handle fontType1COT fonts in CairoFontEngine
	 * Fix generation of PS for some files. Bug #18908
	 * Don't use byte_lookup table when color space doesn't support getLine methods. Bug #11027
	 * Fix rendering of PDF files with malformed patterns. Bug #22835
	 * Add the possibility of disabling font substitution in pdftops. Bug #23030
	 * Fix some radio buttons not being detected as such
	
	glib:
	 * Improvements to the demo
	
	Qt4:
	 * Improvements to the demo
	
	build system:
	 * Use gtkbuilder rather than libglade for some tests
	
	utils:
	 * Fix bug with noCrop parameter in pdftops 

Release 0.11.1 (0.12 Beta 1)

	core:
	 * Support colorizing text in pattern colorspace. Bug #19670 and #19994
	 * Add the possibility of forcing no hinting of fonts in the Splash backend
	 * Support multiple page sizes when converting to PS. Bug #19777
	 * Also tokens with leading 00 when parsing the char to unicode map. Bug #22025
	 * Improvements of rendering speed in documents using PS transformations a lot. Bug #21562
	 * More work on Annotations support
	 * Use Interpolate flag to decide whether applying image interpolation during rendering. Bug #9860
	 * Handle Streams in CMap definitions. Bug #22334
	 * Fix some bugs in JBIG2Stream handling
	 * Fix dashed line in page 1 of bug 20011
	 * Fix exit(1) when rendering a file
	 * Fix pdftops crash on file from KDE bug #174899
	 * Fix PS generation in some files. Bug #20420
	 * Do not create the GfxColorTransform if the lcms could not be created. Bug #20108
	 * Check Mask entries are int before using them, if they are real cast to int and try to use them. Bug #21841
	 * Use the correct value when creating the V field for form combo boxes
	 * Give an error when using level1sep in pdftops without having CMYK support. Bug #22026
	 * Don't include lcms.h in GfxState.h
	 * Fix splashColorModeNComps to correctly include all values for each SplashColorMode
	 * Add splashClearColor that assigns white to the given colorptr
	 * Kill support for specifying extension in openTmpFile. Bug #21713
	 * Fix "Conditional jump or move depends on uninitialised value". Bug #20011
	
	glib:
	 * Add poppler_annot_markup_has_popup()
	 * Hyphenate UTF-8 and UTF-16BE. Bug #21953
	 * Use g_strerror instead of strerror. Bug #22095
	 * Fix a crash when a destination points to an invalid page
	 * Improvements to the demo
	
	Qt4:
	 * Add LinkDestination::destinationName()
	 * Do not try to resolve named destinations for GoTo links pointing to external documents
	 * Add Page::thumbnail()
	 * Improvements to the demo
	 * Improvements to the documentation
	
	build system:
	 * Build fix for MSVC
	 * Better lcms cmake check comming from kdelibs
	 * Use pkgconfig for autotools lcms check
	 * Remove unneeded files from repo. Bug #22094

Release 0.11.0 (0.12 Alpha)

	core:
	 * Add initial support for color management
	 * Remove case-insensitive matching of filenames in PDFDoc constructor
	 * Fix extraction of some ActualText content
	 * More work on Annotations support
	 * Improve font rendering in Cairo output device
	 * Fix bug in cairo backend with nested masks
	 * Fix cairo luminosity smask rendering
	 * Add optionally text support to Cairo output device
	 * Add the possibility of setting the datadir on runtime
	 * Return an error code instead of a boolean when saving
	 * Make the font scanner more versatile
	 * Small opimization in documents that use PostScriptFunction transforms
	 * Minor optimization to Stream handling
	 * Fix some compile warnings
	
	glib:
	 * Optional content support
	 * More work on Annotations support
	 * Improvements to the demo
	 * Documentation improvements
	 * Fix build when compiling with GTK_DISABLE_SINGLE_INCLUDES
	
	Qt4:
	 * Support URI actions for Table Of Contents items
	 * Documentation improvements
	 * Improvements to the demo
	 * Add a FontIterator for iterating through the fonts of the document
	
	utils:
	 * Allow the use of cropbox in pdftoppm
	 * Make pdftohtml output png images when the image stream is not a jpeg
	 * Make pdftotext accept cropping options like pdftoppm
	 * Support rendering non-square pixels in pdftoppm
	
	build system:
	 * Require Cairo 1.8.4 for the Cairo output device
	 * Require CMake 2.6 when using the CMake build system
	 * Optionally require libpng for pdftohtml
	 * Optionally require libcms for color management

Release 0.10.6

	core:
	 * Fix problems that happen when parsing broken JBIG2 files.
	   CVE-2009-0799, CVE-2009-0800, CVE-2009-1179, CVE-2009-1180
	   CVE-2009-1181, CVE-2009-1182, CVE-2009-1183, CVE-2009-1187, CVE-2009-1188
	 * Fix parsing of incorrect border arrays. Bug #19761
	 * Fix clip test for fonts. Bug #20950
	 * Fix getGlyphAdvance to behave correctly on font size changes. Bug #20769
	 * Misc build fixes
	 
	build system:
	 * Fix the Qt4 version we need

Release 0.10.5

	core:
	 * Read the UF entry if present and prefer it over F in Filespec dictionary
	 * Fix typo that was making CairoOutputDev crash on some files. Bug #17337
	 * Make JBIG2Stream more robust to corrupt input data
	 * Do not blindly follow loops parsing OutlineItem. Bug #18364
	 * Set up the error manager before calling jpeg_create_decompress. Bug #20484
	 * Check there is an optional content config before using it. Bug #20587
	 * Fix rendering of some PDF with OpenType fonts. Bug #20605

	build system:
	 * Yet more support for build on windows
	 * Use AC_CHECK_HEADER to find headers. Bug #20538
	 * Check for pkgconfig before using it
	 * General autotools improvements

Release 0.10.4

	core:
	 * Fix a memory leak when asking for a document-level JS
	 * Do not crash in some PDF we do not parse correctly. Bug #19702
	 * Fix crash on unexepcted form Opt value. Bug #19790
	
	utils:
	 * Fix pdfimages to extract i color components per pixel jpeg images. Bug #19789

Release 0.10.3

	core:
	 * Fix a crash on documents with malformed outline. Bug #19024
	 * Fix leak on AnnotScreen destructor. Bug #19095
	 * Fix wrong PS generation when a large image is in Patterns. Bug #18908
	 * Remove BaseFile.h it was never used. Bug #19298
	 * Improve document saving
	 * Fix PS generation of PDF with malformed font Length2 definition
	 * Fix a leak while parsing annotations
	 * Fix rendering of some checkboxes
	
	Qt4:
	 * Fix positioning of Form rects on PDF with cropbox
	 * Fix positioning of Annotation rects on PDF with cropbox. Bug #18558.
	 * Small documentation improvements
	 * Make Document::fonts() work when called more than once. Bug #19405
	
	build system:
	 * CMake: look harder for openjpeg
	 * CMake: update the poppler core headers installation
	 * Autotools: do not install Function.cc as it's not a header
	
	Qt:
	 * Fix deserialization of links right coordinate

Release 0.10.2

	core:
	 * Fix a crash when selecting text in word mode
	 * Fix a crash in some malformed documents (second argument of opMarkPoint is not a dictionary)
	 * Ensure cairo font matrix is invertable. Fixes bugs #18254 and #18429
	 * Fix a memory leak (Bug #18924)
	
	Qt4:
	 * Fix deserization of links right coordinate
	
	misc:
	 * Fix build on Solaris 10 + Sun Studio 12
	 * Compile with -pedantic

Release 0.10.1

	core:
	 * Improvements in Optional Content support
	 * Small fix in Form support
	 * Fix memory leak in case of error
	 * Fix potential crash on text search
	 * Try render documents with invalid indexed color space parameters. Bug #18374
	 * Fix crash on text extraction when poppler-data is not installed. Bug #18023
	
	Qt:
	 * Fix two memory leaks
	
	Qt4:
	 * Small documentation improvement
	 * Fix memory leak in the demo code

Release 0.10.0

	core:
	 * Fix crashes on PDF using Stitching or Axial Shading painting
	 * Fix rendering of PDF with Type1 fonts that have more than
	   one encoding definition per line
	 * Do not try to save documents that have Encryption as we
	   do not support that and the user ended with a broken file
	 * Fix crash on files with OptionalContentGroup but no Name
	
	Qt4:
	 * Fix the area of the links to be correctly reported on rotated documents
	
	misc:
	 * Mingw+Msys should work

Release 0.9.3 (0.10 RC 2)

	core:
	 * Fix rendering regression on some embedded fonts
	 * Fix rendering regression of some special fonts
	 * Fix crash on documents with bogus jpeg data
	
	Qt4:
	 * The printing flag defaults to true on PSConverter
	 * Documentation improvement
	
	utils:
	 * Fix regression that made HmtlOutputDev ignore jpeg images
	
	misc:
	 * Improve compilation on mingw

Release 0.9.2 (0.10 RC 1)

	core:
	 * Fix conversion to PS some files (bug #17645)
	 * Small Form fixes
	 * Small JS fixes
	 * Improve memory usage of the cairo renderer
	
	utils:
	 * Fix mismatched free/delete in pdftohtml
	 * Fix memory leak in pdftohtml
	 * Fix crash in pdftohtml
	
	glib:
	 * Fix a crash in forms demo
	
	misc:
	 * Compile with -pedantic

Release 0.9.1 (0.10 Beta 2)

	Core:
		* Fix crash on some AESv2 encrypted files (bugs #13972, #16092, #17523)
		* Improve parsing of broken files (bug #17568)
		
	glib frontend:
		* Minor improvements to the demo application
	
	utils:
		* pdftohtml: Generate the outline file in the same place
		             of the other generated files (bug #17504)

Release 0.9.0 (0.10 Beta 1)

	Core:
	 * Initial JavaScript support
	 * Annotation improvements
	 * Improvements in the Arthur based renderer
	 * Improvements in the Cairo based renderer
	 * Added a JPEG2000 decoder based on OpenJPEG
	 * Small fixes in ActualText implementation
	 * Fix jpeg rendering when not using the libjpeg based decoder
	 * Movie fixes
	 * Do not get out of memory on documents that specify huge fonts
	 * Emulate Adobe Reader behaviour on documents with duplicate keys in Dictionaries
	 * Forms improvements	
	
	Qt4 frontend:
	 * Annotation improvements
	 * Forms improvements
	 * Add the possibility of extracting embedded fonts
	 * Initial Movie support
	 * Documentation improvements
	 * Small improvements in the PS exporter
	
	glib frontend:
	 * Annotation improvements
	 * Attachment fixes
	
	utils:
	 * updated man pages
	 * Added -listenc to pdfinfo and pdftotext

Release 0.8.7

	Core:
	 * Fix regression in Form rendering
	 * Fix memory leak in the cairo backend

Release 0.8.6

	Core:
	 * Call error() when font loading fails
	 * Be less strict parsing TTF tables (bug #16940)
	 * Fix crash due to uninitialized variable

	Qt 4 frontend:
	 * Make the paper color setting working as it should
	 * Make sure to use the correct page width/height for form widgets coordinates

Release 0.8.5

	Core:
	 * Fix crash on PDF that define a page thumbnail but it's not a Stream
	 * Fix crash when Annots object is not of the desired type
	 * Fix crash when obtaining fonts in PDF where XObjects link themselves in loops
	 * Fix crash on documents with an IRT object
	 * Saving should work much better now
	 * Plug some memory leaks in Annotation handling
	
	Utils:
	 * pdftohtml: Don't crash on documents that specify an invalid named dest for a link
	 * pdftohtml: Make html output to keep all the spaces with &nbsp;
	 * pdftohtml: Improve a bit text layout
	 * pdftohtml: Make xml output valid xml

Release 0.8.4

	Core:
	 * Fix leak in ABWOutputDev.cc
	 * Fix uninitialized variable that broke file saving in some cases
	 * Use a single global FT_Library in CairoOutputDev.
	   Fixes some crashes in CairoOutputDev.
	
	Qt 4 frontend:
	 * Fix saving over existing files
	
	build system:
	 * Make sure Qt4 moc is used to generate moc files in Qt4 frontend

Release 0.8.3

	Core:
	 * Fix crash when reading some PDF with annotations
	 * Fix crash on PDF that reference Optional Content elements that don't exist
	 * Fix leaks on error conditions
	 * Do not limit CharCodeToUnicodeString to 8 characters
	 * Support for surrogates outside the BMP plane
	
	Qt 3 frontend:
	 * Fix crash when reading PDF with password
	 * Fix leak when calling scanForFonts()
	
	Qt 4 frontend:
	 * Fix the text() method
	
	Splash renderer:
	 * Fix compilation with --enable-fixedpoint

Release 0.8.2

	core:
	 * Fix call broken by a fix introduced in 0.8.1

Release 0.8.1

	core:
	 * Do not call FT_Done_Face on a live cairo_font_face_t as it might cause crashes
	 * Do not take into account Colorspace resource subdictionary for image XObjects
	 * Downsample 16 bit per component images to 8 bit per component so they render
	
	build system:
	 * Link to pthread when the system needs it

	windows:
	 * Fix comparing against NULL instead against INVALID_HANDLE_VALUE when calling FindFirstFile

Release 0.8.0

	* Fix caching of members in the glib frontend causing issues with rendering
	* Change glib public api to have a correct naming
	* Some better error handling on corner cases
	* Check the document stream is seekable when opening it
	* Build fixes with autotools and with cmake
	* Fix infinite recursion on some malformed documents when consulting the fonts
	* Fix possible crash when asking for Movie contents

Release 0.7.3 (0.8 RC 2)

	* Fix regression in Splash renderer
	* Fix off-by-one write in Splash
	* Plug some minor leaks in Optional Content code
	* Improve error handling when creating a document in the glib frontend

Release 0.7.2 (0.8 RC 1)

	Major Changes:
	 * Improve font matching not forcing default values onto Fontconfig
	 * Add preliminary annotations support in the glib frontend
	 * Initial Movie support in the core
	 * Make GDK dependency optional in glib bindings

	Minor Changes:
	 * Make the core able to read mime types of embedded files
	 * Qt4 API for accessing mime types of embedded files
	 * Handle correctly check state of optional content groups
	   regarding parents state
	 * Avoid setting singular CTM matrices on the Cairo backend
	 * Improved Qt4 API to get character position
	 * Qt4 api documentation improvements
	 * Qt4 minor stability fixes
	 * Proper lib64 Qt detection
	 * Fix build when compiling without cairo support

Release 0.7.1 (0.8 Beta 2)

	Major Changes:
	 * Really distribute CMake files as optional build tool
	 * Initial Optional Content support in core and in the Qt4 frontend

	Minor Changes:
	 * Allow grouped checkboxes to be selected individually
	 * Qt4 demo program improvements
	 * Keep cairo and cairo_shape consistent
	 * Safety checks on Splash renderer so that it does not draw outside the allocated bitmap
	 * Do not try to display bitmaps of invalid size
	 * Fix building with exceptions
	 * Improvements for building with MSVC and CMake

Release 0.7.0 (0.8 Beta 1)

	* Saving support
	* Partial annotation support
	* Forms improvements
	* Add support for ActualText entries
	* Display characters outside of unicode BMP with TT font
	* CJK rendering fixes
	* Implement Adobe Glyph Naming convention for fonts
	* CMake as optional build tool
	* Better font scaling for non embedded fonts
	* Preserve PDF page labels when we output as postscript

Release 0.6.4

	Qt4 frontend:
	 * Fix crash on links that point to a non existent page
	 * Make Document::renderHints return the correct render hints
	 * Fix infinite loop when parsing LineAnnotation

	core:
	 * Fix crash in the Splash renderer when T3 fonts are badly defined
	 * Draw underlined Links correctly

	utils:
	 * Fix two use after free bugs in HtmlOutputDev.cc

	build system:
	 * Fix build on mingw32

	tests:
	 * Distribute the glade file of pdf-inspector

Release 0.6.3

	core:
	 * Fix crash in extra debug code

	glib frontend:
	 * Make sure passwords are passed correctly to poppler core

	Qt frontend:
	 * Fix crash on documents that specify an empty date
	
	build system:
	 * Disable gtk tests if the user disabled glib frontend

Release 0.6.2

	poppler core:
	 * Fix CVE-2007-4352, CVE-2007-5392 and CVE-2007-5393
	 * Fix a crash on documents with wrong CCITTFaxStream
	 * Fix a crash in the Cairo renderer with invalid embedded fonts
	 * Fix a crash with invalid TrueType fonts
	 * Check if font is inside the clip area before rendering
	   it to a temporary bitmap in the Splash renderer. Fixes crashes on
	   incorrect documents
	 * Do not use exit(1) on DCTStream errors
	 * Detect form fields at any depth level
	 * Do not generate appearance stream for radio buttons that are not active
	 * mingw fixes
	
	build system:
	 * Require fontconfig >= 2.0
	 * builddir != srcdir fixes
	
	Qt4 frontend:
	 * Improved documentation
	
	misc:
	 * Fix FSF address

Release 0.6.1

	poppler core:
	 * Fix printing with different x and y scale
	 * Fix crash when Form Fields array contains references to non existent objects
	 * Fix crash in CairoOutputDev::drawMaskedImage()
	 * Fix embedded file description not working on some cases
	
	Qt4 frontend:
	 * Fix printing issue
	 * Avoid double free
	 * Fix memory leak when dealing with embedded files
	
	glib frontend:
	 * Fix build with --disable-cairo-output
	 * Do not return unknown field type for signature form fields
	
	build system:
	 * Support automake-1.10
	 * More compatible sh code in qt.m4
	
	utils:
	 * Fix build on Sun Studio compiler

Release 0.6

	- CairoOutputDev fixes
	- Allow pdftoppm to read/write from stdin/stdout
	- API work on Qt4 frontend
	- Fix pdfimages produces inverted image for black & white image
	- Fix error on the NFKC text matching routine
	- Add support for word and line selections
	- Do not enforce %%EOF at the end of file
	- Pad zeroes instead of aborting when rendering 1-bit images
	  and the stream is too short
	- Update glib bindings documentation

Release 0.5.91 (0.6 Release Candidate 2)

	- Various memory leaks fixed
	- Compile with --enable-fixedpoint. Bug #11110
	- Header cleanup
	- Remove dependency on debugxml. Bug #11187
	- Allow access to document metadata in glib and qt4 frontends
	- Several glib API frontend improvements
	- Fix crash on accessing embedded files
	- Build on Sun Force compiler
	- Render '*' instead of the actual content in password form fields
	- Fix pdftohtml complex output. Bug #9746 and #11610
	- Windows build fixes
	- Improve Japanese font support. Bug #11413
	- Do not exit the program on files that confuse libjpeg
	- Update required cairo version to 1.4
	- Fix CVE-2007-3387

Release 0.5.9 (0.6 Release Candidate)

	- Merge xpdf 3.02 changes
	- Qt4 frontend is not marked anymore as unstable
	- Support for Sound objects
	- Support for Opening/Closing page actions 
	- Support for page duration
	- Improve PS Tokenizer performance thanks to Scott Turner
	- Various speed ups by Krzysztof Kowalczyk
	- Beginning of Interactive Form support by Julien Rebetez
	- xpdfrc is no longer used for anything
	- Add AbiWord output device and pdftoabw program by Jauco Noordzij
	- Fix security issue MOAB-06-01-2007
	- Lots of bugs fixed

Release 0.5.4

	- Automatically read in CJK encoding files if they're
          installed (#2984, #7105, #7093).  This works with the new
          poppler-data package.
	- Speed ups by Krzysztof Kowalczyk (#8112)
	- Patch from Dom Lachowicz to let the utils take input on stdin.
	- Bugs fixed (#8182, #4649, #7906, #8048, #7113, #4515, #3948,
          #7924, #7780, #7646, #6948, #7788, #7661, #7005)

Release 0.5.3

	- Add poppler as a private requires of poppler-glib.
	- Allow CairoFont creation to fail more gracefully (#4030).
	- Back out the rest of krh's type3 font work.
	- Revert splashModeRGB8 changes.
 	- Add missing poppler-annotation-helper.h.

Release 0.5.2

	- Much improved Qt bindings (Albert Astals Cid).
	- Cairo backend now supports masked images (Jeff Muizelaar, #6174).
	- Patches from Kouhei Sutou to make glib bindings more
	  language binding friendly (#6907, #6897, #6899, #6905).
	- Search now works with ligatures (Ed Catmull, #2929).
	- The glib bindings now has an entry point to render to a cairo_t.
	- GCC 4.1 and MSVC compilation fixes.
	- Memory leaks plugged: #6908, #6947, #6765, #6764, #6187
	- Misc bug fixes: #6984, #6896, #6913, #6926, #4481, #5951,
	  #6551, #6500, #6492, #6454, #6079, #6167.

Release 0.5.1

	- Support for embedded files.
	- Handle 0-width lines correctly.
	- Avoid external file use when opening fonts.
	- Only use vector fonts returned from fontconfig (#5758).
	- Fix scaled 1x1 pixmaps use for drawing lines (#3387).
	- drawSoftMaskedImage support in cairo backend.
	- Misc bug fixes: #5922, #5946, #5749, #5952, #4030, #5420.

Release 0.5.0

	- Font matching code for non embedded fonts now use fontconfig
          instead of hard coded list of fonts.
	- Merge in Xpdf 3.01 changes.
	- Add command line tools from Xpdf.
	- Make install of Xpdf header files ./configure'able.

Release 0.4.0

	- Real text selection.
	- API breakage in glib wrapper: dropping dest_x and dest_y
	  arguments from poppler_page_render_to_pixbuf().

Release 0.3.3

	- New glib API to get document font information (Marco).
	- More document properties available as glib properties (Emil
	  Soleyman-Zomalan, #3359)
	- Optimize color conversion for images.
	- Support for constant opacity.
	- Fix problems with pkg-config files.
	- Bugs fixes: #3491, #2911, #3362, #3340, #3265, #3239, #3396.

Release 0.3.2

	- New API to get poppler version and backend type. 
	- Various font fixes from Albert Astals Cid.
	- Update to cairo 0.5.0 API, including better font support.
	- Meta data for the glib binding.

Release 0.3.1

	- Add qt/poppler-private.h to SOURCES
	- Jeff's path to use zlib instead of builtin decompression.
	- Bug fixes: #2934, segfault on invalid links, #3114

Release 0.3.0

	- First cut at qt wrapper, including a getText() method for
	  getting text from a page.
	- More glib functionality: meta data, set page orientation,
	  print to PS
	- Performance fixes for glib cairo
	- Bug fixes

Release 0.2.0  (Tue Apr  5 12:32:10 EDT 2005)

	- Add glib wrapper for poppler, which will use cairo rendering
	  if available
	- Support for page labels
	- configure and build fixes.

Release 0.1.2  (Wed Mar  9 10:45:58 EST 2005)

	- cairo optimizations and fixes from Jeff Muizelaar
	- Bump cairo requirement to 0.4
	- Make cairo and gtk checks fail gracefully

Release 0.1.1

	- Fix issues with installed header files including config.h
	- Fix a couple of typos in pkg-config files
	- Install splash and cairo header files when necessary

Release 0.1 - no date yet

	- First release
	- More NEWS here
