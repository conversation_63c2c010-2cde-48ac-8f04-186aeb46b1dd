import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Database file path
const dbPath = path.join(process.cwd(), 'data', 'study_app.db');

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Initialize database
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Database migration function
export function migrateDatabase() {
  try {
    // Check if new columns exist, if not add them
    const tableInfo = db.prepare("PRAGMA table_info(images)").all() as any[];
    const hasPageType = tableInfo.some(col => col.name === 'page_type');
    const hasUploadOrder = tableInfo.some(col => col.name === 'upload_order');

    if (!hasPageType) {
      db.exec("ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'");
      console.log('Added page_type column to images table');
    }

    if (!hasUploadOrder) {
      db.exec("ALTER TABLE images ADD COLUMN upload_order INTEGER");
      // Set upload order for existing images based on uploaded_at
      db.exec(`
        UPDATE images
        SET upload_order = (
          SELECT COUNT(*) + 1
          FROM images i2
          WHERE i2.class_id = images.class_id
          AND i2.subject_id = images.subject_id
          AND i2.uploaded_at < images.uploaded_at
        )
      `);
      console.log('Added upload_order column to images table');
    }

    // Check if processed column exists in ocr_text table
    const ocrTableInfo = db.prepare("PRAGMA table_info(ocr_text)").all() as any[];
    const hasProcessed = ocrTableInfo.some(col => col.name === 'processed');

    if (!hasProcessed) {
      db.exec("ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1");
      console.log('Added processed column to ocr_text table');
    }

    // Check if books table exists
    const tablesResult = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='books'").get();
    if (!tablesResult) {
      console.log('Creating books table...');
      db.exec(`
        CREATE TABLE books (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          class_id INTEGER NOT NULL,
          subject_id INTEGER NOT NULL,
          description TEXT,
          cover_image_path TEXT,
          total_pages INTEGER DEFAULT 0,
          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
        )
      `);
      console.log('Books table created successfully');
    }

    // Check if images table has book_id column
    const hasBookId = tableInfo.some(col => col.name === 'book_id');
    if (!hasBookId) {
      console.log('Adding book_id column to images table...');
      db.exec("ALTER TABLE images ADD COLUMN book_id INTEGER");
      console.log('Added book_id column to images table');
    }

  } catch (error) {
    console.error('Migration error:', error);
  }
}

// Database schema initialization
export function initializeDatabase() {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),
      password_hash TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Classes table
  db.exec(`
    CREATE TABLE IF NOT EXISTS classes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Subjects table
  db.exec(`
    CREATE TABLE IF NOT EXISTS subjects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      class_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      UNIQUE(class_id, name)
    )
  `);

  // Books table - containers for textbook content
  db.exec(`
    CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      description TEXT,
      cover_image_path TEXT,
      total_pages INTEGER DEFAULT 0,
      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);

  // Images table
  db.exec(`
    CREATE TABLE IF NOT EXISTS images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      file_path TEXT NOT NULL,
      original_name TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),
      upload_order INTEGER,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);

  // OCR text table
  db.exec(`
    CREATE TABLE IF NOT EXISTS ocr_text (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      image_id INTEGER NOT NULL,
      content TEXT NOT NULL,
      processed BOOLEAN DEFAULT FALSE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE
    )
  `);

  // Questions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS questions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      chapter TEXT,
      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),
      content TEXT NOT NULL,
      options TEXT, -- JSON string for MCQ options
      correct_answer TEXT,
      marks INTEGER DEFAULT 1,
      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);

  // Tests table
  db.exec(`
    CREATE TABLE IF NOT EXISTS tests (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      chapters TEXT, -- JSON string of selected chapters
      time_min INTEGER NOT NULL, -- Time limit in minutes
      total_marks INTEGER DEFAULT 0,
      instructions TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      created_by INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
      FOREIGN KEY (created_by) REFERENCES users(id)
    )
  `);

  // Test questions junction table
  db.exec(`
    CREATE TABLE IF NOT EXISTS test_questions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      test_id INTEGER NOT NULL,
      question_id INTEGER NOT NULL,
      question_order INTEGER NOT NULL,
      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
      UNIQUE(test_id, question_id),
      UNIQUE(test_id, question_order)
    )
  `);

  // Test results table
  db.exec(`
    CREATE TABLE IF NOT EXISTS test_results (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      test_id INTEGER NOT NULL,
      user_id INTEGER NOT NULL,
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      submitted_at DATETIME,
      total_score REAL DEFAULT 0,
      max_score REAL DEFAULT 0,
      time_taken INTEGER, -- Time taken in minutes
      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),
      graded_by INTEGER,
      graded_at DATETIME,
      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (graded_by) REFERENCES users(id),
      UNIQUE(test_id, user_id)
    )
  `);

  // Test answers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS test_answers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      result_id INTEGER NOT NULL,
      question_id INTEGER NOT NULL,
      user_answer TEXT,
      is_correct BOOLEAN,
      score REAL DEFAULT 0,
      max_score REAL DEFAULT 0,
      graded_by INTEGER,
      graded_at DATETIME,
      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,
      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
      FOREIGN KEY (graded_by) REFERENCES users(id),
      UNIQUE(result_id, question_id)
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);
    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);
    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);
    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);
  `);

  console.log('Database initialized successfully');

  // Run migrations after initialization
  migrateDatabase();
}

// Create default admin user if none exists
export function createDefaultAdmin() {
  const bcrypt = require('bcryptjs');

  const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');

  if (adminExists.count === 0) {
    const hashedPassword = bcrypt.hashSync('admin123', 10);

    db.prepare(`
      INSERT INTO users (name, email, role, password_hash)
      VALUES (?, ?, ?, ?)
    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);

    console.log('Default admin user created: <EMAIL> / admin123');
  }
}

// Clear OCR data to force re-processing with real OCR
export function clearOCRData() {
  try {
    console.log('Clearing existing OCR data to enable fresh processing...');
    db.exec('DELETE FROM ocr_text');
    db.exec('DELETE FROM questions');
    console.log('OCR data cleared successfully');
  } catch (error) {
    console.error('Error clearing OCR data:', error);
  }
}

// Initialize database on import
initializeDatabase();
createDefaultAdmin();

export default db;
