module.exports = {

"[project]/node_modules/bcryptjs/umd/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// GENERATED FILE. DO NOT EDIT.
(function(global, factory) {
    function preferDefault(exports1) {
        return exports1.default || exports1;
    }
    if (typeof define === "function" && define.amd) {
        ((r)=>r !== undefined && __turbopack_context__.v(r))(function(_crypto) {
            var exports1 = {};
            factory(exports1, _crypto);
            return preferDefault(exports1);
        }(__turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)")));
    } else if ("TURBOPACK compile-time truthy", 1) {
        factory(exports, __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)"));
        if ("TURBOPACK compile-time truthy", 1) module.exports = preferDefault(exports);
    } else {
        "TURBOPACK unreachable";
    }
})(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : this, function(_exports, _crypto) {
    "use strict";
    Object.defineProperty(_exports, "__esModule", {
        value: true
    });
    _exports.compare = compare;
    _exports.compareSync = compareSync;
    _exports.decodeBase64 = decodeBase64;
    _exports.default = void 0;
    _exports.encodeBase64 = encodeBase64;
    _exports.genSalt = genSalt;
    _exports.genSaltSync = genSaltSync;
    _exports.getRounds = getRounds;
    _exports.getSalt = getSalt;
    _exports.hash = hash;
    _exports.hashSync = hashSync;
    _exports.setRandomFallback = setRandomFallback;
    _exports.truncates = truncates;
    _crypto = _interopRequireDefault(_crypto);
    function _interopRequireDefault(e) {
        return e && e.__esModule ? e : {
            default: e
        };
    }
    /*
   Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>
   Copyright (c) 2012 Shane Girish <<EMAIL>>
   Copyright (c) 2025 Daniel Wirtz <<EMAIL>>
  
   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:
   1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.
   2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.
   3. The name of the author may not be used to endorse or promote products
   derived from this software without specific prior written permission.
  
   THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
   IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
   OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
   IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
   INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
   NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
   THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
   (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
   THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
   */ // The Node.js crypto module is used as a fallback for the Web Crypto API. When
    // building for the browser, inclusion of the crypto module should be disabled,
    // which the package hints at in its package.json for bundlers that support it.
    /**
     * The random implementation to use as a fallback.
     * @type {?function(number):!Array.<number>}
     * @inner
     */ var randomFallback = null;
    /**
     * Generates cryptographically secure random bytes.
     * @function
     * @param {number} len Bytes length
     * @returns {!Array.<number>} Random bytes
     * @throws {Error} If no random implementation is available
     * @inner
     */ function randomBytes(len) {
        // Web Crypto API. Globally available in the browser and in Node.js >=23.
        try {
            return crypto.getRandomValues(new Uint8Array(len));
        } catch  {}
        // Node.js crypto module for non-browser environments.
        try {
            return _crypto.default.randomBytes(len);
        } catch  {}
        // Custom fallback specified with `setRandomFallback`.
        if (!randomFallback) {
            throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");
        }
        return randomFallback(len);
    }
    /**
     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto
     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it
     *  is seeded properly!
     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its
     *  sole argument, returning the corresponding array of cryptographically secure random byte values.
     * @see http://nodejs.org/api/crypto.html
     * @see http://www.w3.org/TR/WebCryptoAPI/
     */ function setRandomFallback(random) {
        randomFallback = random;
    }
    /**
     * Synchronously generates a salt.
     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted
     * @param {number=} seed_length Not supported.
     * @returns {string} Resulting salt
     * @throws {Error} If a random fallback is required but not set
     */ function genSaltSync(rounds, seed_length) {
        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;
        if (typeof rounds !== "number") throw Error("Illegal arguments: " + typeof rounds + ", " + typeof seed_length);
        if (rounds < 4) rounds = 4;
        else if (rounds > 31) rounds = 31;
        var salt = [];
        salt.push("$2b$");
        if (rounds < 10) salt.push("0");
        salt.push(rounds.toString());
        salt.push("$");
        salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw
        return salt.join("");
    }
    /**
     * Asynchronously generates a salt.
     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted
     * @param {(number|function(Error, string=))=} seed_length Not supported.
     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt
     * @returns {!Promise} If `callback` has been omitted
     * @throws {Error} If `callback` is present but not a function
     */ function genSalt(rounds, seed_length, callback) {
        if (typeof seed_length === "function") callback = seed_length, seed_length = undefined; // Not supported.
        if (typeof rounds === "function") callback = rounds, rounds = undefined;
        if (typeof rounds === "undefined") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;
        else if (typeof rounds !== "number") throw Error("illegal arguments: " + typeof rounds);
        function _async(callback) {
            nextTick(function() {
                // Pretty thin, but salting is fast enough
                try {
                    callback(null, genSaltSync(rounds));
                } catch (err) {
                    callback(err);
                }
            });
        }
        if (callback) {
            if (typeof callback !== "function") throw Error("Illegal callback: " + typeof callback);
            _async(callback);
        } else return new Promise(function(resolve, reject) {
            _async(function(err, res) {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(res);
            });
        });
    }
    /**
     * Synchronously generates a hash for the given password.
     * @param {string} password Password to hash
     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10
     * @returns {string} Resulting hash
     */ function hashSync(password, salt) {
        if (typeof salt === "undefined") salt = GENSALT_DEFAULT_LOG2_ROUNDS;
        if (typeof salt === "number") salt = genSaltSync(salt);
        if (typeof password !== "string" || typeof salt !== "string") throw Error("Illegal arguments: " + typeof password + ", " + typeof salt);
        return _hash(password, salt);
    }
    /**
     * Asynchronously generates a hash for the given password.
     * @param {string} password Password to hash
     * @param {number|string} salt Salt length to generate or salt to use
     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash
     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed
     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.
     * @returns {!Promise} If `callback` has been omitted
     * @throws {Error} If `callback` is present but not a function
     */ function hash(password, salt, callback, progressCallback) {
        function _async(callback) {
            if (typeof password === "string" && typeof salt === "number") genSalt(salt, function(err, salt) {
                _hash(password, salt, callback, progressCallback);
            });
            else if (typeof password === "string" && typeof salt === "string") _hash(password, salt, callback, progressCallback);
            else nextTick(callback.bind(this, Error("Illegal arguments: " + typeof password + ", " + typeof salt)));
        }
        if (callback) {
            if (typeof callback !== "function") throw Error("Illegal callback: " + typeof callback);
            _async(callback);
        } else return new Promise(function(resolve, reject) {
            _async(function(err, res) {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(res);
            });
        });
    }
    /**
     * Compares two strings of the same length in constant time.
     * @param {string} known Must be of the correct length
     * @param {string} unknown Must be the same length as `known`
     * @returns {boolean}
     * @inner
     */ function safeStringCompare(known, unknown) {
        var diff = known.length ^ unknown.length;
        for(var i = 0; i < known.length; ++i){
            diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);
        }
        return diff === 0;
    }
    /**
     * Synchronously tests a password against a hash.
     * @param {string} password Password to compare
     * @param {string} hash Hash to test against
     * @returns {boolean} true if matching, otherwise false
     * @throws {Error} If an argument is illegal
     */ function compareSync(password, hash) {
        if (typeof password !== "string" || typeof hash !== "string") throw Error("Illegal arguments: " + typeof password + ", " + typeof hash);
        if (hash.length !== 60) return false;
        return safeStringCompare(hashSync(password, hash.substring(0, hash.length - 31)), hash);
    }
    /**
     * Asynchronously tests a password against a hash.
     * @param {string} password Password to compare
     * @param {string} hashValue Hash to test against
     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result
     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed
     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.
     * @returns {!Promise} If `callback` has been omitted
     * @throws {Error} If `callback` is present but not a function
     */ function compare(password, hashValue, callback, progressCallback) {
        function _async(callback) {
            if (typeof password !== "string" || typeof hashValue !== "string") {
                nextTick(callback.bind(this, Error("Illegal arguments: " + typeof password + ", " + typeof hashValue)));
                return;
            }
            if (hashValue.length !== 60) {
                nextTick(callback.bind(this, null, false));
                return;
            }
            hash(password, hashValue.substring(0, 29), function(err, comp) {
                if (err) callback(err);
                else callback(null, safeStringCompare(comp, hashValue));
            }, progressCallback);
        }
        if (callback) {
            if (typeof callback !== "function") throw Error("Illegal callback: " + typeof callback);
            _async(callback);
        } else return new Promise(function(resolve, reject) {
            _async(function(err, res) {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(res);
            });
        });
    }
    /**
     * Gets the number of rounds used to encrypt the specified hash.
     * @param {string} hash Hash to extract the used number of rounds from
     * @returns {number} Number of rounds used
     * @throws {Error} If `hash` is not a string
     */ function getRounds(hash) {
        if (typeof hash !== "string") throw Error("Illegal arguments: " + typeof hash);
        return parseInt(hash.split("$")[2], 10);
    }
    /**
     * Gets the salt portion from a hash. Does not validate the hash.
     * @param {string} hash Hash to extract the salt from
     * @returns {string} Extracted salt part
     * @throws {Error} If `hash` is not a string or otherwise invalid
     */ function getSalt(hash) {
        if (typeof hash !== "string") throw Error("Illegal arguments: " + typeof hash);
        if (hash.length !== 60) throw Error("Illegal hash length: " + hash.length + " != 60");
        return hash.substring(0, 29);
    }
    /**
     * Tests if a password will be truncated when hashed, that is its length is
     * greater than 72 bytes when converted to UTF-8.
     * @param {string} password The password to test
     * @returns {boolean} `true` if truncated, otherwise `false`
     */ function truncates(password) {
        if (typeof password !== "string") throw Error("Illegal arguments: " + typeof password);
        return utf8Length(password) > 72;
    }
    /**
     * Continues with the callback on the next tick.
     * @function
     * @param {function(...[*])} callback Callback to execute
     * @inner
     */ var nextTick = typeof process !== "undefined" && process && typeof process.nextTick === "function" ? typeof setImmediate === "function" ? setImmediate : process.nextTick : setTimeout;
    /** Calculates the byte length of a string encoded as UTF8. */ function utf8Length(string) {
        var len = 0, c = 0;
        for(var i = 0; i < string.length; ++i){
            c = string.charCodeAt(i);
            if (c < 128) len += 1;
            else if (c < 2048) len += 2;
            else if ((c & 0xfc00) === 0xd800 && (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {
                ++i;
                len += 4;
            } else len += 3;
        }
        return len;
    }
    /** Converts a string to an array of UTF8 bytes. */ function utf8Array(string) {
        var offset = 0, c1, c2;
        var buffer = new Array(utf8Length(string));
        for(var i = 0, k = string.length; i < k; ++i){
            c1 = string.charCodeAt(i);
            if (c1 < 128) {
                buffer[offset++] = c1;
            } else if (c1 < 2048) {
                buffer[offset++] = c1 >> 6 | 192;
                buffer[offset++] = c1 & 63 | 128;
            } else if ((c1 & 0xfc00) === 0xd800 && ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00) {
                c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);
                ++i;
                buffer[offset++] = c1 >> 18 | 240;
                buffer[offset++] = c1 >> 12 & 63 | 128;
                buffer[offset++] = c1 >> 6 & 63 | 128;
                buffer[offset++] = c1 & 63 | 128;
            } else {
                buffer[offset++] = c1 >> 12 | 224;
                buffer[offset++] = c1 >> 6 & 63 | 128;
                buffer[offset++] = c1 & 63 | 128;
            }
        }
        return buffer;
    }
    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.
    /**
     * bcrypt's own non-standard base64 dictionary.
     * @type {!Array.<string>}
     * @const
     * @inner
     **/ var BASE64_CODE = "./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");
    /**
     * @type {!Array.<number>}
     * @const
     * @inner
     **/ var BASE64_INDEX = [
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        0,
        1,
        54,
        55,
        56,
        57,
        58,
        59,
        60,
        61,
        62,
        63,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        -1,
        -1,
        -1,
        -1,
        -1,
        -1,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        35,
        36,
        37,
        38,
        39,
        40,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        50,
        51,
        52,
        53,
        -1,
        -1,
        -1,
        -1,
        -1
    ];
    /**
     * Encodes a byte array to base64 with up to len bytes of input.
     * @param {!Array.<number>} b Byte array
     * @param {number} len Maximum input length
     * @returns {string}
     * @inner
     */ function base64_encode(b, len) {
        var off = 0, rs = [], c1, c2;
        if (len <= 0 || len > b.length) throw Error("Illegal len: " + len);
        while(off < len){
            c1 = b[off++] & 0xff;
            rs.push(BASE64_CODE[c1 >> 2 & 0x3f]);
            c1 = (c1 & 0x03) << 4;
            if (off >= len) {
                rs.push(BASE64_CODE[c1 & 0x3f]);
                break;
            }
            c2 = b[off++] & 0xff;
            c1 |= c2 >> 4 & 0x0f;
            rs.push(BASE64_CODE[c1 & 0x3f]);
            c1 = (c2 & 0x0f) << 2;
            if (off >= len) {
                rs.push(BASE64_CODE[c1 & 0x3f]);
                break;
            }
            c2 = b[off++] & 0xff;
            c1 |= c2 >> 6 & 0x03;
            rs.push(BASE64_CODE[c1 & 0x3f]);
            rs.push(BASE64_CODE[c2 & 0x3f]);
        }
        return rs.join("");
    }
    /**
     * Decodes a base64 encoded string to up to len bytes of output.
     * @param {string} s String to decode
     * @param {number} len Maximum output length
     * @returns {!Array.<number>}
     * @inner
     */ function base64_decode(s, len) {
        var off = 0, slen = s.length, olen = 0, rs = [], c1, c2, c3, c4, o, code;
        if (len <= 0) throw Error("Illegal len: " + len);
        while(off < slen - 1 && olen < len){
            code = s.charCodeAt(off++);
            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
            code = s.charCodeAt(off++);
            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
            if (c1 == -1 || c2 == -1) break;
            o = c1 << 2 >>> 0;
            o |= (c2 & 0x30) >> 4;
            rs.push(String.fromCharCode(o));
            if (++olen >= len || off >= slen) break;
            code = s.charCodeAt(off++);
            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
            if (c3 == -1) break;
            o = (c2 & 0x0f) << 4 >>> 0;
            o |= (c3 & 0x3c) >> 2;
            rs.push(String.fromCharCode(o));
            if (++olen >= len || off >= slen) break;
            code = s.charCodeAt(off++);
            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
            o = (c3 & 0x03) << 6 >>> 0;
            o |= c4;
            rs.push(String.fromCharCode(o));
            ++olen;
        }
        var res = [];
        for(off = 0; off < olen; off++)res.push(rs[off].charCodeAt(0));
        return res;
    }
    /**
     * @type {number}
     * @const
     * @inner
     */ var BCRYPT_SALT_LEN = 16;
    /**
     * @type {number}
     * @const
     * @inner
     */ var GENSALT_DEFAULT_LOG2_ROUNDS = 10;
    /**
     * @type {number}
     * @const
     * @inner
     */ var BLOWFISH_NUM_ROUNDS = 16;
    /**
     * @type {number}
     * @const
     * @inner
     */ var MAX_EXECUTION_TIME = 100;
    /**
     * @type {Array.<number>}
     * @const
     * @inner
     */ var P_ORIG = [
        0x243f6a88,
        0x85a308d3,
        0x13198a2e,
        0x03707344,
        0xa4093822,
        0x299f31d0,
        0x082efa98,
        0xec4e6c89,
        0x452821e6,
        0x38d01377,
        0xbe5466cf,
        0x34e90c6c,
        0xc0ac29b7,
        0xc97c50dd,
        0x3f84d5b5,
        0xb5470917,
        0x9216d5d9,
        0x8979fb1b
    ];
    /**
     * @type {Array.<number>}
     * @const
     * @inner
     */ var S_ORIG = [
        0xd1310ba6,
        0x98dfb5ac,
        0x2ffd72db,
        0xd01adfb7,
        0xb8e1afed,
        0x6a267e96,
        0xba7c9045,
        0xf12c7f99,
        0x24a19947,
        0xb3916cf7,
        0x0801f2e2,
        0x858efc16,
        0x636920d8,
        0x71574e69,
        0xa458fea3,
        0xf4933d7e,
        0x0d95748f,
        0x728eb658,
        0x718bcd58,
        0x82154aee,
        0x7b54a41d,
        0xc25a59b5,
        0x9c30d539,
        0x2af26013,
        0xc5d1b023,
        0x286085f0,
        0xca417918,
        0xb8db38ef,
        0x8e79dcb0,
        0x603a180e,
        0x6c9e0e8b,
        0xb01e8a3e,
        0xd71577c1,
        0xbd314b27,
        0x78af2fda,
        0x55605c60,
        0xe65525f3,
        0xaa55ab94,
        0x57489862,
        0x63e81440,
        0x55ca396a,
        0x2aab10b6,
        0xb4cc5c34,
        0x1141e8ce,
        0xa15486af,
        0x7c72e993,
        0xb3ee1411,
        0x636fbc2a,
        0x2ba9c55d,
        0x741831f6,
        0xce5c3e16,
        0x9b87931e,
        0xafd6ba33,
        0x6c24cf5c,
        0x7a325381,
        0x28958677,
        0x3b8f4898,
        0x6b4bb9af,
        0xc4bfe81b,
        0x66282193,
        0x61d809cc,
        0xfb21a991,
        0x487cac60,
        0x5dec8032,
        0xef845d5d,
        0xe98575b1,
        0xdc262302,
        0xeb651b88,
        0x23893e81,
        0xd396acc5,
        0x0f6d6ff3,
        0x83f44239,
        0x2e0b4482,
        0xa4842004,
        0x69c8f04a,
        0x9e1f9b5e,
        0x21c66842,
        0xf6e96c9a,
        0x670c9c61,
        0xabd388f0,
        0x6a51a0d2,
        0xd8542f68,
        0x960fa728,
        0xab5133a3,
        0x6eef0b6c,
        0x137a3be4,
        0xba3bf050,
        0x7efb2a98,
        0xa1f1651d,
        0x39af0176,
        0x66ca593e,
        0x82430e88,
        0x8cee8619,
        0x456f9fb4,
        0x7d84a5c3,
        0x3b8b5ebe,
        0xe06f75d8,
        0x85c12073,
        0x401a449f,
        0x56c16aa6,
        0x4ed3aa62,
        0x363f7706,
        0x1bfedf72,
        0x429b023d,
        0x37d0d724,
        0xd00a1248,
        0xdb0fead3,
        0x49f1c09b,
        0x075372c9,
        0x80991b7b,
        0x25d479d8,
        0xf6e8def7,
        0xe3fe501a,
        0xb6794c3b,
        0x976ce0bd,
        0x04c006ba,
        0xc1a94fb6,
        0x409f60c4,
        0x5e5c9ec2,
        0x196a2463,
        0x68fb6faf,
        0x3e6c53b5,
        0x1339b2eb,
        0x3b52ec6f,
        0x6dfc511f,
        0x9b30952c,
        0xcc814544,
        0xaf5ebd09,
        0xbee3d004,
        0xde334afd,
        0x660f2807,
        0x192e4bb3,
        0xc0cba857,
        0x45c8740f,
        0xd20b5f39,
        0xb9d3fbdb,
        0x5579c0bd,
        0x1a60320a,
        0xd6a100c6,
        0x402c7279,
        0x679f25fe,
        0xfb1fa3cc,
        0x8ea5e9f8,
        0xdb3222f8,
        0x3c7516df,
        0xfd616b15,
        0x2f501ec8,
        0xad0552ab,
        0x323db5fa,
        0xfd238760,
        0x53317b48,
        0x3e00df82,
        0x9e5c57bb,
        0xca6f8ca0,
        0x1a87562e,
        0xdf1769db,
        0xd542a8f6,
        0x287effc3,
        0xac6732c6,
        0x8c4f5573,
        0x695b27b0,
        0xbbca58c8,
        0xe1ffa35d,
        0xb8f011a0,
        0x10fa3d98,
        0xfd2183b8,
        0x4afcb56c,
        0x2dd1d35b,
        0x9a53e479,
        0xb6f84565,
        0xd28e49bc,
        0x4bfb9790,
        0xe1ddf2da,
        0xa4cb7e33,
        0x62fb1341,
        0xcee4c6e8,
        0xef20cada,
        0x36774c01,
        0xd07e9efe,
        0x2bf11fb4,
        0x95dbda4d,
        0xae909198,
        0xeaad8e71,
        0x6b93d5a0,
        0xd08ed1d0,
        0xafc725e0,
        0x8e3c5b2f,
        0x8e7594b7,
        0x8ff6e2fb,
        0xf2122b64,
        0x8888b812,
        0x900df01c,
        0x4fad5ea0,
        0x688fc31c,
        0xd1cff191,
        0xb3a8c1ad,
        0x2f2f2218,
        0xbe0e1777,
        0xea752dfe,
        0x8b021fa1,
        0xe5a0cc0f,
        0xb56f74e8,
        0x18acf3d6,
        0xce89e299,
        0xb4a84fe0,
        0xfd13e0b7,
        0x7cc43b81,
        0xd2ada8d9,
        0x165fa266,
        0x80957705,
        0x93cc7314,
        0x211a1477,
        0xe6ad2065,
        0x77b5fa86,
        0xc75442f5,
        0xfb9d35cf,
        0xebcdaf0c,
        0x7b3e89a0,
        0xd6411bd3,
        0xae1e7e49,
        0x00250e2d,
        0x2071b35e,
        0x226800bb,
        0x57b8e0af,
        0x2464369b,
        0xf009b91e,
        0x5563911d,
        0x59dfa6aa,
        0x78c14389,
        0xd95a537f,
        0x207d5ba2,
        0x02e5b9c5,
        0x83260376,
        0x6295cfa9,
        0x11c81968,
        0x4e734a41,
        0xb3472dca,
        0x7b14a94a,
        0x1b510052,
        0x9a532915,
        0xd60f573f,
        0xbc9bc6e4,
        0x2b60a476,
        0x81e67400,
        0x08ba6fb5,
        0x571be91f,
        0xf296ec6b,
        0x2a0dd915,
        0xb6636521,
        0xe7b9f9b6,
        0xff34052e,
        0xc5855664,
        0x53b02d5d,
        0xa99f8fa1,
        0x08ba4799,
        0x6e85076a,
        0x4b7a70e9,
        0xb5b32944,
        0xdb75092e,
        0xc4192623,
        0xad6ea6b0,
        0x49a7df7d,
        0x9cee60b8,
        0x8fedb266,
        0xecaa8c71,
        0x699a17ff,
        0x5664526c,
        0xc2b19ee1,
        0x193602a5,
        0x75094c29,
        0xa0591340,
        0xe4183a3e,
        0x3f54989a,
        0x5b429d65,
        0x6b8fe4d6,
        0x99f73fd6,
        0xa1d29c07,
        0xefe830f5,
        0x4d2d38e6,
        0xf0255dc1,
        0x4cdd2086,
        0x8470eb26,
        0x6382e9c6,
        0x021ecc5e,
        0x09686b3f,
        0x3ebaefc9,
        0x3c971814,
        0x6b6a70a1,
        0x687f3584,
        0x52a0e286,
        0xb79c5305,
        0xaa500737,
        0x3e07841c,
        0x7fdeae5c,
        0x8e7d44ec,
        0x5716f2b8,
        0xb03ada37,
        0xf0500c0d,
        0xf01c1f04,
        0x0200b3ff,
        0xae0cf51a,
        0x3cb574b2,
        0x25837a58,
        0xdc0921bd,
        0xd19113f9,
        0x7ca92ff6,
        0x94324773,
        0x22f54701,
        0x3ae5e581,
        0x37c2dadc,
        0xc8b57634,
        0x9af3dda7,
        0xa9446146,
        0x0fd0030e,
        0xecc8c73e,
        0xa4751e41,
        0xe238cd99,
        0x3bea0e2f,
        0x3280bba1,
        0x183eb331,
        0x4e548b38,
        0x4f6db908,
        0x6f420d03,
        0xf60a04bf,
        0x2cb81290,
        0x24977c79,
        0x5679b072,
        0xbcaf89af,
        0xde9a771f,
        0xd9930810,
        0xb38bae12,
        0xdccf3f2e,
        0x5512721f,
        0x2e6b7124,
        0x501adde6,
        0x9f84cd87,
        0x7a584718,
        0x7408da17,
        0xbc9f9abc,
        0xe94b7d8c,
        0xec7aec3a,
        0xdb851dfa,
        0x63094366,
        0xc464c3d2,
        0xef1c1847,
        0x3215d908,
        0xdd433b37,
        0x24c2ba16,
        0x12a14d43,
        0x2a65c451,
        0x50940002,
        0x133ae4dd,
        0x71dff89e,
        0x10314e55,
        0x81ac77d6,
        0x5f11199b,
        0x043556f1,
        0xd7a3c76b,
        0x3c11183b,
        0x5924a509,
        0xf28fe6ed,
        0x97f1fbfa,
        0x9ebabf2c,
        0x1e153c6e,
        0x86e34570,
        0xeae96fb1,
        0x860e5e0a,
        0x5a3e2ab3,
        0x771fe71c,
        0x4e3d06fa,
        0x2965dcb9,
        0x99e71d0f,
        0x803e89d6,
        0x5266c825,
        0x2e4cc978,
        0x9c10b36a,
        0xc6150eba,
        0x94e2ea78,
        0xa5fc3c53,
        0x1e0a2df4,
        0xf2f74ea7,
        0x361d2b3d,
        0x1939260f,
        0x19c27960,
        0x5223a708,
        0xf71312b6,
        0xebadfe6e,
        0xeac31f66,
        0xe3bc4595,
        0xa67bc883,
        0xb17f37d1,
        0x018cff28,
        0xc332ddef,
        0xbe6c5aa5,
        0x65582185,
        0x68ab9802,
        0xeecea50f,
        0xdb2f953b,
        0x2aef7dad,
        0x5b6e2f84,
        0x1521b628,
        0x29076170,
        0xecdd4775,
        0x619f1510,
        0x13cca830,
        0xeb61bd96,
        0x0334fe1e,
        0xaa0363cf,
        0xb5735c90,
        0x4c70a239,
        0xd59e9e0b,
        0xcbaade14,
        0xeecc86bc,
        0x60622ca7,
        0x9cab5cab,
        0xb2f3846e,
        0x648b1eaf,
        0x19bdf0ca,
        0xa02369b9,
        0x655abb50,
        0x40685a32,
        0x3c2ab4b3,
        0x319ee9d5,
        0xc021b8f7,
        0x9b540b19,
        0x875fa099,
        0x95f7997e,
        0x623d7da8,
        0xf837889a,
        0x97e32d77,
        0x11ed935f,
        0x16681281,
        0x0e358829,
        0xc7e61fd6,
        0x96dedfa1,
        0x7858ba99,
        0x57f584a5,
        0x1b227263,
        0x9b83c3ff,
        0x1ac24696,
        0xcdb30aeb,
        0x532e3054,
        0x8fd948e4,
        0x6dbc3128,
        0x58ebf2ef,
        0x34c6ffea,
        0xfe28ed61,
        0xee7c3c73,
        0x5d4a14d9,
        0xe864b7e3,
        0x42105d14,
        0x203e13e0,
        0x45eee2b6,
        0xa3aaabea,
        0xdb6c4f15,
        0xfacb4fd0,
        0xc742f442,
        0xef6abbb5,
        0x654f3b1d,
        0x41cd2105,
        0xd81e799e,
        0x86854dc7,
        0xe44b476a,
        0x3d816250,
        0xcf62a1f2,
        0x5b8d2646,
        0xfc8883a0,
        0xc1c7b6a3,
        0x7f1524c3,
        0x69cb7492,
        0x47848a0b,
        0x5692b285,
        0x095bbf00,
        0xad19489d,
        0x1462b174,
        0x23820e00,
        0x58428d2a,
        0x0c55f5ea,
        0x1dadf43e,
        0x233f7061,
        0x3372f092,
        0x8d937e41,
        0xd65fecf1,
        0x6c223bdb,
        0x7cde3759,
        0xcbee7460,
        0x4085f2a7,
        0xce77326e,
        0xa6078084,
        0x19f8509e,
        0xe8efd855,
        0x61d99735,
        0xa969a7aa,
        0xc50c06c2,
        0x5a04abfc,
        0x800bcadc,
        0x9e447a2e,
        0xc3453484,
        0xfdd56705,
        0x0e1e9ec9,
        0xdb73dbd3,
        0x105588cd,
        0x675fda79,
        0xe3674340,
        0xc5c43465,
        0x713e38d8,
        0x3d28f89e,
        0xf16dff20,
        0x153e21e7,
        0x8fb03d4a,
        0xe6e39f2b,
        0xdb83adf7,
        0xe93d5a68,
        0x948140f7,
        0xf64c261c,
        0x94692934,
        0x411520f7,
        0x7602d4f7,
        0xbcf46b2e,
        0xd4a20068,
        0xd4082471,
        0x3320f46a,
        0x43b7d4b7,
        0x500061af,
        0x1e39f62e,
        0x97244546,
        0x14214f74,
        0xbf8b8840,
        0x4d95fc1d,
        0x96b591af,
        0x70f4ddd3,
        0x66a02f45,
        0xbfbc09ec,
        0x03bd9785,
        0x7fac6dd0,
        0x31cb8504,
        0x96eb27b3,
        0x55fd3941,
        0xda2547e6,
        0xabca0a9a,
        0x28507825,
        0x530429f4,
        0x0a2c86da,
        0xe9b66dfb,
        0x68dc1462,
        0xd7486900,
        0x680ec0a4,
        0x27a18dee,
        0x4f3ffea2,
        0xe887ad8c,
        0xb58ce006,
        0x7af4d6b6,
        0xaace1e7c,
        0xd3375fec,
        0xce78a399,
        0x406b2a42,
        0x20fe9e35,
        0xd9f385b9,
        0xee39d7ab,
        0x3b124e8b,
        0x1dc9faf7,
        0x4b6d1856,
        0x26a36631,
        0xeae397b2,
        0x3a6efa74,
        0xdd5b4332,
        0x6841e7f7,
        0xca7820fb,
        0xfb0af54e,
        0xd8feb397,
        0x454056ac,
        0xba489527,
        0x55533a3a,
        0x20838d87,
        0xfe6ba9b7,
        0xd096954b,
        0x55a867bc,
        0xa1159a58,
        0xcca92963,
        0x99e1db33,
        0xa62a4a56,
        0x3f3125f9,
        0x5ef47e1c,
        0x9029317c,
        0xfdf8e802,
        0x04272f70,
        0x80bb155c,
        0x05282ce3,
        0x95c11548,
        0xe4c66d22,
        0x48c1133f,
        0xc70f86dc,
        0x07f9c9ee,
        0x41041f0f,
        0x404779a4,
        0x5d886e17,
        0x325f51eb,
        0xd59bc0d1,
        0xf2bcc18f,
        0x41113564,
        0x257b7834,
        0x602a9c60,
        0xdff8e8a3,
        0x1f636c1b,
        0x0e12b4c2,
        0x02e1329e,
        0xaf664fd1,
        0xcad18115,
        0x6b2395e0,
        0x333e92e1,
        0x3b240b62,
        0xeebeb922,
        0x85b2a20e,
        0xe6ba0d99,
        0xde720c8c,
        0x2da2f728,
        0xd0127845,
        0x95b794fd,
        0x647d0862,
        0xe7ccf5f0,
        0x5449a36f,
        0x877d48fa,
        0xc39dfd27,
        0xf33e8d1e,
        0x0a476341,
        0x992eff74,
        0x3a6f6eab,
        0xf4f8fd37,
        0xa812dc60,
        0xa1ebddf8,
        0x991be14c,
        0xdb6e6b0d,
        0xc67b5510,
        0x6d672c37,
        0x2765d43b,
        0xdcd0e804,
        0xf1290dc7,
        0xcc00ffa3,
        0xb5390f92,
        0x690fed0b,
        0x667b9ffb,
        0xcedb7d9c,
        0xa091cf0b,
        0xd9155ea3,
        0xbb132f88,
        0x515bad24,
        0x7b9479bf,
        0x763bd6eb,
        0x37392eb3,
        0xcc115979,
        0x8026e297,
        0xf42e312d,
        0x6842ada7,
        0xc66a2b3b,
        0x12754ccc,
        0x782ef11c,
        0x6a124237,
        0xb79251e7,
        0x06a1bbe6,
        0x4bfb6350,
        0x1a6b1018,
        0x11caedfa,
        0x3d25bdd8,
        0xe2e1c3c9,
        0x44421659,
        0x0a121386,
        0xd90cec6e,
        0xd5abea2a,
        0x64af674e,
        0xda86a85f,
        0xbebfe988,
        0x64e4c3fe,
        0x9dbc8057,
        0xf0f7c086,
        0x60787bf8,
        0x6003604d,
        0xd1fd8346,
        0xf6381fb0,
        0x7745ae04,
        0xd736fccc,
        0x83426b33,
        0xf01eab71,
        0xb0804187,
        0x3c005e5f,
        0x77a057be,
        0xbde8ae24,
        0x55464299,
        0xbf582e61,
        0x4e58f48f,
        0xf2ddfda2,
        0xf474ef38,
        0x8789bdc2,
        0x5366f9c3,
        0xc8b38e74,
        0xb475f255,
        0x46fcd9b9,
        0x7aeb2661,
        0x8b1ddf84,
        0x846a0e79,
        0x915f95e2,
        0x466e598e,
        0x20b45770,
        0x8cd55591,
        0xc902de4c,
        0xb90bace1,
        0xbb8205d0,
        0x11a86248,
        0x7574a99e,
        0xb77f19b6,
        0xe0a9dc09,
        0x662d09a1,
        0xc4324633,
        0xe85a1f02,
        0x09f0be8c,
        0x4a99a025,
        0x1d6efe10,
        0x1ab93d1d,
        0x0ba5a4df,
        0xa186f20f,
        0x2868f169,
        0xdcb7da83,
        0x573906fe,
        0xa1e2ce9b,
        0x4fcd7f52,
        0x50115e01,
        0xa70683fa,
        0xa002b5c4,
        0x0de6d027,
        0x9af88c27,
        0x773f8641,
        0xc3604c06,
        0x61a806b5,
        0xf0177a28,
        0xc0f586e0,
        0x006058aa,
        0x30dc7d62,
        0x11e69ed7,
        0x2338ea63,
        0x53c2dd94,
        0xc2c21634,
        0xbbcbee56,
        0x90bcb6de,
        0xebfc7da1,
        0xce591d76,
        0x6f05e409,
        0x4b7c0188,
        0x39720a3d,
        0x7c927c24,
        0x86e3725f,
        0x724d9db9,
        0x1ac15bb4,
        0xd39eb8fc,
        0xed545578,
        0x08fca5b5,
        0xd83d7cd3,
        0x4dad0fc4,
        0x1e50ef5e,
        0xb161e6f8,
        0xa28514d9,
        0x6c51133c,
        0x6fd5c7e7,
        0x56e14ec4,
        0x362abfce,
        0xddc6c837,
        0xd79a3234,
        0x92638212,
        0x670efa8e,
        0x406000e0,
        0x3a39ce37,
        0xd3faf5cf,
        0xabc27737,
        0x5ac52d1b,
        0x5cb0679e,
        0x4fa33742,
        0xd3822740,
        0x99bc9bbe,
        0xd5118e9d,
        0xbf0f7315,
        0xd62d1c7e,
        0xc700c47b,
        0xb78c1b6b,
        0x21a19045,
        0xb26eb1be,
        0x6a366eb4,
        0x5748ab2f,
        0xbc946e79,
        0xc6a376d2,
        0x6549c2c8,
        0x530ff8ee,
        0x468dde7d,
        0xd5730a1d,
        0x4cd04dc6,
        0x2939bbdb,
        0xa9ba4650,
        0xac9526e8,
        0xbe5ee304,
        0xa1fad5f0,
        0x6a2d519a,
        0x63ef8ce2,
        0x9a86ee22,
        0xc089c2b8,
        0x43242ef6,
        0xa51e03aa,
        0x9cf2d0a4,
        0x83c061ba,
        0x9be96a4d,
        0x8fe51550,
        0xba645bd6,
        0x2826a2f9,
        0xa73a3ae1,
        0x4ba99586,
        0xef5562e9,
        0xc72fefd3,
        0xf752f7da,
        0x3f046f69,
        0x77fa0a59,
        0x80e4a915,
        0x87b08601,
        0x9b09e6ad,
        0x3b3ee593,
        0xe990fd5a,
        0x9e34d797,
        0x2cf0b7d9,
        0x022b8b51,
        0x96d5ac3a,
        0x017da67d,
        0xd1cf3ed6,
        0x7c7d2d28,
        0x1f9f25cf,
        0xadf2b89b,
        0x5ad6b472,
        0x5a88f54c,
        0xe029ac71,
        0xe019a5e6,
        0x47b0acfd,
        0xed93fa9b,
        0xe8d3c48d,
        0x283b57cc,
        0xf8d56629,
        0x79132e28,
        0x785f0191,
        0xed756055,
        0xf7960e44,
        0xe3d35e8c,
        0x15056dd4,
        0x88f46dba,
        0x03a16125,
        0x0564f0bd,
        0xc3eb9e15,
        0x3c9057a2,
        0x97271aec,
        0xa93a072a,
        0x1b3f6d9b,
        0x1e6321f5,
        0xf59c66fb,
        0x26dcf319,
        0x7533d928,
        0xb155fdf5,
        0x03563482,
        0x8aba3cbb,
        0x28517711,
        0xc20ad9f8,
        0xabcc5167,
        0xccad925f,
        0x4de81751,
        0x3830dc8e,
        0x379d5862,
        0x9320f991,
        0xea7a90c2,
        0xfb3e7bce,
        0x5121ce64,
        0x774fbe32,
        0xa8b6e37e,
        0xc3293d46,
        0x48de5369,
        0x6413e680,
        0xa2ae0810,
        0xdd6db224,
        0x69852dfd,
        0x09072166,
        0xb39a460a,
        0x6445c0dd,
        0x586cdecf,
        0x1c20c8ae,
        0x5bbef7dd,
        0x1b588d40,
        0xccd2017f,
        0x6bb4e3bb,
        0xdda26a7e,
        0x3a59ff45,
        0x3e350a44,
        0xbcb4cdd5,
        0x72eacea8,
        0xfa6484bb,
        0x8d6612ae,
        0xbf3c6f47,
        0xd29be463,
        0x542f5d9e,
        0xaec2771b,
        0xf64e6370,
        0x740e0d8d,
        0xe75b1357,
        0xf8721671,
        0xaf537d5d,
        0x4040cb08,
        0x4eb4e2cc,
        0x34d2466a,
        0x0115af84,
        0xe1b00428,
        0x95983a1d,
        0x06b89fb4,
        0xce6ea048,
        0x6f3f3b82,
        0x3520ab82,
        0x011a1d4b,
        0x277227f8,
        0x611560b1,
        0xe7933fdc,
        0xbb3a792b,
        0x344525bd,
        0xa08839e1,
        0x51ce794b,
        0x2f32c9b7,
        0xa01fbac9,
        0xe01cc87e,
        0xbcc7d1f6,
        0xcf0111c3,
        0xa1e8aac7,
        0x1a908749,
        0xd44fbd9a,
        0xd0dadecb,
        0xd50ada38,
        0x0339c32a,
        0xc6913667,
        0x8df9317c,
        0xe0b12b4f,
        0xf79e59b7,
        0x43f5bb3a,
        0xf2d519ff,
        0x27d9459c,
        0xbf97222c,
        0x15e6fc2a,
        0x0f91fc71,
        0x9b941525,
        0xfae59361,
        0xceb69ceb,
        0xc2a86459,
        0x12baa8d1,
        0xb6c1075e,
        0xe3056a0c,
        0x10d25065,
        0xcb03a442,
        0xe0ec6e0e,
        0x1698db3b,
        0x4c98a0be,
        0x3278e964,
        0x9f1f9532,
        0xe0d392df,
        0xd3a0342b,
        0x8971f21e,
        0x1b0a7441,
        0x4ba3348c,
        0xc5be7120,
        0xc37632d8,
        0xdf359f8d,
        0x9b992f2e,
        0xe60b6f47,
        0x0fe3f11d,
        0xe54cda54,
        0x1edad891,
        0xce6279cf,
        0xcd3e7e6f,
        0x1618b166,
        0xfd2c1d05,
        0x848fd2c5,
        0xf6fb2299,
        0xf523f357,
        0xa6327623,
        0x93a83531,
        0x56cccd02,
        0xacf08162,
        0x5a75ebb5,
        0x6e163697,
        0x88d273cc,
        0xde966292,
        0x81b949d0,
        0x4c50901b,
        0x71c65614,
        0xe6c6c7bd,
        0x327a140a,
        0x45e1d006,
        0xc3f27b9a,
        0xc9aa53fd,
        0x62a80f00,
        0xbb25bfe2,
        0x35bdd2f6,
        0x71126905,
        0xb2040222,
        0xb6cbcf7c,
        0xcd769c2b,
        0x53113ec0,
        0x1640e3d3,
        0x38abbd60,
        0x2547adf0,
        0xba38209c,
        0xf746ce76,
        0x77afa1c5,
        0x20756060,
        0x85cbfe4e,
        0x8ae88dd8,
        0x7aaaf9b0,
        0x4cf9aa7e,
        0x1948c25c,
        0x02fb8a8c,
        0x01c36ae4,
        0xd6ebe1f9,
        0x90d4f869,
        0xa65cdea0,
        0x3f09252d,
        0xc208e69f,
        0xb74e6132,
        0xce77e25b,
        0x578fdfe3,
        0x3ac372e6
    ];
    /**
     * @type {Array.<number>}
     * @const
     * @inner
     */ var C_ORIG = [
        0x4f727068,
        0x65616e42,
        0x65686f6c,
        0x64657253,
        0x63727944,
        0x6f756274
    ];
    /**
     * @param {Array.<number>} lr
     * @param {number} off
     * @param {Array.<number>} P
     * @param {Array.<number>} S
     * @returns {Array.<number>}
     * @inner
     */ function _encipher(lr, off, P, S) {
        // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt
        var n, l = lr[off], r = lr[off + 1];
        l ^= P[0];
        /*
      for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)
          // Feistel substitution on left word
          n  = S[l >>> 24],
          n += S[0x100 | ((l >> 16) & 0xff)],
          n ^= S[0x200 | ((l >> 8) & 0xff)],
          n += S[0x300 | (l & 0xff)],
          r ^= n ^ P[++i],
          // Feistel substitution on right word
          n  = S[r >>> 24],
          n += S[0x100 | ((r >> 16) & 0xff)],
          n ^= S[0x200 | ((r >> 8) & 0xff)],
          n += S[0x300 | (r & 0xff)],
          l ^= n ^ P[++i];
      */ //The following is an unrolled version of the above loop.
        //Iteration 0
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[1];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[2];
        //Iteration 1
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[3];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[4];
        //Iteration 2
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[5];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[6];
        //Iteration 3
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[7];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[8];
        //Iteration 4
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[9];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[10];
        //Iteration 5
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[11];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[12];
        //Iteration 6
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[13];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[14];
        //Iteration 7
        n = S[l >>> 24];
        n += S[0x100 | l >> 16 & 0xff];
        n ^= S[0x200 | l >> 8 & 0xff];
        n += S[0x300 | l & 0xff];
        r ^= n ^ P[15];
        n = S[r >>> 24];
        n += S[0x100 | r >> 16 & 0xff];
        n ^= S[0x200 | r >> 8 & 0xff];
        n += S[0x300 | r & 0xff];
        l ^= n ^ P[16];
        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];
        lr[off + 1] = l;
        return lr;
    }
    /**
     * @param {Array.<number>} data
     * @param {number} offp
     * @returns {{key: number, offp: number}}
     * @inner
     */ function _streamtoword(data, offp) {
        for(var i = 0, word = 0; i < 4; ++i)word = word << 8 | data[offp] & 0xff, offp = (offp + 1) % data.length;
        return {
            key: word,
            offp: offp
        };
    }
    /**
     * @param {Array.<number>} key
     * @param {Array.<number>} P
     * @param {Array.<number>} S
     * @inner
     */ function _key(key, P, S) {
        var offset = 0, lr = [
            0,
            0
        ], plen = P.length, slen = S.length, sw;
        for(var i = 0; i < plen; i++)sw = _streamtoword(key, offset), offset = sw.offp, P[i] = P[i] ^ sw.key;
        for(i = 0; i < plen; i += 2)lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];
        for(i = 0; i < slen; i += 2)lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];
    }
    /**
     * Expensive key schedule Blowfish.
     * @param {Array.<number>} data
     * @param {Array.<number>} key
     * @param {Array.<number>} P
     * @param {Array.<number>} S
     * @inner
     */ function _ekskey(data, key, P, S) {
        var offp = 0, lr = [
            0,
            0
        ], plen = P.length, slen = S.length, sw;
        for(var i = 0; i < plen; i++)sw = _streamtoword(key, offp), offp = sw.offp, P[i] = P[i] ^ sw.key;
        offp = 0;
        for(i = 0; i < plen; i += 2)sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];
        for(i = 0; i < slen; i += 2)sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];
    }
    /**
     * Internaly crypts a string.
     * @param {Array.<number>} b Bytes to crypt
     * @param {Array.<number>} salt Salt bytes to use
     * @param {number} rounds Number of rounds
     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If
     *  omitted, the operation will be performed synchronously.
     *  @param {function(number)=} progressCallback Callback called with the current progress
     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`
     * @inner
     */ function _crypt(b, salt, rounds, callback, progressCallback) {
        var cdata = C_ORIG.slice(), clen = cdata.length, err;
        // Validate
        if (rounds < 4 || rounds > 31) {
            err = Error("Illegal number of rounds (4-31): " + rounds);
            if (callback) {
                nextTick(callback.bind(this, err));
                return;
            } else throw err;
        }
        if (salt.length !== BCRYPT_SALT_LEN) {
            err = Error("Illegal salt length: " + salt.length + " != " + BCRYPT_SALT_LEN);
            if (callback) {
                nextTick(callback.bind(this, err));
                return;
            } else throw err;
        }
        rounds = 1 << rounds >>> 0;
        var P, S, i = 0, j;
        //Use typed arrays when available - huge speedup!
        if (typeof Int32Array === "function") {
            P = new Int32Array(P_ORIG);
            S = new Int32Array(S_ORIG);
        } else {
            P = P_ORIG.slice();
            S = S_ORIG.slice();
        }
        _ekskey(salt, b, P, S);
        /**
       * Calcualtes the next round.
       * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`
       * @inner
       */ function next() {
            if (progressCallback) progressCallback(i / rounds);
            if (i < rounds) {
                var start = Date.now();
                for(; i < rounds;){
                    i = i + 1;
                    _key(b, P, S);
                    _key(salt, P, S);
                    if (Date.now() - start > MAX_EXECUTION_TIME) break;
                }
            } else {
                for(i = 0; i < 64; i++)for(j = 0; j < clen >> 1; j++)_encipher(cdata, j << 1, P, S);
                var ret = [];
                for(i = 0; i < clen; i++)ret.push((cdata[i] >> 24 & 0xff) >>> 0), ret.push((cdata[i] >> 16 & 0xff) >>> 0), ret.push((cdata[i] >> 8 & 0xff) >>> 0), ret.push((cdata[i] & 0xff) >>> 0);
                if (callback) {
                    callback(null, ret);
                    return;
                } else return ret;
            }
            if (callback) nextTick(next);
        }
        // Async
        if (typeof callback !== "undefined") {
            next();
        // Sync
        } else {
            var res;
            while(true)if (typeof (res = next()) !== "undefined") return res || [];
        }
    }
    /**
     * Internally hashes a password.
     * @param {string} password Password to hash
     * @param {?string} salt Salt to use, actually never null
     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,
     *  hashing is performed synchronously.
     *  @param {function(number)=} progressCallback Callback called with the current progress
     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`
     * @inner
     */ function _hash(password, salt, callback, progressCallback) {
        var err;
        if (typeof password !== "string" || typeof salt !== "string") {
            err = Error("Invalid string / salt: Not a string");
            if (callback) {
                nextTick(callback.bind(this, err));
                return;
            } else throw err;
        }
        // Validate the salt
        var minor, offset;
        if (salt.charAt(0) !== "$" || salt.charAt(1) !== "2") {
            err = Error("Invalid salt version: " + salt.substring(0, 2));
            if (callback) {
                nextTick(callback.bind(this, err));
                return;
            } else throw err;
        }
        if (salt.charAt(2) === "$") minor = String.fromCharCode(0), offset = 3;
        else {
            minor = salt.charAt(2);
            if (minor !== "a" && minor !== "b" && minor !== "y" || salt.charAt(3) !== "$") {
                err = Error("Invalid salt revision: " + salt.substring(2, 4));
                if (callback) {
                    nextTick(callback.bind(this, err));
                    return;
                } else throw err;
            }
            offset = 4;
        }
        // Extract number of rounds
        if (salt.charAt(offset + 2) > "$") {
            err = Error("Missing salt rounds");
            if (callback) {
                nextTick(callback.bind(this, err));
                return;
            } else throw err;
        }
        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10, r2 = parseInt(salt.substring(offset + 1, offset + 2), 10), rounds = r1 + r2, real_salt = salt.substring(offset + 3, offset + 25);
        password += minor >= "a" ? "\x00" : "";
        var passwordb = utf8Array(password), saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);
        /**
       * Finishes hashing.
       * @param {Array.<number>} bytes Byte array
       * @returns {string}
       * @inner
       */ function finish(bytes) {
            var res = [];
            res.push("$2");
            if (minor >= "a") res.push(minor);
            res.push("$");
            if (rounds < 10) res.push("0");
            res.push(rounds.toString());
            res.push("$");
            res.push(base64_encode(saltb, saltb.length));
            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));
            return res.join("");
        }
        // Sync
        if (typeof callback == "undefined") return finish(_crypt(passwordb, saltb, rounds));
        else {
            _crypt(passwordb, saltb, rounds, function(err, bytes) {
                if (err) callback(err, null);
                else callback(null, finish(bytes));
            }, progressCallback);
        }
    }
    /**
     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.
     * @function
     * @param {!Array.<number>} bytes Byte array
     * @param {number} length Maximum input length
     * @returns {string}
     */ function encodeBase64(bytes, length) {
        return base64_encode(bytes, length);
    }
    /**
     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.
     * @function
     * @param {string} string String to decode
     * @param {number} length Maximum output length
     * @returns {!Array.<number>}
     */ function decodeBase64(string, length) {
        return base64_decode(string, length);
    }
    var _default = _exports.default = {
        setRandomFallback,
        genSaltSync,
        genSalt,
        hashSync,
        hash,
        compareSync,
        compare,
        getRounds,
        getSalt,
        truncates,
        encodeBase64,
        decodeBase64
    };
});
}}),
"[project]/node_modules/regenerator-runtime/runtime.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Copyright (c) 2014-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var runtime = function(exports) {
    "use strict";
    var Op = Object.prototype;
    var hasOwn = Op.hasOwnProperty;
    var defineProperty = Object.defineProperty || function(obj, key, desc) {
        obj[key] = desc.value;
    };
    var undefined; // More compressible than void 0.
    var $Symbol = typeof Symbol === "function" ? Symbol : {};
    var iteratorSymbol = $Symbol.iterator || "@@iterator";
    var asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator";
    var toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag";
    function define(obj, key, value) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
        return obj[key];
    }
    try {
        // IE 8 has a broken Object.defineProperty that only works on DOM objects.
        define({}, "");
    } catch (err) {
        define = function(obj, key, value) {
            return obj[key] = value;
        };
    }
    function wrap(innerFn, outerFn, self, tryLocsList) {
        // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.
        var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;
        var generator = Object.create(protoGenerator.prototype);
        var context = new Context(tryLocsList || []);
        // The ._invoke method unifies the implementations of the .next,
        // .throw, and .return methods.
        defineProperty(generator, "_invoke", {
            value: makeInvokeMethod(innerFn, self, context)
        });
        return generator;
    }
    exports.wrap = wrap;
    // Try/catch helper to minimize deoptimizations. Returns a completion
    // record like context.tryEntries[i].completion. This interface could
    // have been (and was previously) designed to take a closure to be
    // invoked without arguments, but in all the cases we care about we
    // already have an existing method we want to call, so there's no need
    // to create a new function object. We can even get away with assuming
    // the method takes exactly one argument, since that happens to be true
    // in every case, so we don't have to touch the arguments object. The
    // only additional allocation required is the completion record, which
    // has a stable shape and so hopefully should be cheap to allocate.
    function tryCatch(fn, obj, arg) {
        try {
            return {
                type: "normal",
                arg: fn.call(obj, arg)
            };
        } catch (err) {
            return {
                type: "throw",
                arg: err
            };
        }
    }
    var GenStateSuspendedStart = "suspendedStart";
    var GenStateSuspendedYield = "suspendedYield";
    var GenStateExecuting = "executing";
    var GenStateCompleted = "completed";
    // Returning this object from the innerFn has the same effect as
    // breaking out of the dispatch switch statement.
    var ContinueSentinel = {};
    // Dummy constructor functions that we use as the .constructor and
    // .constructor.prototype properties for functions that return Generator
    // objects. For full spec compliance, you may wish to configure your
    // minifier not to mangle the names of these two functions.
    function Generator() {}
    function GeneratorFunction() {}
    function GeneratorFunctionPrototype() {}
    // This is a polyfill for %IteratorPrototype% for environments that
    // don't natively support it.
    var IteratorPrototype = {};
    define(IteratorPrototype, iteratorSymbol, function() {
        return this;
    });
    var getProto = Object.getPrototypeOf;
    var NativeIteratorPrototype = getProto && getProto(getProto(values([])));
    if (NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {
        // This environment has a native %IteratorPrototype%; use it instead
        // of the polyfill.
        IteratorPrototype = NativeIteratorPrototype;
    }
    var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);
    GeneratorFunction.prototype = GeneratorFunctionPrototype;
    defineProperty(Gp, "constructor", {
        value: GeneratorFunctionPrototype,
        configurable: true
    });
    defineProperty(GeneratorFunctionPrototype, "constructor", {
        value: GeneratorFunction,
        configurable: true
    });
    GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction");
    // Helper for defining the .next, .throw, and .return methods of the
    // Iterator interface in terms of a single ._invoke method.
    function defineIteratorMethods(prototype) {
        [
            "next",
            "throw",
            "return"
        ].forEach(function(method) {
            define(prototype, method, function(arg) {
                return this._invoke(method, arg);
            });
        });
    }
    exports.isGeneratorFunction = function(genFun) {
        var ctor = typeof genFun === "function" && genFun.constructor;
        return ctor ? ctor === GeneratorFunction || // For the native GeneratorFunction constructor, the best we can
        // do is to check its .name property.
        (ctor.displayName || ctor.name) === "GeneratorFunction" : false;
    };
    exports.mark = function(genFun) {
        if (Object.setPrototypeOf) {
            Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);
        } else {
            genFun.__proto__ = GeneratorFunctionPrototype;
            define(genFun, toStringTagSymbol, "GeneratorFunction");
        }
        genFun.prototype = Object.create(Gp);
        return genFun;
    };
    // Within the body of any async function, `await x` is transformed to
    // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test
    // `hasOwn.call(value, "__await")` to determine if the yielded value is
    // meant to be awaited.
    exports.awrap = function(arg) {
        return {
            __await: arg
        };
    };
    function AsyncIterator(generator, PromiseImpl) {
        function invoke(method, arg, resolve, reject) {
            var record = tryCatch(generator[method], generator, arg);
            if (record.type === "throw") {
                reject(record.arg);
            } else {
                var result = record.arg;
                var value = result.value;
                if (value && typeof value === "object" && hasOwn.call(value, "__await")) {
                    return PromiseImpl.resolve(value.__await).then(function(value) {
                        invoke("next", value, resolve, reject);
                    }, function(err) {
                        invoke("throw", err, resolve, reject);
                    });
                }
                return PromiseImpl.resolve(value).then(function(unwrapped) {
                    // When a yielded Promise is resolved, its final value becomes
                    // the .value of the Promise<{value,done}> result for the
                    // current iteration.
                    result.value = unwrapped;
                    resolve(result);
                }, function(error) {
                    // If a rejected Promise was yielded, throw the rejection back
                    // into the async generator function so it can be handled there.
                    return invoke("throw", error, resolve, reject);
                });
            }
        }
        var previousPromise;
        function enqueue(method, arg) {
            function callInvokeWithMethodAndArg() {
                return new PromiseImpl(function(resolve, reject) {
                    invoke(method, arg, resolve, reject);
                });
            }
            return previousPromise = // If enqueue has been called before, then we want to wait until
            // all previous Promises have been resolved before calling invoke,
            // so that results are always delivered in the correct order. If
            // enqueue has not been called before, then it is important to
            // call invoke immediately, without waiting on a callback to fire,
            // so that the async generator function has the opportunity to do
            // any necessary setup in a predictable way. This predictability
            // is why the Promise constructor synchronously invokes its
            // executor callback, and why async functions synchronously
            // execute code before the first await. Since we implement simple
            // async functions in terms of async generators, it is especially
            // important to get this right, even though it requires care.
            previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, // Avoid propagating failures to Promises returned by later
            // invocations of the iterator.
            callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();
        }
        // Define the unified helper method that is used to implement .next,
        // .throw, and .return (see defineIteratorMethods).
        defineProperty(this, "_invoke", {
            value: enqueue
        });
    }
    defineIteratorMethods(AsyncIterator.prototype);
    define(AsyncIterator.prototype, asyncIteratorSymbol, function() {
        return this;
    });
    exports.AsyncIterator = AsyncIterator;
    // Note that simple async functions are implemented on top of
    // AsyncIterator objects; they just return a Promise for the value of
    // the final result produced by the iterator.
    exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {
        if (PromiseImpl === void 0) PromiseImpl = Promise;
        var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);
        return exports.isGeneratorFunction(outerFn) ? iter // If outerFn is a generator, return the full iterator.
         : iter.next().then(function(result) {
            return result.done ? result.value : iter.next();
        });
    };
    function makeInvokeMethod(innerFn, self, context) {
        var state = GenStateSuspendedStart;
        return function invoke(method, arg) {
            if (state === GenStateExecuting) {
                throw new Error("Generator is already running");
            }
            if (state === GenStateCompleted) {
                if (method === "throw") {
                    throw arg;
                }
                // Be forgiving, per 25.3.3.3.3 of the spec:
                // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume
                return doneResult();
            }
            context.method = method;
            context.arg = arg;
            while(true){
                var delegate = context.delegate;
                if (delegate) {
                    var delegateResult = maybeInvokeDelegate(delegate, context);
                    if (delegateResult) {
                        if (delegateResult === ContinueSentinel) continue;
                        return delegateResult;
                    }
                }
                if (context.method === "next") {
                    // Setting context._sent for legacy support of Babel's
                    // function.sent implementation.
                    context.sent = context._sent = context.arg;
                } else if (context.method === "throw") {
                    if (state === GenStateSuspendedStart) {
                        state = GenStateCompleted;
                        throw context.arg;
                    }
                    context.dispatchException(context.arg);
                } else if (context.method === "return") {
                    context.abrupt("return", context.arg);
                }
                state = GenStateExecuting;
                var record = tryCatch(innerFn, self, context);
                if (record.type === "normal") {
                    // If an exception is thrown from innerFn, we leave state ===
                    // GenStateExecuting and loop back for another invocation.
                    state = context.done ? GenStateCompleted : GenStateSuspendedYield;
                    if (record.arg === ContinueSentinel) {
                        continue;
                    }
                    return {
                        value: record.arg,
                        done: context.done
                    };
                } else if (record.type === "throw") {
                    state = GenStateCompleted;
                    // Dispatch the exception by looping back around to the
                    // context.dispatchException(context.arg) call above.
                    context.method = "throw";
                    context.arg = record.arg;
                }
            }
        };
    }
    // Call delegate.iterator[context.method](context.arg) and handle the
    // result, either by returning a { value, done } result from the
    // delegate iterator, or by modifying context.method and context.arg,
    // setting context.delegate to null, and returning the ContinueSentinel.
    function maybeInvokeDelegate(delegate, context) {
        var methodName = context.method;
        var method = delegate.iterator[methodName];
        if (method === undefined) {
            // A .throw or .return when the delegate iterator has no .throw
            // method, or a missing .next mehtod, always terminate the
            // yield* loop.
            context.delegate = null;
            // Note: ["return"] must be used for ES3 parsing compatibility.
            if (methodName === "throw" && delegate.iterator["return"]) {
                // If the delegate iterator has a return method, give it a
                // chance to clean up.
                context.method = "return";
                context.arg = undefined;
                maybeInvokeDelegate(delegate, context);
                if (context.method === "throw") {
                    // If maybeInvokeDelegate(context) changed context.method from
                    // "return" to "throw", let that override the TypeError below.
                    return ContinueSentinel;
                }
            }
            if (methodName !== "return") {
                context.method = "throw";
                context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method");
            }
            return ContinueSentinel;
        }
        var record = tryCatch(method, delegate.iterator, context.arg);
        if (record.type === "throw") {
            context.method = "throw";
            context.arg = record.arg;
            context.delegate = null;
            return ContinueSentinel;
        }
        var info = record.arg;
        if (!info) {
            context.method = "throw";
            context.arg = new TypeError("iterator result is not an object");
            context.delegate = null;
            return ContinueSentinel;
        }
        if (info.done) {
            // Assign the result of the finished delegate to the temporary
            // variable specified by delegate.resultName (see delegateYield).
            context[delegate.resultName] = info.value;
            // Resume execution at the desired location (see delegateYield).
            context.next = delegate.nextLoc;
            // If context.method was "throw" but the delegate handled the
            // exception, let the outer generator proceed normally. If
            // context.method was "next", forget context.arg since it has been
            // "consumed" by the delegate iterator. If context.method was
            // "return", allow the original .return call to continue in the
            // outer generator.
            if (context.method !== "return") {
                context.method = "next";
                context.arg = undefined;
            }
        } else {
            // Re-yield the result returned by the delegate method.
            return info;
        }
        // The delegate iterator is finished, so forget it and continue with
        // the outer generator.
        context.delegate = null;
        return ContinueSentinel;
    }
    // Define Generator.prototype.{next,throw,return} in terms of the
    // unified ._invoke helper method.
    defineIteratorMethods(Gp);
    define(Gp, toStringTagSymbol, "Generator");
    // A Generator should always return itself as the iterator object when the
    // @@iterator function is called on it. Some browsers' implementations of the
    // iterator prototype chain incorrectly implement this, causing the Generator
    // object to not be returned from this call. This ensures that doesn't happen.
    // See https://github.com/facebook/regenerator/issues/274 for more details.
    define(Gp, iteratorSymbol, function() {
        return this;
    });
    define(Gp, "toString", function() {
        return "[object Generator]";
    });
    function pushTryEntry(locs) {
        var entry = {
            tryLoc: locs[0]
        };
        if (1 in locs) {
            entry.catchLoc = locs[1];
        }
        if (2 in locs) {
            entry.finallyLoc = locs[2];
            entry.afterLoc = locs[3];
        }
        this.tryEntries.push(entry);
    }
    function resetTryEntry(entry) {
        var record = entry.completion || {};
        record.type = "normal";
        delete record.arg;
        entry.completion = record;
    }
    function Context(tryLocsList) {
        // The root entry object (effectively a try statement without a catch
        // or a finally block) gives us a place to store values thrown from
        // locations where there is no enclosing try statement.
        this.tryEntries = [
            {
                tryLoc: "root"
            }
        ];
        tryLocsList.forEach(pushTryEntry, this);
        this.reset(true);
    }
    exports.keys = function(val) {
        var object = Object(val);
        var keys = [];
        for(var key in object){
            keys.push(key);
        }
        keys.reverse();
        // Rather than returning an object with a next method, we keep
        // things simple and return the next function itself.
        return function next() {
            while(keys.length){
                var key = keys.pop();
                if (key in object) {
                    next.value = key;
                    next.done = false;
                    return next;
                }
            }
            // To avoid creating an additional object, we just hang the .value
            // and .done properties off the next function object itself. This
            // also ensures that the minifier will not anonymize the function.
            next.done = true;
            return next;
        };
    };
    function values(iterable) {
        if (iterable) {
            var iteratorMethod = iterable[iteratorSymbol];
            if (iteratorMethod) {
                return iteratorMethod.call(iterable);
            }
            if (typeof iterable.next === "function") {
                return iterable;
            }
            if (!isNaN(iterable.length)) {
                var i = -1, next = function next() {
                    while(++i < iterable.length){
                        if (hasOwn.call(iterable, i)) {
                            next.value = iterable[i];
                            next.done = false;
                            return next;
                        }
                    }
                    next.value = undefined;
                    next.done = true;
                    return next;
                };
                return next.next = next;
            }
        }
        // Return an iterator with no values.
        return {
            next: doneResult
        };
    }
    exports.values = values;
    function doneResult() {
        return {
            value: undefined,
            done: true
        };
    }
    Context.prototype = {
        constructor: Context,
        reset: function(skipTempReset) {
            this.prev = 0;
            this.next = 0;
            // Resetting context._sent for legacy support of Babel's
            // function.sent implementation.
            this.sent = this._sent = undefined;
            this.done = false;
            this.delegate = null;
            this.method = "next";
            this.arg = undefined;
            this.tryEntries.forEach(resetTryEntry);
            if (!skipTempReset) {
                for(var name in this){
                    // Not sure about the optimal order of these conditions:
                    if (name.charAt(0) === "t" && hasOwn.call(this, name) && !isNaN(+name.slice(1))) {
                        this[name] = undefined;
                    }
                }
            }
        },
        stop: function() {
            this.done = true;
            var rootEntry = this.tryEntries[0];
            var rootRecord = rootEntry.completion;
            if (rootRecord.type === "throw") {
                throw rootRecord.arg;
            }
            return this.rval;
        },
        dispatchException: function(exception) {
            if (this.done) {
                throw exception;
            }
            var context = this;
            function handle(loc, caught) {
                record.type = "throw";
                record.arg = exception;
                context.next = loc;
                if (caught) {
                    // If the dispatched exception was caught by a catch block,
                    // then let that catch block handle the exception normally.
                    context.method = "next";
                    context.arg = undefined;
                }
                return !!caught;
            }
            for(var i = this.tryEntries.length - 1; i >= 0; --i){
                var entry = this.tryEntries[i];
                var record = entry.completion;
                if (entry.tryLoc === "root") {
                    // Exception thrown outside of any try block that could handle
                    // it, so set the completion value of the entire function to
                    // throw the exception.
                    return handle("end");
                }
                if (entry.tryLoc <= this.prev) {
                    var hasCatch = hasOwn.call(entry, "catchLoc");
                    var hasFinally = hasOwn.call(entry, "finallyLoc");
                    if (hasCatch && hasFinally) {
                        if (this.prev < entry.catchLoc) {
                            return handle(entry.catchLoc, true);
                        } else if (this.prev < entry.finallyLoc) {
                            return handle(entry.finallyLoc);
                        }
                    } else if (hasCatch) {
                        if (this.prev < entry.catchLoc) {
                            return handle(entry.catchLoc, true);
                        }
                    } else if (hasFinally) {
                        if (this.prev < entry.finallyLoc) {
                            return handle(entry.finallyLoc);
                        }
                    } else {
                        throw new Error("try statement without catch or finally");
                    }
                }
            }
        },
        abrupt: function(type, arg) {
            for(var i = this.tryEntries.length - 1; i >= 0; --i){
                var entry = this.tryEntries[i];
                if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) {
                    var finallyEntry = entry;
                    break;
                }
            }
            if (finallyEntry && (type === "break" || type === "continue") && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc) {
                // Ignore the finally entry if control is not jumping to a
                // location outside the try/catch block.
                finallyEntry = null;
            }
            var record = finallyEntry ? finallyEntry.completion : {};
            record.type = type;
            record.arg = arg;
            if (finallyEntry) {
                this.method = "next";
                this.next = finallyEntry.finallyLoc;
                return ContinueSentinel;
            }
            return this.complete(record);
        },
        complete: function(record, afterLoc) {
            if (record.type === "throw") {
                throw record.arg;
            }
            if (record.type === "break" || record.type === "continue") {
                this.next = record.arg;
            } else if (record.type === "return") {
                this.rval = this.arg = record.arg;
                this.method = "return";
                this.next = "end";
            } else if (record.type === "normal" && afterLoc) {
                this.next = afterLoc;
            }
            return ContinueSentinel;
        },
        finish: function(finallyLoc) {
            for(var i = this.tryEntries.length - 1; i >= 0; --i){
                var entry = this.tryEntries[i];
                if (entry.finallyLoc === finallyLoc) {
                    this.complete(entry.completion, entry.afterLoc);
                    resetTryEntry(entry);
                    return ContinueSentinel;
                }
            }
        },
        "catch": function(tryLoc) {
            for(var i = this.tryEntries.length - 1; i >= 0; --i){
                var entry = this.tryEntries[i];
                if (entry.tryLoc === tryLoc) {
                    var record = entry.completion;
                    if (record.type === "throw") {
                        var thrown = record.arg;
                        resetTryEntry(entry);
                    }
                    return thrown;
                }
            }
            // The context.catch method must only be called with a location
            // argument that corresponds to a known catch block.
            throw new Error("illegal catch attempt");
        },
        delegateYield: function(iterable, resultName, nextLoc) {
            this.delegate = {
                iterator: values(iterable),
                resultName: resultName,
                nextLoc: nextLoc
            };
            if (this.method === "next") {
                // Deliberately forget the last sent value so that we don't
                // accidentally pass it on to the delegate.
                this.arg = undefined;
            }
            return ContinueSentinel;
        }
    };
    // Regardless of whether this script is executing as a CommonJS module
    // or not, return the runtime object so that we can declare the variable
    // regeneratorRuntime in the outer scope, which allows this module to be
    // injected easily by `bin/regenerator --include-runtime script.js`.
    return exports;
}(// If this script is executing as a CommonJS module, use module.exports
// as the regeneratorRuntime namespace. Otherwise create a new empty
// object. Either way, the resulting object will be used to initialize
// the regeneratorRuntime variable at the top of this file.
("TURBOPACK compile-time truthy", 1) ? module.exports : ("TURBOPACK unreachable", undefined));
try {
    regeneratorRuntime = runtime;
} catch (accidentalStrictMode) {
    // This module should not be running in strict mode, so the above
    // assignment should always work unless something is misconfigured. Just
    // in case runtime.js accidentally runs in strict mode, in modern engines
    // we can explicitly access globalThis. In older engines we can escape
    // strict mode using a global Function call. This could conceivably fail
    // if a Content Security Policy forbids using Function, but in that case
    // the proper solution is to fix the accidental strict mode problem. If
    // you've misconfigured your bundler to force strict mode and applied a
    // CSP to forbid Function, and you're not willing to fix either of those
    // problems, please detail your unique predicament in a GitHub issue.
    if (typeof globalThis === "object") {
        globalThis.regeneratorRuntime = runtime;
    } else {
        Function("r", "regeneratorRuntime = r")(runtime);
    }
}
}}),
"[project]/node_modules/tesseract.js/src/utils/getId.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = (prefix, cnt)=>`${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`;
}}),
"[project]/node_modules/tesseract.js/src/createJob.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const getId = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/getId.js [app-route] (ecmascript)");
let jobCounter = 0;
module.exports = ({ id: _id, action, payload = {} })=>{
    let id = _id;
    if (typeof id === 'undefined') {
        id = getId('Job', jobCounter);
        jobCounter += 1;
    }
    return {
        id,
        action,
        payload
    };
};
}}),
"[project]/node_modules/tesseract.js/src/utils/log.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
let logging = false;
exports.logging = logging;
exports.setLogging = (_logging)=>{
    logging = _logging;
};
exports.log = (...args)=>logging ? console.log.apply(this, args) : null;
}}),
"[project]/node_modules/tesseract.js/src/createScheduler.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const createJob = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/createJob.js [app-route] (ecmascript)");
const { log } = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/log.js [app-route] (ecmascript)");
const getId = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/getId.js [app-route] (ecmascript)");
let schedulerCounter = 0;
module.exports = ()=>{
    const id = getId('Scheduler', schedulerCounter);
    const workers = {};
    const runningWorkers = {};
    let jobQueue = [];
    schedulerCounter += 1;
    const getQueueLen = ()=>jobQueue.length;
    const getNumWorkers = ()=>Object.keys(workers).length;
    const dequeue = ()=>{
        if (jobQueue.length !== 0) {
            const wIds = Object.keys(workers);
            for(let i = 0; i < wIds.length; i += 1){
                if (typeof runningWorkers[wIds[i]] === 'undefined') {
                    jobQueue[0](workers[wIds[i]]);
                    break;
                }
            }
        }
    };
    const queue = (action, payload)=>new Promise((resolve, reject)=>{
            const job = createJob({
                action,
                payload
            });
            jobQueue.push(async (w)=>{
                jobQueue.shift();
                runningWorkers[w.id] = job;
                try {
                    resolve(await w[action].apply(this, [
                        ...payload,
                        job.id
                    ]));
                } catch (err) {
                    reject(err);
                } finally{
                    delete runningWorkers[w.id];
                    dequeue();
                }
            });
            log(`[${id}]: Add ${job.id} to JobQueue`);
            log(`[${id}]: JobQueue length=${jobQueue.length}`);
            dequeue();
        });
    const addWorker = (w)=>{
        workers[w.id] = w;
        log(`[${id}]: Add ${w.id}`);
        log(`[${id}]: Number of workers=${getNumWorkers()}`);
        dequeue();
        return w.id;
    };
    const addJob = async (action, ...payload)=>{
        if (getNumWorkers() === 0) {
            throw Error(`[${id}]: You need to have at least one worker before adding jobs`);
        }
        return queue(action, payload);
    };
    const terminate = async ()=>{
        Object.keys(workers).forEach(async (wid)=>{
            await workers[wid].terminate();
        });
        jobQueue = [];
    };
    return {
        addWorker,
        addJob,
        terminate,
        getQueueLen,
        getNumWorkers
    };
};
}}),
"[project]/node_modules/tesseract.js/src/utils/getEnvironment.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = (key)=>{
    const env = {};
    if (typeof WorkerGlobalScope !== 'undefined') {
        env.type = 'webworker';
    } else if (typeof document === 'object') {
        env.type = 'browser';
    } else if (typeof process === 'object' && ("TURBOPACK compile-time value", "function") === 'function') {
        env.type = 'node';
    }
    if (typeof key === 'undefined') {
        return env;
    }
    return env[key];
};
}}),
"[project]/node_modules/tesseract.js/src/utils/resolvePaths.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const isBrowser = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/getEnvironment.js [app-route] (ecmascript)")('type') === 'browser';
const resolveURL = isBrowser ? (s)=>new URL(s, window.location.href).href : (s)=>s; // eslint-disable-line
module.exports = (options)=>{
    const opts = {
        ...options
    };
    [
        'corePath',
        'workerPath',
        'langPath'
    ].forEach((key)=>{
        if (options[key]) {
            opts[key] = resolveURL(opts[key]);
        }
    });
    return opts;
};
}}),
"[project]/node_modules/tesseract.js/src/constants/OEM.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/*
 * OEM = OCR Engine Mode, and there are 4 possible modes.
 *
 * By default tesseract.js uses LSTM_ONLY mode.
 *
 */ module.exports = {
    TESSERACT_ONLY: 0,
    LSTM_ONLY: 1,
    TESSERACT_LSTM_COMBINED: 2,
    DEFAULT: 3
};
}}),
"[project]/node_modules/tesseract.js/src/constants/defaultOptions.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = {
    /*
   * Use BlobURL for worker script by default
   * TODO: remove this option
   *
   */ workerBlobURL: true,
    logger: ()=>{}
};
}}),
"[project]/node_modules/tesseract.js/src/worker/node/defaultOptions.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
const defaultOptions = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/constants/defaultOptions.js [app-route] (ecmascript)");
/*
 * Default options for node worker
 */ module.exports = {
    ...defaultOptions,
    workerPath: path.join(__dirname, '..', '..', 'worker-script', 'node', 'index.js')
};
}}),
"[project]/node_modules/tesseract.js/src/worker/node/spawnWorker.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const { Worker } = __turbopack_context__.r("[externals]/worker_threads [external] (worker_threads, cjs)");
/**
 * spawnWorker
 *
 * @name spawnWorker
 * @function fork a new process in node
 * @access public
 */ module.exports = ({ workerPath })=>new Worker(workerPath);
}}),
"[project]/node_modules/tesseract.js/src/worker/node/terminateWorker.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * terminateWorker
 *
 * @name terminateWorker
 * @function kill worker
 * @access public
 */ module.exports = (worker)=>{
    worker.terminate();
};
}}),
"[project]/node_modules/tesseract.js/src/worker/node/onMessage.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = (worker, handler)=>{
    worker.on('message', handler);
};
}}),
"[project]/node_modules/tesseract.js/src/worker/node/send.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 * send
 *
 * @name send
 * @function send packet to worker and create a job
 * @access public
 */ module.exports = async (worker, packet)=>{
    worker.postMessage(packet);
};
}}),
"[project]/node_modules/tesseract.js/src/worker/node/loadImage.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
// Use built-in fetch if available, otherwise fallback to node-fetch
const fetch = global.fetch || __turbopack_context__.r("[project]/node_modules/node-fetch/lib/index.mjs [app-route] (ecmascript)");
const isURL = __turbopack_context__.r("[project]/node_modules/is-url/index.js [app-route] (ecmascript)");
const readFile = util.promisify(fs.readFile);
/**
 * loadImage
 *
 * @name loadImage
 * @function load image from different source
 * @access public
 */ module.exports = async (image)=>{
    let data = image;
    if (typeof image === 'undefined') {
        return image;
    }
    if (typeof image === 'string') {
        if (isURL(image) || image.startsWith('moz-extension://') || image.startsWith('chrome-extension://') || image.startsWith('file://')) {
            const resp = await fetch(image);
            data = await resp.arrayBuffer();
        } else if (/data:image\/([a-zA-Z]*);base64,([^"]*)/.test(image)) {
            data = Buffer.from(image.split(',')[1], 'base64');
        } else {
            data = await readFile(image);
        }
    } else if (Buffer.isBuffer(image)) {
        data = image;
    }
    return new Uint8Array(data);
};
}}),
"[project]/node_modules/tesseract.js/src/worker/node/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 *
 * Tesseract Worker impl. for node (using child_process)
 *
 * @fileoverview Tesseract Worker impl. for node
 * <AUTHOR> Kwok <<EMAIL>>
 * <AUTHOR> Webster <<EMAIL>>
 * <AUTHOR> Wu <<EMAIL>>
 */ const defaultOptions = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/defaultOptions.js [app-route] (ecmascript)");
const spawnWorker = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/spawnWorker.js [app-route] (ecmascript)");
const terminateWorker = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/terminateWorker.js [app-route] (ecmascript)");
const onMessage = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/onMessage.js [app-route] (ecmascript)");
const send = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/send.js [app-route] (ecmascript)");
const loadImage = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/loadImage.js [app-route] (ecmascript)");
module.exports = {
    defaultOptions,
    spawnWorker,
    terminateWorker,
    onMessage,
    send,
    loadImage
};
}}),
"[project]/node_modules/tesseract.js/src/createWorker.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const resolvePaths = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/resolvePaths.js [app-route] (ecmascript)");
const createJob = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/createJob.js [app-route] (ecmascript)");
const { log } = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/log.js [app-route] (ecmascript)");
const getId = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/getId.js [app-route] (ecmascript)");
const OEM = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/constants/OEM.js [app-route] (ecmascript)");
const { defaultOptions, spawnWorker, terminateWorker, onMessage, loadImage, send } = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/worker/node/index.js [app-route] (ecmascript)");
let workerCounter = 0;
module.exports = async (langs = 'eng', oem = OEM.LSTM_ONLY, _options = {}, config = {})=>{
    const id = getId('Worker', workerCounter);
    const { logger, errorHandler, ...options } = resolvePaths({
        ...defaultOptions,
        ..._options
    });
    const promises = {};
    // Current langs, oem, and config file.
    // Used if the user ever re-initializes the worker using `worker.reinitialize`.
    const currentLangs = typeof langs === 'string' ? langs.split('+') : langs;
    let currentOem = oem;
    let currentConfig = config;
    const lstmOnlyCore = [
        OEM.DEFAULT,
        OEM.LSTM_ONLY
    ].includes(oem) && !options.legacyCore;
    let workerResReject;
    let workerResResolve;
    const workerRes = new Promise((resolve, reject)=>{
        workerResResolve = resolve;
        workerResReject = reject;
    });
    const workerError = (event)=>{
        workerResReject(event.message);
    };
    let worker = spawnWorker(options);
    worker.onerror = workerError;
    workerCounter += 1;
    const startJob = ({ id: jobId, action, payload })=>new Promise((resolve, reject)=>{
            log(`[${id}]: Start ${jobId}, action=${action}`);
            // Using both `action` and `jobId` in case user provides non-unique `jobId`.
            const promiseId = `${action}-${jobId}`;
            promises[promiseId] = {
                resolve,
                reject
            };
            send(worker, {
                workerId: id,
                jobId,
                action,
                payload
            });
        });
    const load = ()=>console.warn('`load` is depreciated and should be removed from code (workers now come pre-loaded)');
    const loadInternal = (jobId)=>startJob(createJob({
            id: jobId,
            action: 'load',
            payload: {
                options: {
                    lstmOnly: lstmOnlyCore,
                    corePath: options.corePath,
                    logging: options.logging
                }
            }
        }));
    const writeText = (path, text, jobId)=>startJob(createJob({
            id: jobId,
            action: 'FS',
            payload: {
                method: 'writeFile',
                args: [
                    path,
                    text
                ]
            }
        }));
    const readText = (path, jobId)=>startJob(createJob({
            id: jobId,
            action: 'FS',
            payload: {
                method: 'readFile',
                args: [
                    path,
                    {
                        encoding: 'utf8'
                    }
                ]
            }
        }));
    const removeFile = (path, jobId)=>startJob(createJob({
            id: jobId,
            action: 'FS',
            payload: {
                method: 'unlink',
                args: [
                    path
                ]
            }
        }));
    const FS = (method, args, jobId)=>startJob(createJob({
            id: jobId,
            action: 'FS',
            payload: {
                method,
                args
            }
        }));
    const loadLanguageInternal = (_langs, jobId)=>startJob(createJob({
            id: jobId,
            action: 'loadLanguage',
            payload: {
                langs: _langs,
                options: {
                    langPath: options.langPath,
                    dataPath: options.dataPath,
                    cachePath: options.cachePath,
                    cacheMethod: options.cacheMethod,
                    gzip: options.gzip,
                    lstmOnly: [
                        OEM.DEFAULT,
                        OEM.LSTM_ONLY
                    ].includes(currentOem) && !options.legacyLang
                }
            }
        }));
    const initializeInternal = (_langs, _oem, _config, jobId)=>startJob(createJob({
            id: jobId,
            action: 'initialize',
            payload: {
                langs: _langs,
                oem: _oem,
                config: _config
            }
        }));
    const reinitialize = (langs = 'eng', oem, config, jobId)=>{
        if (lstmOnlyCore && [
            OEM.TESSERACT_ONLY,
            OEM.TESSERACT_LSTM_COMBINED
        ].includes(oem)) throw Error('Legacy model requested but code missing.');
        const _oem = oem || currentOem;
        currentOem = _oem;
        const _config = config || currentConfig;
        currentConfig = _config;
        // Only load langs that are not already loaded.
        // This logic fails if the user downloaded the LSTM-only English data for a language
        // and then uses `worker.reinitialize` to switch to the Legacy engine.
        // However, the correct data will still be downloaded after initialization fails
        // and this can be avoided entirely if the user loads the correct data ahead of time.
        const langsArr = typeof langs === 'string' ? langs.split('+') : langs;
        const _langs = langsArr.filter((x)=>!currentLangs.includes(x));
        currentLangs.push(..._langs);
        if (_langs.length > 0) {
            return loadLanguageInternal(_langs, jobId).then(()=>initializeInternal(langs, _oem, _config, jobId));
        }
        return initializeInternal(langs, _oem, _config, jobId);
    };
    const setParameters = (params = {}, jobId)=>startJob(createJob({
            id: jobId,
            action: 'setParameters',
            payload: {
                params
            }
        }));
    const recognize = async (image, opts = {}, output = {
        text: true
    }, jobId)=>startJob(createJob({
            id: jobId,
            action: 'recognize',
            payload: {
                image: await loadImage(image),
                options: opts,
                output
            }
        }));
    const detect = async (image, jobId)=>{
        if (lstmOnlyCore) throw Error('`worker.detect` requires Legacy model, which was not loaded.');
        return startJob(createJob({
            id: jobId,
            action: 'detect',
            payload: {
                image: await loadImage(image)
            }
        }));
    };
    const terminate = async ()=>{
        if (worker !== null) {
            /*
      await startJob(createJob({
        id: jobId,
        action: 'terminate',
      }));
      */ terminateWorker(worker);
            worker = null;
        }
        return Promise.resolve();
    };
    onMessage(worker, ({ workerId, jobId, status, action, data })=>{
        const promiseId = `${action}-${jobId}`;
        if (status === 'resolve') {
            log(`[${workerId}]: Complete ${jobId}`);
            promises[promiseId].resolve({
                jobId,
                data
            });
            delete promises[promiseId];
        } else if (status === 'reject') {
            promises[promiseId].reject(data);
            delete promises[promiseId];
            if (action === 'load') workerResReject(data);
            if (errorHandler) {
                errorHandler(data);
            } else {
                throw Error(data);
            }
        } else if (status === 'progress') {
            logger({
                ...data,
                userJobId: jobId
            });
        }
    });
    const resolveObj = {
        id,
        worker,
        load,
        writeText,
        readText,
        removeFile,
        FS,
        reinitialize,
        setParameters,
        recognize,
        detect,
        terminate
    };
    loadInternal().then(()=>loadLanguageInternal(langs)).then(()=>initializeInternal(langs, oem, config)).then(()=>workerResResolve(resolveObj)).catch(()=>{});
    return workerRes;
};
}}),
"[project]/node_modules/tesseract.js/src/Tesseract.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const createWorker = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/createWorker.js [app-route] (ecmascript)");
const recognize = async (image, langs, options)=>{
    const worker = await createWorker(langs, 1, options);
    return worker.recognize(image).finally(async ()=>{
        await worker.terminate();
    });
};
const detect = async (image, options)=>{
    const worker = await createWorker('osd', 0, options);
    return worker.detect(image).finally(async ()=>{
        await worker.terminate();
    });
};
module.exports = {
    recognize,
    detect
};
}}),
"[project]/node_modules/tesseract.js/src/constants/languages.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/*
 * languages with existing tesseract traineddata
 * https://tesseract-ocr.github.io/tessdoc/Data-Files#data-files-for-version-400-november-29-2016
 */ /**
 * @typedef {object} Languages
 * @property {string} AFR Afrikaans
 * @property {string} AMH Amharic
 * @property {string} ARA Arabic
 * @property {string} ASM Assamese
 * @property {string} AZE Azerbaijani
 * @property {string} AZE_CYRL Azerbaijani - Cyrillic
 * @property {string} BEL Belarusian
 * @property {string} BEN Bengali
 * @property {string} BOD Tibetan
 * @property {string} BOS Bosnian
 * @property {string} BUL Bulgarian
 * @property {string} CAT Catalan; Valencian
 * @property {string} CEB Cebuano
 * @property {string} CES Czech
 * @property {string} CHI_SIM Chinese - Simplified
 * @property {string} CHI_TRA Chinese - Traditional
 * @property {string} CHR Cherokee
 * @property {string} CYM Welsh
 * @property {string} DAN Danish
 * @property {string} DEU German
 * @property {string} DZO Dzongkha
 * @property {string} ELL Greek, Modern (1453-)
 * @property {string} ENG English
 * @property {string} ENM English, Middle (1100-1500)
 * @property {string} EPO Esperanto
 * @property {string} EST Estonian
 * @property {string} EUS Basque
 * @property {string} FAS Persian
 * @property {string} FIN Finnish
 * @property {string} FRA French
 * @property {string} FRK German Fraktur
 * @property {string} FRM French, Middle (ca. 1400-1600)
 * @property {string} GLE Irish
 * @property {string} GLG Galician
 * @property {string} GRC Greek, Ancient (-1453)
 * @property {string} GUJ Gujarati
 * @property {string} HAT Haitian; Haitian Creole
 * @property {string} HEB Hebrew
 * @property {string} HIN Hindi
 * @property {string} HRV Croatian
 * @property {string} HUN Hungarian
 * @property {string} IKU Inuktitut
 * @property {string} IND Indonesian
 * @property {string} ISL Icelandic
 * @property {string} ITA Italian
 * @property {string} ITA_OLD Italian - Old
 * @property {string} JAV Javanese
 * @property {string} JPN Japanese
 * @property {string} KAN Kannada
 * @property {string} KAT Georgian
 * @property {string} KAT_OLD Georgian - Old
 * @property {string} KAZ Kazakh
 * @property {string} KHM Central Khmer
 * @property {string} KIR Kirghiz; Kyrgyz
 * @property {string} KOR Korean
 * @property {string} KUR Kurdish
 * @property {string} LAO Lao
 * @property {string} LAT Latin
 * @property {string} LAV Latvian
 * @property {string} LIT Lithuanian
 * @property {string} MAL Malayalam
 * @property {string} MAR Marathi
 * @property {string} MKD Macedonian
 * @property {string} MLT Maltese
 * @property {string} MSA Malay
 * @property {string} MYA Burmese
 * @property {string} NEP Nepali
 * @property {string} NLD Dutch; Flemish
 * @property {string} NOR Norwegian
 * @property {string} ORI Oriya
 * @property {string} PAN Panjabi; Punjabi
 * @property {string} POL Polish
 * @property {string} POR Portuguese
 * @property {string} PUS Pushto; Pashto
 * @property {string} RON Romanian; Moldavian; Moldovan
 * @property {string} RUS Russian
 * @property {string} SAN Sanskrit
 * @property {string} SIN Sinhala; Sinhalese
 * @property {string} SLK Slovak
 * @property {string} SLV Slovenian
 * @property {string} SPA Spanish; Castilian
 * @property {string} SPA_OLD Spanish; Castilian - Old
 * @property {string} SQI Albanian
 * @property {string} SRP Serbian
 * @property {string} SRP_LATN Serbian - Latin
 * @property {string} SWA Swahili
 * @property {string} SWE Swedish
 * @property {string} SYR Syriac
 * @property {string} TAM Tamil
 * @property {string} TEL Telugu
 * @property {string} TGK Tajik
 * @property {string} TGL Tagalog
 * @property {string} THA Thai
 * @property {string} TIR Tigrinya
 * @property {string} TUR Turkish
 * @property {string} UIG Uighur; Uyghur
 * @property {string} UKR Ukrainian
 * @property {string} URD Urdu
 * @property {string} UZB Uzbek
 * @property {string} UZB_CYRL Uzbek - Cyrillic
 * @property {string} VIE Vietnamese
 * @property {string} YID Yiddish
 */ /**
  * @type {Languages}
  */ module.exports = {
    AFR: 'afr',
    AMH: 'amh',
    ARA: 'ara',
    ASM: 'asm',
    AZE: 'aze',
    AZE_CYRL: 'aze_cyrl',
    BEL: 'bel',
    BEN: 'ben',
    BOD: 'bod',
    BOS: 'bos',
    BUL: 'bul',
    CAT: 'cat',
    CEB: 'ceb',
    CES: 'ces',
    CHI_SIM: 'chi_sim',
    CHI_TRA: 'chi_tra',
    CHR: 'chr',
    CYM: 'cym',
    DAN: 'dan',
    DEU: 'deu',
    DZO: 'dzo',
    ELL: 'ell',
    ENG: 'eng',
    ENM: 'enm',
    EPO: 'epo',
    EST: 'est',
    EUS: 'eus',
    FAS: 'fas',
    FIN: 'fin',
    FRA: 'fra',
    FRK: 'frk',
    FRM: 'frm',
    GLE: 'gle',
    GLG: 'glg',
    GRC: 'grc',
    GUJ: 'guj',
    HAT: 'hat',
    HEB: 'heb',
    HIN: 'hin',
    HRV: 'hrv',
    HUN: 'hun',
    IKU: 'iku',
    IND: 'ind',
    ISL: 'isl',
    ITA: 'ita',
    ITA_OLD: 'ita_old',
    JAV: 'jav',
    JPN: 'jpn',
    KAN: 'kan',
    KAT: 'kat',
    KAT_OLD: 'kat_old',
    KAZ: 'kaz',
    KHM: 'khm',
    KIR: 'kir',
    KOR: 'kor',
    KUR: 'kur',
    LAO: 'lao',
    LAT: 'lat',
    LAV: 'lav',
    LIT: 'lit',
    MAL: 'mal',
    MAR: 'mar',
    MKD: 'mkd',
    MLT: 'mlt',
    MSA: 'msa',
    MYA: 'mya',
    NEP: 'nep',
    NLD: 'nld',
    NOR: 'nor',
    ORI: 'ori',
    PAN: 'pan',
    POL: 'pol',
    POR: 'por',
    PUS: 'pus',
    RON: 'ron',
    RUS: 'rus',
    SAN: 'san',
    SIN: 'sin',
    SLK: 'slk',
    SLV: 'slv',
    SPA: 'spa',
    SPA_OLD: 'spa_old',
    SQI: 'sqi',
    SRP: 'srp',
    SRP_LATN: 'srp_latn',
    SWA: 'swa',
    SWE: 'swe',
    SYR: 'syr',
    TAM: 'tam',
    TEL: 'tel',
    TGK: 'tgk',
    TGL: 'tgl',
    THA: 'tha',
    TIR: 'tir',
    TUR: 'tur',
    UIG: 'uig',
    UKR: 'ukr',
    URD: 'urd',
    UZB: 'uzb',
    UZB_CYRL: 'uzb_cyrl',
    VIE: 'vie',
    YID: 'yid'
};
}}),
"[project]/node_modules/tesseract.js/src/constants/PSM.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/*
 * PSM = Page Segmentation Mode
 */ module.exports = {
    OSD_ONLY: '0',
    AUTO_OSD: '1',
    AUTO_ONLY: '2',
    AUTO: '3',
    SINGLE_COLUMN: '4',
    SINGLE_BLOCK_VERT_TEXT: '5',
    SINGLE_BLOCK: '6',
    SINGLE_LINE: '7',
    SINGLE_WORD: '8',
    CIRCLE_WORD: '9',
    SINGLE_CHAR: '10',
    SPARSE_TEXT: '11',
    SPARSE_TEXT_OSD: '12',
    RAW_LINE: '13'
};
}}),
"[project]/node_modules/tesseract.js/src/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
/**
 *
 * Entry point for tesseract.js, should be the entry when bundling.
 *
 * @fileoverview entry point for tesseract.js
 * <AUTHOR> Kwok <<EMAIL>>
 * <AUTHOR> Webster <<EMAIL>>
 * <AUTHOR> Wu <<EMAIL>>
 */ __turbopack_context__.r("[project]/node_modules/regenerator-runtime/runtime.js [app-route] (ecmascript)");
const createScheduler = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/createScheduler.js [app-route] (ecmascript)");
const createWorker = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/createWorker.js [app-route] (ecmascript)");
const Tesseract = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/Tesseract.js [app-route] (ecmascript)");
const languages = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/constants/languages.js [app-route] (ecmascript)");
const OEM = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/constants/OEM.js [app-route] (ecmascript)");
const PSM = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/constants/PSM.js [app-route] (ecmascript)");
const { setLogging } = __turbopack_context__.r("[project]/node_modules/tesseract.js/src/utils/log.js [app-route] (ecmascript)");
module.exports = {
    languages,
    OEM,
    PSM,
    createScheduler,
    createWorker,
    setLogging,
    ...Tesseract
};
}}),
"[project]/node_modules/webidl-conversions/lib/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var conversions = {};
module.exports = conversions;
function sign(x) {
    return x < 0 ? -1 : 1;
}
function evenRound(x) {
    // Round x to the nearest integer, choosing the even integer if it lies halfway between two.
    if (x % 1 === 0.5 && (x & 1) === 0) {
        return Math.floor(x);
    } else {
        return Math.round(x);
    }
}
function createNumberConversion(bitLength, typeOpts) {
    if (!typeOpts.unsigned) {
        --bitLength;
    }
    const lowerBound = typeOpts.unsigned ? 0 : -Math.pow(2, bitLength);
    const upperBound = Math.pow(2, bitLength) - 1;
    const moduloVal = typeOpts.moduloBitLength ? Math.pow(2, typeOpts.moduloBitLength) : Math.pow(2, bitLength);
    const moduloBound = typeOpts.moduloBitLength ? Math.pow(2, typeOpts.moduloBitLength - 1) : Math.pow(2, bitLength - 1);
    return function(V, opts) {
        if (!opts) opts = {};
        let x = +V;
        if (opts.enforceRange) {
            if (!Number.isFinite(x)) {
                throw new TypeError("Argument is not a finite number");
            }
            x = sign(x) * Math.floor(Math.abs(x));
            if (x < lowerBound || x > upperBound) {
                throw new TypeError("Argument is not in byte range");
            }
            return x;
        }
        if (!isNaN(x) && opts.clamp) {
            x = evenRound(x);
            if (x < lowerBound) x = lowerBound;
            if (x > upperBound) x = upperBound;
            return x;
        }
        if (!Number.isFinite(x) || x === 0) {
            return 0;
        }
        x = sign(x) * Math.floor(Math.abs(x));
        x = x % moduloVal;
        if (!typeOpts.unsigned && x >= moduloBound) {
            return x - moduloVal;
        } else if (typeOpts.unsigned) {
            if (x < 0) {
                x += moduloVal;
            } else if (x === -0) {
                return 0;
            }
        }
        return x;
    };
}
conversions["void"] = function() {
    return undefined;
};
conversions["boolean"] = function(val) {
    return !!val;
};
conversions["byte"] = createNumberConversion(8, {
    unsigned: false
});
conversions["octet"] = createNumberConversion(8, {
    unsigned: true
});
conversions["short"] = createNumberConversion(16, {
    unsigned: false
});
conversions["unsigned short"] = createNumberConversion(16, {
    unsigned: true
});
conversions["long"] = createNumberConversion(32, {
    unsigned: false
});
conversions["unsigned long"] = createNumberConversion(32, {
    unsigned: true
});
conversions["long long"] = createNumberConversion(32, {
    unsigned: false,
    moduloBitLength: 64
});
conversions["unsigned long long"] = createNumberConversion(32, {
    unsigned: true,
    moduloBitLength: 64
});
conversions["double"] = function(V) {
    const x = +V;
    if (!Number.isFinite(x)) {
        throw new TypeError("Argument is not a finite floating-point value");
    }
    return x;
};
conversions["unrestricted double"] = function(V) {
    const x = +V;
    if (isNaN(x)) {
        throw new TypeError("Argument is NaN");
    }
    return x;
};
// not quite valid, but good enough for JS
conversions["float"] = conversions["double"];
conversions["unrestricted float"] = conversions["unrestricted double"];
conversions["DOMString"] = function(V, opts) {
    if (!opts) opts = {};
    if (opts.treatNullAsEmptyString && V === null) {
        return "";
    }
    return String(V);
};
conversions["ByteString"] = function(V, opts) {
    const x = String(V);
    let c = undefined;
    for(let i = 0; (c = x.codePointAt(i)) !== undefined; ++i){
        if (c > 255) {
            throw new TypeError("Argument is not a valid bytestring");
        }
    }
    return x;
};
conversions["USVString"] = function(V) {
    const S = String(V);
    const n = S.length;
    const U = [];
    for(let i = 0; i < n; ++i){
        const c = S.charCodeAt(i);
        if (c < 0xD800 || c > 0xDFFF) {
            U.push(String.fromCodePoint(c));
        } else if (0xDC00 <= c && c <= 0xDFFF) {
            U.push(String.fromCodePoint(0xFFFD));
        } else {
            if (i === n - 1) {
                U.push(String.fromCodePoint(0xFFFD));
            } else {
                const d = S.charCodeAt(i + 1);
                if (0xDC00 <= d && d <= 0xDFFF) {
                    const a = c & 0x3FF;
                    const b = d & 0x3FF;
                    U.push(String.fromCodePoint((2 << 15) + (2 << 9) * a + b));
                    ++i;
                } else {
                    U.push(String.fromCodePoint(0xFFFD));
                }
            }
        }
    }
    return U.join('');
};
conversions["Date"] = function(V, opts) {
    if (!(V instanceof Date)) {
        throw new TypeError("Argument is not a Date object");
    }
    if (isNaN(V)) {
        return undefined;
    }
    return V;
};
conversions["RegExp"] = function(V, opts) {
    if (!(V instanceof RegExp)) {
        V = new RegExp(V);
    }
    return V;
};
}}),
"[project]/node_modules/whatwg-url/lib/utils.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports.mixin = function mixin(target, source) {
    const keys = Object.getOwnPropertyNames(source);
    for(let i = 0; i < keys.length; ++i){
        Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));
    }
};
module.exports.wrapperSymbol = Symbol("wrapper");
module.exports.implSymbol = Symbol("impl");
module.exports.wrapperForImpl = function(impl) {
    return impl[module.exports.wrapperSymbol];
};
module.exports.implForWrapper = function(wrapper) {
    return wrapper[module.exports.implSymbol];
};
}}),
"[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const punycode = __turbopack_context__.r("[externals]/punycode [external] (punycode, cjs)");
const tr46 = __turbopack_context__.r("[project]/node_modules/tr46/index.js [app-route] (ecmascript)");
const specialSchemes = {
    ftp: 21,
    file: null,
    gopher: 70,
    http: 80,
    https: 443,
    ws: 80,
    wss: 443
};
const failure = Symbol("failure");
function countSymbols(str) {
    return punycode.ucs2.decode(str).length;
}
function at(input, idx) {
    const c = input[idx];
    return isNaN(c) ? undefined : String.fromCodePoint(c);
}
function isASCIIDigit(c) {
    return c >= 0x30 && c <= 0x39;
}
function isASCIIAlpha(c) {
    return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A;
}
function isASCIIAlphanumeric(c) {
    return isASCIIAlpha(c) || isASCIIDigit(c);
}
function isASCIIHex(c) {
    return isASCIIDigit(c) || c >= 0x41 && c <= 0x46 || c >= 0x61 && c <= 0x66;
}
function isSingleDot(buffer) {
    return buffer === "." || buffer.toLowerCase() === "%2e";
}
function isDoubleDot(buffer) {
    buffer = buffer.toLowerCase();
    return buffer === ".." || buffer === "%2e." || buffer === ".%2e" || buffer === "%2e%2e";
}
function isWindowsDriveLetterCodePoints(cp1, cp2) {
    return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);
}
function isWindowsDriveLetterString(string) {
    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === ":" || string[1] === "|");
}
function isNormalizedWindowsDriveLetterString(string) {
    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === ":";
}
function containsForbiddenHostCodePoint(string) {
    return string.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|%|\/|:|\?|@|\[|\\|\]/) !== -1;
}
function containsForbiddenHostCodePointExcludingPercent(string) {
    return string.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|\/|:|\?|@|\[|\\|\]/) !== -1;
}
function isSpecialScheme(scheme) {
    return specialSchemes[scheme] !== undefined;
}
function isSpecial(url) {
    return isSpecialScheme(url.scheme);
}
function defaultPort(scheme) {
    return specialSchemes[scheme];
}
function percentEncode(c) {
    let hex = c.toString(16).toUpperCase();
    if (hex.length === 1) {
        hex = "0" + hex;
    }
    return "%" + hex;
}
function utf8PercentEncode(c) {
    const buf = new Buffer(c);
    let str = "";
    for(let i = 0; i < buf.length; ++i){
        str += percentEncode(buf[i]);
    }
    return str;
}
function utf8PercentDecode(str) {
    const input = new Buffer(str);
    const output = [];
    for(let i = 0; i < input.length; ++i){
        if (input[i] !== 37) {
            output.push(input[i]);
        } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {
            output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));
            i += 2;
        } else {
            output.push(input[i]);
        }
    }
    return new Buffer(output).toString();
}
function isC0ControlPercentEncode(c) {
    return c <= 0x1F || c > 0x7E;
}
const extraPathPercentEncodeSet = new Set([
    32,
    34,
    35,
    60,
    62,
    63,
    96,
    123,
    125
]);
function isPathPercentEncode(c) {
    return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);
}
const extraUserinfoPercentEncodeSet = new Set([
    47,
    58,
    59,
    61,
    64,
    91,
    92,
    93,
    94,
    124
]);
function isUserinfoPercentEncode(c) {
    return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);
}
function percentEncodeChar(c, encodeSetPredicate) {
    const cStr = String.fromCodePoint(c);
    if (encodeSetPredicate(c)) {
        return utf8PercentEncode(cStr);
    }
    return cStr;
}
function parseIPv4Number(input) {
    let R = 10;
    if (input.length >= 2 && input.charAt(0) === "0" && input.charAt(1).toLowerCase() === "x") {
        input = input.substring(2);
        R = 16;
    } else if (input.length >= 2 && input.charAt(0) === "0") {
        input = input.substring(1);
        R = 8;
    }
    if (input === "") {
        return 0;
    }
    const regex = R === 10 ? /[^0-9]/ : R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/;
    if (regex.test(input)) {
        return failure;
    }
    return parseInt(input, R);
}
function parseIPv4(input) {
    const parts = input.split(".");
    if (parts[parts.length - 1] === "") {
        if (parts.length > 1) {
            parts.pop();
        }
    }
    if (parts.length > 4) {
        return input;
    }
    const numbers = [];
    for (const part of parts){
        if (part === "") {
            return input;
        }
        const n = parseIPv4Number(part);
        if (n === failure) {
            return input;
        }
        numbers.push(n);
    }
    for(let i = 0; i < numbers.length - 1; ++i){
        if (numbers[i] > 255) {
            return failure;
        }
    }
    if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {
        return failure;
    }
    let ipv4 = numbers.pop();
    let counter = 0;
    for (const n of numbers){
        ipv4 += n * Math.pow(256, 3 - counter);
        ++counter;
    }
    return ipv4;
}
function serializeIPv4(address) {
    let output = "";
    let n = address;
    for(let i = 1; i <= 4; ++i){
        output = String(n % 256) + output;
        if (i !== 4) {
            output = "." + output;
        }
        n = Math.floor(n / 256);
    }
    return output;
}
function parseIPv6(input) {
    const address = [
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0
    ];
    let pieceIndex = 0;
    let compress = null;
    let pointer = 0;
    input = punycode.ucs2.decode(input);
    if (input[pointer] === 58) {
        if (input[pointer + 1] !== 58) {
            return failure;
        }
        pointer += 2;
        ++pieceIndex;
        compress = pieceIndex;
    }
    while(pointer < input.length){
        if (pieceIndex === 8) {
            return failure;
        }
        if (input[pointer] === 58) {
            if (compress !== null) {
                return failure;
            }
            ++pointer;
            ++pieceIndex;
            compress = pieceIndex;
            continue;
        }
        let value = 0;
        let length = 0;
        while(length < 4 && isASCIIHex(input[pointer])){
            value = value * 0x10 + parseInt(at(input, pointer), 16);
            ++pointer;
            ++length;
        }
        if (input[pointer] === 46) {
            if (length === 0) {
                return failure;
            }
            pointer -= length;
            if (pieceIndex > 6) {
                return failure;
            }
            let numbersSeen = 0;
            while(input[pointer] !== undefined){
                let ipv4Piece = null;
                if (numbersSeen > 0) {
                    if (input[pointer] === 46 && numbersSeen < 4) {
                        ++pointer;
                    } else {
                        return failure;
                    }
                }
                if (!isASCIIDigit(input[pointer])) {
                    return failure;
                }
                while(isASCIIDigit(input[pointer])){
                    const number = parseInt(at(input, pointer));
                    if (ipv4Piece === null) {
                        ipv4Piece = number;
                    } else if (ipv4Piece === 0) {
                        return failure;
                    } else {
                        ipv4Piece = ipv4Piece * 10 + number;
                    }
                    if (ipv4Piece > 255) {
                        return failure;
                    }
                    ++pointer;
                }
                address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;
                ++numbersSeen;
                if (numbersSeen === 2 || numbersSeen === 4) {
                    ++pieceIndex;
                }
            }
            if (numbersSeen !== 4) {
                return failure;
            }
            break;
        } else if (input[pointer] === 58) {
            ++pointer;
            if (input[pointer] === undefined) {
                return failure;
            }
        } else if (input[pointer] !== undefined) {
            return failure;
        }
        address[pieceIndex] = value;
        ++pieceIndex;
    }
    if (compress !== null) {
        let swaps = pieceIndex - compress;
        pieceIndex = 7;
        while(pieceIndex !== 0 && swaps > 0){
            const temp = address[compress + swaps - 1];
            address[compress + swaps - 1] = address[pieceIndex];
            address[pieceIndex] = temp;
            --pieceIndex;
            --swaps;
        }
    } else if (compress === null && pieceIndex !== 8) {
        return failure;
    }
    return address;
}
function serializeIPv6(address) {
    let output = "";
    const seqResult = findLongestZeroSequence(address);
    const compress = seqResult.idx;
    let ignore0 = false;
    for(let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex){
        if (ignore0 && address[pieceIndex] === 0) {
            continue;
        } else if (ignore0) {
            ignore0 = false;
        }
        if (compress === pieceIndex) {
            const separator = pieceIndex === 0 ? "::" : ":";
            output += separator;
            ignore0 = true;
            continue;
        }
        output += address[pieceIndex].toString(16);
        if (pieceIndex !== 7) {
            output += ":";
        }
    }
    return output;
}
function parseHost(input, isSpecialArg) {
    if (input[0] === "[") {
        if (input[input.length - 1] !== "]") {
            return failure;
        }
        return parseIPv6(input.substring(1, input.length - 1));
    }
    if (!isSpecialArg) {
        return parseOpaqueHost(input);
    }
    const domain = utf8PercentDecode(input);
    const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);
    if (asciiDomain === null) {
        return failure;
    }
    if (containsForbiddenHostCodePoint(asciiDomain)) {
        return failure;
    }
    const ipv4Host = parseIPv4(asciiDomain);
    if (typeof ipv4Host === "number" || ipv4Host === failure) {
        return ipv4Host;
    }
    return asciiDomain;
}
function parseOpaqueHost(input) {
    if (containsForbiddenHostCodePointExcludingPercent(input)) {
        return failure;
    }
    let output = "";
    const decoded = punycode.ucs2.decode(input);
    for(let i = 0; i < decoded.length; ++i){
        output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);
    }
    return output;
}
function findLongestZeroSequence(arr) {
    let maxIdx = null;
    let maxLen = 1; // only find elements > 1
    let currStart = null;
    let currLen = 0;
    for(let i = 0; i < arr.length; ++i){
        if (arr[i] !== 0) {
            if (currLen > maxLen) {
                maxIdx = currStart;
                maxLen = currLen;
            }
            currStart = null;
            currLen = 0;
        } else {
            if (currStart === null) {
                currStart = i;
            }
            ++currLen;
        }
    }
    // if trailing zeros
    if (currLen > maxLen) {
        maxIdx = currStart;
        maxLen = currLen;
    }
    return {
        idx: maxIdx,
        len: maxLen
    };
}
function serializeHost(host) {
    if (typeof host === "number") {
        return serializeIPv4(host);
    }
    // IPv6 serializer
    if (host instanceof Array) {
        return "[" + serializeIPv6(host) + "]";
    }
    return host;
}
function trimControlChars(url) {
    return url.replace(/^[\u0000-\u001F\u0020]+|[\u0000-\u001F\u0020]+$/g, "");
}
function trimTabAndNewline(url) {
    return url.replace(/\u0009|\u000A|\u000D/g, "");
}
function shortenPath(url) {
    const path = url.path;
    if (path.length === 0) {
        return;
    }
    if (url.scheme === "file" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {
        return;
    }
    path.pop();
}
function includesCredentials(url) {
    return url.username !== "" || url.password !== "";
}
function cannotHaveAUsernamePasswordPort(url) {
    return url.host === null || url.host === "" || url.cannotBeABaseURL || url.scheme === "file";
}
function isNormalizedWindowsDriveLetter(string) {
    return /^[A-Za-z]:$/.test(string);
}
function URLStateMachine(input, base, encodingOverride, url, stateOverride) {
    this.pointer = 0;
    this.input = input;
    this.base = base || null;
    this.encodingOverride = encodingOverride || "utf-8";
    this.stateOverride = stateOverride;
    this.url = url;
    this.failure = false;
    this.parseError = false;
    if (!this.url) {
        this.url = {
            scheme: "",
            username: "",
            password: "",
            host: null,
            port: null,
            path: [],
            query: null,
            fragment: null,
            cannotBeABaseURL: false
        };
        const res = trimControlChars(this.input);
        if (res !== this.input) {
            this.parseError = true;
        }
        this.input = res;
    }
    const res = trimTabAndNewline(this.input);
    if (res !== this.input) {
        this.parseError = true;
    }
    this.input = res;
    this.state = stateOverride || "scheme start";
    this.buffer = "";
    this.atFlag = false;
    this.arrFlag = false;
    this.passwordTokenSeenFlag = false;
    this.input = punycode.ucs2.decode(this.input);
    for(; this.pointer <= this.input.length; ++this.pointer){
        const c = this.input[this.pointer];
        const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);
        // exec state machine
        const ret = this["parse " + this.state](c, cStr);
        if (!ret) {
            break; // terminate algorithm
        } else if (ret === failure) {
            this.failure = true;
            break;
        }
    }
}
URLStateMachine.prototype["parse scheme start"] = function parseSchemeStart(c, cStr) {
    if (isASCIIAlpha(c)) {
        this.buffer += cStr.toLowerCase();
        this.state = "scheme";
    } else if (!this.stateOverride) {
        this.state = "no scheme";
        --this.pointer;
    } else {
        this.parseError = true;
        return failure;
    }
    return true;
};
URLStateMachine.prototype["parse scheme"] = function parseScheme(c, cStr) {
    if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {
        this.buffer += cStr.toLowerCase();
    } else if (c === 58) {
        if (this.stateOverride) {
            if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {
                return false;
            }
            if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {
                return false;
            }
            if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === "file") {
                return false;
            }
            if (this.url.scheme === "file" && (this.url.host === "" || this.url.host === null)) {
                return false;
            }
        }
        this.url.scheme = this.buffer;
        this.buffer = "";
        if (this.stateOverride) {
            return false;
        }
        if (this.url.scheme === "file") {
            if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {
                this.parseError = true;
            }
            this.state = "file";
        } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {
            this.state = "special relative or authority";
        } else if (isSpecial(this.url)) {
            this.state = "special authority slashes";
        } else if (this.input[this.pointer + 1] === 47) {
            this.state = "path or authority";
            ++this.pointer;
        } else {
            this.url.cannotBeABaseURL = true;
            this.url.path.push("");
            this.state = "cannot-be-a-base-URL path";
        }
    } else if (!this.stateOverride) {
        this.buffer = "";
        this.state = "no scheme";
        this.pointer = -1;
    } else {
        this.parseError = true;
        return failure;
    }
    return true;
};
URLStateMachine.prototype["parse no scheme"] = function parseNoScheme(c) {
    if (this.base === null || this.base.cannotBeABaseURL && c !== 35) {
        return failure;
    } else if (this.base.cannotBeABaseURL && c === 35) {
        this.url.scheme = this.base.scheme;
        this.url.path = this.base.path.slice();
        this.url.query = this.base.query;
        this.url.fragment = "";
        this.url.cannotBeABaseURL = true;
        this.state = "fragment";
    } else if (this.base.scheme === "file") {
        this.state = "file";
        --this.pointer;
    } else {
        this.state = "relative";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse special relative or authority"] = function parseSpecialRelativeOrAuthority(c) {
    if (c === 47 && this.input[this.pointer + 1] === 47) {
        this.state = "special authority ignore slashes";
        ++this.pointer;
    } else {
        this.parseError = true;
        this.state = "relative";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse path or authority"] = function parsePathOrAuthority(c) {
    if (c === 47) {
        this.state = "authority";
    } else {
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse relative"] = function parseRelative(c) {
    this.url.scheme = this.base.scheme;
    if (isNaN(c)) {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice();
        this.url.query = this.base.query;
    } else if (c === 47) {
        this.state = "relative slash";
    } else if (c === 63) {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice();
        this.url.query = "";
        this.state = "query";
    } else if (c === 35) {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice();
        this.url.query = this.base.query;
        this.url.fragment = "";
        this.state = "fragment";
    } else if (isSpecial(this.url) && c === 92) {
        this.parseError = true;
        this.state = "relative slash";
    } else {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice(0, this.base.path.length - 1);
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse relative slash"] = function parseRelativeSlash(c) {
    if (isSpecial(this.url) && (c === 47 || c === 92)) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "special authority ignore slashes";
    } else if (c === 47) {
        this.state = "authority";
    } else {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse special authority slashes"] = function parseSpecialAuthoritySlashes(c) {
    if (c === 47 && this.input[this.pointer + 1] === 47) {
        this.state = "special authority ignore slashes";
        ++this.pointer;
    } else {
        this.parseError = true;
        this.state = "special authority ignore slashes";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse special authority ignore slashes"] = function parseSpecialAuthorityIgnoreSlashes(c) {
    if (c !== 47 && c !== 92) {
        this.state = "authority";
        --this.pointer;
    } else {
        this.parseError = true;
    }
    return true;
};
URLStateMachine.prototype["parse authority"] = function parseAuthority(c, cStr) {
    if (c === 64) {
        this.parseError = true;
        if (this.atFlag) {
            this.buffer = "%40" + this.buffer;
        }
        this.atFlag = true;
        // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars
        const len = countSymbols(this.buffer);
        for(let pointer = 0; pointer < len; ++pointer){
            const codePoint = this.buffer.codePointAt(pointer);
            if (codePoint === 58 && !this.passwordTokenSeenFlag) {
                this.passwordTokenSeenFlag = true;
                continue;
            }
            const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);
            if (this.passwordTokenSeenFlag) {
                this.url.password += encodedCodePoints;
            } else {
                this.url.username += encodedCodePoints;
            }
        }
        this.buffer = "";
    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {
        if (this.atFlag && this.buffer === "") {
            this.parseError = true;
            return failure;
        }
        this.pointer -= countSymbols(this.buffer) + 1;
        this.buffer = "";
        this.state = "host";
    } else {
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse hostname"] = URLStateMachine.prototype["parse host"] = function parseHostName(c, cStr) {
    if (this.stateOverride && this.url.scheme === "file") {
        --this.pointer;
        this.state = "file host";
    } else if (c === 58 && !this.arrFlag) {
        if (this.buffer === "") {
            this.parseError = true;
            return failure;
        }
        const host = parseHost(this.buffer, isSpecial(this.url));
        if (host === failure) {
            return failure;
        }
        this.url.host = host;
        this.buffer = "";
        this.state = "port";
        if (this.stateOverride === "hostname") {
            return false;
        }
    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {
        --this.pointer;
        if (isSpecial(this.url) && this.buffer === "") {
            this.parseError = true;
            return failure;
        } else if (this.stateOverride && this.buffer === "" && (includesCredentials(this.url) || this.url.port !== null)) {
            this.parseError = true;
            return false;
        }
        const host = parseHost(this.buffer, isSpecial(this.url));
        if (host === failure) {
            return failure;
        }
        this.url.host = host;
        this.buffer = "";
        this.state = "path start";
        if (this.stateOverride) {
            return false;
        }
    } else {
        if (c === 91) {
            this.arrFlag = true;
        } else if (c === 93) {
            this.arrFlag = false;
        }
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse port"] = function parsePort(c, cStr) {
    if (isASCIIDigit(c)) {
        this.buffer += cStr;
    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92 || this.stateOverride) {
        if (this.buffer !== "") {
            const port = parseInt(this.buffer);
            if (port > Math.pow(2, 16) - 1) {
                this.parseError = true;
                return failure;
            }
            this.url.port = port === defaultPort(this.url.scheme) ? null : port;
            this.buffer = "";
        }
        if (this.stateOverride) {
            return false;
        }
        this.state = "path start";
        --this.pointer;
    } else {
        this.parseError = true;
        return failure;
    }
    return true;
};
const fileOtherwiseCodePoints = new Set([
    47,
    92,
    63,
    35
]);
URLStateMachine.prototype["parse file"] = function parseFile(c) {
    this.url.scheme = "file";
    if (c === 47 || c === 92) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "file slash";
    } else if (this.base !== null && this.base.scheme === "file") {
        if (isNaN(c)) {
            this.url.host = this.base.host;
            this.url.path = this.base.path.slice();
            this.url.query = this.base.query;
        } else if (c === 63) {
            this.url.host = this.base.host;
            this.url.path = this.base.path.slice();
            this.url.query = "";
            this.state = "query";
        } else if (c === 35) {
            this.url.host = this.base.host;
            this.url.path = this.base.path.slice();
            this.url.query = this.base.query;
            this.url.fragment = "";
            this.state = "fragment";
        } else {
            if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points
            !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) || this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points
            !fileOtherwiseCodePoints.has(this.input[this.pointer + 2])) {
                this.url.host = this.base.host;
                this.url.path = this.base.path.slice();
                shortenPath(this.url);
            } else {
                this.parseError = true;
            }
            this.state = "path";
            --this.pointer;
        }
    } else {
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse file slash"] = function parseFileSlash(c) {
    if (c === 47 || c === 92) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "file host";
    } else {
        if (this.base !== null && this.base.scheme === "file") {
            if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {
                this.url.path.push(this.base.path[0]);
            } else {
                this.url.host = this.base.host;
            }
        }
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse file host"] = function parseFileHost(c, cStr) {
    if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {
        --this.pointer;
        if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {
            this.parseError = true;
            this.state = "path";
        } else if (this.buffer === "") {
            this.url.host = "";
            if (this.stateOverride) {
                return false;
            }
            this.state = "path start";
        } else {
            let host = parseHost(this.buffer, isSpecial(this.url));
            if (host === failure) {
                return failure;
            }
            if (host === "localhost") {
                host = "";
            }
            this.url.host = host;
            if (this.stateOverride) {
                return false;
            }
            this.buffer = "";
            this.state = "path start";
        }
    } else {
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse path start"] = function parsePathStart(c) {
    if (isSpecial(this.url)) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "path";
        if (c !== 47 && c !== 92) {
            --this.pointer;
        }
    } else if (!this.stateOverride && c === 63) {
        this.url.query = "";
        this.state = "query";
    } else if (!this.stateOverride && c === 35) {
        this.url.fragment = "";
        this.state = "fragment";
    } else if (c !== undefined) {
        this.state = "path";
        if (c !== 47) {
            --this.pointer;
        }
    }
    return true;
};
URLStateMachine.prototype["parse path"] = function parsePath(c) {
    if (isNaN(c) || c === 47 || isSpecial(this.url) && c === 92 || !this.stateOverride && (c === 63 || c === 35)) {
        if (isSpecial(this.url) && c === 92) {
            this.parseError = true;
        }
        if (isDoubleDot(this.buffer)) {
            shortenPath(this.url);
            if (c !== 47 && !(isSpecial(this.url) && c === 92)) {
                this.url.path.push("");
            }
        } else if (isSingleDot(this.buffer) && c !== 47 && !(isSpecial(this.url) && c === 92)) {
            this.url.path.push("");
        } else if (!isSingleDot(this.buffer)) {
            if (this.url.scheme === "file" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {
                if (this.url.host !== "" && this.url.host !== null) {
                    this.parseError = true;
                    this.url.host = "";
                }
                this.buffer = this.buffer[0] + ":";
            }
            this.url.path.push(this.buffer);
        }
        this.buffer = "";
        if (this.url.scheme === "file" && (c === undefined || c === 63 || c === 35)) {
            while(this.url.path.length > 1 && this.url.path[0] === ""){
                this.parseError = true;
                this.url.path.shift();
            }
        }
        if (c === 63) {
            this.url.query = "";
            this.state = "query";
        }
        if (c === 35) {
            this.url.fragment = "";
            this.state = "fragment";
        }
    } else {
        // TODO: If c is not a URL code point and not "%", parse error.
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        this.buffer += percentEncodeChar(c, isPathPercentEncode);
    }
    return true;
};
URLStateMachine.prototype["parse cannot-be-a-base-URL path"] = function parseCannotBeABaseURLPath(c) {
    if (c === 63) {
        this.url.query = "";
        this.state = "query";
    } else if (c === 35) {
        this.url.fragment = "";
        this.state = "fragment";
    } else {
        // TODO: Add: not a URL code point
        if (!isNaN(c) && c !== 37) {
            this.parseError = true;
        }
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        if (!isNaN(c)) {
            this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);
        }
    }
    return true;
};
URLStateMachine.prototype["parse query"] = function parseQuery(c, cStr) {
    if (isNaN(c) || !this.stateOverride && c === 35) {
        if (!isSpecial(this.url) || this.url.scheme === "ws" || this.url.scheme === "wss") {
            this.encodingOverride = "utf-8";
        }
        const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead
        for(let i = 0; i < buffer.length; ++i){
            if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 || buffer[i] === 0x3C || buffer[i] === 0x3E) {
                this.url.query += percentEncode(buffer[i]);
            } else {
                this.url.query += String.fromCodePoint(buffer[i]);
            }
        }
        this.buffer = "";
        if (c === 35) {
            this.url.fragment = "";
            this.state = "fragment";
        }
    } else {
        // TODO: If c is not a URL code point and not "%", parse error.
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse fragment"] = function parseFragment(c) {
    if (isNaN(c)) {} else if (c === 0x0) {
        this.parseError = true;
    } else {
        // TODO: If c is not a URL code point and not "%", parse error.
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);
    }
    return true;
};
function serializeURL(url, excludeFragment) {
    let output = url.scheme + ":";
    if (url.host !== null) {
        output += "//";
        if (url.username !== "" || url.password !== "") {
            output += url.username;
            if (url.password !== "") {
                output += ":" + url.password;
            }
            output += "@";
        }
        output += serializeHost(url.host);
        if (url.port !== null) {
            output += ":" + url.port;
        }
    } else if (url.host === null && url.scheme === "file") {
        output += "//";
    }
    if (url.cannotBeABaseURL) {
        output += url.path[0];
    } else {
        for (const string of url.path){
            output += "/" + string;
        }
    }
    if (url.query !== null) {
        output += "?" + url.query;
    }
    if (!excludeFragment && url.fragment !== null) {
        output += "#" + url.fragment;
    }
    return output;
}
function serializeOrigin(tuple) {
    let result = tuple.scheme + "://";
    result += serializeHost(tuple.host);
    if (tuple.port !== null) {
        result += ":" + tuple.port;
    }
    return result;
}
module.exports.serializeURL = serializeURL;
module.exports.serializeURLOrigin = function(url) {
    // https://url.spec.whatwg.org/#concept-url-origin
    switch(url.scheme){
        case "blob":
            try {
                return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));
            } catch (e) {
                // serializing an opaque origin returns "null"
                return "null";
            }
        case "ftp":
        case "gopher":
        case "http":
        case "https":
        case "ws":
        case "wss":
            return serializeOrigin({
                scheme: url.scheme,
                host: url.host,
                port: url.port
            });
        case "file":
            // spec says "exercise to the reader", chrome says "file://"
            return "file://";
        default:
            // serializing an opaque origin returns "null"
            return "null";
    }
};
module.exports.basicURLParse = function(input, options) {
    if (options === undefined) {
        options = {};
    }
    const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);
    if (usm.failure) {
        return "failure";
    }
    return usm.url;
};
module.exports.setTheUsername = function(url, username) {
    url.username = "";
    const decoded = punycode.ucs2.decode(username);
    for(let i = 0; i < decoded.length; ++i){
        url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);
    }
};
module.exports.setThePassword = function(url, password) {
    url.password = "";
    const decoded = punycode.ucs2.decode(password);
    for(let i = 0; i < decoded.length; ++i){
        url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);
    }
};
module.exports.serializeHost = serializeHost;
module.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;
module.exports.serializeInteger = function(integer) {
    return String(integer);
};
module.exports.parseURL = function(input, options) {
    if (options === undefined) {
        options = {};
    }
    // We don't handle blobs, so this just delegates:
    return module.exports.basicURLParse(input, {
        baseURL: options.baseURL,
        encodingOverride: options.encodingOverride
    });
};
}}),
"[project]/node_modules/whatwg-url/lib/URL-impl.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const usm = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)");
exports.implementation = class URLImpl {
    constructor(constructorArgs){
        const url = constructorArgs[0];
        const base = constructorArgs[1];
        let parsedBase = null;
        if (base !== undefined) {
            parsedBase = usm.basicURLParse(base);
            if (parsedBase === "failure") {
                throw new TypeError("Invalid base URL");
            }
        }
        const parsedURL = usm.basicURLParse(url, {
            baseURL: parsedBase
        });
        if (parsedURL === "failure") {
            throw new TypeError("Invalid URL");
        }
        this._url = parsedURL;
    // TODO: query stuff
    }
    get href() {
        return usm.serializeURL(this._url);
    }
    set href(v) {
        const parsedURL = usm.basicURLParse(v);
        if (parsedURL === "failure") {
            throw new TypeError("Invalid URL");
        }
        this._url = parsedURL;
    }
    get origin() {
        return usm.serializeURLOrigin(this._url);
    }
    get protocol() {
        return this._url.scheme + ":";
    }
    set protocol(v) {
        usm.basicURLParse(v + ":", {
            url: this._url,
            stateOverride: "scheme start"
        });
    }
    get username() {
        return this._url.username;
    }
    set username(v) {
        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {
            return;
        }
        usm.setTheUsername(this._url, v);
    }
    get password() {
        return this._url.password;
    }
    set password(v) {
        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {
            return;
        }
        usm.setThePassword(this._url, v);
    }
    get host() {
        const url = this._url;
        if (url.host === null) {
            return "";
        }
        if (url.port === null) {
            return usm.serializeHost(url.host);
        }
        return usm.serializeHost(url.host) + ":" + usm.serializeInteger(url.port);
    }
    set host(v) {
        if (this._url.cannotBeABaseURL) {
            return;
        }
        usm.basicURLParse(v, {
            url: this._url,
            stateOverride: "host"
        });
    }
    get hostname() {
        if (this._url.host === null) {
            return "";
        }
        return usm.serializeHost(this._url.host);
    }
    set hostname(v) {
        if (this._url.cannotBeABaseURL) {
            return;
        }
        usm.basicURLParse(v, {
            url: this._url,
            stateOverride: "hostname"
        });
    }
    get port() {
        if (this._url.port === null) {
            return "";
        }
        return usm.serializeInteger(this._url.port);
    }
    set port(v) {
        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {
            return;
        }
        if (v === "") {
            this._url.port = null;
        } else {
            usm.basicURLParse(v, {
                url: this._url,
                stateOverride: "port"
            });
        }
    }
    get pathname() {
        if (this._url.cannotBeABaseURL) {
            return this._url.path[0];
        }
        if (this._url.path.length === 0) {
            return "";
        }
        return "/" + this._url.path.join("/");
    }
    set pathname(v) {
        if (this._url.cannotBeABaseURL) {
            return;
        }
        this._url.path = [];
        usm.basicURLParse(v, {
            url: this._url,
            stateOverride: "path start"
        });
    }
    get search() {
        if (this._url.query === null || this._url.query === "") {
            return "";
        }
        return "?" + this._url.query;
    }
    set search(v) {
        // TODO: query stuff
        const url = this._url;
        if (v === "") {
            url.query = null;
            return;
        }
        const input = v[0] === "?" ? v.substring(1) : v;
        url.query = "";
        usm.basicURLParse(input, {
            url,
            stateOverride: "query"
        });
    }
    get hash() {
        if (this._url.fragment === null || this._url.fragment === "") {
            return "";
        }
        return "#" + this._url.fragment;
    }
    set hash(v) {
        if (v === "") {
            this._url.fragment = null;
            return;
        }
        const input = v[0] === "#" ? v.substring(1) : v;
        this._url.fragment = "";
        usm.basicURLParse(input, {
            url: this._url,
            stateOverride: "fragment"
        });
    }
    toJSON() {
        return this.href;
    }
};
}}),
"[project]/node_modules/whatwg-url/lib/URL.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const conversions = __turbopack_context__.r("[project]/node_modules/webidl-conversions/lib/index.js [app-route] (ecmascript)");
const utils = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/utils.js [app-route] (ecmascript)");
const Impl = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/URL-impl.js [app-route] (ecmascript)");
const impl = utils.implSymbol;
function URL(url) {
    if (!this || this[impl] || !(this instanceof URL)) {
        throw new TypeError("Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");
    }
    if (arguments.length < 1) {
        throw new TypeError("Failed to construct 'URL': 1 argument required, but only " + arguments.length + " present.");
    }
    const args = [];
    for(let i = 0; i < arguments.length && i < 2; ++i){
        args[i] = arguments[i];
    }
    args[0] = conversions["USVString"](args[0]);
    if (args[1] !== undefined) {
        args[1] = conversions["USVString"](args[1]);
    }
    module.exports.setup(this, args);
}
URL.prototype.toJSON = function toJSON() {
    if (!this || !module.exports.is(this)) {
        throw new TypeError("Illegal invocation");
    }
    const args = [];
    for(let i = 0; i < arguments.length && i < 0; ++i){
        args[i] = arguments[i];
    }
    return this[impl].toJSON.apply(this[impl], args);
};
Object.defineProperty(URL.prototype, "href", {
    get () {
        return this[impl].href;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].href = V;
    },
    enumerable: true,
    configurable: true
});
URL.prototype.toString = function() {
    if (!this || !module.exports.is(this)) {
        throw new TypeError("Illegal invocation");
    }
    return this.href;
};
Object.defineProperty(URL.prototype, "origin", {
    get () {
        return this[impl].origin;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "protocol", {
    get () {
        return this[impl].protocol;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].protocol = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "username", {
    get () {
        return this[impl].username;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].username = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "password", {
    get () {
        return this[impl].password;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].password = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "host", {
    get () {
        return this[impl].host;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].host = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "hostname", {
    get () {
        return this[impl].hostname;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].hostname = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "port", {
    get () {
        return this[impl].port;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].port = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "pathname", {
    get () {
        return this[impl].pathname;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].pathname = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "search", {
    get () {
        return this[impl].search;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].search = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "hash", {
    get () {
        return this[impl].hash;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].hash = V;
    },
    enumerable: true,
    configurable: true
});
module.exports = {
    is (obj) {
        return !!obj && obj[impl] instanceof Impl.implementation;
    },
    create (constructorArgs, privateData) {
        let obj = Object.create(URL.prototype);
        this.setup(obj, constructorArgs, privateData);
        return obj;
    },
    setup (obj, constructorArgs, privateData) {
        if (!privateData) privateData = {};
        privateData.wrapper = obj;
        obj[impl] = new Impl.implementation(constructorArgs, privateData);
        obj[impl][utils.wrapperSymbol] = obj;
    },
    interface: URL,
    expose: {
        Window: {
            URL: URL
        },
        Worker: {
            URL: URL
        }
    }
};
}}),
"[project]/node_modules/whatwg-url/lib/public-api.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
exports.URL = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/URL.js [app-route] (ecmascript)").interface;
exports.serializeURL = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeURL;
exports.serializeURLOrigin = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeURLOrigin;
exports.basicURLParse = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").basicURLParse;
exports.setTheUsername = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").setTheUsername;
exports.setThePassword = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").setThePassword;
exports.serializeHost = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeHost;
exports.serializeInteger = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeInteger;
exports.parseURL = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").parseURL;
}}),
"[project]/node_modules/node-fetch/lib/index.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AbortError": (()=>AbortError),
    "FetchError": (()=>FetchError),
    "Headers": (()=>Headers),
    "Request": (()=>Request),
    "Response": (()=>Response),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/stream [external] (stream, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/http [external] (http, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/url [external] (url, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$url$2f$lib$2f$public$2d$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/whatwg-url/lib/public-api.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/https [external] (https, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/zlib [external] (zlib, cjs)");
;
;
;
;
;
;
// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js
// fix for "Readable" isn't a named export issue
const Readable = __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable;
const BUFFER = Symbol('buffer');
const TYPE = Symbol('type');
class Blob {
    constructor(){
        this[TYPE] = '';
        const blobParts = arguments[0];
        const options = arguments[1];
        const buffers = [];
        let size = 0;
        if (blobParts) {
            const a = blobParts;
            const length = Number(a.length);
            for(let i = 0; i < length; i++){
                const element = a[i];
                let buffer;
                if (element instanceof Buffer) {
                    buffer = element;
                } else if (ArrayBuffer.isView(element)) {
                    buffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);
                } else if (element instanceof ArrayBuffer) {
                    buffer = Buffer.from(element);
                } else if (element instanceof Blob) {
                    buffer = element[BUFFER];
                } else {
                    buffer = Buffer.from(typeof element === 'string' ? element : String(element));
                }
                size += buffer.length;
                buffers.push(buffer);
            }
        }
        this[BUFFER] = Buffer.concat(buffers);
        let type = options && options.type !== undefined && String(options.type).toLowerCase();
        if (type && !/[^\u0020-\u007E]/.test(type)) {
            this[TYPE] = type;
        }
    }
    get size() {
        return this[BUFFER].length;
    }
    get type() {
        return this[TYPE];
    }
    text() {
        return Promise.resolve(this[BUFFER].toString());
    }
    arrayBuffer() {
        const buf = this[BUFFER];
        const ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);
        return Promise.resolve(ab);
    }
    stream() {
        const readable = new Readable();
        readable._read = function() {};
        readable.push(this[BUFFER]);
        readable.push(null);
        return readable;
    }
    toString() {
        return '[object Blob]';
    }
    slice() {
        const size = this.size;
        const start = arguments[0];
        const end = arguments[1];
        let relativeStart, relativeEnd;
        if (start === undefined) {
            relativeStart = 0;
        } else if (start < 0) {
            relativeStart = Math.max(size + start, 0);
        } else {
            relativeStart = Math.min(start, size);
        }
        if (end === undefined) {
            relativeEnd = size;
        } else if (end < 0) {
            relativeEnd = Math.max(size + end, 0);
        } else {
            relativeEnd = Math.min(end, size);
        }
        const span = Math.max(relativeEnd - relativeStart, 0);
        const buffer = this[BUFFER];
        const slicedBuffer = buffer.slice(relativeStart, relativeStart + span);
        const blob = new Blob([], {
            type: arguments[2]
        });
        blob[BUFFER] = slicedBuffer;
        return blob;
    }
}
Object.defineProperties(Blob.prototype, {
    size: {
        enumerable: true
    },
    type: {
        enumerable: true
    },
    slice: {
        enumerable: true
    }
});
Object.defineProperty(Blob.prototype, Symbol.toStringTag, {
    value: 'Blob',
    writable: false,
    enumerable: false,
    configurable: true
});
/**
 * fetch-error.js
 *
 * FetchError interface for operational errors
 */ /**
 * Create FetchError instance
 *
 * @param   String      message      Error message for human
 * @param   String      type         Error type for machine
 * @param   String      systemError  For Node.js system error
 * @return  FetchError
 */ function FetchError(message, type, systemError) {
    Error.call(this, message);
    this.message = message;
    this.type = type;
    // when err.type is `system`, err.code contains system error code
    if (systemError) {
        this.code = this.errno = systemError.code;
    }
    // hide custom error implementation details from end-users
    Error.captureStackTrace(this, this.constructor);
}
FetchError.prototype = Object.create(Error.prototype);
FetchError.prototype.constructor = FetchError;
FetchError.prototype.name = 'FetchError';
let convert;
try {
    convert = (()=>{
        const e = new Error("Cannot find module 'encoding'");
        e.code = 'MODULE_NOT_FOUND';
        throw e;
    })().convert;
} catch (e) {}
const INTERNALS = Symbol('Body internals');
// fix an issue where "PassThrough" isn't a named export for node <10
const PassThrough = __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].PassThrough;
/**
 * Body mixin
 *
 * Ref: https://fetch.spec.whatwg.org/#body
 *
 * @param   Stream  body  Readable stream
 * @param   Object  opts  Response options
 * @return  Void
 */ function Body(body) {
    var _this = this;
    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}, _ref$size = _ref.size;
    let size = _ref$size === undefined ? 0 : _ref$size;
    var _ref$timeout = _ref.timeout;
    let timeout = _ref$timeout === undefined ? 0 : _ref$timeout;
    if (body == null) {
        // body is undefined or null
        body = null;
    } else if (isURLSearchParams(body)) {
        // body is a URLSearchParams
        body = Buffer.from(body.toString());
    } else if (isBlob(body)) ;
    else if (Buffer.isBuffer(body)) ;
    else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {
        // body is ArrayBuffer
        body = Buffer.from(body);
    } else if (ArrayBuffer.isView(body)) {
        // body is ArrayBufferView
        body = Buffer.from(body.buffer, body.byteOffset, body.byteLength);
    } else if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"]) ;
    else {
        // none of the above
        // coerce to string then buffer
        body = Buffer.from(String(body));
    }
    this[INTERNALS] = {
        body,
        disturbed: false,
        error: null
    };
    this.size = size;
    this.timeout = timeout;
    if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"]) {
        body.on('error', function(err) {
            const error = err.name === 'AbortError' ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, 'system', err);
            _this[INTERNALS].error = error;
        });
    }
}
Body.prototype = {
    get body () {
        return this[INTERNALS].body;
    },
    get bodyUsed () {
        return this[INTERNALS].disturbed;
    },
    /**
  * Decode response as ArrayBuffer
  *
  * @return  Promise
  */ arrayBuffer () {
        return consumeBody.call(this).then(function(buf) {
            return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);
        });
    },
    /**
  * Return raw response as Blob
  *
  * @return Promise
  */ blob () {
        let ct = this.headers && this.headers.get('content-type') || '';
        return consumeBody.call(this).then(function(buf) {
            return Object.assign(// Prevent copying
            new Blob([], {
                type: ct.toLowerCase()
            }), {
                [BUFFER]: buf
            });
        });
    },
    /**
  * Decode response as json
  *
  * @return  Promise
  */ json () {
        var _this2 = this;
        return consumeBody.call(this).then(function(buffer) {
            try {
                return JSON.parse(buffer.toString());
            } catch (err) {
                return Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, 'invalid-json'));
            }
        });
    },
    /**
  * Decode response as text
  *
  * @return  Promise
  */ text () {
        return consumeBody.call(this).then(function(buffer) {
            return buffer.toString();
        });
    },
    /**
  * Decode response as buffer (non-spec api)
  *
  * @return  Promise
  */ buffer () {
        return consumeBody.call(this);
    },
    /**
  * Decode response as text, while automatically detecting the encoding and
  * trying to decode to UTF-8 (non-spec api)
  *
  * @return  Promise
  */ textConverted () {
        var _this3 = this;
        return consumeBody.call(this).then(function(buffer) {
            return convertBody(buffer, _this3.headers);
        });
    }
};
// In browsers, all properties are enumerable.
Object.defineProperties(Body.prototype, {
    body: {
        enumerable: true
    },
    bodyUsed: {
        enumerable: true
    },
    arrayBuffer: {
        enumerable: true
    },
    blob: {
        enumerable: true
    },
    json: {
        enumerable: true
    },
    text: {
        enumerable: true
    }
});
Body.mixIn = function(proto) {
    for (const name of Object.getOwnPropertyNames(Body.prototype)){
        // istanbul ignore else: future proof
        if (!(name in proto)) {
            const desc = Object.getOwnPropertyDescriptor(Body.prototype, name);
            Object.defineProperty(proto, name, desc);
        }
    }
};
/**
 * Consume and convert an entire Body to a Buffer.
 *
 * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body
 *
 * @return  Promise
 */ function consumeBody() {
    var _this4 = this;
    if (this[INTERNALS].disturbed) {
        return Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));
    }
    this[INTERNALS].disturbed = true;
    if (this[INTERNALS].error) {
        return Body.Promise.reject(this[INTERNALS].error);
    }
    let body = this.body;
    // body is null
    if (body === null) {
        return Body.Promise.resolve(Buffer.alloc(0));
    }
    // body is blob
    if (isBlob(body)) {
        body = body.stream();
    }
    // body is buffer
    if (Buffer.isBuffer(body)) {
        return Body.Promise.resolve(body);
    }
    // istanbul ignore if: should never happen
    if (!(body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"])) {
        return Body.Promise.resolve(Buffer.alloc(0));
    }
    // body is stream
    // get ready to actually consume the body
    let accum = [];
    let accumBytes = 0;
    let abort = false;
    return new Body.Promise(function(resolve, reject) {
        let resTimeout;
        // allow timeout on slow response body
        if (_this4.timeout) {
            resTimeout = setTimeout(function() {
                abort = true;
                reject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, 'body-timeout'));
            }, _this4.timeout);
        }
        // handle stream errors
        body.on('error', function(err) {
            if (err.name === 'AbortError') {
                // if the request was aborted, reject with this Error
                abort = true;
                reject(err);
            } else {
                // other errors, such as incorrect content-encoding
                reject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, 'system', err));
            }
        });
        body.on('data', function(chunk) {
            if (abort || chunk === null) {
                return;
            }
            if (_this4.size && accumBytes + chunk.length > _this4.size) {
                abort = true;
                reject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, 'max-size'));
                return;
            }
            accumBytes += chunk.length;
            accum.push(chunk);
        });
        body.on('end', function() {
            if (abort) {
                return;
            }
            clearTimeout(resTimeout);
            try {
                resolve(Buffer.concat(accum, accumBytes));
            } catch (err) {
                // handle streams that have accumulated too much data (issue #414)
                reject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, 'system', err));
            }
        });
    });
}
/**
 * Detect buffer encoding and convert to target encoding
 * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding
 *
 * @param   Buffer  buffer    Incoming buffer
 * @param   String  encoding  Target encoding
 * @return  String
 */ function convertBody(buffer, headers) {
    if (typeof convert !== 'function') {
        throw new Error('The package `encoding` must be installed to use the textConverted() function');
    }
    const ct = headers.get('content-type');
    let charset = 'utf-8';
    let res, str;
    // header
    if (ct) {
        res = /charset=([^;]*)/i.exec(ct);
    }
    // no charset in content type, peek at response body for at most 1024 bytes
    str = buffer.slice(0, 1024).toString();
    // html5
    if (!res && str) {
        res = /<meta.+?charset=(['"])(.+?)\1/i.exec(str);
    }
    // html4
    if (!res && str) {
        res = /<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(str);
        if (!res) {
            res = /<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(str);
            if (res) {
                res.pop(); // drop last quote
            }
        }
        if (res) {
            res = /charset=(.*)/i.exec(res.pop());
        }
    }
    // xml
    if (!res && str) {
        res = /<\?xml.+?encoding=(['"])(.+?)\1/i.exec(str);
    }
    // found charset
    if (res) {
        charset = res.pop();
        // prevent decode issues when sites use incorrect encoding
        // ref: https://hsivonen.fi/encoding-menu/
        if (charset === 'gb2312' || charset === 'gbk') {
            charset = 'gb18030';
        }
    }
    // turn raw buffers into a single utf-8 buffer
    return convert(buffer, 'UTF-8', charset).toString();
}
/**
 * Detect a URLSearchParams object
 * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143
 *
 * @param   Object  obj     Object to detect by type or brand
 * @return  String
 */ function isURLSearchParams(obj) {
    // Duck-typing as a necessary condition.
    if (typeof obj !== 'object' || typeof obj.append !== 'function' || typeof obj.delete !== 'function' || typeof obj.get !== 'function' || typeof obj.getAll !== 'function' || typeof obj.has !== 'function' || typeof obj.set !== 'function') {
        return false;
    }
    // Brand-checking and more duck-typing as optional condition.
    return obj.constructor.name === 'URLSearchParams' || Object.prototype.toString.call(obj) === '[object URLSearchParams]' || typeof obj.sort === 'function';
}
/**
 * Check if `obj` is a W3C `Blob` object (which `File` inherits from)
 * @param  {*} obj
 * @return {boolean}
 */ function isBlob(obj) {
    return typeof obj === 'object' && typeof obj.arrayBuffer === 'function' && typeof obj.type === 'string' && typeof obj.stream === 'function' && typeof obj.constructor === 'function' && typeof obj.constructor.name === 'string' && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);
}
/**
 * Clone body given Res/Req instance
 *
 * @param   Mixed  instance  Response or Request instance
 * @return  Mixed
 */ function clone(instance) {
    let p1, p2;
    let body = instance.body;
    // don't allow cloning a used body
    if (instance.bodyUsed) {
        throw new Error('cannot clone body after it is used');
    }
    // check that body is a stream and not form-data object
    // note: we can't clone the form-data object without having it as a dependency
    if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"] && typeof body.getBoundary !== 'function') {
        // tee instance body
        p1 = new PassThrough();
        p2 = new PassThrough();
        body.pipe(p1);
        body.pipe(p2);
        // set instance body to teed body and return the other teed body
        instance[INTERNALS].body = p1;
        body = p2;
    }
    return body;
}
/**
 * Performs the operation "extract a `Content-Type` value from |object|" as
 * specified in the specification:
 * https://fetch.spec.whatwg.org/#concept-bodyinit-extract
 *
 * This function assumes that instance.body is present.
 *
 * @param   Mixed  instance  Any options.body input
 */ function extractContentType(body) {
    if (body === null) {
        // body is null
        return null;
    } else if (typeof body === 'string') {
        // body is string
        return 'text/plain;charset=UTF-8';
    } else if (isURLSearchParams(body)) {
        // body is a URLSearchParams
        return 'application/x-www-form-urlencoded;charset=UTF-8';
    } else if (isBlob(body)) {
        // body is blob
        return body.type || null;
    } else if (Buffer.isBuffer(body)) {
        // body is buffer
        return null;
    } else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {
        // body is ArrayBuffer
        return null;
    } else if (ArrayBuffer.isView(body)) {
        // body is ArrayBufferView
        return null;
    } else if (typeof body.getBoundary === 'function') {
        // detect form data input from form-data module
        return `multipart/form-data;boundary=${body.getBoundary()}`;
    } else if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"]) {
        // body is stream
        // can't really do much about this
        return null;
    } else {
        // Body constructor defaults other things to string
        return 'text/plain;charset=UTF-8';
    }
}
/**
 * The Fetch Standard treats this as if "total bytes" is a property on the body.
 * For us, we have to explicitly get it with a function.
 *
 * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes
 *
 * @param   Body    instance   Instance of Body
 * @return  Number?            Number of bytes, or null if not possible
 */ function getTotalBytes(instance) {
    const body = instance.body;
    if (body === null) {
        // body is null
        return 0;
    } else if (isBlob(body)) {
        return body.size;
    } else if (Buffer.isBuffer(body)) {
        // body is buffer
        return body.length;
    } else if (body && typeof body.getLengthSync === 'function') {
        // detect form data input from form-data module
        if (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x
        body.hasKnownLength && body.hasKnownLength()) {
            // 2.x
            return body.getLengthSync();
        }
        return null;
    } else {
        // body is stream
        return null;
    }
}
/**
 * Write a Body to a Node.js WritableStream (e.g. http.Request) object.
 *
 * @param   Body    instance   Instance of Body
 * @return  Void
 */ function writeToStream(dest, instance) {
    const body = instance.body;
    if (body === null) {
        // body is null
        dest.end();
    } else if (isBlob(body)) {
        body.stream().pipe(dest);
    } else if (Buffer.isBuffer(body)) {
        // body is buffer
        dest.write(body);
        dest.end();
    } else {
        // body is stream
        body.pipe(dest);
    }
}
// expose Promise
Body.Promise = global.Promise;
/**
 * headers.js
 *
 * Headers class offers convenient helpers
 */ const invalidTokenRegex = /[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/;
const invalidHeaderCharRegex = /[^\t\x20-\x7e\x80-\xff]/;
function validateName(name) {
    name = `${name}`;
    if (invalidTokenRegex.test(name) || name === '') {
        throw new TypeError(`${name} is not a legal HTTP header name`);
    }
}
function validateValue(value) {
    value = `${value}`;
    if (invalidHeaderCharRegex.test(value)) {
        throw new TypeError(`${value} is not a legal HTTP header value`);
    }
}
/**
 * Find the key in the map object given a header name.
 *
 * Returns undefined if not found.
 *
 * @param   String  name  Header name
 * @return  String|Undefined
 */ function find(map, name) {
    name = name.toLowerCase();
    for(const key in map){
        if (key.toLowerCase() === name) {
            return key;
        }
    }
    return undefined;
}
const MAP = Symbol('map');
class Headers {
    /**
  * Headers class
  *
  * @param   Object  headers  Response headers
  * @return  Void
  */ constructor(){
        let init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;
        this[MAP] = Object.create(null);
        if (init instanceof Headers) {
            const rawHeaders = init.raw();
            const headerNames = Object.keys(rawHeaders);
            for (const headerName of headerNames){
                for (const value of rawHeaders[headerName]){
                    this.append(headerName, value);
                }
            }
            return;
        }
        // We don't worry about converting prop to ByteString here as append()
        // will handle it.
        if (init == null) ;
        else if (typeof init === 'object') {
            const method = init[Symbol.iterator];
            if (method != null) {
                if (typeof method !== 'function') {
                    throw new TypeError('Header pairs must be iterable');
                }
                // sequence<sequence<ByteString>>
                // Note: per spec we have to first exhaust the lists then process them
                const pairs = [];
                for (const pair of init){
                    if (typeof pair !== 'object' || typeof pair[Symbol.iterator] !== 'function') {
                        throw new TypeError('Each header pair must be iterable');
                    }
                    pairs.push(Array.from(pair));
                }
                for (const pair of pairs){
                    if (pair.length !== 2) {
                        throw new TypeError('Each header pair must be a name/value tuple');
                    }
                    this.append(pair[0], pair[1]);
                }
            } else {
                // record<ByteString, ByteString>
                for (const key of Object.keys(init)){
                    const value = init[key];
                    this.append(key, value);
                }
            }
        } else {
            throw new TypeError('Provided initializer must be an object');
        }
    }
    /**
  * Return combined header value given name
  *
  * @param   String  name  Header name
  * @return  Mixed
  */ get(name) {
        name = `${name}`;
        validateName(name);
        const key = find(this[MAP], name);
        if (key === undefined) {
            return null;
        }
        return this[MAP][key].join(', ');
    }
    /**
  * Iterate over all headers
  *
  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)
  * @param   Boolean   thisArg   `this` context for callback function
  * @return  Void
  */ forEach(callback) {
        let thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;
        let pairs = getHeaders(this);
        let i = 0;
        while(i < pairs.length){
            var _pairs$i = pairs[i];
            const name = _pairs$i[0], value = _pairs$i[1];
            callback.call(thisArg, value, name, this);
            pairs = getHeaders(this);
            i++;
        }
    }
    /**
  * Overwrite header values given name
  *
  * @param   String  name   Header name
  * @param   String  value  Header value
  * @return  Void
  */ set(name, value) {
        name = `${name}`;
        value = `${value}`;
        validateName(name);
        validateValue(value);
        const key = find(this[MAP], name);
        this[MAP][key !== undefined ? key : name] = [
            value
        ];
    }
    /**
  * Append a value onto existing header
  *
  * @param   String  name   Header name
  * @param   String  value  Header value
  * @return  Void
  */ append(name, value) {
        name = `${name}`;
        value = `${value}`;
        validateName(name);
        validateValue(value);
        const key = find(this[MAP], name);
        if (key !== undefined) {
            this[MAP][key].push(value);
        } else {
            this[MAP][name] = [
                value
            ];
        }
    }
    /**
  * Check for header name existence
  *
  * @param   String   name  Header name
  * @return  Boolean
  */ has(name) {
        name = `${name}`;
        validateName(name);
        return find(this[MAP], name) !== undefined;
    }
    /**
  * Delete all header values given name
  *
  * @param   String  name  Header name
  * @return  Void
  */ delete(name) {
        name = `${name}`;
        validateName(name);
        const key = find(this[MAP], name);
        if (key !== undefined) {
            delete this[MAP][key];
        }
    }
    /**
  * Return raw headers (non-spec api)
  *
  * @return  Object
  */ raw() {
        return this[MAP];
    }
    /**
  * Get an iterator on keys.
  *
  * @return  Iterator
  */ keys() {
        return createHeadersIterator(this, 'key');
    }
    /**
  * Get an iterator on values.
  *
  * @return  Iterator
  */ values() {
        return createHeadersIterator(this, 'value');
    }
    /**
  * Get an iterator on entries.
  *
  * This is the default iterator of the Headers object.
  *
  * @return  Iterator
  */ [Symbol.iterator]() {
        return createHeadersIterator(this, 'key+value');
    }
}
Headers.prototype.entries = Headers.prototype[Symbol.iterator];
Object.defineProperty(Headers.prototype, Symbol.toStringTag, {
    value: 'Headers',
    writable: false,
    enumerable: false,
    configurable: true
});
Object.defineProperties(Headers.prototype, {
    get: {
        enumerable: true
    },
    forEach: {
        enumerable: true
    },
    set: {
        enumerable: true
    },
    append: {
        enumerable: true
    },
    has: {
        enumerable: true
    },
    delete: {
        enumerable: true
    },
    keys: {
        enumerable: true
    },
    values: {
        enumerable: true
    },
    entries: {
        enumerable: true
    }
});
function getHeaders(headers) {
    let kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key+value';
    const keys = Object.keys(headers[MAP]).sort();
    return keys.map(kind === 'key' ? function(k) {
        return k.toLowerCase();
    } : kind === 'value' ? function(k) {
        return headers[MAP][k].join(', ');
    } : function(k) {
        return [
            k.toLowerCase(),
            headers[MAP][k].join(', ')
        ];
    });
}
const INTERNAL = Symbol('internal');
function createHeadersIterator(target, kind) {
    const iterator = Object.create(HeadersIteratorPrototype);
    iterator[INTERNAL] = {
        target,
        kind,
        index: 0
    };
    return iterator;
}
const HeadersIteratorPrototype = Object.setPrototypeOf({
    next () {
        // istanbul ignore if
        if (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {
            throw new TypeError('Value of `this` is not a HeadersIterator');
        }
        var _INTERNAL = this[INTERNAL];
        const target = _INTERNAL.target, kind = _INTERNAL.kind, index = _INTERNAL.index;
        const values = getHeaders(target, kind);
        const len = values.length;
        if (index >= len) {
            return {
                value: undefined,
                done: true
            };
        }
        this[INTERNAL].index = index + 1;
        return {
            value: values[index],
            done: false
        };
    }
}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));
Object.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {
    value: 'HeadersIterator',
    writable: false,
    enumerable: false,
    configurable: true
});
/**
 * Export the Headers object in a form that Node.js can consume.
 *
 * @param   Headers  headers
 * @return  Object
 */ function exportNodeCompatibleHeaders(headers) {
    const obj = Object.assign({
        __proto__: null
    }, headers[MAP]);
    // http.request() only supports string as Host header. This hack makes
    // specifying custom Host header possible.
    const hostHeaderKey = find(headers[MAP], 'Host');
    if (hostHeaderKey !== undefined) {
        obj[hostHeaderKey] = obj[hostHeaderKey][0];
    }
    return obj;
}
/**
 * Create a Headers object from an object of headers, ignoring those that do
 * not conform to HTTP grammar productions.
 *
 * @param   Object  obj  Object of headers
 * @return  Headers
 */ function createHeadersLenient(obj) {
    const headers = new Headers();
    for (const name of Object.keys(obj)){
        if (invalidTokenRegex.test(name)) {
            continue;
        }
        if (Array.isArray(obj[name])) {
            for (const val of obj[name]){
                if (invalidHeaderCharRegex.test(val)) {
                    continue;
                }
                if (headers[MAP][name] === undefined) {
                    headers[MAP][name] = [
                        val
                    ];
                } else {
                    headers[MAP][name].push(val);
                }
            }
        } else if (!invalidHeaderCharRegex.test(obj[name])) {
            headers[MAP][name] = [
                obj[name]
            ];
        }
    }
    return headers;
}
const INTERNALS$1 = Symbol('Response internals');
// fix an issue where "STATUS_CODES" aren't a named export for node <10
const STATUS_CODES = __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__["default"].STATUS_CODES;
/**
 * Response class
 *
 * @param   Stream  body  Readable stream
 * @param   Object  opts  Response options
 * @return  Void
 */ class Response {
    constructor(){
        let body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
        let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        Body.call(this, body, opts);
        const status = opts.status || 200;
        const headers = new Headers(opts.headers);
        if (body != null && !headers.has('Content-Type')) {
            const contentType = extractContentType(body);
            if (contentType) {
                headers.append('Content-Type', contentType);
            }
        }
        this[INTERNALS$1] = {
            url: opts.url,
            status,
            statusText: opts.statusText || STATUS_CODES[status],
            headers,
            counter: opts.counter
        };
    }
    get url() {
        return this[INTERNALS$1].url || '';
    }
    get status() {
        return this[INTERNALS$1].status;
    }
    /**
  * Convenience property representing if the request ended normally
  */ get ok() {
        return this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;
    }
    get redirected() {
        return this[INTERNALS$1].counter > 0;
    }
    get statusText() {
        return this[INTERNALS$1].statusText;
    }
    get headers() {
        return this[INTERNALS$1].headers;
    }
    /**
  * Clone this response
  *
  * @return  Response
  */ clone() {
        return new Response(clone(this), {
            url: this.url,
            status: this.status,
            statusText: this.statusText,
            headers: this.headers,
            ok: this.ok,
            redirected: this.redirected
        });
    }
}
Body.mixIn(Response.prototype);
Object.defineProperties(Response.prototype, {
    url: {
        enumerable: true
    },
    status: {
        enumerable: true
    },
    ok: {
        enumerable: true
    },
    redirected: {
        enumerable: true
    },
    statusText: {
        enumerable: true
    },
    headers: {
        enumerable: true
    },
    clone: {
        enumerable: true
    }
});
Object.defineProperty(Response.prototype, Symbol.toStringTag, {
    value: 'Response',
    writable: false,
    enumerable: false,
    configurable: true
});
const INTERNALS$2 = Symbol('Request internals');
const URL = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].URL || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$url$2f$lib$2f$public$2d$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].URL;
// fix an issue where "format", "parse" aren't a named export for node <10
const parse_url = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].parse;
const format_url = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].format;
/**
 * Wrapper around `new URL` to handle arbitrary URLs
 *
 * @param  {string} urlStr
 * @return {void}
 */ function parseURL(urlStr) {
    /*
 	Check whether the URL is absolute or not
 		Scheme: https://tools.ietf.org/html/rfc3986#section-3.1
 	Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3
 */ if (/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(urlStr)) {
        urlStr = new URL(urlStr).toString();
    }
    // Fallback to old implementation for arbitrary URLs
    return parse_url(urlStr);
}
const streamDestructionSupported = 'destroy' in __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable.prototype;
/**
 * Check if a value is an instance of Request.
 *
 * @param   Mixed   input
 * @return  Boolean
 */ function isRequest(input) {
    return typeof input === 'object' && typeof input[INTERNALS$2] === 'object';
}
function isAbortSignal(signal) {
    const proto = signal && typeof signal === 'object' && Object.getPrototypeOf(signal);
    return !!(proto && proto.constructor.name === 'AbortSignal');
}
/**
 * Request class
 *
 * @param   Mixed   input  Url or Request instance
 * @param   Object  init   Custom options
 * @return  Void
 */ class Request {
    constructor(input){
        let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        let parsedURL;
        // normalize input
        if (!isRequest(input)) {
            if (input && input.href) {
                // in order to support Node.js' Url objects; though WHATWG's URL objects
                // will fall into this branch also (since their `toString()` will return
                // `href` property anyway)
                parsedURL = parseURL(input.href);
            } else {
                // coerce input to a string before attempting to parse
                parsedURL = parseURL(`${input}`);
            }
            input = {};
        } else {
            parsedURL = parseURL(input.url);
        }
        let method = init.method || input.method || 'GET';
        method = method.toUpperCase();
        if ((init.body != null || isRequest(input) && input.body !== null) && (method === 'GET' || method === 'HEAD')) {
            throw new TypeError('Request with GET/HEAD method cannot have body');
        }
        let inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;
        Body.call(this, inputBody, {
            timeout: init.timeout || input.timeout || 0,
            size: init.size || input.size || 0
        });
        const headers = new Headers(init.headers || input.headers || {});
        if (inputBody != null && !headers.has('Content-Type')) {
            const contentType = extractContentType(inputBody);
            if (contentType) {
                headers.append('Content-Type', contentType);
            }
        }
        let signal = isRequest(input) ? input.signal : null;
        if ('signal' in init) signal = init.signal;
        if (signal != null && !isAbortSignal(signal)) {
            throw new TypeError('Expected signal to be an instanceof AbortSignal');
        }
        this[INTERNALS$2] = {
            method,
            redirect: init.redirect || input.redirect || 'follow',
            headers,
            parsedURL,
            signal
        };
        // node-fetch-only options
        this.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;
        this.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;
        this.counter = init.counter || input.counter || 0;
        this.agent = init.agent || input.agent;
    }
    get method() {
        return this[INTERNALS$2].method;
    }
    get url() {
        return format_url(this[INTERNALS$2].parsedURL);
    }
    get headers() {
        return this[INTERNALS$2].headers;
    }
    get redirect() {
        return this[INTERNALS$2].redirect;
    }
    get signal() {
        return this[INTERNALS$2].signal;
    }
    /**
  * Clone this request
  *
  * @return  Request
  */ clone() {
        return new Request(this);
    }
}
Body.mixIn(Request.prototype);
Object.defineProperty(Request.prototype, Symbol.toStringTag, {
    value: 'Request',
    writable: false,
    enumerable: false,
    configurable: true
});
Object.defineProperties(Request.prototype, {
    method: {
        enumerable: true
    },
    url: {
        enumerable: true
    },
    headers: {
        enumerable: true
    },
    redirect: {
        enumerable: true
    },
    clone: {
        enumerable: true
    },
    signal: {
        enumerable: true
    }
});
/**
 * Convert a Request to Node.js http request options.
 *
 * @param   Request  A Request instance
 * @return  Object   The options object to be passed to http.request
 */ function getNodeRequestOptions(request) {
    const parsedURL = request[INTERNALS$2].parsedURL;
    const headers = new Headers(request[INTERNALS$2].headers);
    // fetch step 1.3
    if (!headers.has('Accept')) {
        headers.set('Accept', '*/*');
    }
    // Basic fetch
    if (!parsedURL.protocol || !parsedURL.hostname) {
        throw new TypeError('Only absolute URLs are supported');
    }
    if (!/^https?:$/.test(parsedURL.protocol)) {
        throw new TypeError('Only HTTP(S) protocols are supported');
    }
    if (request.signal && request.body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable && !streamDestructionSupported) {
        throw new Error('Cancellation of streamed requests with AbortSignal is not supported in node < 8');
    }
    // HTTP-network-or-cache fetch steps 2.4-2.7
    let contentLengthValue = null;
    if (request.body == null && /^(POST|PUT)$/i.test(request.method)) {
        contentLengthValue = '0';
    }
    if (request.body != null) {
        const totalBytes = getTotalBytes(request);
        if (typeof totalBytes === 'number') {
            contentLengthValue = String(totalBytes);
        }
    }
    if (contentLengthValue) {
        headers.set('Content-Length', contentLengthValue);
    }
    // HTTP-network-or-cache fetch step 2.11
    if (!headers.has('User-Agent')) {
        headers.set('User-Agent', 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)');
    }
    // HTTP-network-or-cache fetch step 2.15
    if (request.compress && !headers.has('Accept-Encoding')) {
        headers.set('Accept-Encoding', 'gzip,deflate');
    }
    let agent = request.agent;
    if (typeof agent === 'function') {
        agent = agent(parsedURL);
    }
    // HTTP-network fetch step 4.2
    // chunked encoding is handled by Node.js
    return Object.assign({}, parsedURL, {
        method: request.method,
        headers: exportNodeCompatibleHeaders(headers),
        agent
    });
}
/**
 * abort-error.js
 *
 * AbortError interface for cancelled requests
 */ /**
 * Create AbortError instance
 *
 * @param   String      message      Error message for human
 * @return  AbortError
 */ function AbortError(message) {
    Error.call(this, message);
    this.type = 'aborted';
    this.message = message;
    // hide custom error implementation details from end-users
    Error.captureStackTrace(this, this.constructor);
}
AbortError.prototype = Object.create(Error.prototype);
AbortError.prototype.constructor = AbortError;
AbortError.prototype.name = 'AbortError';
const URL$1 = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].URL || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$url$2f$lib$2f$public$2d$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].URL;
// fix an issue where "PassThrough", "resolve" aren't a named export for node <10
const PassThrough$1 = __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].PassThrough;
const isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {
    const orig = new URL$1(original).hostname;
    const dest = new URL$1(destination).hostname;
    return orig === dest || orig[orig.length - dest.length - 1] === '.' && orig.endsWith(dest);
};
/**
 * isSameProtocol reports whether the two provided URLs use the same protocol.
 *
 * Both domains must already be in canonical form.
 * @param {string|URL} original
 * @param {string|URL} destination
 */ const isSameProtocol = function isSameProtocol(destination, original) {
    const orig = new URL$1(original).protocol;
    const dest = new URL$1(destination).protocol;
    return orig === dest;
};
/**
 * Fetch function
 *
 * @param   Mixed    url   Absolute url or Request instance
 * @param   Object   opts  Fetch options
 * @return  Promise
 */ function fetch(url, opts) {
    // allow custom promise
    if (!fetch.Promise) {
        throw new Error('native promise missing, set fetch.Promise to your favorite alternative');
    }
    Body.Promise = fetch.Promise;
    // wrap http.request into fetch
    return new fetch.Promise(function(resolve, reject) {
        // build request object
        const request = new Request(url, opts);
        const options = getNodeRequestOptions(request);
        const send = (options.protocol === 'https:' ? __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__["default"] : __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__["default"]).request;
        const signal = request.signal;
        let response = null;
        const abort = function abort() {
            let error = new AbortError('The user aborted a request.');
            reject(error);
            if (request.body && request.body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable) {
                destroyStream(request.body, error);
            }
            if (!response || !response.body) return;
            response.body.emit('error', error);
        };
        if (signal && signal.aborted) {
            abort();
            return;
        }
        const abortAndFinalize = function abortAndFinalize() {
            abort();
            finalize();
        };
        // send request
        const req = send(options);
        let reqTimeout;
        if (signal) {
            signal.addEventListener('abort', abortAndFinalize);
        }
        function finalize() {
            req.abort();
            if (signal) signal.removeEventListener('abort', abortAndFinalize);
            clearTimeout(reqTimeout);
        }
        if (request.timeout) {
            req.once('socket', function(socket) {
                reqTimeout = setTimeout(function() {
                    reject(new FetchError(`network timeout at: ${request.url}`, 'request-timeout'));
                    finalize();
                }, request.timeout);
            });
        }
        req.on('error', function(err) {
            reject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, 'system', err));
            if (response && response.body) {
                destroyStream(response.body, err);
            }
            finalize();
        });
        fixResponseChunkedTransferBadEnding(req, function(err) {
            if (signal && signal.aborted) {
                return;
            }
            if (response && response.body) {
                destroyStream(response.body, err);
            }
        });
        /* c8 ignore next 18 */ if (parseInt(process.version.substring(1)) < 14) {
            // Before Node.js 14, pipeline() does not fully support async iterators and does not always
            // properly handle when the socket close/end events are out of order.
            req.on('socket', function(s) {
                s.addListener('close', function(hadError) {
                    // if a data listener is still present we didn't end cleanly
                    const hasDataListener = s.listenerCount('data') > 0;
                    // if end happened before close but the socket didn't emit an error, do it now
                    if (response && hasDataListener && !hadError && !(signal && signal.aborted)) {
                        const err = new Error('Premature close');
                        err.code = 'ERR_STREAM_PREMATURE_CLOSE';
                        response.body.emit('error', err);
                    }
                });
            });
        }
        req.on('response', function(res) {
            clearTimeout(reqTimeout);
            const headers = createHeadersLenient(res.headers);
            // HTTP fetch step 5
            if (fetch.isRedirect(res.statusCode)) {
                // HTTP fetch step 5.2
                const location = headers.get('Location');
                // HTTP fetch step 5.3
                let locationURL = null;
                try {
                    locationURL = location === null ? null : new URL$1(location, request.url).toString();
                } catch (err) {
                    // error here can only be invalid URL in Location: header
                    // do not throw when options.redirect == manual
                    // let the user extract the errorneous redirect URL
                    if (request.redirect !== 'manual') {
                        reject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, 'invalid-redirect'));
                        finalize();
                        return;
                    }
                }
                // HTTP fetch step 5.5
                switch(request.redirect){
                    case 'error':
                        reject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, 'no-redirect'));
                        finalize();
                        return;
                    case 'manual':
                        // node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.
                        if (locationURL !== null) {
                            // handle corrupted header
                            try {
                                headers.set('Location', locationURL);
                            } catch (err) {
                                // istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request
                                reject(err);
                            }
                        }
                        break;
                    case 'follow':
                        // HTTP-redirect fetch step 2
                        if (locationURL === null) {
                            break;
                        }
                        // HTTP-redirect fetch step 5
                        if (request.counter >= request.follow) {
                            reject(new FetchError(`maximum redirect reached at: ${request.url}`, 'max-redirect'));
                            finalize();
                            return;
                        }
                        // HTTP-redirect fetch step 6 (counter increment)
                        // Create a new Request object.
                        const requestOpts = {
                            headers: new Headers(request.headers),
                            follow: request.follow,
                            counter: request.counter + 1,
                            agent: request.agent,
                            compress: request.compress,
                            method: request.method,
                            body: request.body,
                            signal: request.signal,
                            timeout: request.timeout,
                            size: request.size
                        };
                        if (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {
                            for (const name of [
                                'authorization',
                                'www-authenticate',
                                'cookie',
                                'cookie2'
                            ]){
                                requestOpts.headers.delete(name);
                            }
                        }
                        // HTTP-redirect fetch step 9
                        if (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {
                            reject(new FetchError('Cannot follow redirect with body being a readable stream', 'unsupported-redirect'));
                            finalize();
                            return;
                        }
                        // HTTP-redirect fetch step 11
                        if (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === 'POST') {
                            requestOpts.method = 'GET';
                            requestOpts.body = undefined;
                            requestOpts.headers.delete('content-length');
                        }
                        // HTTP-redirect fetch step 15
                        resolve(fetch(new Request(locationURL, requestOpts)));
                        finalize();
                        return;
                }
            }
            // prepare response
            res.once('end', function() {
                if (signal) signal.removeEventListener('abort', abortAndFinalize);
            });
            let body = res.pipe(new PassThrough$1());
            const response_options = {
                url: request.url,
                status: res.statusCode,
                statusText: res.statusMessage,
                headers: headers,
                size: request.size,
                timeout: request.timeout,
                counter: request.counter
            };
            // HTTP-network fetch step ********
            const codings = headers.get('Content-Encoding');
            // HTTP-network fetch step ********: handle content codings
            // in following scenarios we ignore compression support
            // 1. compression support is disabled
            // 2. HEAD request
            // 3. no Content-Encoding header
            // 4. no content response (204)
            // 5. content not modified response (304)
            if (!request.compress || request.method === 'HEAD' || codings === null || res.statusCode === 204 || res.statusCode === 304) {
                response = new Response(body, response_options);
                resolve(response);
                return;
            }
            // For Node v6+
            // Be less strict when decoding compressed responses, since sometimes
            // servers send slightly invalid responses that are still accepted
            // by common browsers.
            // Always using Z_SYNC_FLUSH is what cURL does.
            const zlibOptions = {
                flush: __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].Z_SYNC_FLUSH,
                finishFlush: __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].Z_SYNC_FLUSH
            };
            // for gzip
            if (codings == 'gzip' || codings == 'x-gzip') {
                body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createGunzip(zlibOptions));
                response = new Response(body, response_options);
                resolve(response);
                return;
            }
            // for deflate
            if (codings == 'deflate' || codings == 'x-deflate') {
                // handle the infamous raw deflate response from old servers
                // a hack for old IIS and Apache servers
                const raw = res.pipe(new PassThrough$1());
                raw.once('data', function(chunk) {
                    // see http://stackoverflow.com/questions/37519828
                    if ((chunk[0] & 0x0F) === 0x08) {
                        body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createInflate());
                    } else {
                        body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createInflateRaw());
                    }
                    response = new Response(body, response_options);
                    resolve(response);
                });
                raw.on('end', function() {
                    // some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.
                    if (!response) {
                        response = new Response(body, response_options);
                        resolve(response);
                    }
                });
                return;
            }
            // for br
            if (codings == 'br' && typeof __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createBrotliDecompress === 'function') {
                body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createBrotliDecompress());
                response = new Response(body, response_options);
                resolve(response);
                return;
            }
            // otherwise, use response as-is
            response = new Response(body, response_options);
            resolve(response);
        });
        writeToStream(req, request);
    });
}
function fixResponseChunkedTransferBadEnding(request, errorCallback) {
    let socket;
    request.on('socket', function(s) {
        socket = s;
    });
    request.on('response', function(response) {
        const headers = response.headers;
        if (headers['transfer-encoding'] === 'chunked' && !headers['content-length']) {
            response.once('close', function(hadError) {
                // tests for socket presence, as in some situations the
                // the 'socket' event is not triggered for the request
                // (happens in deno), avoids `TypeError`
                // if a data listener is still present we didn't end cleanly
                const hasDataListener = socket && socket.listenerCount('data') > 0;
                if (hasDataListener && !hadError) {
                    const err = new Error('Premature close');
                    err.code = 'ERR_STREAM_PREMATURE_CLOSE';
                    errorCallback(err);
                }
            });
        }
    });
}
function destroyStream(stream, err) {
    if (stream.destroy) {
        stream.destroy(err);
    } else {
        // node < 8
        stream.emit('error', err);
        stream.end();
    }
}
/**
 * Redirect code matching
 *
 * @param   Number   code  Status code
 * @return  Boolean
 */ fetch.isRedirect = function(code) {
    return code === 301 || code === 302 || code === 303 || code === 307 || code === 308;
};
// expose Promise
fetch.Promise = global.Promise;
const __TURBOPACK__default__export__ = fetch;
;
}}),
"[project]/node_modules/is-url/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Expose `isUrl`.
 */ module.exports = isUrl;
/**
 * RegExps.
 * A URL must match #1 and then at least one of #2/#3.
 * Use two levels of REs to avoid REDOS.
 */ var protocolAndDomainRE = /^(?:\w+:)?\/\/(\S+)$/;
var localhostDomainRE = /^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/;
var nonLocalhostDomainRE = /^[^\s\.]+\.\S{2,}$/;
/**
 * Loosely validate a URL `string`.
 *
 * @param {String} string
 * @return {Boolean}
 */ function isUrl(string) {
    if (typeof string !== 'string') {
        return false;
    }
    var match = string.match(protocolAndDomainRE);
    if (!match) {
        return false;
    }
    var everythingAfterProtocol = match[1];
    if (!everythingAfterProtocol) {
        return false;
    }
    if (localhostDomainRE.test(everythingAfterProtocol) || nonLocalhostDomainRE.test(everythingAfterProtocol)) {
        return true;
    }
    return false;
}
}}),
"[project]/node_modules/gm/lib/options.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = exports = function(proto) {
    proto._options = {};
    proto.options = function setOptions(options) {
        var keys = Object.keys(options), i = keys.length, key;
        while(i--){
            key = keys[i];
            this._options[key] = options[key];
        }
        return this;
    };
};
}}),
"[project]/node_modules/gm/lib/getters.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Extend proto.
 */ module.exports = function(gm) {
    var proto = gm.prototype;
    /**
   * `identify` states
   */ const IDENTIFYING = 1;
    const IDENTIFIED = 2;
    /**
   * Map getter functions to output names.
   *
   * - format: specifying the -format argument (see man gm)
   * - verbose: use -verbose instead of -format (only if necessary b/c its slow)
   * - helper: use the conversion helper
   */ var map = {
        'format': {
            key: 'format',
            format: '%m ',
            helper: 'Format'
        },
        'depth': {
            key: 'depth',
            format: '%q'
        },
        'filesize': {
            key: 'Filesize',
            format: '%b'
        },
        'size': {
            key: 'size',
            format: '%wx%h ',
            helper: 'Geometry'
        },
        'color': {
            key: 'color',
            format: '%k',
            helper: 'Colors'
        },
        'orientation': {
            key: 'Orientation',
            format: '%[EXIF:Orientation]',
            helper: 'Orientation'
        },
        'res': {
            key: 'Resolution',
            verbose: true
        }
    };
    /**
   * Getter functions
   */ Object.keys(map).forEach(function(getter) {
        proto[getter] = function(opts, callback) {
            if (!callback) callback = opts, opts = {};
            if (!callback) return this;
            var val = map[getter], key = val.key, self = this;
            if (self.data[key]) {
                callback.call(self, null, self.data[key]);
                return self;
            }
            self.on(getter, callback);
            self.bufferStream = !!opts.bufferStream;
            if (val.verbose) {
                self.identify(opts, function(err, stdout, stderr, cmd) {
                    if (err) {
                        self.emit(getter, err, self.data[key], stdout, stderr, cmd);
                    } else {
                        self.emit(getter, err, self.data[key]);
                    }
                });
                return self;
            }
            var args = makeArgs(self, val);
            self._exec(args, function(err, stdout, stderr, cmd) {
                if (err) {
                    self.emit(getter, err, self.data[key], stdout, stderr, cmd);
                    return;
                }
                var result = (stdout || '').trim();
                if (val.helper in helper) {
                    helper[val.helper](self.data, result);
                } else {
                    self.data[key] = result;
                }
                self.emit(getter, err, self.data[key]);
            });
            return self;
        };
    });
    /**
   * identify command
   *
   * Overwrites all internal data with the parsed output
   * which is more accurate than the fast shortcut
   * getters.
   */ proto.identify = function identify(opts, callback) {
        // identify with pattern
        if (typeof opts === 'string') {
            opts = {
                format: opts
            };
        }
        if (!callback) callback = opts, opts = {};
        if (!callback) return this;
        if (opts && opts.format) return identifyPattern.call(this, opts, callback);
        var self = this;
        if (IDENTIFIED === self._identifyState) {
            callback.call(self, null, self.data);
            return self;
        }
        self.on('identify', callback);
        if (IDENTIFYING === self._identifyState) {
            return self;
        }
        self._identifyState = IDENTIFYING;
        self.bufferStream = !!opts.bufferStream;
        var args = makeArgs(self, {
            verbose: true
        });
        self._exec(args, function(err, stdout, stderr, cmd) {
            if (err) {
                self.emit('identify', err, self.data, stdout, stderr, cmd);
                return;
            }
            err = parse(stdout, self);
            if (err) {
                self.emit('identify', err, self.data, stdout, stderr, cmd);
                return;
            }
            self.data.path = self.source;
            self.emit('identify', null, self.data);
            self._identifyState = IDENTIFIED;
        });
        return self;
    };
    /**
   * identify with pattern
   *
   * Execute `identify -format` with custom pattern
   */ function identifyPattern(opts, callback) {
        var self = this;
        self.bufferStream = !!opts.bufferStream;
        var args = makeArgs(self, opts);
        self._exec(args, function(err, stdout, stderr, cmd) {
            if (err) {
                return callback.call(self, err, undefined, stdout, stderr, cmd);
            }
            callback.call(self, err, (stdout || '').trim());
        });
        return self;
    }
    /**
   * Parses `identify` responses.
   *
   * @param {String} stdout
   * @param {Gm} self
   * @return {Error} [optionally]
   */ function parse(stdout, self) {
        // normalize
        var parts = (stdout || "").trim().replace(/\r\n|\r/g, "\n").split("\n");
        // skip the first line (its just the filename)
        parts.shift();
        try {
            var len = parts.length, rgx1 = /^( *)(.+?): (.*)$/ // key: val
            , rgx2 = /^( *)(.+?):$/ // key: begin nested object
            , out = {
                indent: {}
            }, level = null, lastkey, i = 0, res, o;
            for(; i < len; ++i){
                res = rgx1.exec(parts[i]) || rgx2.exec(parts[i]);
                if (!res) continue;
                var indent = res[1].length, key = res[2] ? res[2].trim() : '';
                if ('Image' == key || 'Warning' == key) continue;
                var val = res[3] ? res[3].trim() : null;
                // first iteration?
                if (null === level) {
                    level = indent;
                    o = out.root = out.indent[level] = self.data;
                } else if (indent < level) {
                    // outdent
                    if (!(indent in out.indent)) {
                        continue;
                    }
                    o = out.indent[indent];
                } else if (indent > level) {
                    // dropping into a nested object
                    out.indent[level] = o;
                    // weird format, key/val pair with nested children. discard the val
                    o = o[lastkey] = {};
                }
                level = indent;
                if (val) {
                    // if previous key was exist and we got the same key
                    // cast it to an array.
                    if (o.hasOwnProperty(key)) {
                        // cast it to an array and dont forget the previous value
                        if (!Array.isArray(o[key])) {
                            var tmp = o[key];
                            o[key] = [
                                tmp
                            ];
                        }
                        // set value
                        o[key].push(val);
                    } else {
                        o[key] = val;
                    }
                    if (key in helper) {
                        helper[key](o, val);
                    }
                }
                lastkey = key;
            }
        } catch (err) {
            err.message = err.message + "\n\n  Identify stdout:\n  " + stdout;
            return err;
        }
    }
    /**
   * Create an argument array for the identify command.
   *
   * @param {gm} self
   * @param {Object} val
   * @return {Array}
   */ function makeArgs(self, val) {
        var args = [
            'identify',
            '-ping'
        ];
        if (val.format) {
            args.push('-format', val.format);
        }
        if (val.verbose) {
            args.push('-verbose');
        }
        args = args.concat(self.src());
        return args;
    }
    /**
   * Map exif orientation codes to orientation names.
   */ var orientations = {
        '1': 'TopLeft',
        '2': 'TopRight',
        '3': 'BottomRight',
        '4': 'BottomLeft',
        '5': 'LeftTop',
        '6': 'RightTop',
        '7': 'RightBottom',
        '8': 'LeftBottom'
    };
    /**
   * identify -verbose helpers
   */ var helper = gm.identifyHelpers = {};
    helper.Geometry = function Geometry(o, val) {
        // We only want the size of the first frame.
        // Each frame is separated by a space.
        var split = val.split(" ").shift().split("x");
        var width = parseInt(split[0], 10);
        var height = parseInt(split[1], 10);
        if (o.size && o.size.width && o.size.height) {
            if (width > o.size.width) o.size.width = width;
            if (height > o.size.height) o.size.height = height;
        } else {
            o.size = {
                width: width,
                height: height
            };
        }
    };
    helper.Format = function Format(o, val) {
        o.format = val.split(" ")[0];
    };
    helper.Depth = function Depth(o, val) {
        o.depth = parseInt(val, 10);
    };
    helper.Colors = function Colors(o, val) {
        o.color = parseInt(val, 10);
    };
    helper.Orientation = function Orientation(o, val) {
        if (val in orientations) {
            o['Profile-EXIF'] || (o['Profile-EXIF'] = {});
            o['Profile-EXIF'].Orientation = val;
            o.Orientation = orientations[val];
        } else {
            o.Orientation = val || 'Unknown';
        }
    };
};
}}),
"[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Escape the given shell `arg`.
 *
 * @param {String} arg
 * @return {String}
 * @api public
 */ exports.escape = function escape(arg) {
    return '"' + String(arg).trim().replace(/"/g, '\\"') + '"';
};
exports.unescape = function escape(arg) {
    return String(arg).trim().replace(/"/g, "");
};
exports.argsToArray = function(args) {
    var arr = [];
    for(var i = 0; i <= arguments.length; i++){
        if ('undefined' != typeof arguments[i]) arr.push(arguments[i]);
    }
    return arr;
};
exports.isUtil = function(v) {
    var ty = 'object';
    switch(Object.prototype.toString.call(v)){
        case '[object String]':
            ty = 'String';
            break;
        case '[object Array]':
            ty = 'Array';
            break;
        case '[object Boolean]':
            ty = 'Boolean';
            break;
    }
    return ty;
};
}}),
"[project]/node_modules/gm/lib/args.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Dependencies
 */ var argsToArray = __turbopack_context__.r("[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)").argsToArray;
var isUtil = __turbopack_context__.r("[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)").isUtil;
/**
 * Extend proto
 */ module.exports = function(proto) {
    // change the specified frame.
    // See #202.
    proto.selectFrame = function(frame) {
        if (typeof frame === 'number') this.sourceFrames = '[' + frame + ']';
        return this;
    };
    // define the sub-command to use, http://www.graphicsmagick.org/utilities.html
    proto.command = proto.subCommand = function subCommand(name) {
        this._subCommand = name;
        return this;
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-adjoin
    proto.adjoin = function adjoin() {
        return this.out("-adjoin");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-affine
    proto.affine = function affine(matrix) {
        return this.out("-affine", matrix);
    };
    proto.alpha = function alpha(type) {
        if (!this._options.imageMagick) return new Error('Method -alpha is not supported by GraphicsMagick');
        return this.out('-alpha', type);
    };
    /**
   * Appends images to the list of "source" images.
   *
   * We may also specify either top-to-bottom or left-to-right
   * behavior of the appending by passing a boolean argument.
   *
   * Examples:
   *
   *    img = gm(src);
   *
   *    // +append means left-to-right
   *    img.append(img1, img2)       gm convert src img1 img2 -append
   *    img.append(img, true)        gm convert src img +append
   *    img.append(img, false)       gm convert src img -append
   *    img.append(img)              gm convert src img -append
   *    img.append(img).append()     gm convert src img -append
   *    img.append(img).append(true) gm convert src img +append
   *    img.append(img).append(true) gm convert src img +append
   *    img.append(img).background('#222) gm convert src img -background #222 +append
   *    img.append([img1,img2...],true)

   * @param {String} or {Array} [img]
   * @param {Boolean} [ltr]
   * @see http://www.graphicsmagick.org/GraphicsMagick.html#details-append
   */ proto.append = function append(img, ltr) {
        if (!this._append) {
            this._append = [];
            this.addSrcFormatter(function(src) {
                this.out(this._append.ltr ? '+append' : '-append');
                src.push.apply(src, this._append);
            });
        }
        if (0 === arguments.length) {
            this._append.ltr = false;
            return this;
        }
        for(var i = 0; i < arguments.length; ++i){
            var arg = arguments[i];
            switch(isUtil(arg)){
                case 'Boolean':
                    this._append.ltr = arg;
                    break;
                case 'String':
                    this._append.push(arg);
                    break;
                case 'Array':
                    for(var j = 0, len = arg.length; j < len; j++){
                        if (isUtil(arg[j]) == 'String') {
                            this._append.push(arg[j]);
                        }
                    }
                    break;
            }
        }
        return this;
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-authenticate
    proto.authenticate = function authenticate(string) {
        return this.out("-authenticate", string);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-average
    proto.average = function average() {
        return this.out("-average");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-backdrop
    proto.backdrop = function backdrop() {
        return this.out("-backdrop");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-black-threshold
    proto.blackThreshold = function blackThreshold(red, green, blue, opacity) {
        return this.out("-black-threshold", argsToArray(red, green, blue, opacity).join(','));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-blue-primary
    proto.bluePrimary = function bluePrimary(x, y) {
        return this.out("-blue-primary", argsToArray(x, y).join(','));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-border
    proto.border = function border(width, height) {
        return this.out("-border", width + "x" + height);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-bordercolor
    proto.borderColor = function borderColor(color) {
        return this.out("-bordercolor", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-box
    proto.box = function box(color) {
        return this.out("-box", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-channel
    proto.channel = function channel(type) {
        return this.out("-channel", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-chop
    proto.chop = function chop(w, h, x, y) {
        return this.in("-chop", w + "x" + h + "+" + (x || 0) + "+" + (y || 0));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-clip
    proto.clip = function clip() {
        return this.out("-clip");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-coalesce
    proto.coalesce = function coalesce() {
        return this.out("-coalesce");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-colorize
    proto.colorize = function colorize(r, g, b) {
        return this.out("-colorize", [
            r,
            g,
            b
        ].join(","));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-colormap
    proto.colorMap = function colorMap(type) {
        return this.out("-colormap", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-compose
    proto.compose = function compose(operator) {
        return this.out("-compose", operator);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-compress
    proto.compress = function compress(type) {
        return this.out("-compress", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-kernel
    proto.convolve = function convolve(kernel) {
        return this.out("-convolve", kernel);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-create-directories
    proto.createDirectories = function createDirectories() {
        return this.out("-create-directories");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-deconstruct
    proto.deconstruct = function deconstruct() {
        return this.out("-deconstruct");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-define
    proto.define = function define(value) {
        return this.out("-define", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-delay
    proto.delay = function delay(value) {
        return this.out("-delay", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-displace
    proto.displace = function displace(horizontalScale, verticalScale) {
        return this.out("-displace", horizontalScale + 'x' + verticalScale);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-display
    proto.display = function display(value) {
        return this.out("-display", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-dispose
    proto.dispose = function dispose(method) {
        return this.out("-dispose", method);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-dissolve
    proto.dissolve = function dissolve(percent) {
        return this.out("-dissolve", percent + '%');
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-encoding
    proto.encoding = function encoding(type) {
        return this.out("-encoding", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-endian
    proto.endian = function endian(type) {
        return this.out("-endian", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-file
    proto.file = function file(filename) {
        return this.out("-file", filename);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-flatten
    proto.flatten = function flatten() {
        return this.out("-flatten");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-foreground
    proto.foreground = function foreground(color) {
        return this.out("-foreground", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-frame
    proto.frame = function frame(width, height, outerBevelWidth, innerBevelWidth) {
        if (arguments.length == 0) return this.out("-frame");
        return this.out("-frame", width + 'x' + height + '+' + outerBevelWidth + '+' + innerBevelWidth);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-fuzz
    proto.fuzz = function fuzz(distance, percent) {
        return this.out("-fuzz", distance + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-gaussian
    proto.gaussian = function gaussian(radius, sigma) {
        return this.out("-gaussian", argsToArray(radius, sigma).join('x'));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-geometry
    proto.geometry = function geometry(width, height, arg) {
        // If the first argument is a string, and there is only one argument, this is a custom geometry command.
        if (arguments.length == 1 && typeof arguments[0] === "string") return this.out("-geometry", arguments[0]);
        // Otherwise, return a resizing geometry command with an option alrgument.
        return this.out("-geometry", width + 'x' + height + (arg || ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-green-primary
    proto.greenPrimary = function greenPrimary(x, y) {
        return this.out("-green-primary", x + ',' + y);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-highlight-color
    proto.highlightColor = function highlightColor(color) {
        return this.out("-highlight-color", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-highlight-style
    proto.highlightStyle = function highlightStyle(style) {
        return this.out("-highlight-style", style);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-iconGeometry
    proto.iconGeometry = function iconGeometry(geometry) {
        return this.out("-iconGeometry", geometry);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-intent
    proto.intent = function intent(type) {
        return this.out("-intent", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-lat
    proto.lat = function lat(width, height, offset, percent) {
        return this.out("-lat", width + 'x' + height + offset + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-level
    proto.level = function level(blackPoint, gamma, whitePoint, percent) {
        return this.out("-level", argsToArray(blackPoint, gamma, whitePoint).join(',') + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-list
    proto.list = function list(type) {
        return this.out("-list", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-log
    proto.log = function log(string) {
        return this.out("-log", string);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-loop
    proto.loop = function loop(iterations) {
        return this.out("-loop", iterations);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-map
    proto.map = function map(filename) {
        return this.out("-map", filename);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-mask
    proto.mask = function mask(filename) {
        return this.out("-mask", filename);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-matte
    proto.matte = function matte() {
        return this.out("-matte");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-mattecolor
    proto.matteColor = function matteColor(color) {
        return this.out("-mattecolor", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-maximum-error
    proto.maximumError = function maximumError(limit) {
        return this.out("-maximum-error", limit);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-mode
    proto.mode = function mode(value) {
        return this.out("-mode", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-monitor
    proto.monitor = function monitor() {
        return this.out("-monitor");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-mosaic
    proto.mosaic = function mosaic() {
        return this.out("-mosaic");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-motion-blur
    proto.motionBlur = function motionBlur(radius, sigma, angle) {
        var arg = radius;
        if (typeof sigma != 'undefined') arg += 'x' + sigma;
        if (typeof angle != 'undefined') arg += '+' + angle;
        return this.out("-motion-blur", arg);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-name
    proto.name = function name() {
        return this.out("-name");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-noop
    proto.noop = function noop() {
        return this.out("-noop");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-normalize
    proto.normalize = function normalize() {
        return this.out("-normalize");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-opaque
    proto.opaque = function opaque(color) {
        return this.out("-opaque", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-operator
    proto.operator = function operator(channel, operator, rvalue, percent) {
        return this.out("-operator", channel, operator, rvalue + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-ordered-dither
    proto.orderedDither = function orderedDither(channeltype, NxN) {
        return this.out("-ordered-dither", channeltype, NxN);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-output-directory
    proto.outputDirectory = function outputDirectory(directory) {
        return this.out("-output-directory", directory);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-page
    proto.page = function page(width, height, arg) {
        return this.out("-page", width + 'x' + height + (arg || ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-pause
    proto.pause = function pause(seconds) {
        return this.out("-pause", seconds);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-pen
    proto.pen = function pen(color) {
        return this.out("-pen", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-ping
    proto.ping = function ping() {
        return this.out("-ping");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-pointsize
    proto.pointSize = function pointSize(value) {
        return this.out("-pointsize", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-preview
    proto.preview = function preview(type) {
        return this.out("-preview", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-process
    proto.process = function process(command) {
        return this.out("-process", command);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-profile
    proto.profile = function profile(filename) {
        return this.out("-profile", filename);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-progress
    proto.progress = function progress() {
        return this.out("+progress");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-random-threshold
    proto.randomThreshold = function randomThreshold(channeltype, LOWxHIGH) {
        return this.out("-random-threshold", channeltype, LOWxHIGH);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-recolor
    proto.recolor = function recolor(matrix) {
        return this.out("-recolor", matrix);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-red-primary
    proto.redPrimary = function redPrimary(x, y) {
        return this.out("-red-primary", x, y);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-remote
    proto.remote = function remote() {
        return this.out("-remote");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-render
    proto.render = function render() {
        return this.out("-render");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-repage
    proto.repage = function repage(width, height, xoff, yoff, arg) {
        if (arguments[0] === "+") return this.out("+repage");
        return this.out("-repage", width + 'x' + height + '+' + xoff + '+' + yoff + (arg || ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-sample
    proto.sample = function sample(geometry) {
        return this.out("-sample", geometry);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-sampling-factor
    proto.samplingFactor = function samplingFactor(horizontalFactor, verticalFactor) {
        return this.out("-sampling-factor", horizontalFactor + 'x' + verticalFactor);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-scene
    proto.scene = function scene(value) {
        return this.out("-scene", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-scenes
    proto.scenes = function scenes(start, end) {
        return this.out("-scenes", start + '-' + end);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-screen
    proto.screen = function screen() {
        return this.out("-screen");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-set
    proto.set = function set(attribute, value) {
        return this.out("-set", attribute, value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-segment
    proto.segment = function segment(clusterThreshold, smoothingThreshold) {
        return this.out("-segment", clusterThreshold + 'x' + smoothingThreshold);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-shade
    proto.shade = function shade(azimuth, elevation) {
        return this.out("-shade", azimuth + 'x' + elevation);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-shadow
    proto.shadow = function shadow(radius, sigma) {
        return this.out("-shadow", argsToArray(radius, sigma).join('x'));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-shared-memory
    proto.sharedMemory = function sharedMemory() {
        return this.out("-shared-memory");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-shave
    proto.shave = function shave(width, height, percent) {
        return this.out("-shave", width + 'x' + height + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-shear
    proto.shear = function shear(xDegrees, yDegreees) {
        return this.out("-shear", xDegrees + 'x' + yDegreees);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-silent
    proto.silent = function silent(color) {
        return this.out("-silent");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-size
    proto.rawSize = function rawSize(width, height, offset) {
        var off = 'undefined' != typeof offset ? '+' + offset : '';
        return this.out("-size", width + 'x' + height + off);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-snaps
    proto.snaps = function snaps(value) {
        return this.out("-snaps", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-stegano
    proto.stegano = function stegano(offset) {
        return this.out("-stegano", offset);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-stereo
    proto.stereo = function stereo() {
        return this.out("-stereo");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-text-font
    proto.textFont = function textFont(name) {
        return this.out("-text-font", name);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-texture
    proto.texture = function texture(filename) {
        return this.out("-texture", filename);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-threshold
    proto.threshold = function threshold(value, percent) {
        return this.out("-threshold", value + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-thumbnail
    proto.thumbnail = function thumbnail(w, h, options) {
        options = options || "";
        var geometry, wIsValid = Boolean(w || w === 0), hIsValid = Boolean(h || h === 0);
        if (wIsValid && hIsValid) {
            geometry = w + "x" + h + options;
        } else if (wIsValid) {
            // GraphicsMagick requires <width>x<options>, ImageMagick requires <width><options>
            geometry = this._options.imageMagick ? w + options : w + 'x' + options;
        } else if (hIsValid) {
            geometry = 'x' + h + options;
        } else {
            return this;
        }
        return this.out("-thumbnail", geometry);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-tile
    proto.tile = function tile(filename) {
        return this.out("-tile", filename);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-title
    proto.title = function title(string) {
        return this.out("-title", string);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-transform
    proto.transform = function transform(color) {
        return this.out("-transform", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-transparent
    proto.transparent = function transparent(color) {
        return this.out("-transparent", color);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-treedepth
    proto.treeDepth = function treeDepth(value) {
        return this.out("-treedepth", value);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-update
    proto.update = function update(seconds) {
        return this.out("-update", seconds);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-units
    proto.units = function units(type) {
        return this.out("-units", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-unsharp
    proto.unsharp = function unsharp(radius, sigma, amount, threshold) {
        var arg = radius;
        if (typeof sigma != 'undefined') arg += 'x' + sigma;
        if (typeof amount != 'undefined') arg += '+' + amount;
        if (typeof threshold != 'undefined') arg += '+' + threshold;
        return this.out("-unsharp", arg);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-use-pixmap
    proto.usePixmap = function usePixmap() {
        return this.out("-use-pixmap");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-view
    proto.view = function view(string) {
        return this.out("-view", string);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-virtual-pixel
    proto.virtualPixel = function virtualPixel(method) {
        return this.out("-virtual-pixel", method);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-visual
    proto.visual = function visual(type) {
        return this.out("-visual", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-watermark
    proto.watermark = function watermark(brightness, saturation) {
        return this.out("-watermark", brightness + 'x' + saturation);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-wave
    proto.wave = function wave(amplitude, wavelength) {
        return this.out("-wave", amplitude + 'x' + wavelength);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-white-point
    proto.whitePoint = function whitePoint(x, y) {
        return this.out("-white-point", x + 'x' + y);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-white-threshold
    proto.whiteThreshold = function whiteThreshold(red, green, blue, opacity) {
        return this.out("-white-threshold", argsToArray(red, green, blue, opacity).join(','));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-window
    proto.window = function window(id) {
        return this.out("-window", id);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-window-group
    proto.windowGroup = function windowGroup() {
        return this.out("-window-group");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-strip (graphicsMagick >= 1.3.15)
    proto.strip = function strip() {
        if (this._options.imageMagick) return this.out("-strip");
        return this.noProfile().out("+comment"); //Equivalent to "-strip" for all versions of graphicsMagick
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-interlace
    proto.interlace = function interlace(type) {
        return this.out("-interlace", type || "None");
    };
    // force output format
    proto.setFormat = function setFormat(format) {
        if (format) this._outputFormat = format;
        return this;
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-resize
    proto.resize = function resize(w, h, options) {
        options = options || "";
        var geometry, wIsValid = Boolean(w || w === 0), hIsValid = Boolean(h || h === 0);
        if (wIsValid && hIsValid) {
            geometry = w + "x" + h + options;
        } else if (wIsValid) {
            // GraphicsMagick requires <width>x<options>, ImageMagick requires <width><options>
            geometry = this._options.imageMagick ? w + options : w + 'x' + options;
        } else if (hIsValid) {
            geometry = 'x' + h + options;
        } else {
            return this;
        }
        return this.out("-resize", geometry);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-resize with '!' option
    proto.resizeExact = function resize(w, h) {
        var options = "!";
        return proto.resize.apply(this, [
            w,
            h,
            options
        ]);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-scale
    proto.scale = function scale(w, h, options) {
        options = options || "";
        var geometry;
        if (w && h) {
            geometry = w + "x" + h + options;
        } else if (w && !h) {
            geometry = this._options.imageMagick ? w + options : w + 'x' + options;
        } else if (!w && h) {
            geometry = 'x' + h + options;
        }
        return this.out("-scale", geometry);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-filter
    proto.filter = function filter(val) {
        return this.out("-filter", val);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-density
    proto.density = function density(w, h) {
        if (w && !h && this._options.imageMagick) {
            // GraphicsMagick requires <width>x<height>y, ImageMagick may take dpi<resolution>
            // recommended 300dpi for higher quality
            return this.in("-density", w);
        }
        return this.in("-density", w + "x" + h);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-profile
    proto.noProfile = function noProfile() {
        this.out('+profile', '"*"');
        return this;
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-resample
    proto.resample = function resample(w, h) {
        return this.out("-resample", w + "x" + h);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-rotate
    proto.rotate = function rotate(color, deg) {
        return this.out("-background", color, "-rotate", String(deg || 0));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-flip
    proto.flip = function flip() {
        return this.out("-flip");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-flop
    proto.flop = function flop() {
        return this.out("-flop");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-crop
    proto.crop = function crop(w, h, x, y, percent) {
        if (this.inputIs('jpg')) {
            // avoid error "geometry does not contain image (unable to crop image)" - gh-17
            var index = this._in.indexOf('-size');
            if (~index) {
                this._in.splice(index, 2);
            }
        }
        return this.out("-crop", w + "x" + h + "+" + (x || 0) + "+" + (y || 0) + (percent ? '%' : ''));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-magnify
    proto.magnify = function magnify(factor) {
        return this.in("-magnify");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.minify = function minify() {
        return this.in("-minify");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-quality
    proto.quality = function quality(val) {
        return this.in("-quality", val || 75);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-blur
    proto.blur = function blur(radius, sigma) {
        return this.out("-blur", radius + (sigma ? "x" + sigma : ""));
    };
    // http://www.graphicsmagick.org/convert.html
    proto.charcoal = function charcoal(factor) {
        return this.out("-charcoal", factor || 2);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-modulate
    proto.modulate = function modulate(b, s, h) {
        return this.out("-modulate", [
            b,
            s,
            h
        ].join(","));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-antialias
    // note: antialiasing is enabled by default
    proto.antialias = function antialias(disable) {
        return false === disable ? this.out("+antialias") : this;
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-depth
    proto.bitdepth = function bitdepth(val) {
        return this.out("-depth", val);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-colors
    proto.colors = function colors(val) {
        return this.out("-colors", val || 128);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-colorspace
    proto.colorspace = function colorspace(val) {
        return this.out("-colorspace", val);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-comment
    proto.comment = comment("-comment");
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-contrast
    proto.contrast = function contrast(mult) {
        var arg = (parseInt(mult, 10) || 0) > 0 ? "+contrast" : "-contrast";
        mult = Math.abs(mult) || 1;
        while(mult--){
            this.out(arg);
        }
        return this;
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-cycle
    proto.cycle = function cycle(amount) {
        return this.out("-cycle", amount || 2);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.despeckle = function despeckle() {
        return this.out("-despeckle");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-dither
    // note: either colors() or monochrome() must be used for this
    // to take effect.
    proto.dither = function dither(on) {
        var sign = false === on ? "+" : "-";
        return this.out(sign + "dither");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.monochrome = function monochrome() {
        return this.out("-monochrome");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.edge = function edge(radius) {
        return this.out("-edge", radius || 1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.emboss = function emboss(radius) {
        return this.out("-emboss", radius || 1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.enhance = function enhance() {
        return this.out("-enhance");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.equalize = function equalize() {
        return this.out("-equalize");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-gamma
    proto.gamma = function gamma(r, g, b) {
        return this.out("-gamma", [
            r,
            g,
            b
        ].join());
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.implode = function implode(factor) {
        return this.out("-implode", factor || 1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-comment
    proto.label = comment("-label");
    var limits = [
        "disk",
        "file",
        "map",
        "memory",
        "pixels",
        "threads"
    ];
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-limit
    proto.limit = function limit(type, val) {
        type = type.toLowerCase();
        if (!~limits.indexOf(type)) {
            return this;
        }
        return this.out("-limit", type, val);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.median = function median(radius) {
        return this.out("-median", radius || 1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-negate
    proto.negative = function negative(grayscale) {
        var sign = grayscale ? "+" : "-";
        return this.out(sign + "negate");
    };
    var noises = [
        "uniform",
        "gaussian",
        "multiplicative",
        "impulse",
        "laplacian",
        "poisson"
    ];
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-noise
    proto.noise = function noise(radius) {
        radius = String(radius).toLowerCase();
        var sign = ~noises.indexOf(radius) ? "+" : "-";
        return this.out(sign + "noise", radius);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-paint
    proto.paint = function paint(radius) {
        return this.out("-paint", radius);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-raise
    proto.raise = function raise(w, h) {
        return this.out("-raise", (w || 0) + "x" + (h || 0));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-raise
    proto.lower = function lower(w, h) {
        return this.out("+raise", (w || 0) + "x" + (h || 0));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-region
    proto.region = function region(w, h, x, y) {
        w = w || 0;
        h = h || 0;
        x = x || 0;
        y = y || 0;
        return this.out("-region", w + "x" + h + "+" + x + "+" + y);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-roll
    proto.roll = function roll(x, y) {
        x = ((x = parseInt(x, 10) || 0) >= 0 ? "+" : "") + x;
        y = ((y = parseInt(y, 10) || 0) >= 0 ? "+" : "") + y;
        return this.out("-roll", x + y);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-sharpen
    proto.sharpen = function sharpen(radius, sigma) {
        sigma = sigma ? "x" + sigma : "";
        return this.out("-sharpen", radius + sigma);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-solarize
    proto.solarize = function solarize(factor) {
        return this.out("-solarize", (factor || 1) + "%");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-spread
    proto.spread = function spread(amount) {
        return this.out("-spread", amount || 5);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-swirl
    proto.swirl = function swirl(degrees) {
        return this.out("-swirl", degrees || 180);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-type
    proto.type = function type(type) {
        return this.in("-type", type);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-trim
    proto.trim = function trim() {
        return this.out("-trim");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-extent
    proto.extent = function extent(w, h, options) {
        options = options || "";
        var geometry;
        if (w && h) {
            geometry = w + "x" + h + options;
        } else if (w && !h) {
            geometry = this._options.imageMagick ? w + options : w + 'x' + options;
        } else if (!w && h) {
            geometry = 'x' + h + options;
        }
        return this.out("-extent", geometry);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-gravity
    // Be sure to use gravity BEFORE extent
    proto.gravity = function gravity(type) {
        if (!type || !~gravity.types.indexOf(type)) {
            type = "NorthWest"; // Documented default.
        }
        return this.out("-gravity", type);
    };
    proto.gravity.types = [
        "NorthWest",
        "North",
        "NorthEast",
        "West",
        "Center",
        "East",
        "SouthWest",
        "South",
        "SouthEast"
    ];
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-flatten
    proto.flatten = function flatten() {
        return this.out("-flatten");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-background
    proto.background = function background(color) {
        return this.in("-background", color);
    };
};
/**
 * Generates a handler for comments/labels.
 */ function comment(arg) {
    return function(format) {
        format = String(format);
        format = "@" == format.charAt(0) ? format.substring(1) : format;
        return this.out(arg, '"' + format + '"');
    };
}
}}),
"[project]/node_modules/gm/lib/drawing.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Module dependencies.
 */ var escape = __turbopack_context__.r("[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)").escape;
/**
 * Extend proto.
 */ module.exports = function(proto) {
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-fill
    proto.fill = function fill(color) {
        return this.out("-fill", color || "none");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-stroke
    proto.stroke = function stroke(color, width) {
        if (width) {
            this.strokeWidth(width);
        }
        return this.out("-stroke", color || "none");
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-strokewidth
    proto.strokeWidth = function strokeWidth(width) {
        return this.out("-strokewidth", width);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-font
    proto.font = function font(font, size) {
        if (size) {
            this.fontSize(size);
        }
        return this.out("-font", font);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html
    proto.fontSize = function fontSize(size) {
        return this.out("-pointsize", size);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.draw = function draw(args) {
        return this.out("-draw", [].slice.call(arguments).join(" "));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawPoint = function drawPoint(x, y) {
        return this.draw("point", x + "," + y);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawLine = function drawLine(x0, y0, x1, y1) {
        return this.draw("line", x0 + "," + y0, x1 + "," + y1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawRectangle = function drawRectangle(x0, y0, x1, y1, wc, hc) {
        var shape = "rectangle", lastarg;
        if ("undefined" !== typeof wc) {
            shape = "roundRectangle";
            if ("undefined" === typeof hc) {
                hc = wc;
            }
            lastarg = wc + "," + hc;
        }
        return this.draw(shape, x0 + "," + y0, x1 + "," + y1, lastarg);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawArc = function drawArc(x0, y0, x1, y1, a0, a1) {
        return this.draw("arc", x0 + "," + y0, x1 + "," + y1, a0 + "," + a1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawEllipse = function drawEllipse(x0, y0, rx, ry, a0, a1) {
        if (a0 == undefined) a0 = 0;
        if (a1 == undefined) a1 = 360;
        return this.draw("ellipse", x0 + "," + y0, rx + "," + ry, a0 + "," + a1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawCircle = function drawCircle(x0, y0, x1, y1) {
        return this.draw("circle", x0 + "," + y0, x1 + "," + y1);
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawPolyline = function drawPolyline() {
        return this.draw("polyline", formatPoints(arguments));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawPolygon = function drawPolygon() {
        return this.draw("polygon", formatPoints(arguments));
    };
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawBezier = function drawBezier() {
        return this.draw("bezier", formatPoints(arguments));
    };
    proto._gravities = [
        "northwest",
        "north",
        "northeast",
        "west",
        "center",
        "east",
        "southwest",
        "south",
        "southeast"
    ];
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.drawText = function drawText(x0, y0, text, gravity) {
        var gravity = String(gravity || "").toLowerCase(), arg = [
            "text " + x0 + "," + y0 + " " + escape(text)
        ];
        if (~this._gravities.indexOf(gravity)) {
            arg.unshift("gravity", gravity);
        }
        return this.draw.apply(this, arg);
    };
    proto._drawProps = [
        "color",
        "matte"
    ];
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-draw
    proto.setDraw = function setDraw(prop, x, y, method) {
        prop = String(prop || "").toLowerCase();
        if (!~this._drawProps.indexOf(prop)) {
            return this;
        }
        return this.draw(prop, x + "," + y, method);
    };
};
function formatPoints(points) {
    var len = points.length, result = [], i = 0;
    for(; i < len; ++i){
        result.push(points[i].join(","));
    }
    return result;
}
}}),
"[project]/node_modules/gm/lib/convenience/thumb.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Extend proto.
 */ module.exports = function(proto) {
    proto.thumb = function thumb(w, h, name, quality, align, progressive, callback, opts) {
        var self = this, args = Array.prototype.slice.call(arguments);
        opts = args.pop();
        if (typeof opts === 'function') {
            callback = opts;
            opts = '';
        } else {
            callback = args.pop();
        }
        w = args.shift();
        h = args.shift();
        name = args.shift();
        quality = args.shift() || 63;
        align = args.shift() || 'topleft';
        var interlace = args.shift() ? 'Line' : 'None';
        self.size(function(err, size) {
            if (err) {
                return callback.apply(self, arguments);
            }
            w = parseInt(w, 10);
            h = parseInt(h, 10);
            var w1, h1;
            var xoffset = 0;
            var yoffset = 0;
            if (size.width < size.height) {
                w1 = w;
                h1 = Math.floor(size.height * (w / size.width));
                if (h1 < h) {
                    w1 = Math.floor(w1 * ((h - h1) / h + 1));
                    h1 = h;
                }
            } else if (size.width > size.height) {
                h1 = h;
                w1 = Math.floor(size.width * (h / size.height));
                if (w1 < w) {
                    h1 = Math.floor(h1 * ((w - w1) / w + 1));
                    w1 = w;
                }
            } else if (size.width == size.height) {
                var bigger = w > h ? w : h;
                w1 = bigger;
                h1 = bigger;
            }
            if (align == 'center') {
                if (w < w1) {
                    xoffset = (w1 - w) / 2;
                }
                if (h < h1) {
                    yoffset = (h1 - h) / 2;
                }
            }
            self.quality(quality).in("-size", w1 + "x" + h1).scale(w1, h1, opts).crop(w, h, xoffset, yoffset).interlace(interlace).noProfile().write(name, function() {
                callback.apply(self, arguments);
            });
        });
        return self;
    };
    proto.thumbExact = function() {
        var self = this, args = Array.prototype.slice.call(arguments);
        args.push('!');
        self.thumb.apply(self, args);
    };
};
}}),
"[project]/node_modules/gm/lib/convenience/morph.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Module dependencies.
 */ var fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
var parallel = __turbopack_context__.r("[project]/node_modules/array-parallel/index.js [app-route] (ecmascript)");
/**
 * Extend proto.
 */ module.exports = function(proto) {
    /**
   * Do nothing.
   */ function noop() {}
    // http://www.graphicsmagick.org/GraphicsMagick.html#details-morph
    proto.morph = function morph(other, outname, callback) {
        if (!outname) {
            throw new Error("an output filename is required");
        }
        callback = (callback || noop).bind(this);
        var self = this;
        if (Array.isArray(other)) {
            other.forEach(function(img) {
                self.out(img);
            });
            self.out("-morph", other.length);
        } else {
            self.out(other, "-morph", 1);
        }
        self.write(outname, function(err, stdout, stderr, cmd) {
            if (err) return callback(err, stdout, stderr, cmd);
            // Apparently some platforms create the following temporary files.
            // Check if the output file exists, if it doesn't, then
            // work with temporary files.
            fs.exists(outname, function(exists) {
                if (exists) return callback(null, stdout, stderr, cmd);
                parallel([
                    fs.unlink.bind(fs, outname + '.0'),
                    fs.unlink.bind(fs, outname + '.2'),
                    fs.rename.bind(fs, outname + '.1', outname)
                ], function(err) {
                    callback(err, stdout, stderr, cmd);
                });
            });
        });
        return self;
    };
};
}}),
"[project]/node_modules/gm/lib/convenience/sepia.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Extend proto.
 */ module.exports = function(proto) {
    proto.sepia = function sepia() {
        return this.modulate(115, 0, 100).colorize(7, 21, 50);
    };
};
}}),
"[project]/node_modules/gm/lib/convenience/autoOrient.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Extend proto.
 */ module.exports = function(proto) {
    var exifTransforms = {
        topleft: '',
        topright: [
            '-flop'
        ],
        bottomright: [
            '-rotate',
            180
        ],
        bottomleft: [
            '-flip'
        ],
        lefttop: [
            '-flip',
            '-rotate',
            90
        ],
        righttop: [
            '-rotate',
            90
        ],
        rightbottom: [
            '-flop',
            '-rotate',
            90
        ],
        leftbottom: [
            '-rotate',
            270
        ]
    };
    proto.autoOrient = function autoOrient() {
        // Always strip EXIF data since we can't
        // change/edit it.
        // imagemagick has a native -auto-orient option
        // so does graphicsmagick, but in 1.3.18.
        // nativeAutoOrient option enables this if you know you have >= 1.3.18
        if (this._options.nativeAutoOrient || this._options.imageMagick) {
            this.out('-auto-orient');
            return this;
        }
        this.preprocessor(function(callback) {
            this.orientation({
                bufferStream: true
            }, function(err, orientation) {
                if (err) return callback(err);
                var transforms = exifTransforms[orientation.toLowerCase()];
                if (transforms) {
                    // remove any existing transforms that might conflict
                    var index = this._out.indexOf(transforms[0]);
                    if (~index) {
                        this._out.splice(index, transforms.length);
                    }
                    // repage to fix coordinates
                    this._out.unshift.apply(this._out, transforms.concat('-page', '+0+0'));
                }
                callback();
            });
        });
        return this;
    };
};
}}),
"[project]/node_modules/gm/lib/convenience.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Extend proto
 */ module.exports = function(proto) {
    __turbopack_context__.r("[project]/node_modules/gm/lib/convenience/thumb.js [app-route] (ecmascript)")(proto);
    __turbopack_context__.r("[project]/node_modules/gm/lib/convenience/morph.js [app-route] (ecmascript)")(proto);
    __turbopack_context__.r("[project]/node_modules/gm/lib/convenience/sepia.js [app-route] (ecmascript)")(proto);
    __turbopack_context__.r("[project]/node_modules/gm/lib/convenience/autoOrient.js [app-route] (ecmascript)")(proto);
};
}}),
"[project]/node_modules/gm/lib/command.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Module dependencies.
 */ var spawn = __turbopack_context__.r("[project]/node_modules/cross-spawn/index.js [app-route] (ecmascript)");
var utils = __turbopack_context__.r("[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)");
var debug = __turbopack_context__.r("[project]/node_modules/gm/node_modules/debug/src/index.js [app-route] (ecmascript)")('gm');
var series = __turbopack_context__.r("[project]/node_modules/array-series/index.js [app-route] (ecmascript)");
var PassThrough = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)").PassThrough;
/**
 * Error messaging.
 */ var noBufferConcat = 'gm v1.9.0+ required node v0.8+. Please update your version of node, downgrade gm < 1.9, or do not use `bufferStream`.';
/**
 * Extend proto
 */ module.exports = function(proto) {
    function args(prop) {
        return function args() {
            var len = arguments.length;
            var a = [];
            var i = 0;
            for(; i < len; ++i){
                a.push(arguments[i]);
            }
            this[prop] = this[prop].concat(a);
            return this;
        };
    }
    function streamToUnemptyBuffer(stream, callback) {
        var done = false;
        var buffers = [];
        stream.on('data', function(data) {
            buffers.push(data);
        });
        stream.on('end', function() {
            if (done) return;
            done = true;
            let result = Buffer.concat(buffers);
            buffers = null;
            if (result.length === 0) {
                const err = new Error("Stream yields empty buffer");
                callback(err, null);
            } else {
                callback(null, result);
            }
        });
        stream.on('error', function(err) {
            done = true;
            buffers = null;
            callback(err);
        });
    }
    proto.in = args('_in');
    proto.out = args('_out');
    proto._preprocessor = [];
    proto.preprocessor = args('_preprocessor');
    /**
   * Execute the command and write the image to the specified file name.
   *
   * @param {String} name
   * @param {Function} callback
   * @return {Object} gm
   */ proto.write = function write(name, callback) {
        if (!callback) callback = name, name = null;
        if ("function" !== typeof callback) {
            throw new TypeError("gm().write() expects a callback function");
        }
        if (!name) {
            return callback(TypeError("gm().write() expects a filename when writing new files"));
        }
        this.outname = name;
        var self = this;
        this._preprocess(function(err) {
            if (err) return callback(err);
            self._spawn(self.args(), true, callback);
        });
    };
    /**
   * Execute the command and return stdin and stderr
   * ReadableStreams providing the image data.
   * If no callback is passed, a "through" stream will be returned,
   * and stdout will be piped through, otherwise the error will be passed.
   *
   * @param {String} format (optional)
   * @param {Function} callback (optional)
   * @return {Stream}
   */ proto.stream = function stream(format, callback) {
        if (!callback && typeof format === 'function') {
            callback = format;
            format = null;
        }
        var throughStream;
        if ("function" !== typeof callback) {
            throughStream = new PassThrough();
            callback = function(err, stdout, stderr) {
                if (err) throughStream.emit('error', err);
                else stdout.pipe(throughStream);
            };
        }
        if (format) {
            format = format.split('.').pop();
            this.outname = format + ":-";
        }
        var self = this;
        this._preprocess(function(err) {
            if (err) return callback(err);
            return self._spawn(self.args(), false, callback);
        });
        return throughStream || this;
    };
    /**
   * Convenience function for `proto.stream`.
   * Simply returns the buffer instead of the stream.
   *
   * @param {String} format (optional)
   * @param {Function} callback
   * @return {null}
   */ proto.toBuffer = function toBuffer(format, callback) {
        if (!callback) callback = format, format = null;
        if ("function" !== typeof callback) {
            throw new Error('gm().toBuffer() expects a callback.');
        }
        return this.stream(format, function(err, stdout) {
            if (err) return callback(err);
            streamToUnemptyBuffer(stdout, callback);
        });
    };
    /**
    * Run any preProcessor functions in series. Used by autoOrient.
    *
    * @param {Function} callback
    * @return {Object} gm
    */ proto._preprocess = function _preprocess(callback) {
        series(this._preprocessor, this, callback);
    };
    /**
    * Execute the command, buffer input and output, return stdout and stderr buffers.
    *
    * @param {String} bin
    * @param {Array} args
    * @param {Function} callback
    * @return {Object} gm
    */ proto._exec = function _exec(args, callback) {
        return this._spawn(args, true, callback);
    };
    /**
    * Execute the command with stdin, returning stdout and stderr streams or buffers.
    * @param {String} bin
    * @param {Array} args
    * @param {ReadableStream} stream
    * @param {Boolean} shouldBuffer
    * @param {Function} callback, signature (err, stdout, stderr) -> *
    * @return {Object} gm
    * @TODO refactor this mess
    */ proto._spawn = function _spawn(args, bufferOutput, callback) {
        var appPath = this._options.appPath || '';
        var bin;
        // Resolve executable
        switch(this._options.imageMagick){
            // legacy behavior
            case true:
                bin = args.shift();
                break;
            // ImgeMagick >= 7
            case '7+':
                bin = 'magick';
                break;
            // GraphicsMagick
            default:
                bin = 'gm';
                break;
        }
        // Prepend app path
        bin = appPath + bin;
        var cmd = bin + ' ' + args.map(utils.escape).join(' '), self = this, proc, err, timeout = parseInt(this._options.timeout), disposers = this._options.disposers, timeoutId;
        debug(cmd);
        //imageMagick does not support minify (https://github.com/aheckmann/gm/issues/385)
        if (args.indexOf("-minify") > -1 && this._options.imageMagick) {
            return cb(new Error("imageMagick does not support minify, use -scale or -sample. Alternatively, use graphicsMagick"));
        }
        try {
            proc = spawn(bin, args);
        } catch (e) {
            return cb(e);
        }
        proc.stdin.once('error', cb);
        proc.on('error', function(err) {
            if (err.code === 'ENOENT') {
                cb(new Error('Could not execute GraphicsMagick/ImageMagick: ' + cmd + " this most likely means the gm/convert binaries can't be found"));
            } else {
                cb(err);
            }
        });
        if (timeout) {
            timeoutId = setTimeout(function() {
                dispose('gm() resulted in a timeout.');
            }, timeout);
        }
        if (disposers) {
            disposers.forEach(function(disposer) {
                disposer.events.forEach(function(event) {
                    disposer.emitter.on(event, dispose);
                });
            });
        }
        if (self.sourceBuffer) {
            proc.stdin.write(this.sourceBuffer);
            proc.stdin.end();
        } else if (self.sourceStream) {
            if (!self.sourceStream.readable) {
                return cb(new Error("gm().stream() or gm().write() with a non-readable stream."));
            }
            self.sourceStream.pipe(proc.stdin);
            // bufferStream
            // We convert the input source from a stream to a buffer.
            if (self.bufferStream && !this._buffering) {
                if (!Buffer.concat) {
                    throw new Error(noBufferConcat);
                }
                // Incase there are multiple processes in parallel,
                // we only need one
                self._buffering = true;
                streamToUnemptyBuffer(self.sourceStream, function(err, buffer) {
                    self.sourceBuffer = buffer;
                    self.sourceStream = null; // The stream is now dead
                });
            }
        }
        // for _exec operations (identify() mostly), we also
        // need to buffer the output stream before returning
        if (bufferOutput) {
            var stdout = '', stderr = '', onOut, onErr, onExit;
            proc.stdout.on('data', onOut = function(data) {
                stdout += data;
            });
            proc.stderr.on('data', onErr = function(data) {
                stderr += data;
            });
            proc.on('close', onExit = function(code, signal) {
                let err;
                if (code !== 0 || signal !== null) {
                    err = new Error('Command failed: ' + stderr);
                    err.code = code;
                    err.signal = signal;
                }
                ;
                cb(err, stdout, stderr, cmd);
                stdout = stderr = onOut = onErr = onExit = null;
            });
        } else {
            cb(null, proc.stdout, proc.stderr, cmd);
        }
        return self;
        "TURBOPACK unreachable";
        function cb(err, stdout, stderr, cmd) {
            if (cb.called) return;
            if (timeoutId) clearTimeout(timeoutId);
            cb.called = 1;
            if (args[0] !== 'identify' && bin !== 'identify') {
                self._in = [];
                self._out = [];
            }
            callback.call(self, err, stdout, stderr, cmd);
        }
        function dispose(msg) {
            const message = msg ? msg : 'gm() was disposed';
            const err = new Error(message);
            cb(err);
            if (proc.exitCode === null) {
                proc.stdin.pause();
                proc.kill();
            }
        }
    };
    /**
   * Returns arguments to be used in the command.
   *
   * @return {Array}
   */ proto.args = function args() {
        var outname = this.outname || "-";
        if (this._outputFormat) outname = this._outputFormat + ':' + outname;
        return [].concat(this._subCommand, this._in, this.src(), this._out, outname).filter(Boolean); // remove falsey
    };
    /**
   * Adds an img source formatter.
   *
   * `formatters` are passed an array of images which will be
   * used as 'input' images for the command. Useful for methods
   * like `.append()` where multiple source images may be used.
   *
   * @param {Function} formatter
   * @return {gm} this
   */ proto.addSrcFormatter = function addSrcFormatter(formatter) {
        if ('function' != typeof formatter) throw new TypeError('sourceFormatter must be a function');
        this._sourceFormatters || (this._sourceFormatters = []);
        this._sourceFormatters.push(formatter);
        return this;
    };
    /**
   * Applies all _sourceFormatters
   *
   * @return {Array}
   */ proto.src = function src() {
        var arr = [];
        for(var i = 0; i < this._sourceFormatters.length; ++i){
            this._sourceFormatters[i].call(this, arr);
        }
        return arr;
    };
    /**
   * Image types.
   */ var types = {
        'jpg': /\.jpe?g$/i,
        'png': /\.png$/i,
        'gif': /\.gif$/i,
        'tiff': /\.tif?f$/i,
        'bmp': /(?:\.bmp|\.dib)$/i,
        'webp': /\.webp$/i
    };
    types.jpeg = types.jpg;
    types.tif = types.tiff;
    types.dib = types.bmp;
    /**
   * Determine the type of source image.
   *
   * @param {String} type
   * @return {Boolean}
   * @example
   *   if (this.inputIs('png')) ...
   */ proto.inputIs = function inputIs(type) {
        if (!type) return false;
        var rgx = types[type];
        if (!rgx) {
            if ('.' !== type[0]) type = '.' + type;
            rgx = new RegExp('\\' + type + '$', 'i');
        }
        return rgx.test(this.source);
    };
    /**
   * add disposer (like 'close' of http.IncomingMessage) in order to dispose gm() with any event
   *
   * @param {EventEmitter} emitter
   * @param {Array} events
   * @return {Object} gm
   * @example
   *   command.addDisposer(req, ['close', 'end', 'finish']);
   */ proto.addDisposer = function addDisposer(emitter, events) {
        if (!this._options.disposers) {
            this._options.disposers = [];
        }
        this._options.disposers.push({
            emitter: emitter,
            events: events
        });
        return this;
    };
};
}}),
"[project]/node_modules/gm/lib/compare.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// compare
var spawn = __turbopack_context__.r("[project]/node_modules/cross-spawn/index.js [app-route] (ecmascript)");
var debug = __turbopack_context__.r("[project]/node_modules/gm/node_modules/debug/src/index.js [app-route] (ecmascript)")('gm');
var utils = __turbopack_context__.r("[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)");
/**
 * Compare two images uses graphicsmagicks `compare` command.
 *
 * gm.compare(img1, img2, 0.4, function (err, equal, equality) {
 *   if (err) return handle(err);
 *   console.log('The images are equal: %s', equal);
 *   console.log('There equality was %d', equality);
 * });
 *
 * @param {String} orig Path to an image.
 * @param {String} compareTo Path to another image to compare to `orig`.
 * @param {Number|Object} [options] Options object or the amount of difference to tolerate before failing - defaults to 0.4
 * @param {Function} cb(err, Boolean, equality, rawOutput)
 */ module.exports = exports = function(proto) {
    function compare(orig, compareTo, options, cb) {
        var isImageMagick = this._options && this._options.imageMagick;
        var appPath = this._options && this._options.appPath || '';
        var args = [
            '-metric',
            'mse',
            orig,
            compareTo
        ];
        // Resove executable
        let bin;
        switch(isImageMagick){
            case true:
                bin = 'compare';
                break;
            case '7+':
                bin = 'magick';
                args.unshift('compare');
                break;
            default:
                bin = 'gm';
                args.unshift('compare');
                break;
        }
        // Prepend app path
        bin = appPath + bin;
        var tolerance = 0.4;
        // outputting the diff image
        if (typeof options === 'object') {
            if (options.highlightColor && options.highlightColor.indexOf('"') < 0) {
                options.highlightColor = '"' + options.highlightColor + '"';
            }
            if (options.file) {
                if (typeof options.file !== 'string') {
                    throw new TypeError('The path for the diff output is invalid');
                }
                // graphicsmagick defaults to red
                if (options.highlightColor) {
                    args.push('-highlight-color');
                    args.push(options.highlightColor);
                }
                if (options.highlightStyle) {
                    args.push('-highlight-style');
                    args.push(options.highlightStyle);
                }
                // For IM, filename is the last argument. For GM it's `-file <filename>`
                if (!isImageMagick) {
                    args.push('-file');
                }
                args.push(options.file);
            }
            if (typeof options.tolerance != 'undefined') {
                if (typeof options.tolerance !== 'number') {
                    throw new TypeError('The tolerance value should be a number');
                }
                tolerance = options.tolerance;
            }
        } else {
            // For ImageMagick diff file is required but we don't care about it, so null it out
            if (isImageMagick) {
                args.push('null:');
            }
            if (typeof options == 'function') {
                cb = options; // tolerance value not provided, flip the cb place
            } else {
                tolerance = options;
            }
        }
        var cmd = bin + ' ' + args.map(utils.escape).join(' ');
        debug(cmd);
        var proc = spawn(bin, args);
        var stdout = '';
        var stderr = '';
        proc.stdout.on('data', function(data) {
            stdout += data;
        });
        proc.stderr.on('data', function(data) {
            stderr += data;
        });
        proc.on('close', function(code) {
            // ImageMagick returns err code 2 if err, 0 if similar, 1 if dissimilar
            if (isImageMagick) {
                if (code === 0) {
                    return cb(null, 0 <= tolerance, 0, stdout);
                } else if (code === 1) {
                    stdout = stderr;
                } else {
                    return cb(stderr);
                }
            } else {
                if (code !== 0) {
                    return cb(stderr);
                }
            }
            // Since ImageMagick similar gives err code 0 and no stdout, there's really no matching
            // Otherwise, output format for IM is `12.00 (0.123)` and for GM it's `Total: 0.123`
            var regex = isImageMagick ? /\((\d+\.?[\d\-\+e]*)\)/m : /Total: (\d+\.?\d*)/m;
            var match = regex.exec(stdout);
            if (!match) {
                return cb(new Error('Unable to parse output.\nGot ' + stdout));
            }
            var equality = parseFloat(match[1]);
            cb(null, equality <= tolerance, equality, stdout, orig, compareTo);
        });
    }
    if (proto) {
        proto.compare = compare;
    }
    return compare;
};
}}),
"[project]/node_modules/gm/lib/composite.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// composite
/**
 * Composite images together using the `composite` command in graphicsmagick.
 *
 * gm('/path/to/image.jpg')
 * .composite('/path/to/second_image.jpg')
 * .geometry('+100+150')
 * .write('/path/to/composite.png', function(err) {
 *   if(!err) console.log("Written composite image.");
 * });
 *
 * @param {String} other  Path to the image that contains the changes.
 * @param {String} [mask] Path to the image with opacity informtion. Grayscale.
 */ module.exports = exports = function(proto) {
    proto.composite = function(other, mask) {
        this.in(other);
        // If the mask is defined, add it to the output.
        if (typeof mask !== "undefined") this.out(mask);
        this.subCommand("composite");
        return this;
    };
};
}}),
"[project]/node_modules/gm/lib/montage.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// montage
/**
 * Montage images next to each other using the `montage` command in graphicsmagick.
 *
 * gm('/path/to/image.jpg')
 * .montage('/path/to/second_image.jpg')
 * .geometry('+100+150')
 * .write('/path/to/montage.png', function(err) {
 *   if(!err) console.log("Written montage image.");
 * });
 *
 * @param {String} other  Path to the image that contains the changes.
 */ module.exports = exports = function(proto) {
    proto.montage = function(other) {
        this.in(other);
        this.subCommand("montage");
        return this;
    };
};
}}),
"[project]/node_modules/gm/package.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"name\":\"gm\",\"description\":\"GraphicsMagick and ImageMagick for node.js\",\"version\":\"1.25.1\",\"author\":\"Aaron Heckmann <<EMAIL>>\",\"keywords\":[\"graphics\",\"magick\",\"image\",\"graphicsmagick\",\"imagemagick\",\"gm\",\"convert\",\"identify\",\"compare\"],\"engines\":{\"node\":\">=14\"},\"bugs\":{\"url\":\"http://github.com/aheckmann/gm/issues\"},\"licenses\":[{\"type\":\"MIT\",\"url\":\"http://www.opensource.org/licenses/mit-license.php\"}],\"main\":\"./index\",\"scripts\":{\"security\":\"npm audit\",\"test\":\"npm run security && npm run test-integration\",\"test-integration\":\"node test/ --integration\",\"test-unit\":\"node test/\"},\"repository\":{\"type\":\"git\",\"url\":\"https://github.com/aheckmann/gm.git\"},\"license\":\"MIT\",\"devDependencies\":{\"async\":\"~0.9.0\"},\"dependencies\":{\"array-parallel\":\"~0.1.3\",\"array-series\":\"~0.1.5\",\"cross-spawn\":\"^7.0.5\",\"debug\":\"^3.1.0\"}}"));}}),
"[project]/node_modules/gm/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Module dependencies.
 */ var Stream = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)").Stream;
var EventEmitter = __turbopack_context__.r("[externals]/events [external] (events, cjs)").EventEmitter;
var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
util.inherits(gm, EventEmitter);
/**
 * Constructor.
 *
 * @param {String|Number} path - path to img source or ReadableStream or width of img to create
 * @param {Number} [height] - optional filename of ReadableStream or height of img to create
 * @param {String} [color] - optional hex background color of created img
 */ function gm(source, height, color) {
    var width;
    if (!(this instanceof gm)) {
        return new gm(source, height, color);
    }
    EventEmitter.call(this);
    this._options = {};
    this.options(this.__proto__._options);
    this.data = {};
    this._in = [];
    this._out = [];
    this._outputFormat = null;
    this._subCommand = 'convert';
    if (source instanceof Stream) {
        this.sourceStream = source;
        source = height || 'unknown.jpg';
    } else if (Buffer.isBuffer(source)) {
        this.sourceBuffer = source;
        source = height || 'unknown.jpg';
    } else if (height) {
        // new images
        width = source;
        source = "";
        this.in("-size", width + "x" + height);
        if (color) {
            this.in("xc:" + color);
        }
    }
    if (typeof source === "string") {
        // then source is a path
        // parse out gif frame brackets from filename
        // since stream doesn't use source path
        // eg. "filename.gif[0]"
        var frames = source.match(/(\[.+\])$/);
        if (frames) {
            this.sourceFrames = source.substr(frames.index, frames[0].length);
            source = source.substr(0, frames.index);
        }
    }
    this.source = source;
    this.addSrcFormatter(function(src) {
        // must be first source formatter
        var inputFromStdin = this.sourceStream || this.sourceBuffer;
        var ret = inputFromStdin ? '-' : this.source;
        const fileNameProvied = typeof height === 'string';
        if (inputFromStdin && fileNameProvied && /\.ico$/i.test(this.source)) {
            ret = `ico:-`;
        }
        if (ret && this.sourceFrames) ret += this.sourceFrames;
        src.length = 0;
        src[0] = ret;
    });
}
/**
 * Subclasses the gm constructor with custom options.
 *
 * @param {options} options
 * @return {gm} the subclasses gm constructor
 */ var parent = gm;
gm.subClass = function subClass(options) {
    function gm(source, height, color) {
        if (!(this instanceof parent)) {
            return new gm(source, height, color);
        }
        parent.call(this, source, height, color);
    }
    gm.prototype.__proto__ = parent.prototype;
    gm.prototype._options = {};
    gm.prototype.options(options);
    return gm;
};
/**
 * Augment the prototype.
 */ __turbopack_context__.r("[project]/node_modules/gm/lib/options.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/getters.js [app-route] (ecmascript)")(gm);
__turbopack_context__.r("[project]/node_modules/gm/lib/args.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/drawing.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/convenience.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/command.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/compare.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/composite.js [app-route] (ecmascript)")(gm.prototype);
__turbopack_context__.r("[project]/node_modules/gm/lib/montage.js [app-route] (ecmascript)")(gm.prototype);
/**
 * Expose.
 */ module.exports = exports = gm;
module.exports.utils = __turbopack_context__.r("[project]/node_modules/gm/lib/utils.js [app-route] (ecmascript)");
module.exports.compare = __turbopack_context__.r("[project]/node_modules/gm/lib/compare.js [app-route] (ecmascript)")();
module.exports.version = __turbopack_context__.r("[project]/node_modules/gm/package.json (json)").version;
}}),
"[project]/node_modules/array-parallel/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = function parallel(fns, context, callback) {
    if (!callback) {
        if (typeof context === 'function') {
            callback = context;
            context = null;
        } else {
            callback = noop;
        }
    }
    var pending = fns && fns.length;
    if (!pending) return callback(null, []);
    var finished = false;
    var results = new Array(pending);
    fns.forEach(context ? function(fn, i) {
        fn.call(context, maybeDone(i));
    } : function(fn, i) {
        fn(maybeDone(i));
    });
    function maybeDone(i) {
        return function(err, result) {
            if (finished) return;
            if (err) {
                callback(err, results);
                finished = true;
                return;
            }
            results[i] = result;
            if (!--pending) callback(null, results);
        };
    }
};
function noop() {}
}}),
"[project]/node_modules/isexe/windows.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = isexe;
isexe.sync = sync;
var fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
function checkPathExt(path, options) {
    var pathext = options.pathExt !== undefined ? options.pathExt : process.env.PATHEXT;
    if (!pathext) {
        return true;
    }
    pathext = pathext.split(';');
    if (pathext.indexOf('') !== -1) {
        return true;
    }
    for(var i = 0; i < pathext.length; i++){
        var p = pathext[i].toLowerCase();
        if (p && path.substr(-p.length).toLowerCase() === p) {
            return true;
        }
    }
    return false;
}
function checkStat(stat, path, options) {
    if (!stat.isSymbolicLink() && !stat.isFile()) {
        return false;
    }
    return checkPathExt(path, options);
}
function isexe(path, options, cb) {
    fs.stat(path, function(er, stat) {
        cb(er, er ? false : checkStat(stat, path, options));
    });
}
function sync(path, options) {
    return checkStat(fs.statSync(path), path, options);
}
}}),
"[project]/node_modules/isexe/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
var core;
if ("TURBOPACK compile-time truthy", 1) {
    core = __turbopack_context__.r("[project]/node_modules/isexe/windows.js [app-route] (ecmascript)");
} else {
    "TURBOPACK unreachable";
}
module.exports = isexe;
isexe.sync = sync;
function isexe(path, options, cb) {
    if (typeof options === 'function') {
        cb = options;
        options = {};
    }
    if (!cb) {
        if (typeof Promise !== 'function') {
            throw new TypeError('callback not provided');
        }
        return new Promise(function(resolve, reject) {
            isexe(path, options || {}, function(er, is) {
                if (er) {
                    reject(er);
                } else {
                    resolve(is);
                }
            });
        });
    }
    core(path, options || {}, function(er, is) {
        // ignore EACCES because that just means we aren't allowed to run it
        if (er) {
            if (er.code === 'EACCES' || options && options.ignoreErrors) {
                er = null;
                is = false;
            }
        }
        cb(er, is);
    });
}
function sync(path, options) {
    // my kingdom for a filtered catch
    try {
        return core.sync(path, options || {});
    } catch (er) {
        if (options && options.ignoreErrors || er.code === 'EACCES') {
            return false;
        } else {
            throw er;
        }
    }
}
}}),
"[project]/node_modules/which/which.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const isWindows = process.platform === 'win32' || process.env.OSTYPE === 'cygwin' || process.env.OSTYPE === 'msys';
const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
const COLON = ("TURBOPACK compile-time truthy", 1) ? ';' : ("TURBOPACK unreachable", undefined);
const isexe = __turbopack_context__.r("[project]/node_modules/isexe/index.js [app-route] (ecmascript)");
const getNotFoundError = (cmd)=>Object.assign(new Error(`not found: ${cmd}`), {
        code: 'ENOENT'
    });
const getPathInfo = (cmd, opt)=>{
    const colon = opt.colon || COLON;
    // If it has a slash, then we don't bother searching the pathenv.
    // just check the file itself, and that's it.
    const pathEnv = cmd.match(/\//) || isWindows && cmd.match(/\\/) ? [
        ''
    ] : [
        // windows always checks the cwd first
        ...("TURBOPACK compile-time truthy", 1) ? [
            process.cwd()
        ] : ("TURBOPACK unreachable", undefined),
        ...(opt.path || process.env.PATH || /* istanbul ignore next: very unusual */ '').split(colon)
    ];
    const pathExtExe = ("TURBOPACK compile-time truthy", 1) ? opt.pathExt || process.env.PATHEXT || '.EXE;.CMD;.BAT;.COM' : ("TURBOPACK unreachable", undefined);
    const pathExt = ("TURBOPACK compile-time truthy", 1) ? pathExtExe.split(colon) : ("TURBOPACK unreachable", undefined);
    if ("TURBOPACK compile-time truthy", 1) {
        if (cmd.indexOf('.') !== -1 && pathExt[0] !== '') pathExt.unshift('');
    }
    return {
        pathEnv,
        pathExt,
        pathExtExe
    };
};
const which = (cmd, opt, cb)=>{
    if (typeof opt === 'function') {
        cb = opt;
        opt = {};
    }
    if (!opt) opt = {};
    const { pathEnv, pathExt, pathExtExe } = getPathInfo(cmd, opt);
    const found = [];
    const step = (i)=>new Promise((resolve, reject)=>{
            if (i === pathEnv.length) return opt.all && found.length ? resolve(found) : reject(getNotFoundError(cmd));
            const ppRaw = pathEnv[i];
            const pathPart = /^".*"$/.test(ppRaw) ? ppRaw.slice(1, -1) : ppRaw;
            const pCmd = path.join(pathPart, cmd);
            const p = !pathPart && /^\.[\\\/]/.test(cmd) ? cmd.slice(0, 2) + pCmd : pCmd;
            resolve(subStep(p, i, 0));
        });
    const subStep = (p, i, ii)=>new Promise((resolve, reject)=>{
            if (ii === pathExt.length) return resolve(step(i + 1));
            const ext = pathExt[ii];
            isexe(p + ext, {
                pathExt: pathExtExe
            }, (er, is)=>{
                if (!er && is) {
                    if (opt.all) found.push(p + ext);
                    else return resolve(p + ext);
                }
                return resolve(subStep(p, i, ii + 1));
            });
        });
    return cb ? step(0).then((res)=>cb(null, res), cb) : step(0);
};
const whichSync = (cmd, opt)=>{
    opt = opt || {};
    const { pathEnv, pathExt, pathExtExe } = getPathInfo(cmd, opt);
    const found = [];
    for(let i = 0; i < pathEnv.length; i++){
        const ppRaw = pathEnv[i];
        const pathPart = /^".*"$/.test(ppRaw) ? ppRaw.slice(1, -1) : ppRaw;
        const pCmd = path.join(pathPart, cmd);
        const p = !pathPart && /^\.[\\\/]/.test(cmd) ? cmd.slice(0, 2) + pCmd : pCmd;
        for(let j = 0; j < pathExt.length; j++){
            const cur = p + pathExt[j];
            try {
                const is = isexe.sync(cur, {
                    pathExt: pathExtExe
                });
                if (is) {
                    if (opt.all) found.push(cur);
                    else return cur;
                }
            } catch (ex) {}
        }
    }
    if (opt.all && found.length) return found;
    if (opt.nothrow) return null;
    throw getNotFoundError(cmd);
};
module.exports = which;
which.sync = whichSync;
}}),
"[project]/node_modules/path-key/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const pathKey = (options = {})=>{
    const environment = options.env || process.env;
    const platform = options.platform || process.platform;
    if (platform !== 'win32') {
        return 'PATH';
    }
    return Object.keys(environment).reverse().find((key)=>key.toUpperCase() === 'PATH') || 'Path';
};
module.exports = pathKey;
// TODO: Remove this for the next major release
module.exports.default = pathKey;
}}),
"[project]/node_modules/cross-spawn/lib/util/resolveCommand.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
const which = __turbopack_context__.r("[project]/node_modules/which/which.js [app-route] (ecmascript)");
const getPathKey = __turbopack_context__.r("[project]/node_modules/path-key/index.js [app-route] (ecmascript)");
function resolveCommandAttempt(parsed, withoutPathExt) {
    const env = parsed.options.env || process.env;
    const cwd = process.cwd();
    const hasCustomCwd = parsed.options.cwd != null;
    // Worker threads do not have process.chdir()
    const shouldSwitchCwd = hasCustomCwd && process.chdir !== undefined && !process.chdir.disabled;
    // If a custom `cwd` was specified, we need to change the process cwd
    // because `which` will do stat calls but does not support a custom cwd
    if (shouldSwitchCwd) {
        try {
            process.chdir(parsed.options.cwd);
        } catch (err) {
        /* Empty */ }
    }
    let resolved;
    try {
        resolved = which.sync(parsed.command, {
            path: env[getPathKey({
                env
            })],
            pathExt: withoutPathExt ? path.delimiter : undefined
        });
    } catch (e) {
    /* Empty */ } finally{
        if (shouldSwitchCwd) {
            process.chdir(cwd);
        }
    }
    // If we successfully resolved, ensure that an absolute path is returned
    // Note that when a custom `cwd` was used, we need to resolve to an absolute path based on it
    if (resolved) {
        resolved = path.resolve(hasCustomCwd ? parsed.options.cwd : '', resolved);
    }
    return resolved;
}
function resolveCommand(parsed) {
    return resolveCommandAttempt(parsed) || resolveCommandAttempt(parsed, true);
}
module.exports = resolveCommand;
}}),
"[project]/node_modules/cross-spawn/lib/util/escape.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
// See http://www.robvanderwoude.com/escapechars.php
const metaCharsRegExp = /([()\][%!^"`<>&|;, *?])/g;
function escapeCommand(arg) {
    // Escape meta chars
    arg = arg.replace(metaCharsRegExp, '^$1');
    return arg;
}
function escapeArgument(arg, doubleEscapeMetaChars) {
    // Convert to string
    arg = `${arg}`;
    // Algorithm below is based on https://qntm.org/cmd
    // It's slightly altered to disable JS backtracking to avoid hanging on specially crafted input
    // Please see https://github.com/moxystudio/node-cross-spawn/pull/160 for more information
    // Sequence of backslashes followed by a double quote:
    // double up all the backslashes and escape the double quote
    arg = arg.replace(/(?=(\\+?)?)\1"/g, '$1$1\\"');
    // Sequence of backslashes followed by the end of the string
    // (which will become a double quote later):
    // double up all the backslashes
    arg = arg.replace(/(?=(\\+?)?)\1$/, '$1$1');
    // All other backslashes occur literally
    // Quote the whole thing:
    arg = `"${arg}"`;
    // Escape meta chars
    arg = arg.replace(metaCharsRegExp, '^$1');
    // Double escape meta chars if necessary
    if (doubleEscapeMetaChars) {
        arg = arg.replace(metaCharsRegExp, '^$1');
    }
    return arg;
}
module.exports.command = escapeCommand;
module.exports.argument = escapeArgument;
}}),
"[project]/node_modules/cross-spawn/lib/util/readShebang.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
const shebangCommand = __turbopack_context__.r("[project]/node_modules/shebang-command/index.js [app-route] (ecmascript)");
function readShebang(command) {
    // Read the first 150 bytes from the file
    const size = 150;
    const buffer = Buffer.alloc(size);
    let fd;
    try {
        fd = fs.openSync(command, 'r');
        fs.readSync(fd, buffer, 0, size, 0);
        fs.closeSync(fd);
    } catch (e) {}
    // Attempt to extract shebang (null is returned if not a shebang)
    return shebangCommand(buffer.toString());
}
module.exports = readShebang;
}}),
"[project]/node_modules/cross-spawn/lib/parse.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
const resolveCommand = __turbopack_context__.r("[project]/node_modules/cross-spawn/lib/util/resolveCommand.js [app-route] (ecmascript)");
const escape = __turbopack_context__.r("[project]/node_modules/cross-spawn/lib/util/escape.js [app-route] (ecmascript)");
const readShebang = __turbopack_context__.r("[project]/node_modules/cross-spawn/lib/util/readShebang.js [app-route] (ecmascript)");
const isWin = process.platform === 'win32';
const isExecutableRegExp = /\.(?:com|exe)$/i;
const isCmdShimRegExp = /node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;
function detectShebang(parsed) {
    parsed.file = resolveCommand(parsed);
    const shebang = parsed.file && readShebang(parsed.file);
    if (shebang) {
        parsed.args.unshift(parsed.file);
        parsed.command = shebang;
        return resolveCommand(parsed);
    }
    return parsed.file;
}
function parseNonShell(parsed) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Detect & add support for shebangs
    const commandFile = detectShebang(parsed);
    // We don't need a shell if the command filename is an executable
    const needsShell = !isExecutableRegExp.test(commandFile);
    // If a shell is required, use cmd.exe and take care of escaping everything correctly
    // Note that `forceShell` is an hidden option used only in tests
    if (parsed.options.forceShell || needsShell) {
        // Need to double escape meta chars if the command is a cmd-shim located in `node_modules/.bin/`
        // The cmd-shim simply calls execute the package bin file with NodeJS, proxying any argument
        // Because the escape of metachars with ^ gets interpreted when the cmd.exe is first called,
        // we need to double escape them
        const needsDoubleEscapeMetaChars = isCmdShimRegExp.test(commandFile);
        // Normalize posix paths into OS compatible paths (e.g.: foo/bar -> foo\bar)
        // This is necessary otherwise it will always fail with ENOENT in those cases
        parsed.command = path.normalize(parsed.command);
        // Escape command & arguments
        parsed.command = escape.command(parsed.command);
        parsed.args = parsed.args.map((arg)=>escape.argument(arg, needsDoubleEscapeMetaChars));
        const shellCommand = [
            parsed.command
        ].concat(parsed.args).join(' ');
        parsed.args = [
            '/d',
            '/s',
            '/c',
            `"${shellCommand}"`
        ];
        parsed.command = process.env.comspec || 'cmd.exe';
        parsed.options.windowsVerbatimArguments = true; // Tell node's spawn that the arguments are already escaped
    }
    return parsed;
}
function parse(command, args, options) {
    // Normalize arguments, similar to nodejs
    if (args && !Array.isArray(args)) {
        options = args;
        args = null;
    }
    args = args ? args.slice(0) : []; // Clone array to avoid changing the original
    options = Object.assign({}, options); // Clone object to avoid changing the original
    // Build our parsed object
    const parsed = {
        command,
        args,
        options,
        file: undefined,
        original: {
            command,
            args
        }
    };
    // Delegate further parsing to shell or non-shell
    return options.shell ? parsed : parseNonShell(parsed);
}
module.exports = parse;
}}),
"[project]/node_modules/cross-spawn/lib/enoent.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const isWin = process.platform === 'win32';
function notFoundError(original, syscall) {
    return Object.assign(new Error(`${syscall} ${original.command} ENOENT`), {
        code: 'ENOENT',
        errno: 'ENOENT',
        syscall: `${syscall} ${original.command}`,
        path: original.command,
        spawnargs: original.args
    });
}
function hookChildProcess(cp, parsed) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const originalEmit = cp.emit;
    cp.emit = function(name, arg1) {
        // If emitting "exit" event and exit code is 1, we need to check if
        // the command exists and emit an "error" instead
        // See https://github.com/IndigoUnited/node-cross-spawn/issues/16
        if (name === 'exit') {
            const err = verifyENOENT(arg1, parsed);
            if (err) {
                return originalEmit.call(cp, 'error', err);
            }
        }
        return originalEmit.apply(cp, arguments); // eslint-disable-line prefer-rest-params
    };
}
function verifyENOENT(status, parsed) {
    if (isWin && status === 1 && !parsed.file) {
        return notFoundError(parsed.original, 'spawn');
    }
    return null;
}
function verifyENOENTSync(status, parsed) {
    if (isWin && status === 1 && !parsed.file) {
        return notFoundError(parsed.original, 'spawnSync');
    }
    return null;
}
module.exports = {
    hookChildProcess,
    verifyENOENT,
    verifyENOENTSync,
    notFoundError
};
}}),
"[project]/node_modules/cross-spawn/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const cp = __turbopack_context__.r("[externals]/child_process [external] (child_process, cjs)");
const parse = __turbopack_context__.r("[project]/node_modules/cross-spawn/lib/parse.js [app-route] (ecmascript)");
const enoent = __turbopack_context__.r("[project]/node_modules/cross-spawn/lib/enoent.js [app-route] (ecmascript)");
function spawn(command, args, options) {
    // Parse the arguments
    const parsed = parse(command, args, options);
    // Spawn the child process
    const spawned = cp.spawn(parsed.command, parsed.args, parsed.options);
    // Hook into child process "exit" event to emit an error if the command
    // does not exists, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16
    enoent.hookChildProcess(spawned, parsed);
    return spawned;
}
function spawnSync(command, args, options) {
    // Parse the arguments
    const parsed = parse(command, args, options);
    // Spawn the child process
    const result = cp.spawnSync(parsed.command, parsed.args, parsed.options);
    // Analyze if the command does not exist, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16
    result.error = result.error || enoent.verifyENOENTSync(result.status, parsed);
    return result;
}
module.exports = spawn;
module.exports.spawn = spawn;
module.exports.sync = spawnSync;
module.exports._parse = parse;
module.exports._enoent = enoent;
}}),
"[project]/node_modules/shebang-regex/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = /^#!(.*)/;
}}),
"[project]/node_modules/shebang-command/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const shebangRegex = __turbopack_context__.r("[project]/node_modules/shebang-regex/index.js [app-route] (ecmascript)");
module.exports = (string = '')=>{
    const match = string.match(shebangRegex);
    if (!match) {
        return null;
    }
    const [path, argument] = match[0].replace(/#! ?/, '').split(' ');
    const binary = path.split('/').pop();
    if (binary === 'env') {
        return argument;
    }
    return argument ? `${binary} ${argument}` : binary;
};
}}),
"[project]/node_modules/ms/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Helpers.
 */ var s = 1000;
var m = s * 60;
var h = m * 60;
var d = h * 24;
var w = d * 7;
var y = d * 365.25;
/**
 * Parse or format the given `val`.
 *
 * Options:
 *
 *  - `long` verbose formatting [false]
 *
 * @param {String|Number} val
 * @param {Object} [options]
 * @throws {Error} throw an error if val is not a non-empty string or a number
 * @return {String|Number}
 * @api public
 */ module.exports = function(val, options) {
    options = options || {};
    var type = typeof val;
    if (type === 'string' && val.length > 0) {
        return parse(val);
    } else if (type === 'number' && isFinite(val)) {
        return options.long ? fmtLong(val) : fmtShort(val);
    }
    throw new Error('val is not a non-empty string or a valid number. val=' + JSON.stringify(val));
};
/**
 * Parse the given `str` and return milliseconds.
 *
 * @param {String} str
 * @return {Number}
 * @api private
 */ function parse(str) {
    str = String(str);
    if (str.length > 100) {
        return;
    }
    var match = /^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(str);
    if (!match) {
        return;
    }
    var n = parseFloat(match[1]);
    var type = (match[2] || 'ms').toLowerCase();
    switch(type){
        case 'years':
        case 'year':
        case 'yrs':
        case 'yr':
        case 'y':
            return n * y;
        case 'weeks':
        case 'week':
        case 'w':
            return n * w;
        case 'days':
        case 'day':
        case 'd':
            return n * d;
        case 'hours':
        case 'hour':
        case 'hrs':
        case 'hr':
        case 'h':
            return n * h;
        case 'minutes':
        case 'minute':
        case 'mins':
        case 'min':
        case 'm':
            return n * m;
        case 'seconds':
        case 'second':
        case 'secs':
        case 'sec':
        case 's':
            return n * s;
        case 'milliseconds':
        case 'millisecond':
        case 'msecs':
        case 'msec':
        case 'ms':
            return n;
        default:
            return undefined;
    }
}
/**
 * Short format for `ms`.
 *
 * @param {Number} ms
 * @return {String}
 * @api private
 */ function fmtShort(ms) {
    var msAbs = Math.abs(ms);
    if (msAbs >= d) {
        return Math.round(ms / d) + 'd';
    }
    if (msAbs >= h) {
        return Math.round(ms / h) + 'h';
    }
    if (msAbs >= m) {
        return Math.round(ms / m) + 'm';
    }
    if (msAbs >= s) {
        return Math.round(ms / s) + 's';
    }
    return ms + 'ms';
}
/**
 * Long format for `ms`.
 *
 * @param {Number} ms
 * @return {String}
 * @api private
 */ function fmtLong(ms) {
    var msAbs = Math.abs(ms);
    if (msAbs >= d) {
        return plural(ms, msAbs, d, 'day');
    }
    if (msAbs >= h) {
        return plural(ms, msAbs, h, 'hour');
    }
    if (msAbs >= m) {
        return plural(ms, msAbs, m, 'minute');
    }
    if (msAbs >= s) {
        return plural(ms, msAbs, s, 'second');
    }
    return ms + ' ms';
}
/**
 * Pluralization helper.
 */ function plural(ms, msAbs, n, name) {
    var isPlural = msAbs >= n * 1.5;
    return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');
}
}}),
"[project]/node_modules/gm/node_modules/debug/src/common.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * This is the common logic for both the Node.js and web browser
 * implementations of `debug()`.
 */ function setup(env) {
    createDebug.debug = createDebug;
    createDebug.default = createDebug;
    createDebug.coerce = coerce;
    createDebug.disable = disable;
    createDebug.enable = enable;
    createDebug.enabled = enabled;
    createDebug.humanize = __turbopack_context__.r("[project]/node_modules/ms/index.js [app-route] (ecmascript)");
    Object.keys(env).forEach(function(key) {
        createDebug[key] = env[key];
    });
    /**
  * Active `debug` instances.
  */ createDebug.instances = [];
    /**
  * The currently active debug mode names, and names to skip.
  */ createDebug.names = [];
    createDebug.skips = [];
    /**
  * Map of special "%n" handling functions, for the debug "format" argument.
  *
  * Valid key names are a single, lower or upper-case letter, i.e. "n" and "N".
  */ createDebug.formatters = {};
    /**
  * Selects a color for a debug namespace
  * @param {String} namespace The namespace string for the for the debug instance to be colored
  * @return {Number|String} An ANSI color code for the given namespace
  * @api private
  */ function selectColor(namespace) {
        var hash = 0;
        for(var i = 0; i < namespace.length; i++){
            hash = (hash << 5) - hash + namespace.charCodeAt(i);
            hash |= 0; // Convert to 32bit integer
        }
        return createDebug.colors[Math.abs(hash) % createDebug.colors.length];
    }
    createDebug.selectColor = selectColor;
    /**
  * Create a debugger with the given `namespace`.
  *
  * @param {String} namespace
  * @return {Function}
  * @api public
  */ function createDebug(namespace) {
        var prevTime;
        function debug() {
            // Disabled?
            if (!debug.enabled) {
                return;
            }
            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                args[_key] = arguments[_key];
            }
            var self = debug; // Set `diff` timestamp
            var curr = Number(new Date());
            var ms = curr - (prevTime || curr);
            self.diff = ms;
            self.prev = prevTime;
            self.curr = curr;
            prevTime = curr;
            args[0] = createDebug.coerce(args[0]);
            if (typeof args[0] !== 'string') {
                // Anything else let's inspect with %O
                args.unshift('%O');
            } // Apply any `formatters` transformations
            var index = 0;
            args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {
                // If we encounter an escaped % then don't increase the array index
                if (match === '%%') {
                    return match;
                }
                index++;
                var formatter = createDebug.formatters[format];
                if (typeof formatter === 'function') {
                    var val = args[index];
                    match = formatter.call(self, val); // Now we need to remove `args[index]` since it's inlined in the `format`
                    args.splice(index, 1);
                    index--;
                }
                return match;
            }); // Apply env-specific formatting (colors, etc.)
            createDebug.formatArgs.call(self, args);
            var logFn = self.log || createDebug.log;
            logFn.apply(self, args);
        }
        debug.namespace = namespace;
        debug.enabled = createDebug.enabled(namespace);
        debug.useColors = createDebug.useColors();
        debug.color = selectColor(namespace);
        debug.destroy = destroy;
        debug.extend = extend; // Debug.formatArgs = formatArgs;
        // debug.rawLog = rawLog;
        // env-specific initialization logic for debug instances
        if (typeof createDebug.init === 'function') {
            createDebug.init(debug);
        }
        createDebug.instances.push(debug);
        return debug;
    }
    function destroy() {
        var index = createDebug.instances.indexOf(this);
        if (index !== -1) {
            createDebug.instances.splice(index, 1);
            return true;
        }
        return false;
    }
    function extend(namespace, delimiter) {
        return createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);
    }
    /**
  * Enables a debug mode by namespaces. This can include modes
  * separated by a colon and wildcards.
  *
  * @param {String} namespaces
  * @api public
  */ function enable(namespaces) {
        createDebug.save(namespaces);
        createDebug.names = [];
        createDebug.skips = [];
        var i;
        var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\s,]+/);
        var len = split.length;
        for(i = 0; i < len; i++){
            if (!split[i]) {
                continue;
            }
            namespaces = split[i].replace(/\*/g, '.*?');
            if (namespaces[0] === '-') {
                createDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));
            } else {
                createDebug.names.push(new RegExp('^' + namespaces + '$'));
            }
        }
        for(i = 0; i < createDebug.instances.length; i++){
            var instance = createDebug.instances[i];
            instance.enabled = createDebug.enabled(instance.namespace);
        }
    }
    /**
  * Disable debug output.
  *
  * @api public
  */ function disable() {
        createDebug.enable('');
    }
    /**
  * Returns true if the given mode name is enabled, false otherwise.
  *
  * @param {String} name
  * @return {Boolean}
  * @api public
  */ function enabled(name) {
        if (name[name.length - 1] === '*') {
            return true;
        }
        var i;
        var len;
        for(i = 0, len = createDebug.skips.length; i < len; i++){
            if (createDebug.skips[i].test(name)) {
                return false;
            }
        }
        for(i = 0, len = createDebug.names.length; i < len; i++){
            if (createDebug.names[i].test(name)) {
                return true;
            }
        }
        return false;
    }
    /**
  * Coerce `val`.
  *
  * @param {Mixed} val
  * @return {Mixed}
  * @api private
  */ function coerce(val) {
        if (val instanceof Error) {
            return val.stack || val.message;
        }
        return val;
    }
    createDebug.enable(createDebug.load());
    return createDebug;
}
module.exports = setup;
}}),
"[project]/node_modules/gm/node_modules/debug/src/node.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * Module dependencies.
 */ var tty = __turbopack_context__.r("[externals]/tty [external] (tty, cjs)");
var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
/**
 * This is the Node.js implementation of `debug()`.
 */ exports.init = init;
exports.log = log;
exports.formatArgs = formatArgs;
exports.save = save;
exports.load = load;
exports.useColors = useColors;
/**
 * Colors.
 */ exports.colors = [
    6,
    2,
    3,
    4,
    5,
    1
];
try {
    // Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)
    // eslint-disable-next-line import/no-extraneous-dependencies
    var supportsColor = __turbopack_context__.r("[project]/node_modules/supports-color/index.js [app-route] (ecmascript)");
    if (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {
        exports.colors = [
            20,
            21,
            26,
            27,
            32,
            33,
            38,
            39,
            40,
            41,
            42,
            43,
            44,
            45,
            56,
            57,
            62,
            63,
            68,
            69,
            74,
            75,
            76,
            77,
            78,
            79,
            80,
            81,
            92,
            93,
            98,
            99,
            112,
            113,
            128,
            129,
            134,
            135,
            148,
            149,
            160,
            161,
            162,
            163,
            164,
            165,
            166,
            167,
            168,
            169,
            170,
            171,
            172,
            173,
            178,
            179,
            184,
            185,
            196,
            197,
            198,
            199,
            200,
            201,
            202,
            203,
            204,
            205,
            206,
            207,
            208,
            209,
            214,
            215,
            220,
            221
        ];
    }
} catch (error) {} // Swallow - we only care if `supports-color` is available; it doesn't have to be.
/**
 * Build up the default `inspectOpts` object from the environment variables.
 *
 *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js
 */ exports.inspectOpts = Object.keys(process.env).filter(function(key) {
    return /^debug_/i.test(key);
}).reduce(function(obj, key) {
    // Camel-case
    var prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, function(_, k) {
        return k.toUpperCase();
    }); // Coerce string value into JS value
    var val = process.env[key];
    if (/^(yes|on|true|enabled)$/i.test(val)) {
        val = true;
    } else if (/^(no|off|false|disabled)$/i.test(val)) {
        val = false;
    } else if (val === 'null') {
        val = null;
    } else {
        val = Number(val);
    }
    obj[prop] = val;
    return obj;
}, {});
/**
 * Is stdout a TTY? Colored output is enabled when `true`.
 */ function useColors() {
    return 'colors' in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(process.stderr.fd);
}
/**
 * Adds ANSI color escape codes if enabled.
 *
 * @api public
 */ function formatArgs(args) {
    var name = this.namespace, useColors = this.useColors;
    if (useColors) {
        var c = this.color;
        var colorCode = "\x1B[3" + (c < 8 ? c : '8;5;' + c);
        var prefix = "  ".concat(colorCode, ";1m").concat(name, " \x1B[0m");
        args[0] = prefix + args[0].split('\n').join('\n' + prefix);
        args.push(colorCode + 'm+' + module.exports.humanize(this.diff) + "\x1B[0m");
    } else {
        args[0] = getDate() + name + ' ' + args[0];
    }
}
function getDate() {
    if (exports.inspectOpts.hideDate) {
        return '';
    }
    return new Date().toISOString() + ' ';
}
/**
 * Invokes `util.format()` with the specified arguments and writes to stderr.
 */ function log() {
    return process.stderr.write(util.format.apply(util, arguments) + '\n');
}
/**
 * Save `namespaces`.
 *
 * @param {String} namespaces
 * @api private
 */ function save(namespaces) {
    if (namespaces) {
        process.env.DEBUG = namespaces;
    } else {
        // If you set a process.env field to null or undefined, it gets cast to the
        // string 'null' or 'undefined'. Just delete instead.
        delete process.env.DEBUG;
    }
}
/**
 * Load `namespaces`.
 *
 * @return {String} returns the previously persisted debug modes
 * @api private
 */ function load() {
    return process.env.DEBUG;
}
/**
 * Init logic for `debug` instances.
 *
 * Create a new `inspectOpts` object in case `useColors` is set
 * differently for a particular `debug` instance.
 */ function init(debug) {
    debug.inspectOpts = {};
    var keys = Object.keys(exports.inspectOpts);
    for(var i = 0; i < keys.length; i++){
        debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];
    }
}
module.exports = __turbopack_context__.r("[project]/node_modules/gm/node_modules/debug/src/common.js [app-route] (ecmascript)")(exports);
var formatters = module.exports.formatters;
/**
 * Map %o to `util.inspect()`, all on a single line.
 */ formatters.o = function(v) {
    this.inspectOpts.colors = this.useColors;
    return util.inspect(v, this.inspectOpts).split('\n').map(function(str) {
        return str.trim();
    }).join(' ');
};
/**
 * Map %O to `util.inspect()`, allowing multiple lines if needed.
 */ formatters.O = function(v) {
    this.inspectOpts.colors = this.useColors;
    return util.inspect(v, this.inspectOpts);
};
}}),
"[project]/node_modules/gm/node_modules/debug/src/browser.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
function _typeof(obj) {
    if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
        _typeof = function _typeof(obj) {
            return typeof obj;
        };
    } else {
        _typeof = function _typeof(obj) {
            return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
        };
    }
    return _typeof(obj);
}
/* eslint-env browser */ /**
 * This is the web browser implementation of `debug()`.
 */ exports.log = log;
exports.formatArgs = formatArgs;
exports.save = save;
exports.load = load;
exports.useColors = useColors;
exports.storage = localstorage();
/**
 * Colors.
 */ exports.colors = [
    '#0000CC',
    '#0000FF',
    '#0033CC',
    '#0033FF',
    '#0066CC',
    '#0066FF',
    '#0099CC',
    '#0099FF',
    '#00CC00',
    '#00CC33',
    '#00CC66',
    '#00CC99',
    '#00CCCC',
    '#00CCFF',
    '#3300CC',
    '#3300FF',
    '#3333CC',
    '#3333FF',
    '#3366CC',
    '#3366FF',
    '#3399CC',
    '#3399FF',
    '#33CC00',
    '#33CC33',
    '#33CC66',
    '#33CC99',
    '#33CCCC',
    '#33CCFF',
    '#6600CC',
    '#6600FF',
    '#6633CC',
    '#6633FF',
    '#66CC00',
    '#66CC33',
    '#9900CC',
    '#9900FF',
    '#9933CC',
    '#9933FF',
    '#99CC00',
    '#99CC33',
    '#CC0000',
    '#CC0033',
    '#CC0066',
    '#CC0099',
    '#CC00CC',
    '#CC00FF',
    '#CC3300',
    '#CC3333',
    '#CC3366',
    '#CC3399',
    '#CC33CC',
    '#CC33FF',
    '#CC6600',
    '#CC6633',
    '#CC9900',
    '#CC9933',
    '#CCCC00',
    '#CCCC33',
    '#FF0000',
    '#FF0033',
    '#FF0066',
    '#FF0099',
    '#FF00CC',
    '#FF00FF',
    '#FF3300',
    '#FF3333',
    '#FF3366',
    '#FF3399',
    '#FF33CC',
    '#FF33FF',
    '#FF6600',
    '#FF6633',
    '#FF9900',
    '#FF9933',
    '#FFCC00',
    '#FFCC33'
];
/**
 * Currently only WebKit-based Web Inspectors, Firefox >= v31,
 * and the Firebug extension (any Firefox version) are known
 * to support "%c" CSS customizations.
 *
 * TODO: add a `localStorage` variable to explicitly enable/disable colors
 */ // eslint-disable-next-line complexity
function useColors() {
    // NB: In an Electron preload script, document will be defined but not fully
    // initialized. Since we know we're in Chrome, we'll just detect this case
    // explicitly
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } // Internet Explorer and Edge do not support colors.
    if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)) {
        return false;
    } // Is webkit? http://stackoverflow.com/a/16459606/376773
    // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632
    return typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773
    "undefined" !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) || // Is firefox >= v31?
    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages
    typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/) && parseInt(RegExp.$1, 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker
    typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/);
}
/**
 * Colorize log arguments if enabled.
 *
 * @api public
 */ function formatArgs(args) {
    args[0] = (this.useColors ? '%c' : '') + this.namespace + (this.useColors ? ' %c' : ' ') + args[0] + (this.useColors ? '%c ' : ' ') + '+' + module.exports.humanize(this.diff);
    if (!this.useColors) {
        return;
    }
    var c = 'color: ' + this.color;
    args.splice(1, 0, c, 'color: inherit'); // The final "%c" is somewhat tricky, because there could be other
    // arguments passed either before or after the %c, so we need to
    // figure out the correct index to insert the CSS into
    var index = 0;
    var lastC = 0;
    args[0].replace(/%[a-zA-Z%]/g, function(match) {
        if (match === '%%') {
            return;
        }
        index++;
        if (match === '%c') {
            // We only are interested in the *last* %c
            // (the user may have provided their own)
            lastC = index;
        }
    });
    args.splice(lastC, 0, c);
}
/**
 * Invokes `console.log()` when available.
 * No-op when `console.log` is not a "function".
 *
 * @api public
 */ function log() {
    var _console;
    // This hackery is required for IE8/9, where
    // the `console.log` function doesn't have 'apply'
    return (typeof console === "undefined" ? "undefined" : _typeof(console)) === 'object' && console.log && (_console = console).log.apply(_console, arguments);
}
/**
 * Save `namespaces`.
 *
 * @param {String} namespaces
 * @api private
 */ function save(namespaces) {
    try {
        if (namespaces) {
            exports.storage.setItem('debug', namespaces);
        } else {
            exports.storage.removeItem('debug');
        }
    } catch (error) {
    // XXX (@Qix-) should we be logging these?
    }
}
/**
 * Load `namespaces`.
 *
 * @return {String} returns the previously persisted debug modes
 * @api private
 */ function load() {
    var r;
    try {
        r = exports.storage.getItem('debug');
    } catch (error) {} // Swallow
    // XXX (@Qix-) should we be logging these?
    // If debug isn't set in LS, and we're in Electron, try to load $DEBUG
    if (!r && typeof process !== 'undefined' && 'env' in process) {
        r = process.env.DEBUG;
    }
    return r;
}
/**
 * Localstorage attempts to return the localstorage.
 *
 * This is necessary because safari throws
 * when a user disables cookies/localstorage
 * and you attempt to access it.
 *
 * @return {LocalStorage}
 * @api private
 */ function localstorage() {
    try {
        // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context
        // The Browser also has localStorage in the global context.
        return localStorage;
    } catch (error) {
    // XXX (@Qix-) should we be logging these?
    }
}
module.exports = __turbopack_context__.r("[project]/node_modules/gm/node_modules/debug/src/common.js [app-route] (ecmascript)")(exports);
var formatters = module.exports.formatters;
/**
 * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.
 */ formatters.j = function(v) {
    try {
        return JSON.stringify(v);
    } catch (error) {
        return '[UnexpectedJSONParseError]: ' + error.message;
    }
};
}}),
"[project]/node_modules/gm/node_modules/debug/src/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * Detect Electron renderer / nwjs process, which is node, but we should
 * treat as a browser.
 */ if (typeof process === 'undefined' || process.type === 'renderer' || ("TURBOPACK compile-time value", false) === true || process.__nwjs) {
    module.exports = __turbopack_context__.r("[project]/node_modules/gm/node_modules/debug/src/browser.js [app-route] (ecmascript)");
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/gm/node_modules/debug/src/node.js [app-route] (ecmascript)");
}
}}),
"[project]/node_modules/has-flag/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = (flag, argv = process.argv)=>{
    const prefix = flag.startsWith('-') ? '' : flag.length === 1 ? '-' : '--';
    const position = argv.indexOf(prefix + flag);
    const terminatorPosition = argv.indexOf('--');
    return position !== -1 && (terminatorPosition === -1 || position < terminatorPosition);
};
}}),
"[project]/node_modules/supports-color/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const os = __turbopack_context__.r("[externals]/os [external] (os, cjs)");
const tty = __turbopack_context__.r("[externals]/tty [external] (tty, cjs)");
const hasFlag = __turbopack_context__.r("[project]/node_modules/has-flag/index.js [app-route] (ecmascript)");
const { env } = process;
let forceColor;
if (hasFlag('no-color') || hasFlag('no-colors') || hasFlag('color=false') || hasFlag('color=never')) {
    forceColor = 0;
} else if (hasFlag('color') || hasFlag('colors') || hasFlag('color=true') || hasFlag('color=always')) {
    forceColor = 1;
}
if ('FORCE_COLOR' in env) {
    if (env.FORCE_COLOR === 'true') {
        forceColor = 1;
    } else if (env.FORCE_COLOR === 'false') {
        forceColor = 0;
    } else {
        forceColor = env.FORCE_COLOR.length === 0 ? 1 : Math.min(parseInt(env.FORCE_COLOR, 10), 3);
    }
}
function translateLevel(level) {
    if (level === 0) {
        return false;
    }
    return {
        level,
        hasBasic: true,
        has256: level >= 2,
        has16m: level >= 3
    };
}
function supportsColor(haveStream, streamIsTTY) {
    if (forceColor === 0) {
        return 0;
    }
    if (hasFlag('color=16m') || hasFlag('color=full') || hasFlag('color=truecolor')) {
        return 3;
    }
    if (hasFlag('color=256')) {
        return 2;
    }
    if (haveStream && !streamIsTTY && forceColor === undefined) {
        return 0;
    }
    const min = forceColor || 0;
    if (env.TERM === 'dumb') {
        return min;
    }
    if ("TURBOPACK compile-time truthy", 1) {
        // Windows 10 build 10586 is the first Windows release that supports 256 colors.
        // Windows 10 build 14931 is the first release that supports 16m/TrueColor.
        const osRelease = os.release().split('.');
        if (Number(osRelease[0]) >= 10 && Number(osRelease[2]) >= 10586) {
            return Number(osRelease[2]) >= 14931 ? 3 : 2;
        }
        return 1;
    }
    "TURBOPACK unreachable";
}
function getSupportLevel(stream) {
    const level = supportsColor(stream, stream && stream.isTTY);
    return translateLevel(level);
}
module.exports = {
    supportsColor: getSupportLevel,
    stdout: translateLevel(supportsColor(true, tty.isatty(1))),
    stderr: translateLevel(supportsColor(true, tty.isatty(2)))
};
}}),
"[project]/node_modules/array-series/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = function series(fns, context, callback) {
    if (!callback) {
        if (typeof context === 'function') {
            callback = context;
            context = null;
        } else {
            callback = noop;
        }
    }
    if (!(fns && fns.length)) return callback();
    fns = fns.slice(0);
    var call = context ? function() {
        fns.length ? fns.shift().call(context, next) : callback();
    } : function() {
        fns.length ? fns.shift()(next) : callback();
    };
    call();
    function next(err) {
        err ? callback(err) : call();
    }
};
function noop() {}
}}),
"[project]/node_modules/pdf2pic/dist/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var e = __turbopack_context__.r("[project]/node_modules/gm/index.js [app-route] (ecmascript)"), t = __turbopack_context__.r("[externals]/path [external] (path, cjs)"), s = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)"), i = __turbopack_context__.r("[externals]/stream [external] (stream, cjs)");
const r = {
    quality: 0,
    format: "png",
    width: 768,
    height: 512,
    density: 72,
    preserveAspectRatio: !1,
    savePath: "./",
    saveFilename: "untitled",
    compression: "jpeg",
    units: "PixelsPerInch"
};
function n(e, t, s, i) {
    return new (s || (s = Promise))(function(r, n) {
        function a(e) {
            try {
                h(i.next(e));
            } catch (e) {
                n(e);
            }
        }
        function o(e) {
            try {
                h(i.throw(e));
            } catch (e) {
                n(e);
            }
        }
        function h(e) {
            var t;
            e.done ? r(e.value) : (t = e.value, t instanceof s ? t : new s(function(e) {
                e(t);
            })).then(a, o);
        }
        h((i = i.apply(e, t || [])).next());
    });
}
"function" == typeof SuppressedError && SuppressedError;
class a {
    constructor(){
        this.quality = 0, this.format = "png", this.width = 768, this.height = 512, this.preserveAspectRatio = !1, this.density = 72, this.savePath = "./", this.saveFilename = "untitled", this.compression = "jpeg", this.units = "PixelsPerInch", this.gm = e.subClass({
            imageMagick: !1
        });
    }
    generateValidFilename(e) {
        let s = t.join(this.savePath, this.saveFilename);
        return this.savePath.startsWith("./") && (s = `./${s}`), "number" == typeof e && (s = `${s}.${e + 1}`), `${s}.${this.format}`;
    }
    gmBaseCommand(e, t) {
        return this.gm(e, t).density(this.density, this.density).units(this.units).resize(this.width, this.height, this.preserveAspectRatio ? "^" : "!").quality(this.quality).compress(this.compression);
    }
    toBase64(e, t) {
        return n(this, void 0, void 0, function*() {
            const { buffer: s, size: i, page: r } = yield this.toBuffer(e, t);
            return {
                base64: s.toString("base64"),
                size: i,
                page: r
            };
        });
    }
    toBuffer(e, t) {
        const s = `${e.path}[${t}]`;
        return new Promise((i, r)=>{
            this.gmBaseCommand(e, s).stream(this.format, (e, s)=>{
                const n = [];
                if (e) return r(e);
                s.on("data", (e)=>{
                    n.push(e);
                }).on("end", ()=>i({
                        buffer: Buffer.concat(n),
                        size: `${this.width}x${this.height}`,
                        page: t + 1
                    }));
            });
        });
    }
    writeImage(e, i) {
        const r = this.generateValidFilename(i), n = `${e.path}[${i}]`;
        return new Promise((a, o)=>{
            this.gmBaseCommand(e, n).write(r, (e)=>e ? o(e) : a({
                    name: t.basename(r),
                    size: `${this.width}x${this.height}`,
                    fileSize: s.statSync(r).size / 1e3,
                    path: r,
                    page: i + 1
                }));
        });
    }
    identify(e, t) {
        const s = this.gm(e);
        return new Promise((e, i)=>{
            t ? s.identify(t, (t, s)=>t ? i(t) : e(s.replace(/^[\w\W]*?1/, "1"))) : s.identify((t, s)=>t ? i(t) : e(s));
        });
    }
    setQuality(e) {
        return this.quality = e, this;
    }
    setFormat(e) {
        return this.format = e, this;
    }
    setSize(e, t) {
        return this.width = e, this.height = this.preserveAspectRatio || t ? t : e, this;
    }
    setPreserveAspectRatio(e) {
        return this.preserveAspectRatio = e, this;
    }
    setDensity(e) {
        return this.density = e, this;
    }
    setSavePath(e) {
        return this.savePath = e, this;
    }
    setSaveFilename(e) {
        return this.saveFilename = e, this;
    }
    setUnits(e) {
        return this.units = e, this;
    }
    setCompression(e) {
        return this.compression = e, this;
    }
    setGMClass(t) {
        return "boolean" == typeof t ? (this.gm = e.subClass({
            imageMagick: t
        }), this) : "imagemagick" === t.toLocaleLowerCase() ? (this.gm = e.subClass({
            imageMagick: !0
        }), this) : (this.gm = e.subClass({
            appPath: t
        }), this);
    }
    getOptions() {
        return {
            quality: this.quality,
            format: this.format,
            width: this.width,
            height: this.height,
            preserveAspectRatio: this.preserveAspectRatio,
            density: this.density,
            savePath: this.savePath,
            saveFilename: this.saveFilename,
            compression: this.compression,
            units: this.units
        };
    }
}
function o(e) {
    return new i.Readable({
        read () {
            this.push(e), this.push(null);
        }
    });
}
function h(e, t) {
    if ("buffer" === e) return o(t);
    if ("path" === e) return s.createReadStream(t);
    if ("base64" === e) return i = t, o(Buffer.from(i, "base64"));
    var i;
    throw new Error("Cannot recognize specified source");
}
function u(e, t, i = r) {
    const u1 = new a;
    i = Object.assign(Object.assign({}, r), i);
    const c = (e, t, s)=>{
        if (t < 1) throw new Error("Page number should be more than or equal 1");
        const i = ((e)=>{
            var t;
            if (e && "object" != typeof e) throw new Error(`Invalid convertOptions type: ${e}`);
            return null !== (t = null == e ? void 0 : e.responseType) && void 0 !== t ? t : "image";
        })(s);
        switch(i){
            case "base64":
                return u1.toBase64(e, t - 1);
            case "image":
                return u1.writeImage(e, t - 1);
            case "buffer":
                return u1.toBuffer(e, t - 1);
            default:
                throw new Error(`Invalid responseType: ${i}`);
        }
    }, f = (e, t, s)=>Promise.all(t.map((t)=>c(e, t, s))), p = (s = 1, i)=>{
        const r = h(e, t);
        return c(r, s, i);
    };
    return p.bulk = (i, r)=>n(this, void 0, void 0, function*() {
            const a = yield function(e, t) {
                return n(this, void 0, void 0, function*() {
                    if ("buffer" === e) return t;
                    if ("path" === e) return yield s.promises.readFile(t);
                    if ("base64" === e) return Buffer.from(t, "base64");
                    throw new Error("Cannot recognize specified source");
                });
            }(e, t), h = -1 === i ? yield function(e, t) {
                return n(this, void 0, void 0, function*() {
                    return (yield e.identify(t, "%p ")).split(" ").map((e)=>parseInt(e, 10));
                });
            }(u1, o(a)) : Array.isArray(i) ? i : [
                i
            ], c = [];
            for(let e = 0; e < h.length; e += 10)c.push(...yield f(o(a), h.slice(e, e + 10), r));
            return c;
        }), p.setOptions = ()=>(function(e, t) {
            return void e.setQuality(t.quality).setFormat(t.format).setPreserveAspectRatio(t.preserveAspectRatio).setSize(t.width, t.height).setDensity(t.density).setSavePath(t.savePath).setSaveFilename(t.saveFilename).setCompression(t.compression).setUnits(t.units);
        })(u1, i), p.setGMClass = (e)=>{
        u1.setGMClass(e);
    }, p.setOptions(), p;
}
exports.fromBase64 = function(e, t = r) {
    return u("base64", e, t);
}, exports.fromBuffer = function(e, t = r) {
    return u("buffer", e, t);
}, exports.fromPath = function(e, t = r) {
    return u("path", e, t);
};
}}),

};

//# sourceMappingURL=node_modules_87866ba1._.js.map