@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Fix form input text colors and sizing */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
select,
textarea {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
  min-height: 2.5rem;
  padding: 0.5rem 0.75rem;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 1px #6366f1 !important;
}

/* Ensure dropdown options are visible */
select option {
  color: #111827 !important;
  background-color: #ffffff !important;
}

/* Fix button heights to match inputs */
button {
  min-height: 2.5rem;
  padding: 0.5rem 1rem;
}

/* Image preview modal styles */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.image-preview-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.2s ease;
}

.image-preview-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
}

.image-preview-controls button {
  background-color: rgba(255, 255, 255, 0.9);
  color: #111827;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  min-height: auto;
}

.image-preview-controls button:hover {
  background-color: rgba(255, 255, 255, 1);
}
