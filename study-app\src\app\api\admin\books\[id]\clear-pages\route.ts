import { NextRequest, NextResponse } from 'next/server';
import { BookModel } from '@/lib/models';
import fs from 'fs';
import db from '@/lib/database';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const bookId = parseInt(id);

    if (isNaN(bookId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid book ID' },
        { status: 400 }
      );
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json(
        { success: false, error: 'Book not found' },
        { status: 404 }
      );
    }

    console.log(`Clearing all pages for book ${bookId}`);
    
    // Get existing images
    const existingImages = db.prepare('SELECT file_path FROM images WHERE book_id = ?').all(bookId) as any[];
    
    // Delete physical files
    let deletedFiles = 0;
    for (const img of existingImages) {
      try {
        if (fs.existsSync(img.file_path)) {
          fs.unlinkSync(img.file_path);
          deletedFiles++;
        }
      } catch (error) {
        console.warn(`Failed to delete file: ${img.file_path}`, error);
      }
    }
    
    // Delete database records
    db.prepare('DELETE FROM ocr_text WHERE image_id IN (SELECT id FROM images WHERE book_id = ?)').run(bookId);
    const deleteResult = db.prepare('DELETE FROM images WHERE book_id = ?').run(bookId);
    
    // Update book stats
    BookModel.update(bookId, {
      uploaded_pages: 0,
      processed_pages: 0,
      total_questions: 0,
      status: 'draft'
    });

    console.log(`Cleared ${deleteResult.changes} pages and ${deletedFiles} files for book ${bookId}`);

    return NextResponse.json({
      success: true,
      message: `Cleared ${deleteResult.changes} pages`,
      deletedPages: deleteResult.changes,
      deletedFiles
    });

  } catch (error) {
    console.error('Error clearing pages:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to clear pages' },
      { status: 500 }
    );
  }
}
