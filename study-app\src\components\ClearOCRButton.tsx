'use client';

export default function ClearOCRButton() {
  const handleClearOCR = async () => {
    if (confirm('This will clear all OCR data and force re-processing. Continue?')) {
      try {
        const response = await fetch('/api/admin/clear-ocr', { method: 'POST' });
        const result = await response.json();
        if (result.success) {
          alert('OCR data cleared! Re-upload images to get real OCR processing.');
          window.location.reload();
        } else {
          alert('Error: ' + result.error);
        }
      } catch (error) {
        alert('Error clearing OCR data');
      }
    }
  };

  return (
    <button
      onClick={handleClearOCR}
      className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-red-500 rounded-lg border border-gray-300 hover:border-red-400 text-left"
    >
      <div>
        <span className="rounded-lg inline-flex p-3 bg-red-50 text-red-700 ring-4 ring-white">
          <span className="text-lg">🔄</span>
        </span>
      </div>
      <div className="mt-4">
        <h3 className="text-lg font-medium">
          Clear OCR Data
        </h3>
        <p className="mt-2 text-sm text-gray-500">
          Reset OCR to use real text extraction
        </p>
      </div>
    </button>
  );
}
