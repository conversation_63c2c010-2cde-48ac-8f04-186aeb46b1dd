import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { updateImagePageType } from '@/lib/upload';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { pageType } = await request.json();
    const { id } = await params;
    const imageId = parseInt(id);

    if (!pageType) {
      return NextResponse.json({ error: 'Page type is required' }, { status: 400 });
    }

    const success = updateImagePageType(imageId, pageType);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to update page type' }, { status: 500 });
    }

    return NextResponse.json({ success: true, pageType });
  } catch (error) {
    console.error('Update page type error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
