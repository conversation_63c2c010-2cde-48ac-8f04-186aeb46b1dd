import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { BookModel, ClassModel, SubjectModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import Link from 'next/link';

export default async function BooksPage() {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const books = BookModel.getAll();
  const classes = ClassModel.getAll();
  const subjects = SubjectModel.getAll();

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Books Management</h1>
            <p className="mt-1 text-sm text-gray-600">
              Create and manage textbook containers for organizing your content.
            </p>
          </div>
          <Link
            href="/admin/books/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Create New Book
          </Link>
        </div>

        {/* Books Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {books.map((book) => (
            <div key={book.id} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <span className="text-white text-lg font-medium">📚</span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {book.title}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {book.class_name} • {book.subject_name}
                      </p>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      book.status === 'completed' ? 'bg-green-100 text-green-800' :
                      book.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {book.status}
                    </span>
                  </div>
                </div>

                {book.description && (
                  <p className="mt-3 text-sm text-gray-600 line-clamp-2">
                    {book.description}
                  </p>
                )}

                <div className="mt-4 grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-semibold text-gray-900">{book.uploaded_pages}</div>
                    <div className="text-xs text-gray-500">Pages</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-gray-900">{book.processed_pages}</div>
                    <div className="text-xs text-gray-500">Processed</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-gray-900">{book.total_questions}</div>
                    <div className="text-xs text-gray-500">Questions</div>
                  </div>
                </div>

                <div className="mt-6 flex space-x-3">
                  <Link
                    href={`/admin/books/${book.id}`}
                    className="flex-1 bg-indigo-600 text-white text-center px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
                  >
                    Manage
                  </Link>
                  <Link
                    href={`/admin/books/${book.id}/upload`}
                    className="flex-1 bg-green-600 text-white text-center px-3 py-2 rounded-md text-sm font-medium hover:bg-green-700"
                  >
                    Upload
                  </Link>
                </div>
              </div>
            </div>
          ))}

          {books.length === 0 && (
            <div className="col-span-full">
              <div className="text-center py-12">
                <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-2xl">📚</span>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">No books yet</h3>
                <p className="mt-2 text-sm text-gray-500">
                  Get started by creating your first textbook container.
                </p>
                <div className="mt-6">
                  <Link
                    href="/admin/books/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    Create New Book
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        {books.length > 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Quick Statistics
              </h3>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">{books.length}</div>
                  <div className="text-sm text-gray-600">Total Books</div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-900">
                    {books.reduce((sum, book) => sum + book.uploaded_pages, 0)}
                  </div>
                  <div className="text-sm text-blue-600">Total Pages</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-900">
                    {books.reduce((sum, book) => sum + book.processed_pages, 0)}
                  </div>
                  <div className="text-sm text-green-600">Processed Pages</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-900">
                    {books.reduce((sum, book) => sum + book.total_questions, 0)}
                  </div>
                  <div className="text-sm text-yellow-600">Total Questions</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
