{"name": "pdf2json", "version": "3.1.6", "description": "PDF file parser that converts PDF binaries to JSON and text, powered by porting a fork of PDF.JS to Node.js", "keywords": ["pdf", "pdf parser", "pdf2json", "convert pdf to json", "convert pdf form to json", "server side PDF parser", "port pdf.js to node.js", "PDF to text", "PDF text extractor", "PDF binary to text", "PDF form extractor", "command line utility to parse pdf to json", "JSON", "javascript", "PDF canvas"], "author": {"name": "Modesty Zhang", "email": "<EMAIL>", "url": "http://www.codeproject.com/script/Articles/MemberArticles.aspx?amid=62372"}, "homepage": "https://github.com/modesty/pdf2json", "repository": {"type": "git", "url": "git://github.com/modesty/pdf2json.git"}, "main": "./dist/pdfparser.cjs", "module": "./dist/pdfparser.js", "typings": "./pdfparser.d.ts", "scripts": {"pretest": "npm run build", "test:jest": "jest --config ./jest.config.json", "test": "jest --config ./jest.config.json && npm run parse-r && npm run parse-fd", "test:forms": "cd ./test && sh p2j.forms.sh", "test:misc": "cd ./test && sh p2j.one.sh misc . \"Expected: 14 success, 6 fail exception with stack trace\" ", "parse": "./bin/pdf2json.js -f ./test/pdf/fd/form/F1040.pdf -o ./test/target/fd/form", "parse-s": "./bin/pdf2json.js -f ./test/pdf/fd/form/F1040.pdf -o ./test/target/fd/form -s", "parse-t": "./bin/pdf2json.js -f ./test/pdf/fd/form/F1040.pdf -o ./test/target/fd/form -s -t", "parse-c": "./bin/pdf2json.js -f ./test/pdf/fd/form/F1040.pdf -o ./test/target/fd/form -s -t -c", "parse-m": "./bin/pdf2json.js -f ./test/pdf/fd/form/F1040.pdf -o ./test/target/fd/form -s -t -c -m", "parse-r": "./bin/pdf2json.js -f ./test/pdf/fd/form/F1040.pdf -o ./test/target/fd/form -t -c -m -r", "parse-fd": "./bin/pdf2json.js -f ./test/pdf/fd/form/ -o ./test/target/fd/form -t -c -m -r", "parse-tb": "./bin/pdf2json.js -f ./test/pdf/misc/i242_testingWithTable.pdf -o ./test/target/misc", "parse-tc": "./bin/pdf2json.js -f ./test/pdf/misc/i293_pdfpac.pdf -o ./test/target/misc", "parse-rectFix": "./bin/pdf2json.js -f ./test/pdf/misc/pr298_rect_fix_from_upstream.pdf -o ./test/target/misc", "parse-e": "./bin/pdf2json.js -f ./test/pdf/misc/i306_err_invalid.pdf -o ./test/target/misc", "build:rollup": "rollup -c ./rollup.config.js", "build:bundle-pdfjs-base": "node rollup/bundle-pdfjs-base.js", "build": "npm run build:bundle-pdfjs-base && npm run build:rollup", "build:clean": "rm -rf node_modules && rm -f package-lock.json && npm i && npm run build"}, "engines": {"node": ">=20.18.0"}, "devEngines": {"packageManager": {"name": "npm", "version": ">=10.8.2"}}, "type": "module", "bin": {"pdf2json": "./bin/pdf2json.js"}, "dependencies": {}, "bundleDependencies": [], "devDependencies": {"@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-eslint": "^9.0.5", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "jest": "^29.7.0", "rollup": "^4.29.1", "rollup-plugin-node-builtins": "^2.0.0", "rollup-plugin-sourcemaps": "^0.6.3", "tslib": "^2.6.2"}, "resolutions": {"typescript": "5.8.3"}, "maintainers": [{"name": "Modesty Zhang", "email": "<EMAIL>", "url": "http://www.codeproject.com/script/Articles/MemberArticles.aspx?amid=62372"}], "contributors": [], "bugs": {"url": "http://github.com/modesty/pdf2json/issues"}, "license": "Apache-2.0", "readme": "https://github.com/modesty/pdf2json/blob/master/readme.md", "exports": {".": {"types": "./dist/pdfparser.d.ts", "import": "./dist/pdfparser.js", "require": "./dist/pdfparser.cjs"}}, "publishConfig": {"registry": "https://registry.npmjs.com/", "access": "public"}}