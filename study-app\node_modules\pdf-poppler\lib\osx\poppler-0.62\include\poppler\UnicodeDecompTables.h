// Generated by gen-unicode-tables.py

typedef struct {
  Unicode character;
  int length;
  int offset;
} decomposition;

#define DECOMP_TABLE_LENGTH 5143

static const decomposition decomp_table[] = {
  { 0xa0, 1, 0 }, 
  { 0xa8, 2, 1 }, 
  { 0xaa, 1, 3 }, 
  { 0xaf, 2, 4 }, 
  { 0xb2, 1, 6 }, 
  { 0xb3, 1, 7 }, 
  { 0xb4, 2, 8 }, 
  { 0xb5, 1, 10 }, 
  { 0xb8, 2, 11 }, 
  { 0xb9, 1, 13 }, 
  { 0xba, 1, 14 }, 
  { 0xbc, 3, 15 }, 
  { 0xbd, 3, 18 }, 
  { 0xbe, 3, 21 }, 
  { 0xc0, 2, 24 }, 
  { 0xc1, 2, 26 }, 
  { 0xc2, 2, 28 }, 
  { 0xc3, 2, 30 }, 
  { 0xc4, 2, 32 }, 
  { 0xc5, 2, 34 }, 
  { 0xc7, 2, 36 }, 
  { 0xc8, 2, 38 }, 
  { 0xc9, 2, 40 }, 
  { 0xca, 2, 42 }, 
  { 0xcb, 2, 44 }, 
  { 0xcc, 2, 46 }, 
  { 0xcd, 2, 48 }, 
  { 0xce, 2, 50 }, 
  { 0xcf, 2, 52 }, 
  { 0xd1, 2, 54 }, 
  { 0xd2, 2, 56 }, 
  { 0xd3, 2, 58 }, 
  { 0xd4, 2, 60 }, 
  { 0xd5, 2, 62 }, 
  { 0xd6, 2, 64 }, 
  { 0xd9, 2, 66 }, 
  { 0xda, 2, 68 }, 
  { 0xdb, 2, 70 }, 
  { 0xdc, 2, 72 }, 
  { 0xdd, 2, 74 }, 
  { 0xe0, 2, 76 }, 
  { 0xe1, 2, 78 }, 
  { 0xe2, 2, 80 }, 
  { 0xe3, 2, 82 }, 
  { 0xe4, 2, 84 }, 
  { 0xe5, 2, 86 }, 
  { 0xe7, 2, 88 }, 
  { 0xe8, 2, 90 }, 
  { 0xe9, 2, 92 }, 
  { 0xea, 2, 94 }, 
  { 0xeb, 2, 96 }, 
  { 0xec, 2, 98 }, 
  { 0xed, 2, 100 }, 
  { 0xee, 2, 102 }, 
  { 0xef, 2, 104 }, 
  { 0xf1, 2, 106 }, 
  { 0xf2, 2, 108 }, 
  { 0xf3, 2, 110 }, 
  { 0xf4, 2, 112 }, 
  { 0xf5, 2, 114 }, 
  { 0xf6, 2, 116 }, 
  { 0xf9, 2, 118 }, 
  { 0xfa, 2, 120 }, 
  { 0xfb, 2, 122 }, 
  { 0xfc, 2, 124 }, 
  { 0xfd, 2, 126 }, 
  { 0xff, 2, 128 }, 
  { 0x100, 2, 130 }, 
  { 0x101, 2, 132 }, 
  { 0x102, 2, 134 }, 
  { 0x103, 2, 136 }, 
  { 0x104, 2, 138 }, 
  { 0x105, 2, 140 }, 
  { 0x106, 2, 142 }, 
  { 0x107, 2, 144 }, 
  { 0x108, 2, 146 }, 
  { 0x109, 2, 148 }, 
  { 0x10a, 2, 150 }, 
  { 0x10b, 2, 152 }, 
  { 0x10c, 2, 154 }, 
  { 0x10d, 2, 156 }, 
  { 0x10e, 2, 158 }, 
  { 0x10f, 2, 160 }, 
  { 0x112, 2, 162 }, 
  { 0x113, 2, 164 }, 
  { 0x114, 2, 166 }, 
  { 0x115, 2, 168 }, 
  { 0x116, 2, 170 }, 
  { 0x117, 2, 172 }, 
  { 0x118, 2, 174 }, 
  { 0x119, 2, 176 }, 
  { 0x11a, 2, 178 }, 
  { 0x11b, 2, 180 }, 
  { 0x11c, 2, 182 }, 
  { 0x11d, 2, 184 }, 
  { 0x11e, 2, 186 }, 
  { 0x11f, 2, 188 }, 
  { 0x120, 2, 190 }, 
  { 0x121, 2, 192 }, 
  { 0x122, 2, 194 }, 
  { 0x123, 2, 196 }, 
  { 0x124, 2, 198 }, 
  { 0x125, 2, 200 }, 
  { 0x128, 2, 202 }, 
  { 0x129, 2, 204 }, 
  { 0x12a, 2, 206 }, 
  { 0x12b, 2, 208 }, 
  { 0x12c, 2, 210 }, 
  { 0x12d, 2, 212 }, 
  { 0x12e, 2, 214 }, 
  { 0x12f, 2, 216 }, 
  { 0x130, 2, 218 }, 
  { 0x132, 2, 220 }, 
  { 0x133, 2, 222 }, 
  { 0x134, 2, 224 }, 
  { 0x135, 2, 226 }, 
  { 0x136, 2, 228 }, 
  { 0x137, 2, 230 }, 
  { 0x139, 2, 232 }, 
  { 0x13a, 2, 234 }, 
  { 0x13b, 2, 236 }, 
  { 0x13c, 2, 238 }, 
  { 0x13d, 2, 240 }, 
  { 0x13e, 2, 242 }, 
  { 0x13f, 2, 244 }, 
  { 0x140, 2, 246 }, 
  { 0x143, 2, 248 }, 
  { 0x144, 2, 250 }, 
  { 0x145, 2, 252 }, 
  { 0x146, 2, 254 }, 
  { 0x147, 2, 256 }, 
  { 0x148, 2, 258 }, 
  { 0x149, 2, 260 }, 
  { 0x14c, 2, 262 }, 
  { 0x14d, 2, 264 }, 
  { 0x14e, 2, 266 }, 
  { 0x14f, 2, 268 }, 
  { 0x150, 2, 270 }, 
  { 0x151, 2, 272 }, 
  { 0x154, 2, 274 }, 
  { 0x155, 2, 276 }, 
  { 0x156, 2, 278 }, 
  { 0x157, 2, 280 }, 
  { 0x158, 2, 282 }, 
  { 0x159, 2, 284 }, 
  { 0x15a, 2, 286 }, 
  { 0x15b, 2, 288 }, 
  { 0x15c, 2, 290 }, 
  { 0x15d, 2, 292 }, 
  { 0x15e, 2, 294 }, 
  { 0x15f, 2, 296 }, 
  { 0x160, 2, 298 }, 
  { 0x161, 2, 300 }, 
  { 0x162, 2, 302 }, 
  { 0x163, 2, 304 }, 
  { 0x164, 2, 306 }, 
  { 0x165, 2, 308 }, 
  { 0x168, 2, 310 }, 
  { 0x169, 2, 312 }, 
  { 0x16a, 2, 314 }, 
  { 0x16b, 2, 316 }, 
  { 0x16c, 2, 318 }, 
  { 0x16d, 2, 320 }, 
  { 0x16e, 2, 322 }, 
  { 0x16f, 2, 324 }, 
  { 0x170, 2, 326 }, 
  { 0x171, 2, 328 }, 
  { 0x172, 2, 330 }, 
  { 0x173, 2, 332 }, 
  { 0x174, 2, 334 }, 
  { 0x175, 2, 336 }, 
  { 0x176, 2, 338 }, 
  { 0x177, 2, 340 }, 
  { 0x178, 2, 342 }, 
  { 0x179, 2, 344 }, 
  { 0x17a, 2, 346 }, 
  { 0x17b, 2, 348 }, 
  { 0x17c, 2, 350 }, 
  { 0x17d, 2, 352 }, 
  { 0x17e, 2, 354 }, 
  { 0x17f, 1, 356 }, 
  { 0x1a0, 2, 357 }, 
  { 0x1a1, 2, 359 }, 
  { 0x1af, 2, 361 }, 
  { 0x1b0, 2, 363 }, 
  { 0x1c4, 3, 365 }, 
  { 0x1c5, 3, 368 }, 
  { 0x1c6, 3, 371 }, 
  { 0x1c7, 2, 374 }, 
  { 0x1c8, 2, 376 }, 
  { 0x1c9, 2, 378 }, 
  { 0x1ca, 2, 380 }, 
  { 0x1cb, 2, 382 }, 
  { 0x1cc, 2, 384 }, 
  { 0x1cd, 2, 386 }, 
  { 0x1ce, 2, 388 }, 
  { 0x1cf, 2, 390 }, 
  { 0x1d0, 2, 392 }, 
  { 0x1d1, 2, 394 }, 
  { 0x1d2, 2, 396 }, 
  { 0x1d3, 2, 398 }, 
  { 0x1d4, 2, 400 }, 
  { 0x1d5, 3, 402 }, 
  { 0x1d6, 3, 405 }, 
  { 0x1d7, 3, 408 }, 
  { 0x1d8, 3, 411 }, 
  { 0x1d9, 3, 414 }, 
  { 0x1da, 3, 417 }, 
  { 0x1db, 3, 420 }, 
  { 0x1dc, 3, 423 }, 
  { 0x1de, 3, 426 }, 
  { 0x1df, 3, 429 }, 
  { 0x1e0, 3, 432 }, 
  { 0x1e1, 3, 435 }, 
  { 0x1e2, 2, 438 }, 
  { 0x1e3, 2, 440 }, 
  { 0x1e6, 2, 442 }, 
  { 0x1e7, 2, 444 }, 
  { 0x1e8, 2, 446 }, 
  { 0x1e9, 2, 448 }, 
  { 0x1ea, 2, 450 }, 
  { 0x1eb, 2, 452 }, 
  { 0x1ec, 3, 454 }, 
  { 0x1ed, 3, 457 }, 
  { 0x1ee, 2, 460 }, 
  { 0x1ef, 2, 462 }, 
  { 0x1f0, 2, 464 }, 
  { 0x1f1, 2, 466 }, 
  { 0x1f2, 2, 468 }, 
  { 0x1f3, 2, 470 }, 
  { 0x1f4, 2, 472 }, 
  { 0x1f5, 2, 474 }, 
  { 0x1f8, 2, 476 }, 
  { 0x1f9, 2, 478 }, 
  { 0x1fa, 3, 480 }, 
  { 0x1fb, 3, 483 }, 
  { 0x1fc, 2, 486 }, 
  { 0x1fd, 2, 488 }, 
  { 0x1fe, 2, 490 }, 
  { 0x1ff, 2, 492 }, 
  { 0x200, 2, 494 }, 
  { 0x201, 2, 496 }, 
  { 0x202, 2, 498 }, 
  { 0x203, 2, 500 }, 
  { 0x204, 2, 502 }, 
  { 0x205, 2, 504 }, 
  { 0x206, 2, 506 }, 
  { 0x207, 2, 508 }, 
  { 0x208, 2, 510 }, 
  { 0x209, 2, 512 }, 
  { 0x20a, 2, 514 }, 
  { 0x20b, 2, 516 }, 
  { 0x20c, 2, 518 }, 
  { 0x20d, 2, 520 }, 
  { 0x20e, 2, 522 }, 
  { 0x20f, 2, 524 }, 
  { 0x210, 2, 526 }, 
  { 0x211, 2, 528 }, 
  { 0x212, 2, 530 }, 
  { 0x213, 2, 532 }, 
  { 0x214, 2, 534 }, 
  { 0x215, 2, 536 }, 
  { 0x216, 2, 538 }, 
  { 0x217, 2, 540 }, 
  { 0x218, 2, 542 }, 
  { 0x219, 2, 544 }, 
  { 0x21a, 2, 546 }, 
  { 0x21b, 2, 548 }, 
  { 0x21e, 2, 550 }, 
  { 0x21f, 2, 552 }, 
  { 0x226, 2, 554 }, 
  { 0x227, 2, 556 }, 
  { 0x228, 2, 558 }, 
  { 0x229, 2, 560 }, 
  { 0x22a, 3, 562 }, 
  { 0x22b, 3, 565 }, 
  { 0x22c, 3, 568 }, 
  { 0x22d, 3, 571 }, 
  { 0x22e, 2, 574 }, 
  { 0x22f, 2, 576 }, 
  { 0x230, 3, 578 }, 
  { 0x231, 3, 581 }, 
  { 0x232, 2, 584 }, 
  { 0x233, 2, 586 }, 
  { 0x2b0, 1, 588 }, 
  { 0x2b1, 1, 589 }, 
  { 0x2b2, 1, 590 }, 
  { 0x2b3, 1, 591 }, 
  { 0x2b4, 1, 592 }, 
  { 0x2b5, 1, 593 }, 
  { 0x2b6, 1, 594 }, 
  { 0x2b7, 1, 595 }, 
  { 0x2b8, 1, 596 }, 
  { 0x2d8, 2, 597 }, 
  { 0x2d9, 2, 599 }, 
  { 0x2da, 2, 601 }, 
  { 0x2db, 2, 603 }, 
  { 0x2dc, 2, 605 }, 
  { 0x2dd, 2, 607 }, 
  { 0x2e0, 1, 609 }, 
  { 0x2e1, 1, 610 }, 
  { 0x2e2, 1, 356 }, 
  { 0x2e3, 1, 611 }, 
  { 0x2e4, 1, 612 }, 
  { 0x340, 1, 613 }, 
  { 0x341, 1, 614 }, 
  { 0x343, 1, 615 }, 
  { 0x344, 2, 616 }, 
  { 0x374, 1, 618 }, 
  { 0x37a, 2, 619 }, 
  { 0x37e, 1, 621 }, 
  { 0x384, 2, 8 }, 
  { 0x385, 3, 622 }, 
  { 0x386, 2, 625 }, 
  { 0x387, 1, 627 }, 
  { 0x388, 2, 628 }, 
  { 0x389, 2, 630 }, 
  { 0x38a, 2, 632 }, 
  { 0x38c, 2, 634 }, 
  { 0x38e, 2, 636 }, 
  { 0x38f, 2, 638 }, 
  { 0x390, 3, 640 }, 
  { 0x3aa, 2, 643 }, 
  { 0x3ab, 2, 645 }, 
  { 0x3ac, 2, 647 }, 
  { 0x3ad, 2, 649 }, 
  { 0x3ae, 2, 651 }, 
  { 0x3af, 2, 653 }, 
  { 0x3b0, 3, 655 }, 
  { 0x3ca, 2, 658 }, 
  { 0x3cb, 2, 660 }, 
  { 0x3cc, 2, 662 }, 
  { 0x3cd, 2, 664 }, 
  { 0x3ce, 2, 666 }, 
  { 0x3d0, 1, 668 }, 
  { 0x3d1, 1, 669 }, 
  { 0x3d2, 1, 670 }, 
  { 0x3d3, 2, 636 }, 
  { 0x3d4, 2, 645 }, 
  { 0x3d5, 1, 671 }, 
  { 0x3d6, 1, 672 }, 
  { 0x3f0, 1, 673 }, 
  { 0x3f1, 1, 674 }, 
  { 0x3f2, 1, 675 }, 
  { 0x3f4, 1, 676 }, 
  { 0x3f5, 1, 677 }, 
  { 0x400, 2, 678 }, 
  { 0x401, 2, 680 }, 
  { 0x403, 2, 682 }, 
  { 0x407, 2, 684 }, 
  { 0x40c, 2, 686 }, 
  { 0x40d, 2, 688 }, 
  { 0x40e, 2, 690 }, 
  { 0x419, 2, 692 }, 
  { 0x439, 2, 694 }, 
  { 0x450, 2, 696 }, 
  { 0x451, 2, 698 }, 
  { 0x453, 2, 700 }, 
  { 0x457, 2, 702 }, 
  { 0x45c, 2, 704 }, 
  { 0x45d, 2, 706 }, 
  { 0x45e, 2, 708 }, 
  { 0x476, 2, 710 }, 
  { 0x477, 2, 712 }, 
  { 0x4c1, 2, 714 }, 
  { 0x4c2, 2, 716 }, 
  { 0x4d0, 2, 718 }, 
  { 0x4d1, 2, 720 }, 
  { 0x4d2, 2, 722 }, 
  { 0x4d3, 2, 724 }, 
  { 0x4d6, 2, 726 }, 
  { 0x4d7, 2, 728 }, 
  { 0x4da, 2, 730 }, 
  { 0x4db, 2, 732 }, 
  { 0x4dc, 2, 734 }, 
  { 0x4dd, 2, 736 }, 
  { 0x4de, 2, 738 }, 
  { 0x4df, 2, 740 }, 
  { 0x4e2, 2, 742 }, 
  { 0x4e3, 2, 744 }, 
  { 0x4e4, 2, 746 }, 
  { 0x4e5, 2, 748 }, 
  { 0x4e6, 2, 750 }, 
  { 0x4e7, 2, 752 }, 
  { 0x4ea, 2, 754 }, 
  { 0x4eb, 2, 756 }, 
  { 0x4ec, 2, 758 }, 
  { 0x4ed, 2, 760 }, 
  { 0x4ee, 2, 762 }, 
  { 0x4ef, 2, 764 }, 
  { 0x4f0, 2, 766 }, 
  { 0x4f1, 2, 768 }, 
  { 0x4f2, 2, 770 }, 
  { 0x4f3, 2, 772 }, 
  { 0x4f4, 2, 774 }, 
  { 0x4f5, 2, 776 }, 
  { 0x4f8, 2, 778 }, 
  { 0x4f9, 2, 780 }, 
  { 0x587, 2, 782 }, 
  { 0x622, 2, 784 }, 
  { 0x623, 2, 786 }, 
  { 0x624, 2, 788 }, 
  { 0x625, 2, 790 }, 
  { 0x626, 2, 792 }, 
  { 0x675, 2, 794 }, 
  { 0x676, 2, 796 }, 
  { 0x677, 2, 798 }, 
  { 0x678, 2, 800 }, 
  { 0x6c0, 2, 802 }, 
  { 0x6c2, 2, 804 }, 
  { 0x6d3, 2, 806 }, 
  { 0x929, 2, 808 }, 
  { 0x931, 2, 810 }, 
  { 0x934, 2, 812 }, 
  { 0x958, 2, 814 }, 
  { 0x959, 2, 816 }, 
  { 0x95a, 2, 818 }, 
  { 0x95b, 2, 820 }, 
  { 0x95c, 2, 822 }, 
  { 0x95d, 2, 824 }, 
  { 0x95e, 2, 826 }, 
  { 0x95f, 2, 828 }, 
  { 0x9cb, 2, 830 }, 
  { 0x9cc, 2, 832 }, 
  { 0x9dc, 2, 834 }, 
  { 0x9dd, 2, 836 }, 
  { 0x9df, 2, 838 }, 
  { 0xa33, 2, 840 }, 
  { 0xa36, 2, 842 }, 
  { 0xa59, 2, 844 }, 
  { 0xa5a, 2, 846 }, 
  { 0xa5b, 2, 848 }, 
  { 0xa5e, 2, 850 }, 
  { 0xb48, 2, 852 }, 
  { 0xb4b, 2, 854 }, 
  { 0xb4c, 2, 856 }, 
  { 0xb5c, 2, 858 }, 
  { 0xb5d, 2, 860 }, 
  { 0xb94, 2, 862 }, 
  { 0xbca, 2, 864 }, 
  { 0xbcb, 2, 866 }, 
  { 0xbcc, 2, 868 }, 
  { 0xc48, 2, 870 }, 
  { 0xcc0, 2, 872 }, 
  { 0xcc7, 2, 874 }, 
  { 0xcc8, 2, 876 }, 
  { 0xcca, 2, 878 }, 
  { 0xccb, 3, 880 }, 
  { 0xd4a, 2, 883 }, 
  { 0xd4b, 2, 885 }, 
  { 0xd4c, 2, 887 }, 
  { 0xdda, 2, 889 }, 
  { 0xddc, 2, 891 }, 
  { 0xddd, 3, 893 }, 
  { 0xdde, 2, 896 }, 
  { 0xe33, 2, 898 }, 
  { 0xeb3, 2, 900 }, 
  { 0xedc, 2, 902 }, 
  { 0xedd, 2, 904 }, 
  { 0xf0c, 1, 906 }, 
  { 0xf43, 2, 907 }, 
  { 0xf4d, 2, 909 }, 
  { 0xf52, 2, 911 }, 
  { 0xf57, 2, 913 }, 
  { 0xf5c, 2, 915 }, 
  { 0xf69, 2, 917 }, 
  { 0xf73, 2, 919 }, 
  { 0xf75, 2, 921 }, 
  { 0xf76, 2, 923 }, 
  { 0xf77, 3, 925 }, 
  { 0xf78, 2, 928 }, 
  { 0xf79, 3, 930 }, 
  { 0xf81, 2, 933 }, 
  { 0xf93, 2, 935 }, 
  { 0xf9d, 2, 937 }, 
  { 0xfa2, 2, 939 }, 
  { 0xfa7, 2, 941 }, 
  { 0xfac, 2, 943 }, 
  { 0xfb9, 2, 945 }, 
  { 0x1026, 2, 947 }, 
  { 0x1e00, 2, 949 }, 
  { 0x1e01, 2, 951 }, 
  { 0x1e02, 2, 953 }, 
  { 0x1e03, 2, 955 }, 
  { 0x1e04, 2, 957 }, 
  { 0x1e05, 2, 959 }, 
  { 0x1e06, 2, 961 }, 
  { 0x1e07, 2, 963 }, 
  { 0x1e08, 3, 965 }, 
  { 0x1e09, 3, 968 }, 
  { 0x1e0a, 2, 971 }, 
  { 0x1e0b, 2, 973 }, 
  { 0x1e0c, 2, 975 }, 
  { 0x1e0d, 2, 977 }, 
  { 0x1e0e, 2, 979 }, 
  { 0x1e0f, 2, 981 }, 
  { 0x1e10, 2, 983 }, 
  { 0x1e11, 2, 985 }, 
  { 0x1e12, 2, 987 }, 
  { 0x1e13, 2, 989 }, 
  { 0x1e14, 3, 991 }, 
  { 0x1e15, 3, 994 }, 
  { 0x1e16, 3, 997 }, 
  { 0x1e17, 3, 1000 }, 
  { 0x1e18, 2, 1003 }, 
  { 0x1e19, 2, 1005 }, 
  { 0x1e1a, 2, 1007 }, 
  { 0x1e1b, 2, 1009 }, 
  { 0x1e1c, 3, 1011 }, 
  { 0x1e1d, 3, 1014 }, 
  { 0x1e1e, 2, 1017 }, 
  { 0x1e1f, 2, 1019 }, 
  { 0x1e20, 2, 1021 }, 
  { 0x1e21, 2, 1023 }, 
  { 0x1e22, 2, 1025 }, 
  { 0x1e23, 2, 1027 }, 
  { 0x1e24, 2, 1029 }, 
  { 0x1e25, 2, 1031 }, 
  { 0x1e26, 2, 1033 }, 
  { 0x1e27, 2, 1035 }, 
  { 0x1e28, 2, 1037 }, 
  { 0x1e29, 2, 1039 }, 
  { 0x1e2a, 2, 1041 }, 
  { 0x1e2b, 2, 1043 }, 
  { 0x1e2c, 2, 1045 }, 
  { 0x1e2d, 2, 1047 }, 
  { 0x1e2e, 3, 1049 }, 
  { 0x1e2f, 3, 1052 }, 
  { 0x1e30, 2, 1055 }, 
  { 0x1e31, 2, 1057 }, 
  { 0x1e32, 2, 1059 }, 
  { 0x1e33, 2, 1061 }, 
  { 0x1e34, 2, 1063 }, 
  { 0x1e35, 2, 1065 }, 
  { 0x1e36, 2, 1067 }, 
  { 0x1e37, 2, 1069 }, 
  { 0x1e38, 3, 1071 }, 
  { 0x1e39, 3, 1074 }, 
  { 0x1e3a, 2, 1077 }, 
  { 0x1e3b, 2, 1079 }, 
  { 0x1e3c, 2, 1081 }, 
  { 0x1e3d, 2, 1083 }, 
  { 0x1e3e, 2, 1085 }, 
  { 0x1e3f, 2, 1087 }, 
  { 0x1e40, 2, 1089 }, 
  { 0x1e41, 2, 1091 }, 
  { 0x1e42, 2, 1093 }, 
  { 0x1e43, 2, 1095 }, 
  { 0x1e44, 2, 1097 }, 
  { 0x1e45, 2, 1099 }, 
  { 0x1e46, 2, 1101 }, 
  { 0x1e47, 2, 1103 }, 
  { 0x1e48, 2, 1105 }, 
  { 0x1e49, 2, 1107 }, 
  { 0x1e4a, 2, 1109 }, 
  { 0x1e4b, 2, 1111 }, 
  { 0x1e4c, 3, 1113 }, 
  { 0x1e4d, 3, 1116 }, 
  { 0x1e4e, 3, 1119 }, 
  { 0x1e4f, 3, 1122 }, 
  { 0x1e50, 3, 1125 }, 
  { 0x1e51, 3, 1128 }, 
  { 0x1e52, 3, 1131 }, 
  { 0x1e53, 3, 1134 }, 
  { 0x1e54, 2, 1137 }, 
  { 0x1e55, 2, 1139 }, 
  { 0x1e56, 2, 1141 }, 
  { 0x1e57, 2, 1143 }, 
  { 0x1e58, 2, 1145 }, 
  { 0x1e59, 2, 1147 }, 
  { 0x1e5a, 2, 1149 }, 
  { 0x1e5b, 2, 1151 }, 
  { 0x1e5c, 3, 1153 }, 
  { 0x1e5d, 3, 1156 }, 
  { 0x1e5e, 2, 1159 }, 
  { 0x1e5f, 2, 1161 }, 
  { 0x1e60, 2, 1163 }, 
  { 0x1e61, 2, 1165 }, 
  { 0x1e62, 2, 1167 }, 
  { 0x1e63, 2, 1169 }, 
  { 0x1e64, 3, 1171 }, 
  { 0x1e65, 3, 1174 }, 
  { 0x1e66, 3, 1177 }, 
  { 0x1e67, 3, 1180 }, 
  { 0x1e68, 3, 1183 }, 
  { 0x1e69, 3, 1186 }, 
  { 0x1e6a, 2, 1189 }, 
  { 0x1e6b, 2, 1191 }, 
  { 0x1e6c, 2, 1193 }, 
  { 0x1e6d, 2, 1195 }, 
  { 0x1e6e, 2, 1197 }, 
  { 0x1e6f, 2, 1199 }, 
  { 0x1e70, 2, 1201 }, 
  { 0x1e71, 2, 1203 }, 
  { 0x1e72, 2, 1205 }, 
  { 0x1e73, 2, 1207 }, 
  { 0x1e74, 2, 1209 }, 
  { 0x1e75, 2, 1211 }, 
  { 0x1e76, 2, 1213 }, 
  { 0x1e77, 2, 1215 }, 
  { 0x1e78, 3, 1217 }, 
  { 0x1e79, 3, 1220 }, 
  { 0x1e7a, 3, 1223 }, 
  { 0x1e7b, 3, 1226 }, 
  { 0x1e7c, 2, 1229 }, 
  { 0x1e7d, 2, 1231 }, 
  { 0x1e7e, 2, 1233 }, 
  { 0x1e7f, 2, 1235 }, 
  { 0x1e80, 2, 1237 }, 
  { 0x1e81, 2, 1239 }, 
  { 0x1e82, 2, 1241 }, 
  { 0x1e83, 2, 1243 }, 
  { 0x1e84, 2, 1245 }, 
  { 0x1e85, 2, 1247 }, 
  { 0x1e86, 2, 1249 }, 
  { 0x1e87, 2, 1251 }, 
  { 0x1e88, 2, 1253 }, 
  { 0x1e89, 2, 1255 }, 
  { 0x1e8a, 2, 1257 }, 
  { 0x1e8b, 2, 1259 }, 
  { 0x1e8c, 2, 1261 }, 
  { 0x1e8d, 2, 1263 }, 
  { 0x1e8e, 2, 1265 }, 
  { 0x1e8f, 2, 1267 }, 
  { 0x1e90, 2, 1269 }, 
  { 0x1e91, 2, 1271 }, 
  { 0x1e92, 2, 1273 }, 
  { 0x1e93, 2, 1275 }, 
  { 0x1e94, 2, 1277 }, 
  { 0x1e95, 2, 1279 }, 
  { 0x1e96, 2, 1281 }, 
  { 0x1e97, 2, 1283 }, 
  { 0x1e98, 2, 1285 }, 
  { 0x1e99, 2, 1287 }, 
  { 0x1e9a, 2, 1289 }, 
  { 0x1e9b, 2, 1165 }, 
  { 0x1ea0, 2, 1291 }, 
  { 0x1ea1, 2, 1293 }, 
  { 0x1ea2, 2, 1295 }, 
  { 0x1ea3, 2, 1297 }, 
  { 0x1ea4, 3, 1299 }, 
  { 0x1ea5, 3, 1302 }, 
  { 0x1ea6, 3, 1305 }, 
  { 0x1ea7, 3, 1308 }, 
  { 0x1ea8, 3, 1311 }, 
  { 0x1ea9, 3, 1314 }, 
  { 0x1eaa, 3, 1317 }, 
  { 0x1eab, 3, 1320 }, 
  { 0x1eac, 3, 1323 }, 
  { 0x1ead, 3, 1326 }, 
  { 0x1eae, 3, 1329 }, 
  { 0x1eaf, 3, 1332 }, 
  { 0x1eb0, 3, 1335 }, 
  { 0x1eb1, 3, 1338 }, 
  { 0x1eb2, 3, 1341 }, 
  { 0x1eb3, 3, 1344 }, 
  { 0x1eb4, 3, 1347 }, 
  { 0x1eb5, 3, 1350 }, 
  { 0x1eb6, 3, 1353 }, 
  { 0x1eb7, 3, 1356 }, 
  { 0x1eb8, 2, 1359 }, 
  { 0x1eb9, 2, 1361 }, 
  { 0x1eba, 2, 1363 }, 
  { 0x1ebb, 2, 1365 }, 
  { 0x1ebc, 2, 1367 }, 
  { 0x1ebd, 2, 1369 }, 
  { 0x1ebe, 3, 1371 }, 
  { 0x1ebf, 3, 1374 }, 
  { 0x1ec0, 3, 1377 }, 
  { 0x1ec1, 3, 1380 }, 
  { 0x1ec2, 3, 1383 }, 
  { 0x1ec3, 3, 1386 }, 
  { 0x1ec4, 3, 1389 }, 
  { 0x1ec5, 3, 1392 }, 
  { 0x1ec6, 3, 1395 }, 
  { 0x1ec7, 3, 1398 }, 
  { 0x1ec8, 2, 1401 }, 
  { 0x1ec9, 2, 1403 }, 
  { 0x1eca, 2, 1405 }, 
  { 0x1ecb, 2, 1407 }, 
  { 0x1ecc, 2, 1409 }, 
  { 0x1ecd, 2, 1411 }, 
  { 0x1ece, 2, 1413 }, 
  { 0x1ecf, 2, 1415 }, 
  { 0x1ed0, 3, 1417 }, 
  { 0x1ed1, 3, 1420 }, 
  { 0x1ed2, 3, 1423 }, 
  { 0x1ed3, 3, 1426 }, 
  { 0x1ed4, 3, 1429 }, 
  { 0x1ed5, 3, 1432 }, 
  { 0x1ed6, 3, 1435 }, 
  { 0x1ed7, 3, 1438 }, 
  { 0x1ed8, 3, 1441 }, 
  { 0x1ed9, 3, 1444 }, 
  { 0x1eda, 3, 1447 }, 
  { 0x1edb, 3, 1450 }, 
  { 0x1edc, 3, 1453 }, 
  { 0x1edd, 3, 1456 }, 
  { 0x1ede, 3, 1459 }, 
  { 0x1edf, 3, 1462 }, 
  { 0x1ee0, 3, 1465 }, 
  { 0x1ee1, 3, 1468 }, 
  { 0x1ee2, 3, 1471 }, 
  { 0x1ee3, 3, 1474 }, 
  { 0x1ee4, 2, 1477 }, 
  { 0x1ee5, 2, 1479 }, 
  { 0x1ee6, 2, 1481 }, 
  { 0x1ee7, 2, 1483 }, 
  { 0x1ee8, 3, 1485 }, 
  { 0x1ee9, 3, 1488 }, 
  { 0x1eea, 3, 1491 }, 
  { 0x1eeb, 3, 1494 }, 
  { 0x1eec, 3, 1497 }, 
  { 0x1eed, 3, 1500 }, 
  { 0x1eee, 3, 1503 }, 
  { 0x1eef, 3, 1506 }, 
  { 0x1ef0, 3, 1509 }, 
  { 0x1ef1, 3, 1512 }, 
  { 0x1ef2, 2, 1515 }, 
  { 0x1ef3, 2, 1517 }, 
  { 0x1ef4, 2, 1519 }, 
  { 0x1ef5, 2, 1521 }, 
  { 0x1ef6, 2, 1523 }, 
  { 0x1ef7, 2, 1525 }, 
  { 0x1ef8, 2, 1527 }, 
  { 0x1ef9, 2, 1529 }, 
  { 0x1f00, 2, 1531 }, 
  { 0x1f01, 2, 1533 }, 
  { 0x1f02, 3, 1535 }, 
  { 0x1f03, 3, 1538 }, 
  { 0x1f04, 3, 1541 }, 
  { 0x1f05, 3, 1544 }, 
  { 0x1f06, 3, 1547 }, 
  { 0x1f07, 3, 1550 }, 
  { 0x1f08, 2, 1553 }, 
  { 0x1f09, 2, 1555 }, 
  { 0x1f0a, 3, 1557 }, 
  { 0x1f0b, 3, 1560 }, 
  { 0x1f0c, 3, 1563 }, 
  { 0x1f0d, 3, 1566 }, 
  { 0x1f0e, 3, 1569 }, 
  { 0x1f0f, 3, 1572 }, 
  { 0x1f10, 2, 1575 }, 
  { 0x1f11, 2, 1577 }, 
  { 0x1f12, 3, 1579 }, 
  { 0x1f13, 3, 1582 }, 
  { 0x1f14, 3, 1585 }, 
  { 0x1f15, 3, 1588 }, 
  { 0x1f18, 2, 1591 }, 
  { 0x1f19, 2, 1593 }, 
  { 0x1f1a, 3, 1595 }, 
  { 0x1f1b, 3, 1598 }, 
  { 0x1f1c, 3, 1601 }, 
  { 0x1f1d, 3, 1604 }, 
  { 0x1f20, 2, 1607 }, 
  { 0x1f21, 2, 1609 }, 
  { 0x1f22, 3, 1611 }, 
  { 0x1f23, 3, 1614 }, 
  { 0x1f24, 3, 1617 }, 
  { 0x1f25, 3, 1620 }, 
  { 0x1f26, 3, 1623 }, 
  { 0x1f27, 3, 1626 }, 
  { 0x1f28, 2, 1629 }, 
  { 0x1f29, 2, 1631 }, 
  { 0x1f2a, 3, 1633 }, 
  { 0x1f2b, 3, 1636 }, 
  { 0x1f2c, 3, 1639 }, 
  { 0x1f2d, 3, 1642 }, 
  { 0x1f2e, 3, 1645 }, 
  { 0x1f2f, 3, 1648 }, 
  { 0x1f30, 2, 1651 }, 
  { 0x1f31, 2, 1653 }, 
  { 0x1f32, 3, 1655 }, 
  { 0x1f33, 3, 1658 }, 
  { 0x1f34, 3, 1661 }, 
  { 0x1f35, 3, 1664 }, 
  { 0x1f36, 3, 1667 }, 
  { 0x1f37, 3, 1670 }, 
  { 0x1f38, 2, 1673 }, 
  { 0x1f39, 2, 1675 }, 
  { 0x1f3a, 3, 1677 }, 
  { 0x1f3b, 3, 1680 }, 
  { 0x1f3c, 3, 1683 }, 
  { 0x1f3d, 3, 1686 }, 
  { 0x1f3e, 3, 1689 }, 
  { 0x1f3f, 3, 1692 }, 
  { 0x1f40, 2, 1695 }, 
  { 0x1f41, 2, 1697 }, 
  { 0x1f42, 3, 1699 }, 
  { 0x1f43, 3, 1702 }, 
  { 0x1f44, 3, 1705 }, 
  { 0x1f45, 3, 1708 }, 
  { 0x1f48, 2, 1711 }, 
  { 0x1f49, 2, 1713 }, 
  { 0x1f4a, 3, 1715 }, 
  { 0x1f4b, 3, 1718 }, 
  { 0x1f4c, 3, 1721 }, 
  { 0x1f4d, 3, 1724 }, 
  { 0x1f50, 2, 1727 }, 
  { 0x1f51, 2, 1729 }, 
  { 0x1f52, 3, 1731 }, 
  { 0x1f53, 3, 1734 }, 
  { 0x1f54, 3, 1737 }, 
  { 0x1f55, 3, 1740 }, 
  { 0x1f56, 3, 1743 }, 
  { 0x1f57, 3, 1746 }, 
  { 0x1f59, 2, 1749 }, 
  { 0x1f5b, 3, 1751 }, 
  { 0x1f5d, 3, 1754 }, 
  { 0x1f5f, 3, 1757 }, 
  { 0x1f60, 2, 1760 }, 
  { 0x1f61, 2, 1762 }, 
  { 0x1f62, 3, 1764 }, 
  { 0x1f63, 3, 1767 }, 
  { 0x1f64, 3, 1770 }, 
  { 0x1f65, 3, 1773 }, 
  { 0x1f66, 3, 1776 }, 
  { 0x1f67, 3, 1779 }, 
  { 0x1f68, 2, 1782 }, 
  { 0x1f69, 2, 1784 }, 
  { 0x1f6a, 3, 1786 }, 
  { 0x1f6b, 3, 1789 }, 
  { 0x1f6c, 3, 1792 }, 
  { 0x1f6d, 3, 1795 }, 
  { 0x1f6e, 3, 1798 }, 
  { 0x1f6f, 3, 1801 }, 
  { 0x1f70, 2, 1804 }, 
  { 0x1f71, 2, 647 }, 
  { 0x1f72, 2, 1806 }, 
  { 0x1f73, 2, 649 }, 
  { 0x1f74, 2, 1808 }, 
  { 0x1f75, 2, 651 }, 
  { 0x1f76, 2, 1810 }, 
  { 0x1f77, 2, 653 }, 
  { 0x1f78, 2, 1812 }, 
  { 0x1f79, 2, 662 }, 
  { 0x1f7a, 2, 1814 }, 
  { 0x1f7b, 2, 664 }, 
  { 0x1f7c, 2, 1816 }, 
  { 0x1f7d, 2, 666 }, 
  { 0x1f80, 3, 1818 }, 
  { 0x1f81, 3, 1821 }, 
  { 0x1f82, 4, 1824 }, 
  { 0x1f83, 4, 1828 }, 
  { 0x1f84, 4, 1832 }, 
  { 0x1f85, 4, 1836 }, 
  { 0x1f86, 4, 1840 }, 
  { 0x1f87, 4, 1844 }, 
  { 0x1f88, 3, 1848 }, 
  { 0x1f89, 3, 1851 }, 
  { 0x1f8a, 4, 1854 }, 
  { 0x1f8b, 4, 1858 }, 
  { 0x1f8c, 4, 1862 }, 
  { 0x1f8d, 4, 1866 }, 
  { 0x1f8e, 4, 1870 }, 
  { 0x1f8f, 4, 1874 }, 
  { 0x1f90, 3, 1878 }, 
  { 0x1f91, 3, 1881 }, 
  { 0x1f92, 4, 1884 }, 
  { 0x1f93, 4, 1888 }, 
  { 0x1f94, 4, 1892 }, 
  { 0x1f95, 4, 1896 }, 
  { 0x1f96, 4, 1900 }, 
  { 0x1f97, 4, 1904 }, 
  { 0x1f98, 3, 1908 }, 
  { 0x1f99, 3, 1911 }, 
  { 0x1f9a, 4, 1914 }, 
  { 0x1f9b, 4, 1918 }, 
  { 0x1f9c, 4, 1922 }, 
  { 0x1f9d, 4, 1926 }, 
  { 0x1f9e, 4, 1930 }, 
  { 0x1f9f, 4, 1934 }, 
  { 0x1fa0, 3, 1938 }, 
  { 0x1fa1, 3, 1941 }, 
  { 0x1fa2, 4, 1944 }, 
  { 0x1fa3, 4, 1948 }, 
  { 0x1fa4, 4, 1952 }, 
  { 0x1fa5, 4, 1956 }, 
  { 0x1fa6, 4, 1960 }, 
  { 0x1fa7, 4, 1964 }, 
  { 0x1fa8, 3, 1968 }, 
  { 0x1fa9, 3, 1971 }, 
  { 0x1faa, 4, 1974 }, 
  { 0x1fab, 4, 1978 }, 
  { 0x1fac, 4, 1982 }, 
  { 0x1fad, 4, 1986 }, 
  { 0x1fae, 4, 1990 }, 
  { 0x1faf, 4, 1994 }, 
  { 0x1fb0, 2, 1998 }, 
  { 0x1fb1, 2, 2000 }, 
  { 0x1fb2, 3, 2002 }, 
  { 0x1fb3, 2, 2005 }, 
  { 0x1fb4, 3, 2007 }, 
  { 0x1fb6, 2, 2010 }, 
  { 0x1fb7, 3, 2012 }, 
  { 0x1fb8, 2, 2015 }, 
  { 0x1fb9, 2, 2017 }, 
  { 0x1fba, 2, 2019 }, 
  { 0x1fbb, 2, 625 }, 
  { 0x1fbc, 2, 2021 }, 
  { 0x1fbd, 2, 2023 }, 
  { 0x1fbe, 1, 2025 }, 
  { 0x1fbf, 2, 2023 }, 
  { 0x1fc0, 2, 2026 }, 
  { 0x1fc1, 3, 2028 }, 
  { 0x1fc2, 3, 2031 }, 
  { 0x1fc3, 2, 2034 }, 
  { 0x1fc4, 3, 2036 }, 
  { 0x1fc6, 2, 2039 }, 
  { 0x1fc7, 3, 2041 }, 
  { 0x1fc8, 2, 2044 }, 
  { 0x1fc9, 2, 628 }, 
  { 0x1fca, 2, 2046 }, 
  { 0x1fcb, 2, 630 }, 
  { 0x1fcc, 2, 2048 }, 
  { 0x1fcd, 3, 2050 }, 
  { 0x1fce, 3, 2053 }, 
  { 0x1fcf, 3, 2056 }, 
  { 0x1fd0, 2, 2059 }, 
  { 0x1fd1, 2, 2061 }, 
  { 0x1fd2, 3, 2063 }, 
  { 0x1fd3, 3, 640 }, 
  { 0x1fd6, 2, 2066 }, 
  { 0x1fd7, 3, 2068 }, 
  { 0x1fd8, 2, 2071 }, 
  { 0x1fd9, 2, 2073 }, 
  { 0x1fda, 2, 2075 }, 
  { 0x1fdb, 2, 632 }, 
  { 0x1fdd, 3, 2077 }, 
  { 0x1fde, 3, 2080 }, 
  { 0x1fdf, 3, 2083 }, 
  { 0x1fe0, 2, 2086 }, 
  { 0x1fe1, 2, 2088 }, 
  { 0x1fe2, 3, 2090 }, 
  { 0x1fe3, 3, 655 }, 
  { 0x1fe4, 2, 2093 }, 
  { 0x1fe5, 2, 2095 }, 
  { 0x1fe6, 2, 2097 }, 
  { 0x1fe7, 3, 2099 }, 
  { 0x1fe8, 2, 2102 }, 
  { 0x1fe9, 2, 2104 }, 
  { 0x1fea, 2, 2106 }, 
  { 0x1feb, 2, 636 }, 
  { 0x1fec, 2, 2108 }, 
  { 0x1fed, 3, 2110 }, 
  { 0x1fee, 3, 622 }, 
  { 0x1fef, 1, 2113 }, 
  { 0x1ff2, 3, 2114 }, 
  { 0x1ff3, 2, 2117 }, 
  { 0x1ff4, 3, 2119 }, 
  { 0x1ff6, 2, 2122 }, 
  { 0x1ff7, 3, 2124 }, 
  { 0x1ff8, 2, 2127 }, 
  { 0x1ff9, 2, 634 }, 
  { 0x1ffa, 2, 2129 }, 
  { 0x1ffb, 2, 638 }, 
  { 0x1ffc, 2, 2131 }, 
  { 0x1ffd, 2, 8 }, 
  { 0x1ffe, 2, 2133 }, 
  { 0x2000, 1, 0 }, 
  { 0x2001, 1, 0 }, 
  { 0x2002, 1, 0 }, 
  { 0x2003, 1, 0 }, 
  { 0x2004, 1, 0 }, 
  { 0x2005, 1, 0 }, 
  { 0x2006, 1, 0 }, 
  { 0x2007, 1, 0 }, 
  { 0x2008, 1, 0 }, 
  { 0x2009, 1, 0 }, 
  { 0x200a, 1, 0 }, 
  { 0x2011, 1, 2135 }, 
  { 0x2017, 2, 2136 }, 
  { 0x2024, 1, 2138 }, 
  { 0x2025, 2, 2139 }, 
  { 0x2026, 3, 2141 }, 
  { 0x202f, 1, 0 }, 
  { 0x2033, 2, 2144 }, 
  { 0x2034, 3, 2146 }, 
  { 0x2036, 2, 2149 }, 
  { 0x2037, 3, 2151 }, 
  { 0x203c, 2, 2154 }, 
  { 0x203e, 2, 2156 }, 
  { 0x2047, 2, 2158 }, 
  { 0x2048, 2, 2160 }, 
  { 0x2049, 2, 2162 }, 
  { 0x2057, 4, 2164 }, 
  { 0x205f, 1, 0 }, 
  { 0x2070, 1, 2168 }, 
  { 0x2071, 1, 2169 }, 
  { 0x2074, 1, 2170 }, 
  { 0x2075, 1, 2171 }, 
  { 0x2076, 1, 2172 }, 
  { 0x2077, 1, 2173 }, 
  { 0x2078, 1, 2174 }, 
  { 0x2079, 1, 2175 }, 
  { 0x207a, 1, 2176 }, 
  { 0x207b, 1, 2177 }, 
  { 0x207c, 1, 2178 }, 
  { 0x207d, 1, 2179 }, 
  { 0x207e, 1, 2180 }, 
  { 0x207f, 1, 2181 }, 
  { 0x2080, 1, 2168 }, 
  { 0x2081, 1, 13 }, 
  { 0x2082, 1, 6 }, 
  { 0x2083, 1, 7 }, 
  { 0x2084, 1, 2170 }, 
  { 0x2085, 1, 2171 }, 
  { 0x2086, 1, 2172 }, 
  { 0x2087, 1, 2173 }, 
  { 0x2088, 1, 2174 }, 
  { 0x2089, 1, 2175 }, 
  { 0x208a, 1, 2176 }, 
  { 0x208b, 1, 2177 }, 
  { 0x208c, 1, 2178 }, 
  { 0x208d, 1, 2179 }, 
  { 0x208e, 1, 2180 }, 
  { 0x20a8, 2, 2182 }, 
  { 0x2100, 3, 2184 }, 
  { 0x2101, 3, 2187 }, 
  { 0x2102, 1, 2190 }, 
  { 0x2103, 2, 2191 }, 
  { 0x2105, 3, 2193 }, 
  { 0x2106, 3, 2196 }, 
  { 0x2107, 1, 2199 }, 
  { 0x2109, 2, 2200 }, 
  { 0x210a, 1, 2202 }, 
  { 0x210b, 1, 2203 }, 
  { 0x210c, 1, 2203 }, 
  { 0x210d, 1, 2203 }, 
  { 0x210e, 1, 588 }, 
  { 0x210f, 1, 2204 }, 
  { 0x2110, 1, 2205 }, 
  { 0x2111, 1, 2205 }, 
  { 0x2112, 1, 2206 }, 
  { 0x2113, 1, 610 }, 
  { 0x2115, 1, 2207 }, 
  { 0x2116, 2, 2208 }, 
  { 0x2119, 1, 2210 }, 
  { 0x211a, 1, 2211 }, 
  { 0x211b, 1, 2212 }, 
  { 0x211c, 1, 2212 }, 
  { 0x211d, 1, 2212 }, 
  { 0x2120, 2, 2213 }, 
  { 0x2121, 3, 2215 }, 
  { 0x2122, 2, 2218 }, 
  { 0x2124, 1, 2220 }, 
  { 0x2126, 1, 2221 }, 
  { 0x2128, 1, 2220 }, 
  { 0x212a, 1, 2222 }, 
  { 0x212b, 2, 34 }, 
  { 0x212c, 1, 2223 }, 
  { 0x212d, 1, 2190 }, 
  { 0x212f, 1, 2224 }, 
  { 0x2130, 1, 2225 }, 
  { 0x2131, 1, 2226 }, 
  { 0x2133, 1, 2227 }, 
  { 0x2134, 1, 14 }, 
  { 0x2135, 1, 2228 }, 
  { 0x2136, 1, 2229 }, 
  { 0x2137, 1, 2230 }, 
  { 0x2138, 1, 2231 }, 
  { 0x2139, 1, 2169 }, 
  { 0x213d, 1, 2232 }, 
  { 0x213e, 1, 2233 }, 
  { 0x213f, 1, 2234 }, 
  { 0x2140, 1, 2235 }, 
  { 0x2145, 1, 2236 }, 
  { 0x2146, 1, 2237 }, 
  { 0x2147, 1, 2224 }, 
  { 0x2148, 1, 2169 }, 
  { 0x2149, 1, 590 }, 
  { 0x2153, 3, 2238 }, 
  { 0x2154, 3, 2241 }, 
  { 0x2155, 3, 2244 }, 
  { 0x2156, 3, 2247 }, 
  { 0x2157, 3, 2250 }, 
  { 0x2158, 3, 2253 }, 
  { 0x2159, 3, 2256 }, 
  { 0x215a, 3, 2259 }, 
  { 0x215b, 3, 2262 }, 
  { 0x215c, 3, 2265 }, 
  { 0x215d, 3, 2268 }, 
  { 0x215e, 3, 2271 }, 
  { 0x215f, 2, 2274 }, 
  { 0x2160, 1, 2205 }, 
  { 0x2161, 2, 2276 }, 
  { 0x2162, 3, 2278 }, 
  { 0x2163, 2, 2281 }, 
  { 0x2164, 1, 2283 }, 
  { 0x2165, 2, 2284 }, 
  { 0x2166, 3, 2286 }, 
  { 0x2167, 4, 2289 }, 
  { 0x2168, 2, 2293 }, 
  { 0x2169, 1, 2295 }, 
  { 0x216a, 2, 2296 }, 
  { 0x216b, 3, 2298 }, 
  { 0x216c, 1, 2206 }, 
  { 0x216d, 1, 2190 }, 
  { 0x216e, 1, 2236 }, 
  { 0x216f, 1, 2227 }, 
  { 0x2170, 1, 2169 }, 
  { 0x2171, 2, 2301 }, 
  { 0x2172, 3, 2303 }, 
  { 0x2173, 2, 2306 }, 
  { 0x2174, 1, 2308 }, 
  { 0x2175, 2, 2309 }, 
  { 0x2176, 3, 2311 }, 
  { 0x2177, 4, 2314 }, 
  { 0x2178, 2, 2318 }, 
  { 0x2179, 1, 611 }, 
  { 0x217a, 2, 2320 }, 
  { 0x217b, 3, 2322 }, 
  { 0x217c, 1, 610 }, 
  { 0x217d, 1, 2325 }, 
  { 0x217e, 1, 2237 }, 
  { 0x217f, 1, 2326 }, 
  { 0x219a, 2, 2327 }, 
  { 0x219b, 2, 2329 }, 
  { 0x21ae, 2, 2331 }, 
  { 0x21cd, 2, 2333 }, 
  { 0x21ce, 2, 2335 }, 
  { 0x21cf, 2, 2337 }, 
  { 0x2204, 2, 2339 }, 
  { 0x2209, 2, 2341 }, 
  { 0x220c, 2, 2343 }, 
  { 0x2224, 2, 2345 }, 
  { 0x2226, 2, 2347 }, 
  { 0x222c, 2, 2349 }, 
  { 0x222d, 3, 2351 }, 
  { 0x222f, 2, 2354 }, 
  { 0x2230, 3, 2356 }, 
  { 0x2241, 2, 2359 }, 
  { 0x2244, 2, 2361 }, 
  { 0x2247, 2, 2363 }, 
  { 0x2249, 2, 2365 }, 
  { 0x2260, 2, 2367 }, 
  { 0x2262, 2, 2369 }, 
  { 0x226d, 2, 2371 }, 
  { 0x226e, 2, 2373 }, 
  { 0x226f, 2, 2375 }, 
  { 0x2270, 2, 2377 }, 
  { 0x2271, 2, 2379 }, 
  { 0x2274, 2, 2381 }, 
  { 0x2275, 2, 2383 }, 
  { 0x2278, 2, 2385 }, 
  { 0x2279, 2, 2387 }, 
  { 0x2280, 2, 2389 }, 
  { 0x2281, 2, 2391 }, 
  { 0x2284, 2, 2393 }, 
  { 0x2285, 2, 2395 }, 
  { 0x2288, 2, 2397 }, 
  { 0x2289, 2, 2399 }, 
  { 0x22ac, 2, 2401 }, 
  { 0x22ad, 2, 2403 }, 
  { 0x22ae, 2, 2405 }, 
  { 0x22af, 2, 2407 }, 
  { 0x22e0, 2, 2409 }, 
  { 0x22e1, 2, 2411 }, 
  { 0x22e2, 2, 2413 }, 
  { 0x22e3, 2, 2415 }, 
  { 0x22ea, 2, 2417 }, 
  { 0x22eb, 2, 2419 }, 
  { 0x22ec, 2, 2421 }, 
  { 0x22ed, 2, 2423 }, 
  { 0x2329, 1, 2425 }, 
  { 0x232a, 1, 2426 }, 
  { 0x2460, 1, 13 }, 
  { 0x2461, 1, 6 }, 
  { 0x2462, 1, 7 }, 
  { 0x2463, 1, 2170 }, 
  { 0x2464, 1, 2171 }, 
  { 0x2465, 1, 2172 }, 
  { 0x2466, 1, 2173 }, 
  { 0x2467, 1, 2174 }, 
  { 0x2468, 1, 2175 }, 
  { 0x2469, 2, 2427 }, 
  { 0x246a, 2, 2429 }, 
  { 0x246b, 2, 2431 }, 
  { 0x246c, 2, 2433 }, 
  { 0x246d, 2, 2435 }, 
  { 0x246e, 2, 2437 }, 
  { 0x246f, 2, 2439 }, 
  { 0x2470, 2, 2441 }, 
  { 0x2471, 2, 2443 }, 
  { 0x2472, 2, 2445 }, 
  { 0x2473, 2, 2447 }, 
  { 0x2474, 3, 2449 }, 
  { 0x2475, 3, 2452 }, 
  { 0x2476, 3, 2455 }, 
  { 0x2477, 3, 2458 }, 
  { 0x2478, 3, 2461 }, 
  { 0x2479, 3, 2464 }, 
  { 0x247a, 3, 2467 }, 
  { 0x247b, 3, 2470 }, 
  { 0x247c, 3, 2473 }, 
  { 0x247d, 4, 2476 }, 
  { 0x247e, 4, 2480 }, 
  { 0x247f, 4, 2484 }, 
  { 0x2480, 4, 2488 }, 
  { 0x2481, 4, 2492 }, 
  { 0x2482, 4, 2496 }, 
  { 0x2483, 4, 2500 }, 
  { 0x2484, 4, 2504 }, 
  { 0x2485, 4, 2508 }, 
  { 0x2486, 4, 2512 }, 
  { 0x2487, 4, 2516 }, 
  { 0x2488, 2, 2520 }, 
  { 0x2489, 2, 2522 }, 
  { 0x248a, 2, 2524 }, 
  { 0x248b, 2, 2526 }, 
  { 0x248c, 2, 2528 }, 
  { 0x248d, 2, 2530 }, 
  { 0x248e, 2, 2532 }, 
  { 0x248f, 2, 2534 }, 
  { 0x2490, 2, 2536 }, 
  { 0x2491, 3, 2538 }, 
  { 0x2492, 3, 2541 }, 
  { 0x2493, 3, 2544 }, 
  { 0x2494, 3, 2547 }, 
  { 0x2495, 3, 2550 }, 
  { 0x2496, 3, 2553 }, 
  { 0x2497, 3, 2556 }, 
  { 0x2498, 3, 2559 }, 
  { 0x2499, 3, 2562 }, 
  { 0x249a, 3, 2565 }, 
  { 0x249b, 3, 2568 }, 
  { 0x249c, 3, 2571 }, 
  { 0x249d, 3, 2574 }, 
  { 0x249e, 3, 2577 }, 
  { 0x249f, 3, 2580 }, 
  { 0x24a0, 3, 2583 }, 
  { 0x24a1, 3, 2586 }, 
  { 0x24a2, 3, 2589 }, 
  { 0x24a3, 3, 2592 }, 
  { 0x24a4, 3, 2595 }, 
  { 0x24a5, 3, 2598 }, 
  { 0x24a6, 3, 2601 }, 
  { 0x24a7, 3, 2604 }, 
  { 0x24a8, 3, 2607 }, 
  { 0x24a9, 3, 2610 }, 
  { 0x24aa, 3, 2613 }, 
  { 0x24ab, 3, 2616 }, 
  { 0x24ac, 3, 2619 }, 
  { 0x24ad, 3, 2622 }, 
  { 0x24ae, 3, 2625 }, 
  { 0x24af, 3, 2628 }, 
  { 0x24b0, 3, 2631 }, 
  { 0x24b1, 3, 2634 }, 
  { 0x24b2, 3, 2637 }, 
  { 0x24b3, 3, 2640 }, 
  { 0x24b4, 3, 2643 }, 
  { 0x24b5, 3, 2646 }, 
  { 0x24b6, 1, 2649 }, 
  { 0x24b7, 1, 2223 }, 
  { 0x24b8, 1, 2190 }, 
  { 0x24b9, 1, 2236 }, 
  { 0x24ba, 1, 2225 }, 
  { 0x24bb, 1, 2226 }, 
  { 0x24bc, 1, 2650 }, 
  { 0x24bd, 1, 2203 }, 
  { 0x24be, 1, 2205 }, 
  { 0x24bf, 1, 2651 }, 
  { 0x24c0, 1, 2222 }, 
  { 0x24c1, 1, 2206 }, 
  { 0x24c2, 1, 2227 }, 
  { 0x24c3, 1, 2207 }, 
  { 0x24c4, 1, 2652 }, 
  { 0x24c5, 1, 2210 }, 
  { 0x24c6, 1, 2211 }, 
  { 0x24c7, 1, 2212 }, 
  { 0x24c8, 1, 2653 }, 
  { 0x24c9, 1, 2654 }, 
  { 0x24ca, 1, 2655 }, 
  { 0x24cb, 1, 2283 }, 
  { 0x24cc, 1, 2656 }, 
  { 0x24cd, 1, 2295 }, 
  { 0x24ce, 1, 2657 }, 
  { 0x24cf, 1, 2220 }, 
  { 0x24d0, 1, 3 }, 
  { 0x24d1, 1, 2658 }, 
  { 0x24d2, 1, 2325 }, 
  { 0x24d3, 1, 2237 }, 
  { 0x24d4, 1, 2224 }, 
  { 0x24d5, 1, 2659 }, 
  { 0x24d6, 1, 2202 }, 
  { 0x24d7, 1, 588 }, 
  { 0x24d8, 1, 2169 }, 
  { 0x24d9, 1, 590 }, 
  { 0x24da, 1, 2660 }, 
  { 0x24db, 1, 610 }, 
  { 0x24dc, 1, 2326 }, 
  { 0x24dd, 1, 2181 }, 
  { 0x24de, 1, 14 }, 
  { 0x24df, 1, 2661 }, 
  { 0x24e0, 1, 2662 }, 
  { 0x24e1, 1, 591 }, 
  { 0x24e2, 1, 356 }, 
  { 0x24e3, 1, 2663 }, 
  { 0x24e4, 1, 2664 }, 
  { 0x24e5, 1, 2308 }, 
  { 0x24e6, 1, 595 }, 
  { 0x24e7, 1, 611 }, 
  { 0x24e8, 1, 596 }, 
  { 0x24e9, 1, 2665 }, 
  { 0x24ea, 1, 2168 }, 
  { 0x2a0c, 4, 2666 }, 
  { 0x2a74, 3, 2670 }, 
  { 0x2a75, 2, 2673 }, 
  { 0x2a76, 3, 2675 }, 
  { 0x2adc, 2, 2678 }, 
  { 0x2e9f, 1, 2680 }, 
  { 0x2ef3, 1, 2681 }, 
  { 0x2f00, 1, 2682 }, 
  { 0x2f01, 1, 2683 }, 
  { 0x2f02, 1, 2684 }, 
  { 0x2f03, 1, 2685 }, 
  { 0x2f04, 1, 2686 }, 
  { 0x2f05, 1, 2687 }, 
  { 0x2f06, 1, 2688 }, 
  { 0x2f07, 1, 2689 }, 
  { 0x2f08, 1, 2690 }, 
  { 0x2f09, 1, 2691 }, 
  { 0x2f0a, 1, 2692 }, 
  { 0x2f0b, 1, 2693 }, 
  { 0x2f0c, 1, 2694 }, 
  { 0x2f0d, 1, 2695 }, 
  { 0x2f0e, 1, 2696 }, 
  { 0x2f0f, 1, 2697 }, 
  { 0x2f10, 1, 2698 }, 
  { 0x2f11, 1, 2699 }, 
  { 0x2f12, 1, 2700 }, 
  { 0x2f13, 1, 2701 }, 
  { 0x2f14, 1, 2702 }, 
  { 0x2f15, 1, 2703 }, 
  { 0x2f16, 1, 2704 }, 
  { 0x2f17, 1, 2705 }, 
  { 0x2f18, 1, 2706 }, 
  { 0x2f19, 1, 2707 }, 
  { 0x2f1a, 1, 2708 }, 
  { 0x2f1b, 1, 2709 }, 
  { 0x2f1c, 1, 2710 }, 
  { 0x2f1d, 1, 2711 }, 
  { 0x2f1e, 1, 2712 }, 
  { 0x2f1f, 1, 2713 }, 
  { 0x2f20, 1, 2714 }, 
  { 0x2f21, 1, 2715 }, 
  { 0x2f22, 1, 2716 }, 
  { 0x2f23, 1, 2717 }, 
  { 0x2f24, 1, 2718 }, 
  { 0x2f25, 1, 2719 }, 
  { 0x2f26, 1, 2720 }, 
  { 0x2f27, 1, 2721 }, 
  { 0x2f28, 1, 2722 }, 
  { 0x2f29, 1, 2723 }, 
  { 0x2f2a, 1, 2724 }, 
  { 0x2f2b, 1, 2725 }, 
  { 0x2f2c, 1, 2726 }, 
  { 0x2f2d, 1, 2727 }, 
  { 0x2f2e, 1, 2728 }, 
  { 0x2f2f, 1, 2729 }, 
  { 0x2f30, 1, 2730 }, 
  { 0x2f31, 1, 2731 }, 
  { 0x2f32, 1, 2732 }, 
  { 0x2f33, 1, 2733 }, 
  { 0x2f34, 1, 2734 }, 
  { 0x2f35, 1, 2735 }, 
  { 0x2f36, 1, 2736 }, 
  { 0x2f37, 1, 2737 }, 
  { 0x2f38, 1, 2738 }, 
  { 0x2f39, 1, 2739 }, 
  { 0x2f3a, 1, 2740 }, 
  { 0x2f3b, 1, 2741 }, 
  { 0x2f3c, 1, 2742 }, 
  { 0x2f3d, 1, 2743 }, 
  { 0x2f3e, 1, 2744 }, 
  { 0x2f3f, 1, 2745 }, 
  { 0x2f40, 1, 2746 }, 
  { 0x2f41, 1, 2747 }, 
  { 0x2f42, 1, 2748 }, 
  { 0x2f43, 1, 2749 }, 
  { 0x2f44, 1, 2750 }, 
  { 0x2f45, 1, 2751 }, 
  { 0x2f46, 1, 2752 }, 
  { 0x2f47, 1, 2753 }, 
  { 0x2f48, 1, 2754 }, 
  { 0x2f49, 1, 2755 }, 
  { 0x2f4a, 1, 2756 }, 
  { 0x2f4b, 1, 2757 }, 
  { 0x2f4c, 1, 2758 }, 
  { 0x2f4d, 1, 2759 }, 
  { 0x2f4e, 1, 2760 }, 
  { 0x2f4f, 1, 2761 }, 
  { 0x2f50, 1, 2762 }, 
  { 0x2f51, 1, 2763 }, 
  { 0x2f52, 1, 2764 }, 
  { 0x2f53, 1, 2765 }, 
  { 0x2f54, 1, 2766 }, 
  { 0x2f55, 1, 2767 }, 
  { 0x2f56, 1, 2768 }, 
  { 0x2f57, 1, 2769 }, 
  { 0x2f58, 1, 2770 }, 
  { 0x2f59, 1, 2771 }, 
  { 0x2f5a, 1, 2772 }, 
  { 0x2f5b, 1, 2773 }, 
  { 0x2f5c, 1, 2774 }, 
  { 0x2f5d, 1, 2775 }, 
  { 0x2f5e, 1, 2776 }, 
  { 0x2f5f, 1, 2777 }, 
  { 0x2f60, 1, 2778 }, 
  { 0x2f61, 1, 2779 }, 
  { 0x2f62, 1, 2780 }, 
  { 0x2f63, 1, 2781 }, 
  { 0x2f64, 1, 2782 }, 
  { 0x2f65, 1, 2783 }, 
  { 0x2f66, 1, 2784 }, 
  { 0x2f67, 1, 2785 }, 
  { 0x2f68, 1, 2786 }, 
  { 0x2f69, 1, 2787 }, 
  { 0x2f6a, 1, 2788 }, 
  { 0x2f6b, 1, 2789 }, 
  { 0x2f6c, 1, 2790 }, 
  { 0x2f6d, 1, 2791 }, 
  { 0x2f6e, 1, 2792 }, 
  { 0x2f6f, 1, 2793 }, 
  { 0x2f70, 1, 2794 }, 
  { 0x2f71, 1, 2795 }, 
  { 0x2f72, 1, 2796 }, 
  { 0x2f73, 1, 2797 }, 
  { 0x2f74, 1, 2798 }, 
  { 0x2f75, 1, 2799 }, 
  { 0x2f76, 1, 2800 }, 
  { 0x2f77, 1, 2801 }, 
  { 0x2f78, 1, 2802 }, 
  { 0x2f79, 1, 2803 }, 
  { 0x2f7a, 1, 2804 }, 
  { 0x2f7b, 1, 2805 }, 
  { 0x2f7c, 1, 2806 }, 
  { 0x2f7d, 1, 2807 }, 
  { 0x2f7e, 1, 2808 }, 
  { 0x2f7f, 1, 2809 }, 
  { 0x2f80, 1, 2810 }, 
  { 0x2f81, 1, 2811 }, 
  { 0x2f82, 1, 2812 }, 
  { 0x2f83, 1, 2813 }, 
  { 0x2f84, 1, 2814 }, 
  { 0x2f85, 1, 2815 }, 
  { 0x2f86, 1, 2816 }, 
  { 0x2f87, 1, 2817 }, 
  { 0x2f88, 1, 2818 }, 
  { 0x2f89, 1, 2819 }, 
  { 0x2f8a, 1, 2820 }, 
  { 0x2f8b, 1, 2821 }, 
  { 0x2f8c, 1, 2822 }, 
  { 0x2f8d, 1, 2823 }, 
  { 0x2f8e, 1, 2824 }, 
  { 0x2f8f, 1, 2825 }, 
  { 0x2f90, 1, 2826 }, 
  { 0x2f91, 1, 2827 }, 
  { 0x2f92, 1, 2828 }, 
  { 0x2f93, 1, 2829 }, 
  { 0x2f94, 1, 2830 }, 
  { 0x2f95, 1, 2831 }, 
  { 0x2f96, 1, 2832 }, 
  { 0x2f97, 1, 2833 }, 
  { 0x2f98, 1, 2834 }, 
  { 0x2f99, 1, 2835 }, 
  { 0x2f9a, 1, 2836 }, 
  { 0x2f9b, 1, 2837 }, 
  { 0x2f9c, 1, 2838 }, 
  { 0x2f9d, 1, 2839 }, 
  { 0x2f9e, 1, 2840 }, 
  { 0x2f9f, 1, 2841 }, 
  { 0x2fa0, 1, 2842 }, 
  { 0x2fa1, 1, 2843 }, 
  { 0x2fa2, 1, 2844 }, 
  { 0x2fa3, 1, 2845 }, 
  { 0x2fa4, 1, 2846 }, 
  { 0x2fa5, 1, 2847 }, 
  { 0x2fa6, 1, 2848 }, 
  { 0x2fa7, 1, 2849 }, 
  { 0x2fa8, 1, 2850 }, 
  { 0x2fa9, 1, 2851 }, 
  { 0x2faa, 1, 2852 }, 
  { 0x2fab, 1, 2853 }, 
  { 0x2fac, 1, 2854 }, 
  { 0x2fad, 1, 2855 }, 
  { 0x2fae, 1, 2856 }, 
  { 0x2faf, 1, 2857 }, 
  { 0x2fb0, 1, 2858 }, 
  { 0x2fb1, 1, 2859 }, 
  { 0x2fb2, 1, 2860 }, 
  { 0x2fb3, 1, 2861 }, 
  { 0x2fb4, 1, 2862 }, 
  { 0x2fb5, 1, 2863 }, 
  { 0x2fb6, 1, 2864 }, 
  { 0x2fb7, 1, 2865 }, 
  { 0x2fb8, 1, 2866 }, 
  { 0x2fb9, 1, 2867 }, 
  { 0x2fba, 1, 2868 }, 
  { 0x2fbb, 1, 2869 }, 
  { 0x2fbc, 1, 2870 }, 
  { 0x2fbd, 1, 2871 }, 
  { 0x2fbe, 1, 2872 }, 
  { 0x2fbf, 1, 2873 }, 
  { 0x2fc0, 1, 2874 }, 
  { 0x2fc1, 1, 2875 }, 
  { 0x2fc2, 1, 2876 }, 
  { 0x2fc3, 1, 2877 }, 
  { 0x2fc4, 1, 2878 }, 
  { 0x2fc5, 1, 2879 }, 
  { 0x2fc6, 1, 2880 }, 
  { 0x2fc7, 1, 2881 }, 
  { 0x2fc8, 1, 2882 }, 
  { 0x2fc9, 1, 2883 }, 
  { 0x2fca, 1, 2884 }, 
  { 0x2fcb, 1, 2885 }, 
  { 0x2fcc, 1, 2886 }, 
  { 0x2fcd, 1, 2887 }, 
  { 0x2fce, 1, 2888 }, 
  { 0x2fcf, 1, 2889 }, 
  { 0x2fd0, 1, 2890 }, 
  { 0x2fd1, 1, 2891 }, 
  { 0x2fd2, 1, 2892 }, 
  { 0x2fd3, 1, 2893 }, 
  { 0x2fd4, 1, 2894 }, 
  { 0x2fd5, 1, 2895 }, 
  { 0x3000, 1, 0 }, 
  { 0x3036, 1, 2896 }, 
  { 0x3038, 1, 2705 }, 
  { 0x3039, 1, 2897 }, 
  { 0x303a, 1, 2898 }, 
  { 0x304c, 2, 2899 }, 
  { 0x304e, 2, 2901 }, 
  { 0x3050, 2, 2903 }, 
  { 0x3052, 2, 2905 }, 
  { 0x3054, 2, 2907 }, 
  { 0x3056, 2, 2909 }, 
  { 0x3058, 2, 2911 }, 
  { 0x305a, 2, 2913 }, 
  { 0x305c, 2, 2915 }, 
  { 0x305e, 2, 2917 }, 
  { 0x3060, 2, 2919 }, 
  { 0x3062, 2, 2921 }, 
  { 0x3065, 2, 2923 }, 
  { 0x3067, 2, 2925 }, 
  { 0x3069, 2, 2927 }, 
  { 0x3070, 2, 2929 }, 
  { 0x3071, 2, 2931 }, 
  { 0x3073, 2, 2933 }, 
  { 0x3074, 2, 2935 }, 
  { 0x3076, 2, 2937 }, 
  { 0x3077, 2, 2939 }, 
  { 0x3079, 2, 2941 }, 
  { 0x307a, 2, 2943 }, 
  { 0x307c, 2, 2945 }, 
  { 0x307d, 2, 2947 }, 
  { 0x3094, 2, 2949 }, 
  { 0x309b, 2, 2951 }, 
  { 0x309c, 2, 2953 }, 
  { 0x309e, 2, 2955 }, 
  { 0x309f, 2, 2957 }, 
  { 0x30ac, 2, 2959 }, 
  { 0x30ae, 2, 2961 }, 
  { 0x30b0, 2, 2963 }, 
  { 0x30b2, 2, 2965 }, 
  { 0x30b4, 2, 2967 }, 
  { 0x30b6, 2, 2969 }, 
  { 0x30b8, 2, 2971 }, 
  { 0x30ba, 2, 2973 }, 
  { 0x30bc, 2, 2975 }, 
  { 0x30be, 2, 2977 }, 
  { 0x30c0, 2, 2979 }, 
  { 0x30c2, 2, 2981 }, 
  { 0x30c5, 2, 2983 }, 
  { 0x30c7, 2, 2985 }, 
  { 0x30c9, 2, 2987 }, 
  { 0x30d0, 2, 2989 }, 
  { 0x30d1, 2, 2991 }, 
  { 0x30d3, 2, 2993 }, 
  { 0x30d4, 2, 2995 }, 
  { 0x30d6, 2, 2997 }, 
  { 0x30d7, 2, 2999 }, 
  { 0x30d9, 2, 3001 }, 
  { 0x30da, 2, 3003 }, 
  { 0x30dc, 2, 3005 }, 
  { 0x30dd, 2, 3007 }, 
  { 0x30f4, 2, 3009 }, 
  { 0x30f7, 2, 3011 }, 
  { 0x30f8, 2, 3013 }, 
  { 0x30f9, 2, 3015 }, 
  { 0x30fa, 2, 3017 }, 
  { 0x30fe, 2, 3019 }, 
  { 0x30ff, 2, 3021 }, 
  { 0x3131, 1, 3023 }, 
  { 0x3132, 1, 3024 }, 
  { 0x3133, 1, 3025 }, 
  { 0x3134, 1, 3026 }, 
  { 0x3135, 1, 3027 }, 
  { 0x3136, 1, 3028 }, 
  { 0x3137, 1, 3029 }, 
  { 0x3138, 1, 3030 }, 
  { 0x3139, 1, 3031 }, 
  { 0x313a, 1, 3032 }, 
  { 0x313b, 1, 3033 }, 
  { 0x313c, 1, 3034 }, 
  { 0x313d, 1, 3035 }, 
  { 0x313e, 1, 3036 }, 
  { 0x313f, 1, 3037 }, 
  { 0x3140, 1, 3038 }, 
  { 0x3141, 1, 3039 }, 
  { 0x3142, 1, 3040 }, 
  { 0x3143, 1, 3041 }, 
  { 0x3144, 1, 3042 }, 
  { 0x3145, 1, 3043 }, 
  { 0x3146, 1, 3044 }, 
  { 0x3147, 1, 3045 }, 
  { 0x3148, 1, 3046 }, 
  { 0x3149, 1, 3047 }, 
  { 0x314a, 1, 3048 }, 
  { 0x314b, 1, 3049 }, 
  { 0x314c, 1, 3050 }, 
  { 0x314d, 1, 3051 }, 
  { 0x314e, 1, 3052 }, 
  { 0x314f, 1, 3053 }, 
  { 0x3150, 1, 3054 }, 
  { 0x3151, 1, 3055 }, 
  { 0x3152, 1, 3056 }, 
  { 0x3153, 1, 3057 }, 
  { 0x3154, 1, 3058 }, 
  { 0x3155, 1, 3059 }, 
  { 0x3156, 1, 3060 }, 
  { 0x3157, 1, 3061 }, 
  { 0x3158, 1, 3062 }, 
  { 0x3159, 1, 3063 }, 
  { 0x315a, 1, 3064 }, 
  { 0x315b, 1, 3065 }, 
  { 0x315c, 1, 3066 }, 
  { 0x315d, 1, 3067 }, 
  { 0x315e, 1, 3068 }, 
  { 0x315f, 1, 3069 }, 
  { 0x3160, 1, 3070 }, 
  { 0x3161, 1, 3071 }, 
  { 0x3162, 1, 3072 }, 
  { 0x3163, 1, 3073 }, 
  { 0x3164, 1, 3074 }, 
  { 0x3165, 1, 3075 }, 
  { 0x3166, 1, 3076 }, 
  { 0x3167, 1, 3077 }, 
  { 0x3168, 1, 3078 }, 
  { 0x3169, 1, 3079 }, 
  { 0x316a, 1, 3080 }, 
  { 0x316b, 1, 3081 }, 
  { 0x316c, 1, 3082 }, 
  { 0x316d, 1, 3083 }, 
  { 0x316e, 1, 3084 }, 
  { 0x316f, 1, 3085 }, 
  { 0x3170, 1, 3086 }, 
  { 0x3171, 1, 3087 }, 
  { 0x3172, 1, 3088 }, 
  { 0x3173, 1, 3089 }, 
  { 0x3174, 1, 3090 }, 
  { 0x3175, 1, 3091 }, 
  { 0x3176, 1, 3092 }, 
  { 0x3177, 1, 3093 }, 
  { 0x3178, 1, 3094 }, 
  { 0x3179, 1, 3095 }, 
  { 0x317a, 1, 3096 }, 
  { 0x317b, 1, 3097 }, 
  { 0x317c, 1, 3098 }, 
  { 0x317d, 1, 3099 }, 
  { 0x317e, 1, 3100 }, 
  { 0x317f, 1, 3101 }, 
  { 0x3180, 1, 3102 }, 
  { 0x3181, 1, 3103 }, 
  { 0x3182, 1, 3104 }, 
  { 0x3183, 1, 3105 }, 
  { 0x3184, 1, 3106 }, 
  { 0x3185, 1, 3107 }, 
  { 0x3186, 1, 3108 }, 
  { 0x3187, 1, 3109 }, 
  { 0x3188, 1, 3110 }, 
  { 0x3189, 1, 3111 }, 
  { 0x318a, 1, 3112 }, 
  { 0x318b, 1, 3113 }, 
  { 0x318c, 1, 3114 }, 
  { 0x318d, 1, 3115 }, 
  { 0x318e, 1, 3116 }, 
  { 0x3192, 1, 2682 }, 
  { 0x3193, 1, 2688 }, 
  { 0x3194, 1, 3117 }, 
  { 0x3195, 1, 3118 }, 
  { 0x3196, 1, 3119 }, 
  { 0x3197, 1, 3120 }, 
  { 0x3198, 1, 3121 }, 
  { 0x3199, 1, 3122 }, 
  { 0x319a, 1, 2686 }, 
  { 0x319b, 1, 3123 }, 
  { 0x319c, 1, 3124 }, 
  { 0x319d, 1, 3125 }, 
  { 0x319e, 1, 3126 }, 
  { 0x319f, 1, 2690 }, 
  { 0x3200, 3, 3127 }, 
  { 0x3201, 3, 3130 }, 
  { 0x3202, 3, 3133 }, 
  { 0x3203, 3, 3136 }, 
  { 0x3204, 3, 3139 }, 
  { 0x3205, 3, 3142 }, 
  { 0x3206, 3, 3145 }, 
  { 0x3207, 3, 3148 }, 
  { 0x3208, 3, 3151 }, 
  { 0x3209, 3, 3154 }, 
  { 0x320a, 3, 3157 }, 
  { 0x320b, 3, 3160 }, 
  { 0x320c, 3, 3163 }, 
  { 0x320d, 3, 3166 }, 
  { 0x320e, 4, 3169 }, 
  { 0x320f, 4, 3173 }, 
  { 0x3210, 4, 3177 }, 
  { 0x3211, 4, 3181 }, 
  { 0x3212, 4, 3185 }, 
  { 0x3213, 4, 3189 }, 
  { 0x3214, 4, 3193 }, 
  { 0x3215, 4, 3197 }, 
  { 0x3216, 4, 3201 }, 
  { 0x3217, 4, 3205 }, 
  { 0x3218, 4, 3209 }, 
  { 0x3219, 4, 3213 }, 
  { 0x321a, 4, 3217 }, 
  { 0x321b, 4, 3221 }, 
  { 0x321c, 4, 3225 }, 
  { 0x3220, 3, 3229 }, 
  { 0x3221, 3, 3232 }, 
  { 0x3222, 3, 3235 }, 
  { 0x3223, 3, 3238 }, 
  { 0x3224, 3, 3241 }, 
  { 0x3225, 3, 3244 }, 
  { 0x3226, 3, 3247 }, 
  { 0x3227, 3, 3250 }, 
  { 0x3228, 3, 3253 }, 
  { 0x3229, 3, 3256 }, 
  { 0x322a, 3, 3259 }, 
  { 0x322b, 3, 3262 }, 
  { 0x322c, 3, 3265 }, 
  { 0x322d, 3, 3268 }, 
  { 0x322e, 3, 3271 }, 
  { 0x322f, 3, 3274 }, 
  { 0x3230, 3, 3277 }, 
  { 0x3231, 3, 3280 }, 
  { 0x3232, 3, 3283 }, 
  { 0x3233, 3, 3286 }, 
  { 0x3234, 3, 3289 }, 
  { 0x3235, 3, 3292 }, 
  { 0x3236, 3, 3295 }, 
  { 0x3237, 3, 3298 }, 
  { 0x3238, 3, 3301 }, 
  { 0x3239, 3, 3304 }, 
  { 0x323a, 3, 3307 }, 
  { 0x323b, 3, 3310 }, 
  { 0x323c, 3, 3313 }, 
  { 0x323d, 3, 3316 }, 
  { 0x323e, 3, 3319 }, 
  { 0x323f, 3, 3322 }, 
  { 0x3240, 3, 3325 }, 
  { 0x3241, 3, 3328 }, 
  { 0x3242, 3, 3331 }, 
  { 0x3243, 3, 3334 }, 
  { 0x3251, 2, 3337 }, 
  { 0x3252, 2, 3339 }, 
  { 0x3253, 2, 3341 }, 
  { 0x3254, 2, 3343 }, 
  { 0x3255, 2, 3345 }, 
  { 0x3256, 2, 3347 }, 
  { 0x3257, 2, 3349 }, 
  { 0x3258, 2, 3351 }, 
  { 0x3259, 2, 3353 }, 
  { 0x325a, 2, 3355 }, 
  { 0x325b, 2, 3357 }, 
  { 0x325c, 2, 3359 }, 
  { 0x325d, 2, 3361 }, 
  { 0x325e, 2, 3363 }, 
  { 0x325f, 2, 3365 }, 
  { 0x3260, 1, 3023 }, 
  { 0x3261, 1, 3026 }, 
  { 0x3262, 1, 3029 }, 
  { 0x3263, 1, 3031 }, 
  { 0x3264, 1, 3039 }, 
  { 0x3265, 1, 3040 }, 
  { 0x3266, 1, 3043 }, 
  { 0x3267, 1, 3045 }, 
  { 0x3268, 1, 3046 }, 
  { 0x3269, 1, 3048 }, 
  { 0x326a, 1, 3049 }, 
  { 0x326b, 1, 3050 }, 
  { 0x326c, 1, 3051 }, 
  { 0x326d, 1, 3052 }, 
  { 0x326e, 2, 3367 }, 
  { 0x326f, 2, 3369 }, 
  { 0x3270, 2, 3371 }, 
  { 0x3271, 2, 3373 }, 
  { 0x3272, 2, 3375 }, 
  { 0x3273, 2, 3377 }, 
  { 0x3274, 2, 3379 }, 
  { 0x3275, 2, 3381 }, 
  { 0x3276, 2, 3383 }, 
  { 0x3277, 2, 3385 }, 
  { 0x3278, 2, 3387 }, 
  { 0x3279, 2, 3389 }, 
  { 0x327a, 2, 3391 }, 
  { 0x327b, 2, 3393 }, 
  { 0x3280, 1, 2682 }, 
  { 0x3281, 1, 2688 }, 
  { 0x3282, 1, 3117 }, 
  { 0x3283, 1, 3118 }, 
  { 0x3284, 1, 3395 }, 
  { 0x3285, 1, 3396 }, 
  { 0x3286, 1, 3397 }, 
  { 0x3287, 1, 2693 }, 
  { 0x3288, 1, 3398 }, 
  { 0x3289, 1, 2705 }, 
  { 0x328a, 1, 2755 }, 
  { 0x328b, 1, 2767 }, 
  { 0x328c, 1, 2766 }, 
  { 0x328d, 1, 2756 }, 
  { 0x328e, 1, 2848 }, 
  { 0x328f, 1, 2713 }, 
  { 0x3290, 1, 2753 }, 
  { 0x3291, 1, 3399 }, 
  { 0x3292, 1, 3400 }, 
  { 0x3293, 1, 3401 }, 
  { 0x3294, 1, 3402 }, 
  { 0x3295, 1, 3403 }, 
  { 0x3296, 1, 3404 }, 
  { 0x3297, 1, 3405 }, 
  { 0x3298, 1, 3406 }, 
  { 0x3299, 1, 3407 }, 
  { 0x329a, 1, 3408 }, 
  { 0x329b, 1, 2719 }, 
  { 0x329c, 1, 3409 }, 
  { 0x329d, 1, 3410 }, 
  { 0x329e, 1, 3411 }, 
  { 0x329f, 1, 3412 }, 
  { 0x32a0, 1, 3413 }, 
  { 0x32a1, 1, 3414 }, 
  { 0x32a2, 1, 3415 }, 
  { 0x32a3, 1, 3416 }, 
  { 0x32a4, 1, 3119 }, 
  { 0x32a5, 1, 3120 }, 
  { 0x32a6, 1, 3121 }, 
  { 0x32a7, 1, 3417 }, 
  { 0x32a8, 1, 3418 }, 
  { 0x32a9, 1, 3419 }, 
  { 0x32aa, 1, 3420 }, 
  { 0x32ab, 1, 3421 }, 
  { 0x32ac, 1, 3422 }, 
  { 0x32ad, 1, 3423 }, 
  { 0x32ae, 1, 3424 }, 
  { 0x32af, 1, 3425 }, 
  { 0x32b0, 1, 3426 }, 
  { 0x32b1, 2, 3427 }, 
  { 0x32b2, 2, 3429 }, 
  { 0x32b3, 2, 3431 }, 
  { 0x32b4, 2, 3433 }, 
  { 0x32b5, 2, 3435 }, 
  { 0x32b6, 2, 3437 }, 
  { 0x32b7, 2, 3439 }, 
  { 0x32b8, 2, 3441 }, 
  { 0x32b9, 2, 3443 }, 
  { 0x32ba, 2, 3445 }, 
  { 0x32bb, 2, 3447 }, 
  { 0x32bc, 2, 3449 }, 
  { 0x32bd, 2, 3451 }, 
  { 0x32be, 2, 3453 }, 
  { 0x32bf, 2, 3455 }, 
  { 0x32c0, 2, 3457 }, 
  { 0x32c1, 2, 3459 }, 
  { 0x32c2, 2, 3461 }, 
  { 0x32c3, 2, 3463 }, 
  { 0x32c4, 2, 3465 }, 
  { 0x32c5, 2, 3467 }, 
  { 0x32c6, 2, 3469 }, 
  { 0x32c7, 2, 3471 }, 
  { 0x32c8, 2, 3473 }, 
  { 0x32c9, 3, 3475 }, 
  { 0x32ca, 3, 3478 }, 
  { 0x32cb, 3, 3481 }, 
  { 0x32d0, 1, 3484 }, 
  { 0x32d1, 1, 3485 }, 
  { 0x32d2, 1, 3486 }, 
  { 0x32d3, 1, 3487 }, 
  { 0x32d4, 1, 3488 }, 
  { 0x32d5, 1, 3489 }, 
  { 0x32d6, 1, 3490 }, 
  { 0x32d7, 1, 3491 }, 
  { 0x32d8, 1, 3492 }, 
  { 0x32d9, 1, 3493 }, 
  { 0x32da, 1, 3494 }, 
  { 0x32db, 1, 3495 }, 
  { 0x32dc, 1, 3496 }, 
  { 0x32dd, 1, 3497 }, 
  { 0x32de, 1, 3498 }, 
  { 0x32df, 1, 3499 }, 
  { 0x32e0, 1, 3500 }, 
  { 0x32e1, 1, 3501 }, 
  { 0x32e2, 1, 3502 }, 
  { 0x32e3, 1, 3503 }, 
  { 0x32e4, 1, 3504 }, 
  { 0x32e5, 1, 3505 }, 
  { 0x32e6, 1, 3506 }, 
  { 0x32e7, 1, 3507 }, 
  { 0x32e8, 1, 3508 }, 
  { 0x32e9, 1, 3509 }, 
  { 0x32ea, 1, 3510 }, 
  { 0x32eb, 1, 3511 }, 
  { 0x32ec, 1, 3512 }, 
  { 0x32ed, 1, 3513 }, 
  { 0x32ee, 1, 3514 }, 
  { 0x32ef, 1, 3515 }, 
  { 0x32f0, 1, 3516 }, 
  { 0x32f1, 1, 3517 }, 
  { 0x32f2, 1, 3518 }, 
  { 0x32f3, 1, 3519 }, 
  { 0x32f4, 1, 3520 }, 
  { 0x32f5, 1, 3521 }, 
  { 0x32f6, 1, 3522 }, 
  { 0x32f7, 1, 3523 }, 
  { 0x32f8, 1, 3524 }, 
  { 0x32f9, 1, 3525 }, 
  { 0x32fa, 1, 3526 }, 
  { 0x32fb, 1, 3527 }, 
  { 0x32fc, 1, 3528 }, 
  { 0x32fd, 1, 3529 }, 
  { 0x32fe, 1, 3530 }, 
  { 0x3300, 5, 3531 }, 
  { 0x3301, 4, 3536 }, 
  { 0x3302, 5, 3540 }, 
  { 0x3303, 3, 3545 }, 
  { 0x3304, 5, 3548 }, 
  { 0x3305, 3, 3553 }, 
  { 0x3306, 3, 3556 }, 
  { 0x3307, 6, 3559 }, 
  { 0x3308, 4, 3565 }, 
  { 0x3309, 3, 3569 }, 
  { 0x330a, 3, 3572 }, 
  { 0x330b, 3, 3575 }, 
  { 0x330c, 4, 3578 }, 
  { 0x330d, 4, 3582 }, 
  { 0x330e, 4, 3586 }, 
  { 0x330f, 4, 3590 }, 
  { 0x3310, 4, 3594 }, 
  { 0x3311, 4, 3598 }, 
  { 0x3312, 4, 3602 }, 
  { 0x3313, 6, 3606 }, 
  { 0x3314, 2, 3612 }, 
  { 0x3315, 6, 3614 }, 
  { 0x3316, 6, 3620 }, 
  { 0x3317, 5, 3626 }, 
  { 0x3318, 4, 3631 }, 
  { 0x3319, 6, 3635 }, 
  { 0x331a, 6, 3641 }, 
  { 0x331b, 4, 3647 }, 
  { 0x331c, 3, 3651 }, 
  { 0x331d, 3, 3654 }, 
  { 0x331e, 4, 3657 }, 
  { 0x331f, 4, 3661 }, 
  { 0x3320, 5, 3665 }, 
  { 0x3321, 5, 3670 }, 
  { 0x3322, 3, 3675 }, 
  { 0x3323, 3, 3678 }, 
  { 0x3324, 4, 3681 }, 
  { 0x3325, 3, 3685 }, 
  { 0x3326, 3, 3688 }, 
  { 0x3327, 2, 3691 }, 
  { 0x3328, 2, 3693 }, 
  { 0x3329, 3, 3695 }, 
  { 0x332a, 3, 3698 }, 
  { 0x332b, 6, 3701 }, 
  { 0x332c, 4, 3707 }, 
  { 0x332d, 5, 3711 }, 
  { 0x332e, 6, 3716 }, 
  { 0x332f, 4, 3722 }, 
  { 0x3330, 3, 3726 }, 
  { 0x3331, 3, 3729 }, 
  { 0x3332, 6, 3732 }, 
  { 0x3333, 4, 3738 }, 
  { 0x3334, 6, 3742 }, 
  { 0x3335, 3, 3748 }, 
  { 0x3336, 5, 3751 }, 
  { 0x3337, 3, 3756 }, 
  { 0x3338, 4, 3759 }, 
  { 0x3339, 3, 3763 }, 
  { 0x333a, 4, 3766 }, 
  { 0x333b, 5, 3770 }, 
  { 0x333c, 4, 3775 }, 
  { 0x333d, 5, 3779 }, 
  { 0x333e, 4, 3784 }, 
  { 0x333f, 2, 3788 }, 
  { 0x3340, 5, 3790 }, 
  { 0x3341, 3, 3795 }, 
  { 0x3342, 3, 3798 }, 
  { 0x3343, 4, 3801 }, 
  { 0x3344, 3, 3805 }, 
  { 0x3345, 3, 3808 }, 
  { 0x3346, 3, 3811 }, 
  { 0x3347, 5, 3814 }, 
  { 0x3348, 4, 3819 }, 
  { 0x3349, 2, 3823 }, 
  { 0x334a, 6, 3825 }, 
  { 0x334b, 3, 3831 }, 
  { 0x334c, 5, 3834 }, 
  { 0x334d, 4, 3839 }, 
  { 0x334e, 4, 3843 }, 
  { 0x334f, 3, 3847 }, 
  { 0x3350, 3, 3850 }, 
  { 0x3351, 4, 3853 }, 
  { 0x3352, 2, 3857 }, 
  { 0x3353, 4, 3859 }, 
  { 0x3354, 5, 3863 }, 
  { 0x3355, 2, 3868 }, 
  { 0x3356, 6, 3870 }, 
  { 0x3357, 3, 3876 }, 
  { 0x3358, 2, 3879 }, 
  { 0x3359, 2, 3881 }, 
  { 0x335a, 2, 3883 }, 
  { 0x335b, 2, 3885 }, 
  { 0x335c, 2, 3887 }, 
  { 0x335d, 2, 3889 }, 
  { 0x335e, 2, 3891 }, 
  { 0x335f, 2, 3893 }, 
  { 0x3360, 2, 3895 }, 
  { 0x3361, 2, 3897 }, 
  { 0x3362, 3, 3899 }, 
  { 0x3363, 3, 3902 }, 
  { 0x3364, 3, 3905 }, 
  { 0x3365, 3, 3908 }, 
  { 0x3366, 3, 3911 }, 
  { 0x3367, 3, 3914 }, 
  { 0x3368, 3, 3917 }, 
  { 0x3369, 3, 3920 }, 
  { 0x336a, 3, 3923 }, 
  { 0x336b, 3, 3926 }, 
  { 0x336c, 3, 3929 }, 
  { 0x336d, 3, 3932 }, 
  { 0x336e, 3, 3935 }, 
  { 0x336f, 3, 3938 }, 
  { 0x3370, 3, 3941 }, 
  { 0x3371, 3, 3944 }, 
  { 0x3372, 2, 3947 }, 
  { 0x3373, 2, 3949 }, 
  { 0x3374, 3, 3951 }, 
  { 0x3375, 2, 3954 }, 
  { 0x3376, 2, 3956 }, 
  { 0x337b, 2, 3958 }, 
  { 0x337c, 2, 3960 }, 
  { 0x337d, 2, 3962 }, 
  { 0x337e, 2, 3964 }, 
  { 0x337f, 4, 3966 }, 
  { 0x3380, 2, 3970 }, 
  { 0x3381, 2, 3972 }, 
  { 0x3382, 2, 3974 }, 
  { 0x3383, 2, 3976 }, 
  { 0x3384, 2, 3978 }, 
  { 0x3385, 2, 3980 }, 
  { 0x3386, 2, 3982 }, 
  { 0x3387, 2, 3984 }, 
  { 0x3388, 3, 3986 }, 
  { 0x3389, 4, 3989 }, 
  { 0x338a, 2, 3993 }, 
  { 0x338b, 2, 3995 }, 
  { 0x338c, 2, 3997 }, 
  { 0x338d, 2, 3999 }, 
  { 0x338e, 2, 4001 }, 
  { 0x338f, 2, 4003 }, 
  { 0x3390, 2, 4005 }, 
  { 0x3391, 3, 4007 }, 
  { 0x3392, 3, 4010 }, 
  { 0x3393, 3, 4013 }, 
  { 0x3394, 3, 4016 }, 
  { 0x3395, 2, 4019 }, 
  { 0x3396, 2, 4021 }, 
  { 0x3397, 2, 4023 }, 
  { 0x3398, 2, 4025 }, 
  { 0x3399, 2, 4027 }, 
  { 0x339a, 2, 4029 }, 
  { 0x339b, 2, 4031 }, 
  { 0x339c, 2, 4033 }, 
  { 0x339d, 2, 4035 }, 
  { 0x339e, 2, 4037 }, 
  { 0x339f, 3, 4039 }, 
  { 0x33a0, 3, 4042 }, 
  { 0x33a1, 2, 4045 }, 
  { 0x33a2, 3, 4047 }, 
  { 0x33a3, 3, 4050 }, 
  { 0x33a4, 3, 4053 }, 
  { 0x33a5, 2, 4056 }, 
  { 0x33a6, 3, 4058 }, 
  { 0x33a7, 3, 4061 }, 
  { 0x33a8, 4, 4064 }, 
  { 0x33a9, 2, 4068 }, 
  { 0x33aa, 3, 4070 }, 
  { 0x33ab, 3, 4073 }, 
  { 0x33ac, 3, 4076 }, 
  { 0x33ad, 3, 4079 }, 
  { 0x33ae, 5, 4082 }, 
  { 0x33af, 6, 4087 }, 
  { 0x33b0, 2, 4093 }, 
  { 0x33b1, 2, 4095 }, 
  { 0x33b2, 2, 4097 }, 
  { 0x33b3, 2, 4099 }, 
  { 0x33b4, 2, 4101 }, 
  { 0x33b5, 2, 4103 }, 
  { 0x33b6, 2, 4105 }, 
  { 0x33b7, 2, 4107 }, 
  { 0x33b8, 2, 4109 }, 
  { 0x33b9, 2, 4111 }, 
  { 0x33ba, 2, 4113 }, 
  { 0x33bb, 2, 4115 }, 
  { 0x33bc, 2, 4117 }, 
  { 0x33bd, 2, 4119 }, 
  { 0x33be, 2, 4121 }, 
  { 0x33bf, 2, 4123 }, 
  { 0x33c0, 2, 4125 }, 
  { 0x33c1, 2, 4127 }, 
  { 0x33c2, 4, 4129 }, 
  { 0x33c3, 2, 4133 }, 
  { 0x33c4, 2, 4135 }, 
  { 0x33c5, 2, 4137 }, 
  { 0x33c6, 4, 4139 }, 
  { 0x33c7, 3, 4143 }, 
  { 0x33c8, 2, 4146 }, 
  { 0x33c9, 2, 4148 }, 
  { 0x33ca, 2, 4150 }, 
  { 0x33cb, 2, 4152 }, 
  { 0x33cc, 2, 4154 }, 
  { 0x33cd, 2, 4156 }, 
  { 0x33ce, 2, 4158 }, 
  { 0x33cf, 2, 4160 }, 
  { 0x33d0, 2, 4162 }, 
  { 0x33d1, 2, 4164 }, 
  { 0x33d2, 3, 4166 }, 
  { 0x33d3, 2, 4169 }, 
  { 0x33d4, 2, 4171 }, 
  { 0x33d5, 3, 4173 }, 
  { 0x33d6, 3, 4176 }, 
  { 0x33d7, 2, 4179 }, 
  { 0x33d8, 4, 4181 }, 
  { 0x33d9, 3, 4185 }, 
  { 0x33da, 2, 4188 }, 
  { 0x33db, 2, 4190 }, 
  { 0x33dc, 2, 4192 }, 
  { 0x33dd, 2, 4194 }, 
  { 0x33e0, 2, 4196 }, 
  { 0x33e1, 2, 4198 }, 
  { 0x33e2, 2, 4200 }, 
  { 0x33e3, 2, 4202 }, 
  { 0x33e4, 2, 4204 }, 
  { 0x33e5, 2, 4206 }, 
  { 0x33e6, 2, 4208 }, 
  { 0x33e7, 2, 4210 }, 
  { 0x33e8, 2, 4212 }, 
  { 0x33e9, 3, 4214 }, 
  { 0x33ea, 3, 4217 }, 
  { 0x33eb, 3, 4220 }, 
  { 0x33ec, 3, 4223 }, 
  { 0x33ed, 3, 4226 }, 
  { 0x33ee, 3, 4229 }, 
  { 0x33ef, 3, 4232 }, 
  { 0x33f0, 3, 4235 }, 
  { 0x33f1, 3, 4238 }, 
  { 0x33f2, 3, 4241 }, 
  { 0x33f3, 3, 4244 }, 
  { 0x33f4, 3, 4247 }, 
  { 0x33f5, 3, 4250 }, 
  { 0x33f6, 3, 4253 }, 
  { 0x33f7, 3, 4256 }, 
  { 0x33f8, 3, 4259 }, 
  { 0x33f9, 3, 4262 }, 
  { 0x33fa, 3, 4265 }, 
  { 0x33fb, 3, 4268 }, 
  { 0x33fc, 3, 4271 }, 
  { 0x33fd, 3, 4274 }, 
  { 0x33fe, 3, 4277 }, 
  { 0xf900, 1, 4280 }, 
  { 0xf901, 1, 4281 }, 
  { 0xf902, 1, 2840 }, 
  { 0xf903, 1, 4282 }, 
  { 0xf904, 1, 4283 }, 
  { 0xf905, 1, 4284 }, 
  { 0xf906, 1, 4285 }, 
  { 0xf907, 1, 2894 }, 
  { 0xf908, 1, 2894 }, 
  { 0xf909, 1, 4286 }, 
  { 0xf90a, 1, 2848 }, 
  { 0xf90b, 1, 4287 }, 
  { 0xf90c, 1, 4288 }, 
  { 0xf90d, 1, 4289 }, 
  { 0xf90e, 1, 4290 }, 
  { 0xf90f, 1, 4291 }, 
  { 0xf910, 1, 4292 }, 
  { 0xf911, 1, 4293 }, 
  { 0xf912, 1, 4294 }, 
  { 0xf913, 1, 4295 }, 
  { 0xf914, 1, 4296 }, 
  { 0xf915, 1, 4297 }, 
  { 0xf916, 1, 4298 }, 
  { 0xf917, 1, 4299 }, 
  { 0xf918, 1, 4300 }, 
  { 0xf919, 1, 4301 }, 
  { 0xf91a, 1, 4302 }, 
  { 0xf91b, 1, 4303 }, 
  { 0xf91c, 1, 4304 }, 
  { 0xf91d, 1, 4305 }, 
  { 0xf91e, 1, 4306 }, 
  { 0xf91f, 1, 4307 }, 
  { 0xf920, 1, 4308 }, 
  { 0xf921, 1, 4309 }, 
  { 0xf922, 1, 4310 }, 
  { 0xf923, 1, 4311 }, 
  { 0xf924, 1, 4312 }, 
  { 0xf925, 1, 4313 }, 
  { 0xf926, 1, 4314 }, 
  { 0xf927, 1, 4315 }, 
  { 0xf928, 1, 4316 }, 
  { 0xf929, 1, 4317 }, 
  { 0xf92a, 1, 4318 }, 
  { 0xf92b, 1, 4319 }, 
  { 0xf92c, 1, 4320 }, 
  { 0xf92d, 1, 4321 }, 
  { 0xf92e, 1, 4322 }, 
  { 0xf92f, 1, 4323 }, 
  { 0xf930, 1, 4324 }, 
  { 0xf931, 1, 4325 }, 
  { 0xf932, 1, 4326 }, 
  { 0xf933, 1, 4327 }, 
  { 0xf934, 1, 2806 }, 
  { 0xf935, 1, 4328 }, 
  { 0xf936, 1, 4329 }, 
  { 0xf937, 1, 4330 }, 
  { 0xf938, 1, 4331 }, 
  { 0xf939, 1, 4332 }, 
  { 0xf93a, 1, 4333 }, 
  { 0xf93b, 1, 4334 }, 
  { 0xf93c, 1, 4335 }, 
  { 0xf93d, 1, 4336 }, 
  { 0xf93e, 1, 4337 }, 
  { 0xf93f, 1, 4338 }, 
  { 0xf940, 1, 2879 }, 
  { 0xf941, 1, 4339 }, 
  { 0xf942, 1, 4340 }, 
  { 0xf943, 1, 4341 }, 
  { 0xf944, 1, 4342 }, 
  { 0xf945, 1, 4343 }, 
  { 0xf946, 1, 4344 }, 
  { 0xf947, 1, 4345 }, 
  { 0xf948, 1, 4346 }, 
  { 0xf949, 1, 4347 }, 
  { 0xf94a, 1, 4348 }, 
  { 0xf94b, 1, 4349 }, 
  { 0xf94c, 1, 4350 }, 
  { 0xf94d, 1, 4351 }, 
  { 0xf94e, 1, 4352 }, 
  { 0xf94f, 1, 4353 }, 
  { 0xf950, 1, 4354 }, 
  { 0xf951, 1, 4355 }, 
  { 0xf952, 1, 4356 }, 
  { 0xf953, 1, 4357 }, 
  { 0xf954, 1, 4358 }, 
  { 0xf955, 1, 4359 }, 
  { 0xf956, 1, 4360 }, 
  { 0xf957, 1, 4361 }, 
  { 0xf958, 1, 4362 }, 
  { 0xf959, 1, 4363 }, 
  { 0xf95a, 1, 4364 }, 
  { 0xf95b, 1, 4365 }, 
  { 0xf95c, 1, 4296 }, 
  { 0xf95d, 1, 4366 }, 
  { 0xf95e, 1, 4367 }, 
  { 0xf95f, 1, 4368 }, 
  { 0xf960, 1, 4369 }, 
  { 0xf961, 1, 4370 }, 
  { 0xf962, 1, 4371 }, 
  { 0xf963, 1, 4372 }, 
  { 0xf964, 1, 4373 }, 
  { 0xf965, 1, 4374 }, 
  { 0xf966, 1, 4375 }, 
  { 0xf967, 1, 4376 }, 
  { 0xf968, 1, 4377 }, 
  { 0xf969, 1, 4378 }, 
  { 0xf96a, 1, 4379 }, 
  { 0xf96b, 1, 4380 }, 
  { 0xf96c, 1, 4381 }, 
  { 0xf96d, 1, 4382 }, 
  { 0xf96e, 1, 4383 }, 
  { 0xf96f, 1, 4384 }, 
  { 0xf970, 1, 4385 }, 
  { 0xf971, 1, 2842 }, 
  { 0xf972, 1, 4386 }, 
  { 0xf973, 1, 4387 }, 
  { 0xf974, 1, 4388 }, 
  { 0xf975, 1, 4389 }, 
  { 0xf976, 1, 4390 }, 
  { 0xf977, 1, 4391 }, 
  { 0xf978, 1, 4392 }, 
  { 0xf979, 1, 4393 }, 
  { 0xf97a, 1, 4394 }, 
  { 0xf97b, 1, 4395 }, 
  { 0xf97c, 1, 4396 }, 
  { 0xf97d, 1, 4397 }, 
  { 0xf97e, 1, 4398 }, 
  { 0xf97f, 1, 4399 }, 
  { 0xf980, 1, 4400 }, 
  { 0xf981, 1, 2719 }, 
  { 0xf982, 1, 4401 }, 
  { 0xf983, 1, 4402 }, 
  { 0xf984, 1, 4403 }, 
  { 0xf985, 1, 4404 }, 
  { 0xf986, 1, 4405 }, 
  { 0xf987, 1, 4406 }, 
  { 0xf988, 1, 4407 }, 
  { 0xf989, 1, 4408 }, 
  { 0xf98a, 1, 2700 }, 
  { 0xf98b, 1, 4409 }, 
  { 0xf98c, 1, 4410 }, 
  { 0xf98d, 1, 4411 }, 
  { 0xf98e, 1, 4412 }, 
  { 0xf98f, 1, 4413 }, 
  { 0xf990, 1, 4414 }, 
  { 0xf991, 1, 4415 }, 
  { 0xf992, 1, 4416 }, 
  { 0xf993, 1, 4417 }, 
  { 0xf994, 1, 4418 }, 
  { 0xf995, 1, 4419 }, 
  { 0xf996, 1, 4420 }, 
  { 0xf997, 1, 4421 }, 
  { 0xf998, 1, 4422 }, 
  { 0xf999, 1, 4423 }, 
  { 0xf99a, 1, 4424 }, 
  { 0xf99b, 1, 4425 }, 
  { 0xf99c, 1, 4426 }, 
  { 0xf99d, 1, 4427 }, 
  { 0xf99e, 1, 4428 }, 
  { 0xf99f, 1, 4429 }, 
  { 0xf9a0, 1, 4430 }, 
  { 0xf9a1, 1, 4384 }, 
  { 0xf9a2, 1, 4431 }, 
  { 0xf9a3, 1, 4432 }, 
  { 0xf9a4, 1, 4433 }, 
  { 0xf9a5, 1, 4434 }, 
  { 0xf9a6, 1, 4435 }, 
  { 0xf9a7, 1, 4436 }, 
  { 0xf9a8, 1, 4437 }, 
  { 0xf9a9, 1, 4438 }, 
  { 0xf9aa, 1, 4368 }, 
  { 0xf9ab, 1, 4439 }, 
  { 0xf9ac, 1, 4440 }, 
  { 0xf9ad, 1, 4441 }, 
  { 0xf9ae, 1, 4442 }, 
  { 0xf9af, 1, 4443 }, 
  { 0xf9b0, 1, 4444 }, 
  { 0xf9b1, 1, 4445 }, 
  { 0xf9b2, 1, 4446 }, 
  { 0xf9b3, 1, 4447 }, 
  { 0xf9b4, 1, 4448 }, 
  { 0xf9b5, 1, 4449 }, 
  { 0xf9b6, 1, 4450 }, 
  { 0xf9b7, 1, 4451 }, 
  { 0xf9b8, 1, 4452 }, 
  { 0xf9b9, 1, 4453 }, 
  { 0xf9ba, 1, 4454 }, 
  { 0xf9bb, 1, 4455 }, 
  { 0xf9bc, 1, 4456 }, 
  { 0xf9bd, 1, 4457 }, 
  { 0xf9be, 1, 4458 }, 
  { 0xf9bf, 1, 4296 }, 
  { 0xf9c0, 1, 4459 }, 
  { 0xf9c1, 1, 4460 }, 
  { 0xf9c2, 1, 4461 }, 
  { 0xf9c3, 1, 4462 }, 
  { 0xf9c4, 1, 2893 }, 
  { 0xf9c5, 1, 4463 }, 
  { 0xf9c6, 1, 4464 }, 
  { 0xf9c7, 1, 4465 }, 
  { 0xf9c8, 1, 4466 }, 
  { 0xf9c9, 1, 4467 }, 
  { 0xf9ca, 1, 4468 }, 
  { 0xf9cb, 1, 4469 }, 
  { 0xf9cc, 1, 4470 }, 
  { 0xf9cd, 1, 4471 }, 
  { 0xf9ce, 1, 4472 }, 
  { 0xf9cf, 1, 4473 }, 
  { 0xf9d0, 1, 4474 }, 
  { 0xf9d1, 1, 3396 }, 
  { 0xf9d2, 1, 4475 }, 
  { 0xf9d3, 1, 4476 }, 
  { 0xf9d4, 1, 4477 }, 
  { 0xf9d5, 1, 4478 }, 
  { 0xf9d6, 1, 4479 }, 
  { 0xf9d7, 1, 4480 }, 
  { 0xf9d8, 1, 4481 }, 
  { 0xf9d9, 1, 4482 }, 
  { 0xf9da, 1, 4483 }, 
  { 0xf9db, 1, 4370 }, 
  { 0xf9dc, 1, 4484 }, 
  { 0xf9dd, 1, 4485 }, 
  { 0xf9de, 1, 4486 }, 
  { 0xf9df, 1, 4487 }, 
  { 0xf9e0, 1, 4488 }, 
  { 0xf9e1, 1, 4489 }, 
  { 0xf9e2, 1, 4490 }, 
  { 0xf9e3, 1, 4491 }, 
  { 0xf9e4, 1, 4492 }, 
  { 0xf9e5, 1, 4493 }, 
  { 0xf9e6, 1, 4494 }, 
  { 0xf9e7, 1, 4495 }, 
  { 0xf9e8, 1, 4496 }, 
  { 0xf9e9, 1, 2847 }, 
  { 0xf9ea, 1, 4497 }, 
  { 0xf9eb, 1, 4498 }, 
  { 0xf9ec, 1, 4499 }, 
  { 0xf9ed, 1, 4500 }, 
  { 0xf9ee, 1, 4501 }, 
  { 0xf9ef, 1, 4502 }, 
  { 0xf9f0, 1, 4503 }, 
  { 0xf9f1, 1, 4504 }, 
  { 0xf9f2, 1, 4505 }, 
  { 0xf9f3, 1, 4506 }, 
  { 0xf9f4, 1, 4507 }, 
  { 0xf9f5, 1, 4508 }, 
  { 0xf9f6, 1, 4509 }, 
  { 0xf9f7, 1, 2798 }, 
  { 0xf9f8, 1, 4510 }, 
  { 0xf9f9, 1, 4511 }, 
  { 0xf9fa, 1, 4512 }, 
  { 0xf9fb, 1, 4513 }, 
  { 0xf9fc, 1, 4514 }, 
  { 0xf9fd, 1, 4515 }, 
  { 0xf9fe, 1, 4516 }, 
  { 0xf9ff, 1, 4517 }, 
  { 0xfa00, 1, 4518 }, 
  { 0xfa01, 1, 4519 }, 
  { 0xfa02, 1, 4520 }, 
  { 0xfa03, 1, 4521 }, 
  { 0xfa04, 1, 4522 }, 
  { 0xfa05, 1, 4523 }, 
  { 0xfa06, 1, 4524 }, 
  { 0xfa07, 1, 4525 }, 
  { 0xfa08, 1, 2825 }, 
  { 0xfa09, 1, 4526 }, 
  { 0xfa0a, 1, 2828 }, 
  { 0xfa0b, 1, 4527 }, 
  { 0xfa0c, 1, 4528 }, 
  { 0xfa0d, 1, 4529 }, 
  { 0xfa10, 1, 4530 }, 
  { 0xfa12, 1, 4531 }, 
  { 0xfa15, 1, 4532 }, 
  { 0xfa16, 1, 4533 }, 
  { 0xfa17, 1, 4534 }, 
  { 0xfa18, 1, 4535 }, 
  { 0xfa19, 1, 4536 }, 
  { 0xfa1a, 1, 4537 }, 
  { 0xfa1b, 1, 4538 }, 
  { 0xfa1c, 1, 4539 }, 
  { 0xfa1d, 1, 4540 }, 
  { 0xfa1e, 1, 2805 }, 
  { 0xfa20, 1, 4541 }, 
  { 0xfa22, 1, 4542 }, 
  { 0xfa25, 1, 4543 }, 
  { 0xfa26, 1, 4544 }, 
  { 0xfa2a, 1, 4545 }, 
  { 0xfa2b, 1, 4546 }, 
  { 0xfa2c, 1, 4547 }, 
  { 0xfa2d, 1, 4548 }, 
  { 0xfa30, 1, 4549 }, 
  { 0xfa31, 1, 4550 }, 
  { 0xfa32, 1, 4551 }, 
  { 0xfa33, 1, 4552 }, 
  { 0xfa34, 1, 4553 }, 
  { 0xfa35, 1, 4554 }, 
  { 0xfa36, 1, 4555 }, 
  { 0xfa37, 1, 4556 }, 
  { 0xfa38, 1, 4557 }, 
  { 0xfa39, 1, 4558 }, 
  { 0xfa3a, 1, 4559 }, 
  { 0xfa3b, 1, 4560 }, 
  { 0xfa3c, 1, 2726 }, 
  { 0xfa3d, 1, 4561 }, 
  { 0xfa3e, 1, 4562 }, 
  { 0xfa3f, 1, 4563 }, 
  { 0xfa40, 1, 4564 }, 
  { 0xfa41, 1, 4565 }, 
  { 0xfa42, 1, 4566 }, 
  { 0xfa43, 1, 4567 }, 
  { 0xfa44, 1, 4568 }, 
  { 0xfa45, 1, 4569 }, 
  { 0xfa46, 1, 4570 }, 
  { 0xfa47, 1, 4571 }, 
  { 0xfa48, 1, 4572 }, 
  { 0xfa49, 1, 4573 }, 
  { 0xfa4a, 1, 4574 }, 
  { 0xfa4b, 1, 4575 }, 
  { 0xfa4c, 1, 3401 }, 
  { 0xfa4d, 1, 4576 }, 
  { 0xfa4e, 1, 4577 }, 
  { 0xfa4f, 1, 4578 }, 
  { 0xfa50, 1, 4579 }, 
  { 0xfa51, 1, 3405 }, 
  { 0xfa52, 1, 4580 }, 
  { 0xfa53, 1, 4581 }, 
  { 0xfa54, 1, 4582 }, 
  { 0xfa55, 1, 4583 }, 
  { 0xfa56, 1, 4584 }, 
  { 0xfa57, 1, 4420 }, 
  { 0xfa58, 1, 4585 }, 
  { 0xfa59, 1, 4586 }, 
  { 0xfa5a, 1, 4587 }, 
  { 0xfa5b, 1, 4588 }, 
  { 0xfa5c, 1, 4589 }, 
  { 0xfa5d, 1, 4590 }, 
  { 0xfa5e, 1, 4590 }, 
  { 0xfa5f, 1, 4591 }, 
  { 0xfa60, 1, 4592 }, 
  { 0xfa61, 1, 4593 }, 
  { 0xfa62, 1, 4594 }, 
  { 0xfa63, 1, 4595 }, 
  { 0xfa64, 1, 4596 }, 
  { 0xfa65, 1, 4597 }, 
  { 0xfa66, 1, 4598 }, 
  { 0xfa67, 1, 4543 }, 
  { 0xfa68, 1, 4599 }, 
  { 0xfa69, 1, 4600 }, 
  { 0xfa6a, 1, 4601 }, 
  { 0xfb00, 2, 4602 }, 
  { 0xfb01, 2, 4604 }, 
  { 0xfb02, 2, 4606 }, 
  { 0xfb03, 3, 4608 }, 
  { 0xfb04, 3, 4611 }, 
  { 0xfb05, 2, 4614 }, 
  { 0xfb06, 2, 4614 }, 
  { 0xfb13, 2, 4616 }, 
  { 0xfb14, 2, 4618 }, 
  { 0xfb15, 2, 4620 }, 
  { 0xfb16, 2, 4622 }, 
  { 0xfb17, 2, 4624 }, 
  { 0xfb1d, 2, 4626 }, 
  { 0xfb1f, 2, 4628 }, 
  { 0xfb20, 1, 4630 }, 
  { 0xfb21, 1, 2228 }, 
  { 0xfb22, 1, 2231 }, 
  { 0xfb23, 1, 4631 }, 
  { 0xfb24, 1, 4632 }, 
  { 0xfb25, 1, 4633 }, 
  { 0xfb26, 1, 4634 }, 
  { 0xfb27, 1, 4635 }, 
  { 0xfb28, 1, 4636 }, 
  { 0xfb29, 1, 2176 }, 
  { 0xfb2a, 2, 4637 }, 
  { 0xfb2b, 2, 4639 }, 
  { 0xfb2c, 3, 4641 }, 
  { 0xfb2d, 3, 4644 }, 
  { 0xfb2e, 2, 4647 }, 
  { 0xfb2f, 2, 4649 }, 
  { 0xfb30, 2, 4651 }, 
  { 0xfb31, 2, 4653 }, 
  { 0xfb32, 2, 4655 }, 
  { 0xfb33, 2, 4657 }, 
  { 0xfb34, 2, 4659 }, 
  { 0xfb35, 2, 4661 }, 
  { 0xfb36, 2, 4663 }, 
  { 0xfb38, 2, 4665 }, 
  { 0xfb39, 2, 4667 }, 
  { 0xfb3a, 2, 4669 }, 
  { 0xfb3b, 2, 4671 }, 
  { 0xfb3c, 2, 4673 }, 
  { 0xfb3e, 2, 4675 }, 
  { 0xfb40, 2, 4677 }, 
  { 0xfb41, 2, 4679 }, 
  { 0xfb43, 2, 4681 }, 
  { 0xfb44, 2, 4683 }, 
  { 0xfb46, 2, 4685 }, 
  { 0xfb47, 2, 4687 }, 
  { 0xfb48, 2, 4689 }, 
  { 0xfb49, 2, 4691 }, 
  { 0xfb4a, 2, 4693 }, 
  { 0xfb4b, 2, 4695 }, 
  { 0xfb4c, 2, 4697 }, 
  { 0xfb4d, 2, 4699 }, 
  { 0xfb4e, 2, 4701 }, 
  { 0xfb4f, 2, 4703 }, 
  { 0xfb50, 1, 4705 }, 
  { 0xfb51, 1, 4705 }, 
  { 0xfb52, 1, 4706 }, 
  { 0xfb53, 1, 4706 }, 
  { 0xfb54, 1, 4706 }, 
  { 0xfb55, 1, 4706 }, 
  { 0xfb56, 1, 4707 }, 
  { 0xfb57, 1, 4707 }, 
  { 0xfb58, 1, 4707 }, 
  { 0xfb59, 1, 4707 }, 
  { 0xfb5a, 1, 4708 }, 
  { 0xfb5b, 1, 4708 }, 
  { 0xfb5c, 1, 4708 }, 
  { 0xfb5d, 1, 4708 }, 
  { 0xfb5e, 1, 4709 }, 
  { 0xfb5f, 1, 4709 }, 
  { 0xfb60, 1, 4709 }, 
  { 0xfb61, 1, 4709 }, 
  { 0xfb62, 1, 4710 }, 
  { 0xfb63, 1, 4710 }, 
  { 0xfb64, 1, 4710 }, 
  { 0xfb65, 1, 4710 }, 
  { 0xfb66, 1, 4711 }, 
  { 0xfb67, 1, 4711 }, 
  { 0xfb68, 1, 4711 }, 
  { 0xfb69, 1, 4711 }, 
  { 0xfb6a, 1, 4712 }, 
  { 0xfb6b, 1, 4712 }, 
  { 0xfb6c, 1, 4712 }, 
  { 0xfb6d, 1, 4712 }, 
  { 0xfb6e, 1, 4713 }, 
  { 0xfb6f, 1, 4713 }, 
  { 0xfb70, 1, 4713 }, 
  { 0xfb71, 1, 4713 }, 
  { 0xfb72, 1, 4714 }, 
  { 0xfb73, 1, 4714 }, 
  { 0xfb74, 1, 4714 }, 
  { 0xfb75, 1, 4714 }, 
  { 0xfb76, 1, 4715 }, 
  { 0xfb77, 1, 4715 }, 
  { 0xfb78, 1, 4715 }, 
  { 0xfb79, 1, 4715 }, 
  { 0xfb7a, 1, 4716 }, 
  { 0xfb7b, 1, 4716 }, 
  { 0xfb7c, 1, 4716 }, 
  { 0xfb7d, 1, 4716 }, 
  { 0xfb7e, 1, 4717 }, 
  { 0xfb7f, 1, 4717 }, 
  { 0xfb80, 1, 4717 }, 
  { 0xfb81, 1, 4717 }, 
  { 0xfb82, 1, 4718 }, 
  { 0xfb83, 1, 4718 }, 
  { 0xfb84, 1, 4719 }, 
  { 0xfb85, 1, 4719 }, 
  { 0xfb86, 1, 4720 }, 
  { 0xfb87, 1, 4720 }, 
  { 0xfb88, 1, 4721 }, 
  { 0xfb89, 1, 4721 }, 
  { 0xfb8a, 1, 4722 }, 
  { 0xfb8b, 1, 4722 }, 
  { 0xfb8c, 1, 4723 }, 
  { 0xfb8d, 1, 4723 }, 
  { 0xfb8e, 1, 4724 }, 
  { 0xfb8f, 1, 4724 }, 
  { 0xfb90, 1, 4724 }, 
  { 0xfb91, 1, 4724 }, 
  { 0xfb92, 1, 4725 }, 
  { 0xfb93, 1, 4725 }, 
  { 0xfb94, 1, 4725 }, 
  { 0xfb95, 1, 4725 }, 
  { 0xfb96, 1, 4726 }, 
  { 0xfb97, 1, 4726 }, 
  { 0xfb98, 1, 4726 }, 
  { 0xfb99, 1, 4726 }, 
  { 0xfb9a, 1, 4727 }, 
  { 0xfb9b, 1, 4727 }, 
  { 0xfb9c, 1, 4727 }, 
  { 0xfb9d, 1, 4727 }, 
  { 0xfb9e, 1, 4728 }, 
  { 0xfb9f, 1, 4728 }, 
  { 0xfba0, 1, 4729 }, 
  { 0xfba1, 1, 4729 }, 
  { 0xfba2, 1, 4729 }, 
  { 0xfba3, 1, 4729 }, 
  { 0xfba4, 2, 802 }, 
  { 0xfba5, 2, 802 }, 
  { 0xfba6, 1, 4730 }, 
  { 0xfba7, 1, 4730 }, 
  { 0xfba8, 1, 4730 }, 
  { 0xfba9, 1, 4730 }, 
  { 0xfbaa, 1, 4731 }, 
  { 0xfbab, 1, 4731 }, 
  { 0xfbac, 1, 4731 }, 
  { 0xfbad, 1, 4731 }, 
  { 0xfbae, 1, 4732 }, 
  { 0xfbaf, 1, 4732 }, 
  { 0xfbb0, 2, 806 }, 
  { 0xfbb1, 2, 806 }, 
  { 0xfbd3, 1, 4733 }, 
  { 0xfbd4, 1, 4733 }, 
  { 0xfbd5, 1, 4733 }, 
  { 0xfbd6, 1, 4733 }, 
  { 0xfbd7, 1, 4734 }, 
  { 0xfbd8, 1, 4734 }, 
  { 0xfbd9, 1, 4735 }, 
  { 0xfbda, 1, 4735 }, 
  { 0xfbdb, 1, 4736 }, 
  { 0xfbdc, 1, 4736 }, 
  { 0xfbdd, 2, 798 }, 
  { 0xfbde, 1, 4737 }, 
  { 0xfbdf, 1, 4737 }, 
  { 0xfbe0, 1, 4738 }, 
  { 0xfbe1, 1, 4738 }, 
  { 0xfbe2, 1, 4739 }, 
  { 0xfbe3, 1, 4739 }, 
  { 0xfbe4, 1, 4740 }, 
  { 0xfbe5, 1, 4740 }, 
  { 0xfbe6, 1, 4740 }, 
  { 0xfbe7, 1, 4740 }, 
  { 0xfbe8, 1, 4741 }, 
  { 0xfbe9, 1, 4741 }, 
  { 0xfbea, 3, 4742 }, 
  { 0xfbeb, 3, 4742 }, 
  { 0xfbec, 3, 4745 }, 
  { 0xfbed, 3, 4745 }, 
  { 0xfbee, 3, 4748 }, 
  { 0xfbef, 3, 4748 }, 
  { 0xfbf0, 3, 4751 }, 
  { 0xfbf1, 3, 4751 }, 
  { 0xfbf2, 3, 4754 }, 
  { 0xfbf3, 3, 4754 }, 
  { 0xfbf4, 3, 4757 }, 
  { 0xfbf5, 3, 4757 }, 
  { 0xfbf6, 3, 4760 }, 
  { 0xfbf7, 3, 4760 }, 
  { 0xfbf8, 3, 4760 }, 
  { 0xfbf9, 3, 4763 }, 
  { 0xfbfa, 3, 4763 }, 
  { 0xfbfb, 3, 4763 }, 
  { 0xfbfc, 1, 4766 }, 
  { 0xfbfd, 1, 4766 }, 
  { 0xfbfe, 1, 4766 }, 
  { 0xfbff, 1, 4766 }, 
  { 0xfc00, 3, 4767 }, 
  { 0xfc01, 3, 4770 }, 
  { 0xfc02, 3, 4773 }, 
  { 0xfc03, 3, 4763 }, 
  { 0xfc04, 3, 4776 }, 
  { 0xfc05, 2, 4779 }, 
  { 0xfc06, 2, 4781 }, 
  { 0xfc07, 2, 4783 }, 
  { 0xfc08, 2, 4785 }, 
  { 0xfc09, 2, 4787 }, 
  { 0xfc0a, 2, 4789 }, 
  { 0xfc0b, 2, 4791 }, 
  { 0xfc0c, 2, 4793 }, 
  { 0xfc0d, 2, 4795 }, 
  { 0xfc0e, 2, 4797 }, 
  { 0xfc0f, 2, 4799 }, 
  { 0xfc10, 2, 4801 }, 
  { 0xfc11, 2, 4803 }, 
  { 0xfc12, 2, 4805 }, 
  { 0xfc13, 2, 4807 }, 
  { 0xfc14, 2, 4809 }, 
  { 0xfc15, 2, 4811 }, 
  { 0xfc16, 2, 4813 }, 
  { 0xfc17, 2, 4815 }, 
  { 0xfc18, 2, 4817 }, 
  { 0xfc19, 2, 4819 }, 
  { 0xfc1a, 2, 4821 }, 
  { 0xfc1b, 2, 4823 }, 
  { 0xfc1c, 2, 4825 }, 
  { 0xfc1d, 2, 4827 }, 
  { 0xfc1e, 2, 4829 }, 
  { 0xfc1f, 2, 4831 }, 
  { 0xfc20, 2, 4833 }, 
  { 0xfc21, 2, 4835 }, 
  { 0xfc22, 2, 4837 }, 
  { 0xfc23, 2, 4839 }, 
  { 0xfc24, 2, 4841 }, 
  { 0xfc25, 2, 4843 }, 
  { 0xfc26, 2, 4845 }, 
  { 0xfc27, 2, 4847 }, 
  { 0xfc28, 2, 4849 }, 
  { 0xfc29, 2, 4851 }, 
  { 0xfc2a, 2, 4853 }, 
  { 0xfc2b, 2, 4855 }, 
  { 0xfc2c, 2, 4857 }, 
  { 0xfc2d, 2, 4859 }, 
  { 0xfc2e, 2, 4861 }, 
  { 0xfc2f, 2, 4863 }, 
  { 0xfc30, 2, 4865 }, 
  { 0xfc31, 2, 4867 }, 
  { 0xfc32, 2, 4869 }, 
  { 0xfc33, 2, 4871 }, 
  { 0xfc34, 2, 4873 }, 
  { 0xfc35, 2, 4875 }, 
  { 0xfc36, 2, 4877 }, 
  { 0xfc37, 2, 4879 }, 
  { 0xfc38, 2, 4881 }, 
  { 0xfc39, 2, 4883 }, 
  { 0xfc3a, 2, 4885 }, 
  { 0xfc3b, 2, 4887 }, 
  { 0xfc3c, 2, 4889 }, 
  { 0xfc3d, 2, 4891 }, 
  { 0xfc3e, 2, 4893 }, 
  { 0xfc3f, 2, 4895 }, 
  { 0xfc40, 2, 4897 }, 
  { 0xfc41, 2, 4899 }, 
  { 0xfc42, 2, 4901 }, 
  { 0xfc43, 2, 4903 }, 
  { 0xfc44, 2, 4905 }, 
  { 0xfc45, 2, 4907 }, 
  { 0xfc46, 2, 4909 }, 
  { 0xfc47, 2, 4911 }, 
  { 0xfc48, 2, 4913 }, 
  { 0xfc49, 2, 4915 }, 
  { 0xfc4a, 2, 4917 }, 
  { 0xfc4b, 2, 4919 }, 
  { 0xfc4c, 2, 4921 }, 
  { 0xfc4d, 2, 4923 }, 
  { 0xfc4e, 2, 4925 }, 
  { 0xfc4f, 2, 4927 }, 
  { 0xfc50, 2, 4929 }, 
  { 0xfc51, 2, 4931 }, 
  { 0xfc52, 2, 4933 }, 
  { 0xfc53, 2, 4935 }, 
  { 0xfc54, 2, 4937 }, 
  { 0xfc55, 2, 4939 }, 
  { 0xfc56, 2, 4941 }, 
  { 0xfc57, 2, 4943 }, 
  { 0xfc58, 2, 4945 }, 
  { 0xfc59, 2, 4947 }, 
  { 0xfc5a, 2, 4949 }, 
  { 0xfc5b, 2, 4951 }, 
  { 0xfc5c, 2, 4953 }, 
  { 0xfc5d, 2, 4955 }, 
  { 0xfc5e, 3, 4957 }, 
  { 0xfc5f, 3, 4960 }, 
  { 0xfc60, 3, 4963 }, 
  { 0xfc61, 3, 4966 }, 
  { 0xfc62, 3, 4969 }, 
  { 0xfc63, 3, 4972 }, 
  { 0xfc64, 3, 4975 }, 
  { 0xfc65, 3, 4978 }, 
  { 0xfc66, 3, 4773 }, 
  { 0xfc67, 3, 4981 }, 
  { 0xfc68, 3, 4763 }, 
  { 0xfc69, 3, 4776 }, 
  { 0xfc6a, 2, 4984 }, 
  { 0xfc6b, 2, 4986 }, 
  { 0xfc6c, 2, 4785 }, 
  { 0xfc6d, 2, 4988 }, 
  { 0xfc6e, 2, 4787 }, 
  { 0xfc6f, 2, 4789 }, 
  { 0xfc70, 2, 4990 }, 
  { 0xfc71, 2, 4992 }, 
  { 0xfc72, 2, 4797 }, 
  { 0xfc73, 2, 4994 }, 
  { 0xfc74, 2, 4799 }, 
  { 0xfc75, 2, 4801 }, 
  { 0xfc76, 2, 4996 }, 
  { 0xfc77, 2, 4998 }, 
  { 0xfc78, 2, 4805 }, 
  { 0xfc79, 2, 5000 }, 
  { 0xfc7a, 2, 4807 }, 
  { 0xfc7b, 2, 4809 }, 
  { 0xfc7c, 2, 4867 }, 
  { 0xfc7d, 2, 4869 }, 
  { 0xfc7e, 2, 4875 }, 
  { 0xfc7f, 2, 4877 }, 
  { 0xfc80, 2, 4879 }, 
  { 0xfc81, 2, 4887 }, 
  { 0xfc82, 2, 4889 }, 
  { 0xfc83, 2, 4891 }, 
  { 0xfc84, 2, 4893 }, 
  { 0xfc85, 2, 4901 }, 
  { 0xfc86, 2, 4903 }, 
  { 0xfc87, 2, 4905 }, 
  { 0xfc88, 2, 5002 }, 
  { 0xfc89, 2, 4913 }, 
  { 0xfc8a, 2, 5004 }, 
  { 0xfc8b, 2, 5006 }, 
  { 0xfc8c, 2, 4925 }, 
  { 0xfc8d, 2, 5008 }, 
  { 0xfc8e, 2, 4927 }, 
  { 0xfc8f, 2, 4929 }, 
  { 0xfc90, 2, 4955 }, 
  { 0xfc91, 2, 5010 }, 
  { 0xfc92, 2, 5012 }, 
  { 0xfc93, 2, 4945 }, 
  { 0xfc94, 2, 5014 }, 
  { 0xfc95, 2, 4947 }, 
  { 0xfc96, 2, 4949 }, 
  { 0xfc97, 3, 4767 }, 
  { 0xfc98, 3, 4770 }, 
  { 0xfc99, 3, 5016 }, 
  { 0xfc9a, 3, 4773 }, 
  { 0xfc9b, 3, 5019 }, 
  { 0xfc9c, 2, 4779 }, 
  { 0xfc9d, 2, 4781 }, 
  { 0xfc9e, 2, 4783 }, 
  { 0xfc9f, 2, 4785 }, 
  { 0xfca0, 2, 5022 }, 
  { 0xfca1, 2, 4791 }, 
  { 0xfca2, 2, 4793 }, 
  { 0xfca3, 2, 4795 }, 
  { 0xfca4, 2, 4797 }, 
  { 0xfca5, 2, 5024 }, 
  { 0xfca6, 2, 4805 }, 
  { 0xfca7, 2, 4811 }, 
  { 0xfca8, 2, 4813 }, 
  { 0xfca9, 2, 4815 }, 
  { 0xfcaa, 2, 4817 }, 
  { 0xfcab, 2, 4819 }, 
  { 0xfcac, 2, 4823 }, 
  { 0xfcad, 2, 4825 }, 
  { 0xfcae, 2, 4827 }, 
  { 0xfcaf, 2, 4829 }, 
  { 0xfcb0, 2, 4831 }, 
  { 0xfcb1, 2, 4833 }, 
  { 0xfcb2, 2, 5026 }, 
  { 0xfcb3, 2, 4835 }, 
  { 0xfcb4, 2, 4837 }, 
  { 0xfcb5, 2, 4839 }, 
  { 0xfcb6, 2, 4841 }, 
  { 0xfcb7, 2, 4843 }, 
  { 0xfcb8, 2, 4845 }, 
  { 0xfcb9, 2, 4849 }, 
  { 0xfcba, 2, 4851 }, 
  { 0xfcbb, 2, 4853 }, 
  { 0xfcbc, 2, 4855 }, 
  { 0xfcbd, 2, 4857 }, 
  { 0xfcbe, 2, 4859 }, 
  { 0xfcbf, 2, 4861 }, 
  { 0xfcc0, 2, 4863 }, 
  { 0xfcc1, 2, 4865 }, 
  { 0xfcc2, 2, 4871 }, 
  { 0xfcc3, 2, 4873 }, 
  { 0xfcc4, 2, 4881 }, 
  { 0xfcc5, 2, 4883 }, 
  { 0xfcc6, 2, 4885 }, 
  { 0xfcc7, 2, 4887 }, 
  { 0xfcc8, 2, 4889 }, 
  { 0xfcc9, 2, 4895 }, 
  { 0xfcca, 2, 4897 }, 
  { 0xfccb, 2, 4899 }, 
  { 0xfccc, 2, 4901 }, 
  { 0xfccd, 2, 5028 }, 
  { 0xfcce, 2, 4907 }, 
  { 0xfccf, 2, 4909 }, 
  { 0xfcd0, 2, 4911 }, 
  { 0xfcd1, 2, 4913 }, 
  { 0xfcd2, 2, 4919 }, 
  { 0xfcd3, 2, 4921 }, 
  { 0xfcd4, 2, 4923 }, 
  { 0xfcd5, 2, 4925 }, 
  { 0xfcd6, 2, 5030 }, 
  { 0xfcd7, 2, 4931 }, 
  { 0xfcd8, 2, 4933 }, 
  { 0xfcd9, 2, 5032 }, 
  { 0xfcda, 2, 4939 }, 
  { 0xfcdb, 2, 4941 }, 
  { 0xfcdc, 2, 4943 }, 
  { 0xfcdd, 2, 4945 }, 
  { 0xfcde, 2, 5034 }, 
  { 0xfcdf, 3, 4773 }, 
  { 0xfce0, 3, 5019 }, 
  { 0xfce1, 2, 4785 }, 
  { 0xfce2, 2, 5022 }, 
  { 0xfce3, 2, 4797 }, 
  { 0xfce4, 2, 5024 }, 
  { 0xfce5, 2, 4805 }, 
  { 0xfce6, 2, 5036 }, 
  { 0xfce7, 2, 4831 }, 
  { 0xfce8, 2, 5038 }, 
  { 0xfce9, 2, 5040 }, 
  { 0xfcea, 2, 5042 }, 
  { 0xfceb, 2, 4887 }, 
  { 0xfcec, 2, 4889 }, 
  { 0xfced, 2, 4901 }, 
  { 0xfcee, 2, 4925 }, 
  { 0xfcef, 2, 5030 }, 
  { 0xfcf0, 2, 4945 }, 
  { 0xfcf1, 2, 5034 }, 
  { 0xfcf2, 3, 5044 }, 
  { 0xfcf3, 3, 5047 }, 
  { 0xfcf4, 3, 5050 }, 
  { 0xfcf5, 2, 5053 }, 
  { 0xfcf6, 2, 5055 }, 
  { 0xfcf7, 2, 5057 }, 
  { 0xfcf8, 2, 5059 }, 
  { 0xfcf9, 2, 5061 }, 
  { 0xfcfa, 2, 5063 }, 
  { 0xfcfb, 2, 5065 }, 
  { 0xfcfc, 2, 5067 }, 
  { 0xfcfd, 2, 5069 }, 
  { 0xfcfe, 2, 5071 }, 
  { 0xfcff, 2, 5073 }, 
  { 0xfd00, 2, 5075 }, 
  { 0xfd01, 2, 5077 }, 
  { 0xfd02, 2, 5079 }, 
  { 0xfd03, 2, 5081 }, 
  { 0xfd04, 2, 5083 }, 
  { 0xfd05, 2, 5085 }, 
  { 0xfd06, 2, 5087 }, 
  { 0xfd07, 2, 5089 }, 
  { 0xfd08, 2, 5091 }, 
  { 0xfd09, 2, 5093 }, 
  { 0xfd0a, 2, 5095 }, 
  { 0xfd0b, 2, 5097 }, 
  { 0xfd0c, 2, 5040 }, 
  { 0xfd0d, 2, 5099 }, 
  { 0xfd0e, 2, 5101 }, 
  { 0xfd0f, 2, 5103 }, 
  { 0xfd10, 2, 5105 }, 
  { 0xfd11, 2, 5053 }, 
  { 0xfd12, 2, 5055 }, 
  { 0xfd13, 2, 5057 }, 
  { 0xfd14, 2, 5059 }, 
  { 0xfd15, 2, 5061 }, 
  { 0xfd16, 2, 5063 }, 
  { 0xfd17, 2, 5065 }, 
  { 0xfd18, 2, 5067 }, 
  { 0xfd19, 2, 5069 }, 
  { 0xfd1a, 2, 5071 }, 
  { 0xfd1b, 2, 5073 }, 
  { 0xfd1c, 2, 5075 }, 
  { 0xfd1d, 2, 5077 }, 
  { 0xfd1e, 2, 5079 }, 
  { 0xfd1f, 2, 5081 }, 
  { 0xfd20, 2, 5083 }, 
  { 0xfd21, 2, 5085 }, 
  { 0xfd22, 2, 5087 }, 
  { 0xfd23, 2, 5089 }, 
  { 0xfd24, 2, 5091 }, 
  { 0xfd25, 2, 5093 }, 
  { 0xfd26, 2, 5095 }, 
  { 0xfd27, 2, 5097 }, 
  { 0xfd28, 2, 5040 }, 
  { 0xfd29, 2, 5099 }, 
  { 0xfd2a, 2, 5101 }, 
  { 0xfd2b, 2, 5103 }, 
  { 0xfd2c, 2, 5105 }, 
  { 0xfd2d, 2, 5093 }, 
  { 0xfd2e, 2, 5095 }, 
  { 0xfd2f, 2, 5097 }, 
  { 0xfd30, 2, 5040 }, 
  { 0xfd31, 2, 5038 }, 
  { 0xfd32, 2, 5042 }, 
  { 0xfd33, 2, 4847 }, 
  { 0xfd34, 2, 4825 }, 
  { 0xfd35, 2, 4827 }, 
  { 0xfd36, 2, 4829 }, 
  { 0xfd37, 2, 5093 }, 
  { 0xfd38, 2, 5095 }, 
  { 0xfd39, 2, 5097 }, 
  { 0xfd3a, 2, 4847 }, 
  { 0xfd3b, 2, 4849 }, 
  { 0xfd3c, 2, 5107 }, 
  { 0xfd3d, 2, 5107 }, 
  { 0xfd50, 3, 5109 }, 
  { 0xfd51, 3, 5112 }, 
  { 0xfd52, 3, 5112 }, 
  { 0xfd53, 3, 5115 }, 
  { 0xfd54, 3, 5118 }, 
  { 0xfd55, 3, 5121 }, 
  { 0xfd56, 3, 5124 }, 
  { 0xfd57, 3, 5127 }, 
  { 0xfd58, 3, 5130 }, 
  { 0xfd59, 3, 5130 }, 
  { 0xfd5a, 3, 5133 }, 
  { 0xfd5b, 3, 5136 }, 
  { 0xfd5c, 3, 5139 }, 
  { 0xfd5d, 3, 5142 }, 
  { 0xfd5e, 3, 5145 }, 
  { 0xfd5f, 3, 5148 }, 
  { 0xfd60, 3, 5148 }, 
  { 0xfd61, 3, 5151 }, 
  { 0xfd62, 3, 5154 }, 
  { 0xfd63, 3, 5154 }, 
  { 0xfd64, 3, 5157 }, 
  { 0xfd65, 3, 5157 }, 
  { 0xfd66, 3, 5160 }, 
  { 0xfd67, 3, 5163 }, 
  { 0xfd68, 3, 5163 }, 
  { 0xfd69, 3, 5166 }, 
  { 0xfd6a, 3, 5169 }, 
  { 0xfd6b, 3, 5169 }, 
  { 0xfd6c, 3, 5172 }, 
  { 0xfd6d, 3, 5172 }, 
  { 0xfd6e, 3, 5175 }, 
  { 0xfd6f, 3, 5178 }, 
  { 0xfd70, 3, 5178 }, 
  { 0xfd71, 3, 5181 }, 
  { 0xfd72, 3, 5181 }, 
  { 0xfd73, 3, 5184 }, 
  { 0xfd74, 3, 5187 }, 
  { 0xfd75, 3, 5190 }, 
  { 0xfd76, 3, 5193 }, 
  { 0xfd77, 3, 5193 }, 
  { 0xfd78, 3, 5196 }, 
  { 0xfd79, 3, 5199 }, 
  { 0xfd7a, 3, 5202 }, 
  { 0xfd7b, 3, 5205 }, 
  { 0xfd7c, 3, 5208 }, 
  { 0xfd7d, 3, 5208 }, 
  { 0xfd7e, 3, 5211 }, 
  { 0xfd7f, 3, 5214 }, 
  { 0xfd80, 3, 5217 }, 
  { 0xfd81, 3, 5220 }, 
  { 0xfd82, 3, 5223 }, 
  { 0xfd83, 3, 5226 }, 
  { 0xfd84, 3, 5226 }, 
  { 0xfd85, 3, 5229 }, 
  { 0xfd86, 3, 5229 }, 
  { 0xfd87, 3, 5232 }, 
  { 0xfd88, 3, 5232 }, 
  { 0xfd89, 3, 5235 }, 
  { 0xfd8a, 3, 5238 }, 
  { 0xfd8b, 3, 5241 }, 
  { 0xfd8c, 3, 5244 }, 
  { 0xfd8d, 3, 5247 }, 
  { 0xfd8e, 3, 5250 }, 
  { 0xfd8f, 3, 5253 }, 
  { 0xfd92, 3, 5256 }, 
  { 0xfd93, 3, 5259 }, 
  { 0xfd94, 3, 5262 }, 
  { 0xfd95, 3, 5265 }, 
  { 0xfd96, 3, 5268 }, 
  { 0xfd97, 3, 5271 }, 
  { 0xfd98, 3, 5271 }, 
  { 0xfd99, 3, 5274 }, 
  { 0xfd9a, 3, 5277 }, 
  { 0xfd9b, 3, 5280 }, 
  { 0xfd9c, 3, 5283 }, 
  { 0xfd9d, 3, 5283 }, 
  { 0xfd9e, 3, 5286 }, 
  { 0xfd9f, 3, 5289 }, 
  { 0xfda0, 3, 5292 }, 
  { 0xfda1, 3, 5295 }, 
  { 0xfda2, 3, 5298 }, 
  { 0xfda3, 3, 5301 }, 
  { 0xfda4, 3, 5304 }, 
  { 0xfda5, 3, 5307 }, 
  { 0xfda6, 3, 5310 }, 
  { 0xfda7, 3, 5313 }, 
  { 0xfda8, 3, 5316 }, 
  { 0xfda9, 3, 5319 }, 
  { 0xfdaa, 3, 5322 }, 
  { 0xfdab, 3, 5325 }, 
  { 0xfdac, 3, 5328 }, 
  { 0xfdad, 3, 5331 }, 
  { 0xfdae, 3, 5334 }, 
  { 0xfdaf, 3, 5337 }, 
  { 0xfdb0, 3, 5340 }, 
  { 0xfdb1, 3, 5343 }, 
  { 0xfdb2, 3, 5346 }, 
  { 0xfdb3, 3, 5349 }, 
  { 0xfdb4, 3, 5211 }, 
  { 0xfdb5, 3, 5217 }, 
  { 0xfdb6, 3, 5352 }, 
  { 0xfdb7, 3, 5355 }, 
  { 0xfdb8, 3, 5358 }, 
  { 0xfdb9, 3, 5361 }, 
  { 0xfdba, 3, 5364 }, 
  { 0xfdbb, 3, 5367 }, 
  { 0xfdbc, 3, 5364 }, 
  { 0xfdbd, 3, 5358 }, 
  { 0xfdbe, 3, 5370 }, 
  { 0xfdbf, 3, 5373 }, 
  { 0xfdc0, 3, 5376 }, 
  { 0xfdc1, 3, 5379 }, 
  { 0xfdc2, 3, 5382 }, 
  { 0xfdc3, 3, 5367 }, 
  { 0xfdc4, 3, 5190 }, 
  { 0xfdc5, 3, 5160 }, 
  { 0xfdc6, 3, 5385 }, 
  { 0xfdc7, 3, 5388 }, 
  { 0xfdf0, 3, 5391 }, 
  { 0xfdf1, 3, 5394 }, 
  { 0xfdf2, 4, 5397 }, 
  { 0xfdf3, 4, 5401 }, 
  { 0xfdf4, 4, 5405 }, 
  { 0xfdf5, 4, 5409 }, 
  { 0xfdf6, 4, 5413 }, 
  { 0xfdf7, 4, 5417 }, 
  { 0xfdf8, 4, 5421 }, 
  { 0xfdf9, 3, 5425 }, 
  { 0xfdfa, 18, 5428 }, 
  { 0xfdfb, 8, 5446 }, 
  { 0xfdfc, 4, 5454 }, 
  { 0xfe30, 2, 2139 }, 
  { 0xfe31, 1, 5458 }, 
  { 0xfe32, 1, 5459 }, 
  { 0xfe33, 1, 5460 }, 
  { 0xfe34, 1, 5460 }, 
  { 0xfe35, 1, 2179 }, 
  { 0xfe36, 1, 2180 }, 
  { 0xfe37, 1, 5461 }, 
  { 0xfe38, 1, 5462 }, 
  { 0xfe39, 1, 5463 }, 
  { 0xfe3a, 1, 5464 }, 
  { 0xfe3b, 1, 5465 }, 
  { 0xfe3c, 1, 5466 }, 
  { 0xfe3d, 1, 5467 }, 
  { 0xfe3e, 1, 5468 }, 
  { 0xfe3f, 1, 2425 }, 
  { 0xfe40, 1, 2426 }, 
  { 0xfe41, 1, 5469 }, 
  { 0xfe42, 1, 5470 }, 
  { 0xfe43, 1, 5471 }, 
  { 0xfe44, 1, 5472 }, 
  { 0xfe49, 2, 2156 }, 
  { 0xfe4a, 2, 2156 }, 
  { 0xfe4b, 2, 2156 }, 
  { 0xfe4c, 2, 2156 }, 
  { 0xfe4d, 1, 5460 }, 
  { 0xfe4e, 1, 5460 }, 
  { 0xfe4f, 1, 5460 }, 
  { 0xfe50, 1, 5473 }, 
  { 0xfe51, 1, 5474 }, 
  { 0xfe52, 1, 2138 }, 
  { 0xfe54, 1, 621 }, 
  { 0xfe55, 1, 5475 }, 
  { 0xfe56, 1, 5476 }, 
  { 0xfe57, 1, 5477 }, 
  { 0xfe58, 1, 5458 }, 
  { 0xfe59, 1, 2179 }, 
  { 0xfe5a, 1, 2180 }, 
  { 0xfe5b, 1, 5461 }, 
  { 0xfe5c, 1, 5462 }, 
  { 0xfe5d, 1, 5463 }, 
  { 0xfe5e, 1, 5464 }, 
  { 0xfe5f, 1, 5478 }, 
  { 0xfe60, 1, 5479 }, 
  { 0xfe61, 1, 5480 }, 
  { 0xfe62, 1, 2176 }, 
  { 0xfe63, 1, 5481 }, 
  { 0xfe64, 1, 5482 }, 
  { 0xfe65, 1, 5483 }, 
  { 0xfe66, 1, 2178 }, 
  { 0xfe68, 1, 5484 }, 
  { 0xfe69, 1, 5485 }, 
  { 0xfe6a, 1, 5486 }, 
  { 0xfe6b, 1, 5487 }, 
  { 0xfe70, 2, 5488 }, 
  { 0xfe71, 2, 5490 }, 
  { 0xfe72, 2, 5492 }, 
  { 0xfe74, 2, 5494 }, 
  { 0xfe76, 2, 5496 }, 
  { 0xfe77, 2, 5498 }, 
  { 0xfe78, 2, 5500 }, 
  { 0xfe79, 2, 5502 }, 
  { 0xfe7a, 2, 5504 }, 
  { 0xfe7b, 2, 5506 }, 
  { 0xfe7c, 2, 5508 }, 
  { 0xfe7d, 2, 5510 }, 
  { 0xfe7e, 2, 5512 }, 
  { 0xfe7f, 2, 5514 }, 
  { 0xfe80, 1, 5516 }, 
  { 0xfe81, 2, 784 }, 
  { 0xfe82, 2, 784 }, 
  { 0xfe83, 2, 786 }, 
  { 0xfe84, 2, 786 }, 
  { 0xfe85, 2, 788 }, 
  { 0xfe86, 2, 788 }, 
  { 0xfe87, 2, 790 }, 
  { 0xfe88, 2, 790 }, 
  { 0xfe89, 2, 792 }, 
  { 0xfe8a, 2, 792 }, 
  { 0xfe8b, 2, 792 }, 
  { 0xfe8c, 2, 792 }, 
  { 0xfe8d, 1, 5517 }, 
  { 0xfe8e, 1, 5517 }, 
  { 0xfe8f, 1, 5518 }, 
  { 0xfe90, 1, 5518 }, 
  { 0xfe91, 1, 5518 }, 
  { 0xfe92, 1, 5518 }, 
  { 0xfe93, 1, 5519 }, 
  { 0xfe94, 1, 5519 }, 
  { 0xfe95, 1, 5520 }, 
  { 0xfe96, 1, 5520 }, 
  { 0xfe97, 1, 5520 }, 
  { 0xfe98, 1, 5520 }, 
  { 0xfe99, 1, 5521 }, 
  { 0xfe9a, 1, 5521 }, 
  { 0xfe9b, 1, 5521 }, 
  { 0xfe9c, 1, 5521 }, 
  { 0xfe9d, 1, 5522 }, 
  { 0xfe9e, 1, 5522 }, 
  { 0xfe9f, 1, 5522 }, 
  { 0xfea0, 1, 5522 }, 
  { 0xfea1, 1, 5523 }, 
  { 0xfea2, 1, 5523 }, 
  { 0xfea3, 1, 5523 }, 
  { 0xfea4, 1, 5523 }, 
  { 0xfea5, 1, 5524 }, 
  { 0xfea6, 1, 5524 }, 
  { 0xfea7, 1, 5524 }, 
  { 0xfea8, 1, 5524 }, 
  { 0xfea9, 1, 5525 }, 
  { 0xfeaa, 1, 5525 }, 
  { 0xfeab, 1, 5526 }, 
  { 0xfeac, 1, 5526 }, 
  { 0xfead, 1, 5527 }, 
  { 0xfeae, 1, 5527 }, 
  { 0xfeaf, 1, 5528 }, 
  { 0xfeb0, 1, 5528 }, 
  { 0xfeb1, 1, 5529 }, 
  { 0xfeb2, 1, 5529 }, 
  { 0xfeb3, 1, 5529 }, 
  { 0xfeb4, 1, 5529 }, 
  { 0xfeb5, 1, 5530 }, 
  { 0xfeb6, 1, 5530 }, 
  { 0xfeb7, 1, 5530 }, 
  { 0xfeb8, 1, 5530 }, 
  { 0xfeb9, 1, 5531 }, 
  { 0xfeba, 1, 5531 }, 
  { 0xfebb, 1, 5531 }, 
  { 0xfebc, 1, 5531 }, 
  { 0xfebd, 1, 5532 }, 
  { 0xfebe, 1, 5532 }, 
  { 0xfebf, 1, 5532 }, 
  { 0xfec0, 1, 5532 }, 
  { 0xfec1, 1, 5533 }, 
  { 0xfec2, 1, 5533 }, 
  { 0xfec3, 1, 5533 }, 
  { 0xfec4, 1, 5533 }, 
  { 0xfec5, 1, 5534 }, 
  { 0xfec6, 1, 5534 }, 
  { 0xfec7, 1, 5534 }, 
  { 0xfec8, 1, 5534 }, 
  { 0xfec9, 1, 5535 }, 
  { 0xfeca, 1, 5535 }, 
  { 0xfecb, 1, 5535 }, 
  { 0xfecc, 1, 5535 }, 
  { 0xfecd, 1, 5536 }, 
  { 0xfece, 1, 5536 }, 
  { 0xfecf, 1, 5536 }, 
  { 0xfed0, 1, 5536 }, 
  { 0xfed1, 1, 5537 }, 
  { 0xfed2, 1, 5537 }, 
  { 0xfed3, 1, 5537 }, 
  { 0xfed4, 1, 5537 }, 
  { 0xfed5, 1, 5538 }, 
  { 0xfed6, 1, 5538 }, 
  { 0xfed7, 1, 5538 }, 
  { 0xfed8, 1, 5538 }, 
  { 0xfed9, 1, 5539 }, 
  { 0xfeda, 1, 5539 }, 
  { 0xfedb, 1, 5539 }, 
  { 0xfedc, 1, 5539 }, 
  { 0xfedd, 1, 5540 }, 
  { 0xfede, 1, 5540 }, 
  { 0xfedf, 1, 5540 }, 
  { 0xfee0, 1, 5540 }, 
  { 0xfee1, 1, 5541 }, 
  { 0xfee2, 1, 5541 }, 
  { 0xfee3, 1, 5541 }, 
  { 0xfee4, 1, 5541 }, 
  { 0xfee5, 1, 5542 }, 
  { 0xfee6, 1, 5542 }, 
  { 0xfee7, 1, 5542 }, 
  { 0xfee8, 1, 5542 }, 
  { 0xfee9, 1, 5543 }, 
  { 0xfeea, 1, 5543 }, 
  { 0xfeeb, 1, 5543 }, 
  { 0xfeec, 1, 5543 }, 
  { 0xfeed, 1, 5544 }, 
  { 0xfeee, 1, 5544 }, 
  { 0xfeef, 1, 4741 }, 
  { 0xfef0, 1, 4741 }, 
  { 0xfef1, 1, 5545 }, 
  { 0xfef2, 1, 5545 }, 
  { 0xfef3, 1, 5545 }, 
  { 0xfef4, 1, 5545 }, 
  { 0xfef5, 3, 5546 }, 
  { 0xfef6, 3, 5546 }, 
  { 0xfef7, 3, 5549 }, 
  { 0xfef8, 3, 5549 }, 
  { 0xfef9, 3, 5552 }, 
  { 0xfefa, 3, 5552 }, 
  { 0xfefb, 2, 5555 }, 
  { 0xfefc, 2, 5555 }, 
  { 0xff01, 1, 5477 }, 
  { 0xff02, 1, 5557 }, 
  { 0xff03, 1, 5478 }, 
  { 0xff04, 1, 5485 }, 
  { 0xff05, 1, 5486 }, 
  { 0xff06, 1, 5479 }, 
  { 0xff07, 1, 5558 }, 
  { 0xff08, 1, 2179 }, 
  { 0xff09, 1, 2180 }, 
  { 0xff0a, 1, 5480 }, 
  { 0xff0b, 1, 2176 }, 
  { 0xff0c, 1, 5473 }, 
  { 0xff0d, 1, 5481 }, 
  { 0xff0e, 1, 2138 }, 
  { 0xff0f, 1, 5559 }, 
  { 0xff10, 1, 2168 }, 
  { 0xff11, 1, 13 }, 
  { 0xff12, 1, 6 }, 
  { 0xff13, 1, 7 }, 
  { 0xff14, 1, 2170 }, 
  { 0xff15, 1, 2171 }, 
  { 0xff16, 1, 2172 }, 
  { 0xff17, 1, 2173 }, 
  { 0xff18, 1, 2174 }, 
  { 0xff19, 1, 2175 }, 
  { 0xff1a, 1, 5475 }, 
  { 0xff1b, 1, 621 }, 
  { 0xff1c, 1, 5482 }, 
  { 0xff1d, 1, 2178 }, 
  { 0xff1e, 1, 5483 }, 
  { 0xff1f, 1, 5476 }, 
  { 0xff20, 1, 5487 }, 
  { 0xff21, 1, 2649 }, 
  { 0xff22, 1, 2223 }, 
  { 0xff23, 1, 2190 }, 
  { 0xff24, 1, 2236 }, 
  { 0xff25, 1, 2225 }, 
  { 0xff26, 1, 2226 }, 
  { 0xff27, 1, 2650 }, 
  { 0xff28, 1, 2203 }, 
  { 0xff29, 1, 2205 }, 
  { 0xff2a, 1, 2651 }, 
  { 0xff2b, 1, 2222 }, 
  { 0xff2c, 1, 2206 }, 
  { 0xff2d, 1, 2227 }, 
  { 0xff2e, 1, 2207 }, 
  { 0xff2f, 1, 2652 }, 
  { 0xff30, 1, 2210 }, 
  { 0xff31, 1, 2211 }, 
  { 0xff32, 1, 2212 }, 
  { 0xff33, 1, 2653 }, 
  { 0xff34, 1, 2654 }, 
  { 0xff35, 1, 2655 }, 
  { 0xff36, 1, 2283 }, 
  { 0xff37, 1, 2656 }, 
  { 0xff38, 1, 2295 }, 
  { 0xff39, 1, 2657 }, 
  { 0xff3a, 1, 2220 }, 
  { 0xff3b, 1, 5560 }, 
  { 0xff3c, 1, 5484 }, 
  { 0xff3d, 1, 5561 }, 
  { 0xff3e, 1, 5562 }, 
  { 0xff3f, 1, 5460 }, 
  { 0xff40, 1, 2113 }, 
  { 0xff41, 1, 3 }, 
  { 0xff42, 1, 2658 }, 
  { 0xff43, 1, 2325 }, 
  { 0xff44, 1, 2237 }, 
  { 0xff45, 1, 2224 }, 
  { 0xff46, 1, 2659 }, 
  { 0xff47, 1, 2202 }, 
  { 0xff48, 1, 588 }, 
  { 0xff49, 1, 2169 }, 
  { 0xff4a, 1, 590 }, 
  { 0xff4b, 1, 2660 }, 
  { 0xff4c, 1, 610 }, 
  { 0xff4d, 1, 2326 }, 
  { 0xff4e, 1, 2181 }, 
  { 0xff4f, 1, 14 }, 
  { 0xff50, 1, 2661 }, 
  { 0xff51, 1, 2662 }, 
  { 0xff52, 1, 591 }, 
  { 0xff53, 1, 356 }, 
  { 0xff54, 1, 2663 }, 
  { 0xff55, 1, 2664 }, 
  { 0xff56, 1, 2308 }, 
  { 0xff57, 1, 595 }, 
  { 0xff58, 1, 611 }, 
  { 0xff59, 1, 596 }, 
  { 0xff5a, 1, 2665 }, 
  { 0xff5b, 1, 5461 }, 
  { 0xff5c, 1, 5563 }, 
  { 0xff5d, 1, 5462 }, 
  { 0xff5e, 1, 5564 }, 
  { 0xff5f, 1, 5565 }, 
  { 0xff60, 1, 5566 }, 
  { 0xff61, 1, 5567 }, 
  { 0xff62, 1, 5469 }, 
  { 0xff63, 1, 5470 }, 
  { 0xff64, 1, 5474 }, 
  { 0xff65, 1, 5568 }, 
  { 0xff66, 1, 3530 }, 
  { 0xff67, 1, 5569 }, 
  { 0xff68, 1, 5570 }, 
  { 0xff69, 1, 5571 }, 
  { 0xff6a, 1, 5572 }, 
  { 0xff6b, 1, 5573 }, 
  { 0xff6c, 1, 5574 }, 
  { 0xff6d, 1, 5575 }, 
  { 0xff6e, 1, 5576 }, 
  { 0xff6f, 1, 5577 }, 
  { 0xff70, 1, 5578 }, 
  { 0xff71, 1, 3484 }, 
  { 0xff72, 1, 3485 }, 
  { 0xff73, 1, 3486 }, 
  { 0xff74, 1, 3487 }, 
  { 0xff75, 1, 3488 }, 
  { 0xff76, 1, 3489 }, 
  { 0xff77, 1, 3490 }, 
  { 0xff78, 1, 3491 }, 
  { 0xff79, 1, 3492 }, 
  { 0xff7a, 1, 3493 }, 
  { 0xff7b, 1, 3494 }, 
  { 0xff7c, 1, 3495 }, 
  { 0xff7d, 1, 3496 }, 
  { 0xff7e, 1, 3497 }, 
  { 0xff7f, 1, 3498 }, 
  { 0xff80, 1, 3499 }, 
  { 0xff81, 1, 3500 }, 
  { 0xff82, 1, 3501 }, 
  { 0xff83, 1, 3502 }, 
  { 0xff84, 1, 3503 }, 
  { 0xff85, 1, 3504 }, 
  { 0xff86, 1, 3505 }, 
  { 0xff87, 1, 3506 }, 
  { 0xff88, 1, 3507 }, 
  { 0xff89, 1, 3508 }, 
  { 0xff8a, 1, 3509 }, 
  { 0xff8b, 1, 3510 }, 
  { 0xff8c, 1, 3511 }, 
  { 0xff8d, 1, 3512 }, 
  { 0xff8e, 1, 3513 }, 
  { 0xff8f, 1, 3514 }, 
  { 0xff90, 1, 3515 }, 
  { 0xff91, 1, 3516 }, 
  { 0xff92, 1, 3517 }, 
  { 0xff93, 1, 3518 }, 
  { 0xff94, 1, 3519 }, 
  { 0xff95, 1, 3520 }, 
  { 0xff96, 1, 3521 }, 
  { 0xff97, 1, 3522 }, 
  { 0xff98, 1, 3523 }, 
  { 0xff99, 1, 3524 }, 
  { 0xff9a, 1, 3525 }, 
  { 0xff9b, 1, 3526 }, 
  { 0xff9c, 1, 3527 }, 
  { 0xff9d, 1, 5579 }, 
  { 0xff9e, 1, 5580 }, 
  { 0xff9f, 1, 5581 }, 
  { 0xffa0, 1, 3074 }, 
  { 0xffa1, 1, 3023 }, 
  { 0xffa2, 1, 3024 }, 
  { 0xffa3, 1, 3025 }, 
  { 0xffa4, 1, 3026 }, 
  { 0xffa5, 1, 3027 }, 
  { 0xffa6, 1, 3028 }, 
  { 0xffa7, 1, 3029 }, 
  { 0xffa8, 1, 3030 }, 
  { 0xffa9, 1, 3031 }, 
  { 0xffaa, 1, 3032 }, 
  { 0xffab, 1, 3033 }, 
  { 0xffac, 1, 3034 }, 
  { 0xffad, 1, 3035 }, 
  { 0xffae, 1, 3036 }, 
  { 0xffaf, 1, 3037 }, 
  { 0xffb0, 1, 3038 }, 
  { 0xffb1, 1, 3039 }, 
  { 0xffb2, 1, 3040 }, 
  { 0xffb3, 1, 3041 }, 
  { 0xffb4, 1, 3042 }, 
  { 0xffb5, 1, 3043 }, 
  { 0xffb6, 1, 3044 }, 
  { 0xffb7, 1, 3045 }, 
  { 0xffb8, 1, 3046 }, 
  { 0xffb9, 1, 3047 }, 
  { 0xffba, 1, 3048 }, 
  { 0xffbb, 1, 3049 }, 
  { 0xffbc, 1, 3050 }, 
  { 0xffbd, 1, 3051 }, 
  { 0xffbe, 1, 3052 }, 
  { 0xffc2, 1, 3053 }, 
  { 0xffc3, 1, 3054 }, 
  { 0xffc4, 1, 3055 }, 
  { 0xffc5, 1, 3056 }, 
  { 0xffc6, 1, 3057 }, 
  { 0xffc7, 1, 3058 }, 
  { 0xffca, 1, 3059 }, 
  { 0xffcb, 1, 3060 }, 
  { 0xffcc, 1, 3061 }, 
  { 0xffcd, 1, 3062 }, 
  { 0xffce, 1, 3063 }, 
  { 0xffcf, 1, 3064 }, 
  { 0xffd2, 1, 3065 }, 
  { 0xffd3, 1, 3066 }, 
  { 0xffd4, 1, 3067 }, 
  { 0xffd5, 1, 3068 }, 
  { 0xffd6, 1, 3069 }, 
  { 0xffd7, 1, 3070 }, 
  { 0xffda, 1, 3071 }, 
  { 0xffdb, 1, 3072 }, 
  { 0xffdc, 1, 3073 }, 
  { 0xffe0, 1, 5582 }, 
  { 0xffe1, 1, 5583 }, 
  { 0xffe2, 1, 5584 }, 
  { 0xffe3, 2, 4 }, 
  { 0xffe4, 1, 5585 }, 
  { 0xffe5, 1, 5586 }, 
  { 0xffe6, 1, 5587 }, 
  { 0xffe8, 1, 5588 }, 
  { 0xffe9, 1, 5589 }, 
  { 0xffea, 1, 5590 }, 
  { 0xffeb, 1, 5591 }, 
  { 0xffec, 1, 5592 }, 
  { 0xffed, 1, 5593 }, 
  { 0xffee, 1, 5594 }, 
  { 0x1d15e, 2, 5595 }, 
  { 0x1d15f, 2, 5597 }, 
  { 0x1d160, 3, 5599 }, 
  { 0x1d161, 3, 5602 }, 
  { 0x1d162, 3, 5605 }, 
  { 0x1d163, 3, 5608 }, 
  { 0x1d164, 3, 5611 }, 
  { 0x1d1bb, 2, 5614 }, 
  { 0x1d1bc, 2, 5616 }, 
  { 0x1d1bd, 3, 5618 }, 
  { 0x1d1be, 3, 5621 }, 
  { 0x1d1bf, 3, 5624 }, 
  { 0x1d1c0, 3, 5627 }, 
  { 0x1d400, 1, 2649 }, 
  { 0x1d401, 1, 2223 }, 
  { 0x1d402, 1, 2190 }, 
  { 0x1d403, 1, 2236 }, 
  { 0x1d404, 1, 2225 }, 
  { 0x1d405, 1, 2226 }, 
  { 0x1d406, 1, 2650 }, 
  { 0x1d407, 1, 2203 }, 
  { 0x1d408, 1, 2205 }, 
  { 0x1d409, 1, 2651 }, 
  { 0x1d40a, 1, 2222 }, 
  { 0x1d40b, 1, 2206 }, 
  { 0x1d40c, 1, 2227 }, 
  { 0x1d40d, 1, 2207 }, 
  { 0x1d40e, 1, 2652 }, 
  { 0x1d40f, 1, 2210 }, 
  { 0x1d410, 1, 2211 }, 
  { 0x1d411, 1, 2212 }, 
  { 0x1d412, 1, 2653 }, 
  { 0x1d413, 1, 2654 }, 
  { 0x1d414, 1, 2655 }, 
  { 0x1d415, 1, 2283 }, 
  { 0x1d416, 1, 2656 }, 
  { 0x1d417, 1, 2295 }, 
  { 0x1d418, 1, 2657 }, 
  { 0x1d419, 1, 2220 }, 
  { 0x1d41a, 1, 3 }, 
  { 0x1d41b, 1, 2658 }, 
  { 0x1d41c, 1, 2325 }, 
  { 0x1d41d, 1, 2237 }, 
  { 0x1d41e, 1, 2224 }, 
  { 0x1d41f, 1, 2659 }, 
  { 0x1d420, 1, 2202 }, 
  { 0x1d421, 1, 588 }, 
  { 0x1d422, 1, 2169 }, 
  { 0x1d423, 1, 590 }, 
  { 0x1d424, 1, 2660 }, 
  { 0x1d425, 1, 610 }, 
  { 0x1d426, 1, 2326 }, 
  { 0x1d427, 1, 2181 }, 
  { 0x1d428, 1, 14 }, 
  { 0x1d429, 1, 2661 }, 
  { 0x1d42a, 1, 2662 }, 
  { 0x1d42b, 1, 591 }, 
  { 0x1d42c, 1, 356 }, 
  { 0x1d42d, 1, 2663 }, 
  { 0x1d42e, 1, 2664 }, 
  { 0x1d42f, 1, 2308 }, 
  { 0x1d430, 1, 595 }, 
  { 0x1d431, 1, 611 }, 
  { 0x1d432, 1, 596 }, 
  { 0x1d433, 1, 2665 }, 
  { 0x1d434, 1, 2649 }, 
  { 0x1d435, 1, 2223 }, 
  { 0x1d436, 1, 2190 }, 
  { 0x1d437, 1, 2236 }, 
  { 0x1d438, 1, 2225 }, 
  { 0x1d439, 1, 2226 }, 
  { 0x1d43a, 1, 2650 }, 
  { 0x1d43b, 1, 2203 }, 
  { 0x1d43c, 1, 2205 }, 
  { 0x1d43d, 1, 2651 }, 
  { 0x1d43e, 1, 2222 }, 
  { 0x1d43f, 1, 2206 }, 
  { 0x1d440, 1, 2227 }, 
  { 0x1d441, 1, 2207 }, 
  { 0x1d442, 1, 2652 }, 
  { 0x1d443, 1, 2210 }, 
  { 0x1d444, 1, 2211 }, 
  { 0x1d445, 1, 2212 }, 
  { 0x1d446, 1, 2653 }, 
  { 0x1d447, 1, 2654 }, 
  { 0x1d448, 1, 2655 }, 
  { 0x1d449, 1, 2283 }, 
  { 0x1d44a, 1, 2656 }, 
  { 0x1d44b, 1, 2295 }, 
  { 0x1d44c, 1, 2657 }, 
  { 0x1d44d, 1, 2220 }, 
  { 0x1d44e, 1, 3 }, 
  { 0x1d44f, 1, 2658 }, 
  { 0x1d450, 1, 2325 }, 
  { 0x1d451, 1, 2237 }, 
  { 0x1d452, 1, 2224 }, 
  { 0x1d453, 1, 2659 }, 
  { 0x1d454, 1, 2202 }, 
  { 0x1d456, 1, 2169 }, 
  { 0x1d457, 1, 590 }, 
  { 0x1d458, 1, 2660 }, 
  { 0x1d459, 1, 610 }, 
  { 0x1d45a, 1, 2326 }, 
  { 0x1d45b, 1, 2181 }, 
  { 0x1d45c, 1, 14 }, 
  { 0x1d45d, 1, 2661 }, 
  { 0x1d45e, 1, 2662 }, 
  { 0x1d45f, 1, 591 }, 
  { 0x1d460, 1, 356 }, 
  { 0x1d461, 1, 2663 }, 
  { 0x1d462, 1, 2664 }, 
  { 0x1d463, 1, 2308 }, 
  { 0x1d464, 1, 595 }, 
  { 0x1d465, 1, 611 }, 
  { 0x1d466, 1, 596 }, 
  { 0x1d467, 1, 2665 }, 
  { 0x1d468, 1, 2649 }, 
  { 0x1d469, 1, 2223 }, 
  { 0x1d46a, 1, 2190 }, 
  { 0x1d46b, 1, 2236 }, 
  { 0x1d46c, 1, 2225 }, 
  { 0x1d46d, 1, 2226 }, 
  { 0x1d46e, 1, 2650 }, 
  { 0x1d46f, 1, 2203 }, 
  { 0x1d470, 1, 2205 }, 
  { 0x1d471, 1, 2651 }, 
  { 0x1d472, 1, 2222 }, 
  { 0x1d473, 1, 2206 }, 
  { 0x1d474, 1, 2227 }, 
  { 0x1d475, 1, 2207 }, 
  { 0x1d476, 1, 2652 }, 
  { 0x1d477, 1, 2210 }, 
  { 0x1d478, 1, 2211 }, 
  { 0x1d479, 1, 2212 }, 
  { 0x1d47a, 1, 2653 }, 
  { 0x1d47b, 1, 2654 }, 
  { 0x1d47c, 1, 2655 }, 
  { 0x1d47d, 1, 2283 }, 
  { 0x1d47e, 1, 2656 }, 
  { 0x1d47f, 1, 2295 }, 
  { 0x1d480, 1, 2657 }, 
  { 0x1d481, 1, 2220 }, 
  { 0x1d482, 1, 3 }, 
  { 0x1d483, 1, 2658 }, 
  { 0x1d484, 1, 2325 }, 
  { 0x1d485, 1, 2237 }, 
  { 0x1d486, 1, 2224 }, 
  { 0x1d487, 1, 2659 }, 
  { 0x1d488, 1, 2202 }, 
  { 0x1d489, 1, 588 }, 
  { 0x1d48a, 1, 2169 }, 
  { 0x1d48b, 1, 590 }, 
  { 0x1d48c, 1, 2660 }, 
  { 0x1d48d, 1, 610 }, 
  { 0x1d48e, 1, 2326 }, 
  { 0x1d48f, 1, 2181 }, 
  { 0x1d490, 1, 14 }, 
  { 0x1d491, 1, 2661 }, 
  { 0x1d492, 1, 2662 }, 
  { 0x1d493, 1, 591 }, 
  { 0x1d494, 1, 356 }, 
  { 0x1d495, 1, 2663 }, 
  { 0x1d496, 1, 2664 }, 
  { 0x1d497, 1, 2308 }, 
  { 0x1d498, 1, 595 }, 
  { 0x1d499, 1, 611 }, 
  { 0x1d49a, 1, 596 }, 
  { 0x1d49b, 1, 2665 }, 
  { 0x1d49c, 1, 2649 }, 
  { 0x1d49e, 1, 2190 }, 
  { 0x1d49f, 1, 2236 }, 
  { 0x1d4a2, 1, 2650 }, 
  { 0x1d4a5, 1, 2651 }, 
  { 0x1d4a6, 1, 2222 }, 
  { 0x1d4a9, 1, 2207 }, 
  { 0x1d4aa, 1, 2652 }, 
  { 0x1d4ab, 1, 2210 }, 
  { 0x1d4ac, 1, 2211 }, 
  { 0x1d4ae, 1, 2653 }, 
  { 0x1d4af, 1, 2654 }, 
  { 0x1d4b0, 1, 2655 }, 
  { 0x1d4b1, 1, 2283 }, 
  { 0x1d4b2, 1, 2656 }, 
  { 0x1d4b3, 1, 2295 }, 
  { 0x1d4b4, 1, 2657 }, 
  { 0x1d4b5, 1, 2220 }, 
  { 0x1d4b6, 1, 3 }, 
  { 0x1d4b7, 1, 2658 }, 
  { 0x1d4b8, 1, 2325 }, 
  { 0x1d4b9, 1, 2237 }, 
  { 0x1d4bb, 1, 2659 }, 
  { 0x1d4bd, 1, 588 }, 
  { 0x1d4be, 1, 2169 }, 
  { 0x1d4bf, 1, 590 }, 
  { 0x1d4c0, 1, 2660 }, 
  { 0x1d4c2, 1, 2326 }, 
  { 0x1d4c3, 1, 2181 }, 
  { 0x1d4c5, 1, 2661 }, 
  { 0x1d4c6, 1, 2662 }, 
  { 0x1d4c7, 1, 591 }, 
  { 0x1d4c8, 1, 356 }, 
  { 0x1d4c9, 1, 2663 }, 
  { 0x1d4ca, 1, 2664 }, 
  { 0x1d4cb, 1, 2308 }, 
  { 0x1d4cc, 1, 595 }, 
  { 0x1d4cd, 1, 611 }, 
  { 0x1d4ce, 1, 596 }, 
  { 0x1d4cf, 1, 2665 }, 
  { 0x1d4d0, 1, 2649 }, 
  { 0x1d4d1, 1, 2223 }, 
  { 0x1d4d2, 1, 2190 }, 
  { 0x1d4d3, 1, 2236 }, 
  { 0x1d4d4, 1, 2225 }, 
  { 0x1d4d5, 1, 2226 }, 
  { 0x1d4d6, 1, 2650 }, 
  { 0x1d4d7, 1, 2203 }, 
  { 0x1d4d8, 1, 2205 }, 
  { 0x1d4d9, 1, 2651 }, 
  { 0x1d4da, 1, 2222 }, 
  { 0x1d4db, 1, 2206 }, 
  { 0x1d4dc, 1, 2227 }, 
  { 0x1d4dd, 1, 2207 }, 
  { 0x1d4de, 1, 2652 }, 
  { 0x1d4df, 1, 2210 }, 
  { 0x1d4e0, 1, 2211 }, 
  { 0x1d4e1, 1, 2212 }, 
  { 0x1d4e2, 1, 2653 }, 
  { 0x1d4e3, 1, 2654 }, 
  { 0x1d4e4, 1, 2655 }, 
  { 0x1d4e5, 1, 2283 }, 
  { 0x1d4e6, 1, 2656 }, 
  { 0x1d4e7, 1, 2295 }, 
  { 0x1d4e8, 1, 2657 }, 
  { 0x1d4e9, 1, 2220 }, 
  { 0x1d4ea, 1, 3 }, 
  { 0x1d4eb, 1, 2658 }, 
  { 0x1d4ec, 1, 2325 }, 
  { 0x1d4ed, 1, 2237 }, 
  { 0x1d4ee, 1, 2224 }, 
  { 0x1d4ef, 1, 2659 }, 
  { 0x1d4f0, 1, 2202 }, 
  { 0x1d4f1, 1, 588 }, 
  { 0x1d4f2, 1, 2169 }, 
  { 0x1d4f3, 1, 590 }, 
  { 0x1d4f4, 1, 2660 }, 
  { 0x1d4f5, 1, 610 }, 
  { 0x1d4f6, 1, 2326 }, 
  { 0x1d4f7, 1, 2181 }, 
  { 0x1d4f8, 1, 14 }, 
  { 0x1d4f9, 1, 2661 }, 
  { 0x1d4fa, 1, 2662 }, 
  { 0x1d4fb, 1, 591 }, 
  { 0x1d4fc, 1, 356 }, 
  { 0x1d4fd, 1, 2663 }, 
  { 0x1d4fe, 1, 2664 }, 
  { 0x1d4ff, 1, 2308 }, 
  { 0x1d500, 1, 595 }, 
  { 0x1d501, 1, 611 }, 
  { 0x1d502, 1, 596 }, 
  { 0x1d503, 1, 2665 }, 
  { 0x1d504, 1, 2649 }, 
  { 0x1d505, 1, 2223 }, 
  { 0x1d507, 1, 2236 }, 
  { 0x1d508, 1, 2225 }, 
  { 0x1d509, 1, 2226 }, 
  { 0x1d50a, 1, 2650 }, 
  { 0x1d50d, 1, 2651 }, 
  { 0x1d50e, 1, 2222 }, 
  { 0x1d50f, 1, 2206 }, 
  { 0x1d510, 1, 2227 }, 
  { 0x1d511, 1, 2207 }, 
  { 0x1d512, 1, 2652 }, 
  { 0x1d513, 1, 2210 }, 
  { 0x1d514, 1, 2211 }, 
  { 0x1d516, 1, 2653 }, 
  { 0x1d517, 1, 2654 }, 
  { 0x1d518, 1, 2655 }, 
  { 0x1d519, 1, 2283 }, 
  { 0x1d51a, 1, 2656 }, 
  { 0x1d51b, 1, 2295 }, 
  { 0x1d51c, 1, 2657 }, 
  { 0x1d51e, 1, 3 }, 
  { 0x1d51f, 1, 2658 }, 
  { 0x1d520, 1, 2325 }, 
  { 0x1d521, 1, 2237 }, 
  { 0x1d522, 1, 2224 }, 
  { 0x1d523, 1, 2659 }, 
  { 0x1d524, 1, 2202 }, 
  { 0x1d525, 1, 588 }, 
  { 0x1d526, 1, 2169 }, 
  { 0x1d527, 1, 590 }, 
  { 0x1d528, 1, 2660 }, 
  { 0x1d529, 1, 610 }, 
  { 0x1d52a, 1, 2326 }, 
  { 0x1d52b, 1, 2181 }, 
  { 0x1d52c, 1, 14 }, 
  { 0x1d52d, 1, 2661 }, 
  { 0x1d52e, 1, 2662 }, 
  { 0x1d52f, 1, 591 }, 
  { 0x1d530, 1, 356 }, 
  { 0x1d531, 1, 2663 }, 
  { 0x1d532, 1, 2664 }, 
  { 0x1d533, 1, 2308 }, 
  { 0x1d534, 1, 595 }, 
  { 0x1d535, 1, 611 }, 
  { 0x1d536, 1, 596 }, 
  { 0x1d537, 1, 2665 }, 
  { 0x1d538, 1, 2649 }, 
  { 0x1d539, 1, 2223 }, 
  { 0x1d53b, 1, 2236 }, 
  { 0x1d53c, 1, 2225 }, 
  { 0x1d53d, 1, 2226 }, 
  { 0x1d53e, 1, 2650 }, 
  { 0x1d540, 1, 2205 }, 
  { 0x1d541, 1, 2651 }, 
  { 0x1d542, 1, 2222 }, 
  { 0x1d543, 1, 2206 }, 
  { 0x1d544, 1, 2227 }, 
  { 0x1d546, 1, 2652 }, 
  { 0x1d54a, 1, 2653 }, 
  { 0x1d54b, 1, 2654 }, 
  { 0x1d54c, 1, 2655 }, 
  { 0x1d54d, 1, 2283 }, 
  { 0x1d54e, 1, 2656 }, 
  { 0x1d54f, 1, 2295 }, 
  { 0x1d550, 1, 2657 }, 
  { 0x1d552, 1, 3 }, 
  { 0x1d553, 1, 2658 }, 
  { 0x1d554, 1, 2325 }, 
  { 0x1d555, 1, 2237 }, 
  { 0x1d556, 1, 2224 }, 
  { 0x1d557, 1, 2659 }, 
  { 0x1d558, 1, 2202 }, 
  { 0x1d559, 1, 588 }, 
  { 0x1d55a, 1, 2169 }, 
  { 0x1d55b, 1, 590 }, 
  { 0x1d55c, 1, 2660 }, 
  { 0x1d55d, 1, 610 }, 
  { 0x1d55e, 1, 2326 }, 
  { 0x1d55f, 1, 2181 }, 
  { 0x1d560, 1, 14 }, 
  { 0x1d561, 1, 2661 }, 
  { 0x1d562, 1, 2662 }, 
  { 0x1d563, 1, 591 }, 
  { 0x1d564, 1, 356 }, 
  { 0x1d565, 1, 2663 }, 
  { 0x1d566, 1, 2664 }, 
  { 0x1d567, 1, 2308 }, 
  { 0x1d568, 1, 595 }, 
  { 0x1d569, 1, 611 }, 
  { 0x1d56a, 1, 596 }, 
  { 0x1d56b, 1, 2665 }, 
  { 0x1d56c, 1, 2649 }, 
  { 0x1d56d, 1, 2223 }, 
  { 0x1d56e, 1, 2190 }, 
  { 0x1d56f, 1, 2236 }, 
  { 0x1d570, 1, 2225 }, 
  { 0x1d571, 1, 2226 }, 
  { 0x1d572, 1, 2650 }, 
  { 0x1d573, 1, 2203 }, 
  { 0x1d574, 1, 2205 }, 
  { 0x1d575, 1, 2651 }, 
  { 0x1d576, 1, 2222 }, 
  { 0x1d577, 1, 2206 }, 
  { 0x1d578, 1, 2227 }, 
  { 0x1d579, 1, 2207 }, 
  { 0x1d57a, 1, 2652 }, 
  { 0x1d57b, 1, 2210 }, 
  { 0x1d57c, 1, 2211 }, 
  { 0x1d57d, 1, 2212 }, 
  { 0x1d57e, 1, 2653 }, 
  { 0x1d57f, 1, 2654 }, 
  { 0x1d580, 1, 2655 }, 
  { 0x1d581, 1, 2283 }, 
  { 0x1d582, 1, 2656 }, 
  { 0x1d583, 1, 2295 }, 
  { 0x1d584, 1, 2657 }, 
  { 0x1d585, 1, 2220 }, 
  { 0x1d586, 1, 3 }, 
  { 0x1d587, 1, 2658 }, 
  { 0x1d588, 1, 2325 }, 
  { 0x1d589, 1, 2237 }, 
  { 0x1d58a, 1, 2224 }, 
  { 0x1d58b, 1, 2659 }, 
  { 0x1d58c, 1, 2202 }, 
  { 0x1d58d, 1, 588 }, 
  { 0x1d58e, 1, 2169 }, 
  { 0x1d58f, 1, 590 }, 
  { 0x1d590, 1, 2660 }, 
  { 0x1d591, 1, 610 }, 
  { 0x1d592, 1, 2326 }, 
  { 0x1d593, 1, 2181 }, 
  { 0x1d594, 1, 14 }, 
  { 0x1d595, 1, 2661 }, 
  { 0x1d596, 1, 2662 }, 
  { 0x1d597, 1, 591 }, 
  { 0x1d598, 1, 356 }, 
  { 0x1d599, 1, 2663 }, 
  { 0x1d59a, 1, 2664 }, 
  { 0x1d59b, 1, 2308 }, 
  { 0x1d59c, 1, 595 }, 
  { 0x1d59d, 1, 611 }, 
  { 0x1d59e, 1, 596 }, 
  { 0x1d59f, 1, 2665 }, 
  { 0x1d5a0, 1, 2649 }, 
  { 0x1d5a1, 1, 2223 }, 
  { 0x1d5a2, 1, 2190 }, 
  { 0x1d5a3, 1, 2236 }, 
  { 0x1d5a4, 1, 2225 }, 
  { 0x1d5a5, 1, 2226 }, 
  { 0x1d5a6, 1, 2650 }, 
  { 0x1d5a7, 1, 2203 }, 
  { 0x1d5a8, 1, 2205 }, 
  { 0x1d5a9, 1, 2651 }, 
  { 0x1d5aa, 1, 2222 }, 
  { 0x1d5ab, 1, 2206 }, 
  { 0x1d5ac, 1, 2227 }, 
  { 0x1d5ad, 1, 2207 }, 
  { 0x1d5ae, 1, 2652 }, 
  { 0x1d5af, 1, 2210 }, 
  { 0x1d5b0, 1, 2211 }, 
  { 0x1d5b1, 1, 2212 }, 
  { 0x1d5b2, 1, 2653 }, 
  { 0x1d5b3, 1, 2654 }, 
  { 0x1d5b4, 1, 2655 }, 
  { 0x1d5b5, 1, 2283 }, 
  { 0x1d5b6, 1, 2656 }, 
  { 0x1d5b7, 1, 2295 }, 
  { 0x1d5b8, 1, 2657 }, 
  { 0x1d5b9, 1, 2220 }, 
  { 0x1d5ba, 1, 3 }, 
  { 0x1d5bb, 1, 2658 }, 
  { 0x1d5bc, 1, 2325 }, 
  { 0x1d5bd, 1, 2237 }, 
  { 0x1d5be, 1, 2224 }, 
  { 0x1d5bf, 1, 2659 }, 
  { 0x1d5c0, 1, 2202 }, 
  { 0x1d5c1, 1, 588 }, 
  { 0x1d5c2, 1, 2169 }, 
  { 0x1d5c3, 1, 590 }, 
  { 0x1d5c4, 1, 2660 }, 
  { 0x1d5c5, 1, 610 }, 
  { 0x1d5c6, 1, 2326 }, 
  { 0x1d5c7, 1, 2181 }, 
  { 0x1d5c8, 1, 14 }, 
  { 0x1d5c9, 1, 2661 }, 
  { 0x1d5ca, 1, 2662 }, 
  { 0x1d5cb, 1, 591 }, 
  { 0x1d5cc, 1, 356 }, 
  { 0x1d5cd, 1, 2663 }, 
  { 0x1d5ce, 1, 2664 }, 
  { 0x1d5cf, 1, 2308 }, 
  { 0x1d5d0, 1, 595 }, 
  { 0x1d5d1, 1, 611 }, 
  { 0x1d5d2, 1, 596 }, 
  { 0x1d5d3, 1, 2665 }, 
  { 0x1d5d4, 1, 2649 }, 
  { 0x1d5d5, 1, 2223 }, 
  { 0x1d5d6, 1, 2190 }, 
  { 0x1d5d7, 1, 2236 }, 
  { 0x1d5d8, 1, 2225 }, 
  { 0x1d5d9, 1, 2226 }, 
  { 0x1d5da, 1, 2650 }, 
  { 0x1d5db, 1, 2203 }, 
  { 0x1d5dc, 1, 2205 }, 
  { 0x1d5dd, 1, 2651 }, 
  { 0x1d5de, 1, 2222 }, 
  { 0x1d5df, 1, 2206 }, 
  { 0x1d5e0, 1, 2227 }, 
  { 0x1d5e1, 1, 2207 }, 
  { 0x1d5e2, 1, 2652 }, 
  { 0x1d5e3, 1, 2210 }, 
  { 0x1d5e4, 1, 2211 }, 
  { 0x1d5e5, 1, 2212 }, 
  { 0x1d5e6, 1, 2653 }, 
  { 0x1d5e7, 1, 2654 }, 
  { 0x1d5e8, 1, 2655 }, 
  { 0x1d5e9, 1, 2283 }, 
  { 0x1d5ea, 1, 2656 }, 
  { 0x1d5eb, 1, 2295 }, 
  { 0x1d5ec, 1, 2657 }, 
  { 0x1d5ed, 1, 2220 }, 
  { 0x1d5ee, 1, 3 }, 
  { 0x1d5ef, 1, 2658 }, 
  { 0x1d5f0, 1, 2325 }, 
  { 0x1d5f1, 1, 2237 }, 
  { 0x1d5f2, 1, 2224 }, 
  { 0x1d5f3, 1, 2659 }, 
  { 0x1d5f4, 1, 2202 }, 
  { 0x1d5f5, 1, 588 }, 
  { 0x1d5f6, 1, 2169 }, 
  { 0x1d5f7, 1, 590 }, 
  { 0x1d5f8, 1, 2660 }, 
  { 0x1d5f9, 1, 610 }, 
  { 0x1d5fa, 1, 2326 }, 
  { 0x1d5fb, 1, 2181 }, 
  { 0x1d5fc, 1, 14 }, 
  { 0x1d5fd, 1, 2661 }, 
  { 0x1d5fe, 1, 2662 }, 
  { 0x1d5ff, 1, 591 }, 
  { 0x1d600, 1, 356 }, 
  { 0x1d601, 1, 2663 }, 
  { 0x1d602, 1, 2664 }, 
  { 0x1d603, 1, 2308 }, 
  { 0x1d604, 1, 595 }, 
  { 0x1d605, 1, 611 }, 
  { 0x1d606, 1, 596 }, 
  { 0x1d607, 1, 2665 }, 
  { 0x1d608, 1, 2649 }, 
  { 0x1d609, 1, 2223 }, 
  { 0x1d60a, 1, 2190 }, 
  { 0x1d60b, 1, 2236 }, 
  { 0x1d60c, 1, 2225 }, 
  { 0x1d60d, 1, 2226 }, 
  { 0x1d60e, 1, 2650 }, 
  { 0x1d60f, 1, 2203 }, 
  { 0x1d610, 1, 2205 }, 
  { 0x1d611, 1, 2651 }, 
  { 0x1d612, 1, 2222 }, 
  { 0x1d613, 1, 2206 }, 
  { 0x1d614, 1, 2227 }, 
  { 0x1d615, 1, 2207 }, 
  { 0x1d616, 1, 2652 }, 
  { 0x1d617, 1, 2210 }, 
  { 0x1d618, 1, 2211 }, 
  { 0x1d619, 1, 2212 }, 
  { 0x1d61a, 1, 2653 }, 
  { 0x1d61b, 1, 2654 }, 
  { 0x1d61c, 1, 2655 }, 
  { 0x1d61d, 1, 2283 }, 
  { 0x1d61e, 1, 2656 }, 
  { 0x1d61f, 1, 2295 }, 
  { 0x1d620, 1, 2657 }, 
  { 0x1d621, 1, 2220 }, 
  { 0x1d622, 1, 3 }, 
  { 0x1d623, 1, 2658 }, 
  { 0x1d624, 1, 2325 }, 
  { 0x1d625, 1, 2237 }, 
  { 0x1d626, 1, 2224 }, 
  { 0x1d627, 1, 2659 }, 
  { 0x1d628, 1, 2202 }, 
  { 0x1d629, 1, 588 }, 
  { 0x1d62a, 1, 2169 }, 
  { 0x1d62b, 1, 590 }, 
  { 0x1d62c, 1, 2660 }, 
  { 0x1d62d, 1, 610 }, 
  { 0x1d62e, 1, 2326 }, 
  { 0x1d62f, 1, 2181 }, 
  { 0x1d630, 1, 14 }, 
  { 0x1d631, 1, 2661 }, 
  { 0x1d632, 1, 2662 }, 
  { 0x1d633, 1, 591 }, 
  { 0x1d634, 1, 356 }, 
  { 0x1d635, 1, 2663 }, 
  { 0x1d636, 1, 2664 }, 
  { 0x1d637, 1, 2308 }, 
  { 0x1d638, 1, 595 }, 
  { 0x1d639, 1, 611 }, 
  { 0x1d63a, 1, 596 }, 
  { 0x1d63b, 1, 2665 }, 
  { 0x1d63c, 1, 2649 }, 
  { 0x1d63d, 1, 2223 }, 
  { 0x1d63e, 1, 2190 }, 
  { 0x1d63f, 1, 2236 }, 
  { 0x1d640, 1, 2225 }, 
  { 0x1d641, 1, 2226 }, 
  { 0x1d642, 1, 2650 }, 
  { 0x1d643, 1, 2203 }, 
  { 0x1d644, 1, 2205 }, 
  { 0x1d645, 1, 2651 }, 
  { 0x1d646, 1, 2222 }, 
  { 0x1d647, 1, 2206 }, 
  { 0x1d648, 1, 2227 }, 
  { 0x1d649, 1, 2207 }, 
  { 0x1d64a, 1, 2652 }, 
  { 0x1d64b, 1, 2210 }, 
  { 0x1d64c, 1, 2211 }, 
  { 0x1d64d, 1, 2212 }, 
  { 0x1d64e, 1, 2653 }, 
  { 0x1d64f, 1, 2654 }, 
  { 0x1d650, 1, 2655 }, 
  { 0x1d651, 1, 2283 }, 
  { 0x1d652, 1, 2656 }, 
  { 0x1d653, 1, 2295 }, 
  { 0x1d654, 1, 2657 }, 
  { 0x1d655, 1, 2220 }, 
  { 0x1d656, 1, 3 }, 
  { 0x1d657, 1, 2658 }, 
  { 0x1d658, 1, 2325 }, 
  { 0x1d659, 1, 2237 }, 
  { 0x1d65a, 1, 2224 }, 
  { 0x1d65b, 1, 2659 }, 
  { 0x1d65c, 1, 2202 }, 
  { 0x1d65d, 1, 588 }, 
  { 0x1d65e, 1, 2169 }, 
  { 0x1d65f, 1, 590 }, 
  { 0x1d660, 1, 2660 }, 
  { 0x1d661, 1, 610 }, 
  { 0x1d662, 1, 2326 }, 
  { 0x1d663, 1, 2181 }, 
  { 0x1d664, 1, 14 }, 
  { 0x1d665, 1, 2661 }, 
  { 0x1d666, 1, 2662 }, 
  { 0x1d667, 1, 591 }, 
  { 0x1d668, 1, 356 }, 
  { 0x1d669, 1, 2663 }, 
  { 0x1d66a, 1, 2664 }, 
  { 0x1d66b, 1, 2308 }, 
  { 0x1d66c, 1, 595 }, 
  { 0x1d66d, 1, 611 }, 
  { 0x1d66e, 1, 596 }, 
  { 0x1d66f, 1, 2665 }, 
  { 0x1d670, 1, 2649 }, 
  { 0x1d671, 1, 2223 }, 
  { 0x1d672, 1, 2190 }, 
  { 0x1d673, 1, 2236 }, 
  { 0x1d674, 1, 2225 }, 
  { 0x1d675, 1, 2226 }, 
  { 0x1d676, 1, 2650 }, 
  { 0x1d677, 1, 2203 }, 
  { 0x1d678, 1, 2205 }, 
  { 0x1d679, 1, 2651 }, 
  { 0x1d67a, 1, 2222 }, 
  { 0x1d67b, 1, 2206 }, 
  { 0x1d67c, 1, 2227 }, 
  { 0x1d67d, 1, 2207 }, 
  { 0x1d67e, 1, 2652 }, 
  { 0x1d67f, 1, 2210 }, 
  { 0x1d680, 1, 2211 }, 
  { 0x1d681, 1, 2212 }, 
  { 0x1d682, 1, 2653 }, 
  { 0x1d683, 1, 2654 }, 
  { 0x1d684, 1, 2655 }, 
  { 0x1d685, 1, 2283 }, 
  { 0x1d686, 1, 2656 }, 
  { 0x1d687, 1, 2295 }, 
  { 0x1d688, 1, 2657 }, 
  { 0x1d689, 1, 2220 }, 
  { 0x1d68a, 1, 3 }, 
  { 0x1d68b, 1, 2658 }, 
  { 0x1d68c, 1, 2325 }, 
  { 0x1d68d, 1, 2237 }, 
  { 0x1d68e, 1, 2224 }, 
  { 0x1d68f, 1, 2659 }, 
  { 0x1d690, 1, 2202 }, 
  { 0x1d691, 1, 588 }, 
  { 0x1d692, 1, 2169 }, 
  { 0x1d693, 1, 590 }, 
  { 0x1d694, 1, 2660 }, 
  { 0x1d695, 1, 610 }, 
  { 0x1d696, 1, 2326 }, 
  { 0x1d697, 1, 2181 }, 
  { 0x1d698, 1, 14 }, 
  { 0x1d699, 1, 2661 }, 
  { 0x1d69a, 1, 2662 }, 
  { 0x1d69b, 1, 591 }, 
  { 0x1d69c, 1, 356 }, 
  { 0x1d69d, 1, 2663 }, 
  { 0x1d69e, 1, 2664 }, 
  { 0x1d69f, 1, 2308 }, 
  { 0x1d6a0, 1, 595 }, 
  { 0x1d6a1, 1, 611 }, 
  { 0x1d6a2, 1, 596 }, 
  { 0x1d6a3, 1, 2665 }, 
  { 0x1d6a8, 1, 5630 }, 
  { 0x1d6a9, 1, 5631 }, 
  { 0x1d6aa, 1, 2233 }, 
  { 0x1d6ab, 1, 5632 }, 
  { 0x1d6ac, 1, 5633 }, 
  { 0x1d6ad, 1, 5634 }, 
  { 0x1d6ae, 1, 5635 }, 
  { 0x1d6af, 1, 676 }, 
  { 0x1d6b0, 1, 5636 }, 
  { 0x1d6b1, 1, 5637 }, 
  { 0x1d6b2, 1, 5638 }, 
  { 0x1d6b3, 1, 5639 }, 
  { 0x1d6b4, 1, 5640 }, 
  { 0x1d6b5, 1, 5641 }, 
  { 0x1d6b6, 1, 5642 }, 
  { 0x1d6b7, 1, 2234 }, 
  { 0x1d6b8, 1, 5643 }, 
  { 0x1d6b9, 1, 676 }, 
  { 0x1d6ba, 1, 5644 }, 
  { 0x1d6bb, 1, 5645 }, 
  { 0x1d6bc, 1, 670 }, 
  { 0x1d6bd, 1, 5646 }, 
  { 0x1d6be, 1, 5647 }, 
  { 0x1d6bf, 1, 5648 }, 
  { 0x1d6c0, 1, 2221 }, 
  { 0x1d6c1, 1, 5649 }, 
  { 0x1d6c2, 1, 5650 }, 
  { 0x1d6c3, 1, 668 }, 
  { 0x1d6c4, 1, 2232 }, 
  { 0x1d6c5, 1, 5651 }, 
  { 0x1d6c6, 1, 677 }, 
  { 0x1d6c7, 1, 5652 }, 
  { 0x1d6c8, 1, 5653 }, 
  { 0x1d6c9, 1, 669 }, 
  { 0x1d6ca, 1, 2025 }, 
  { 0x1d6cb, 1, 673 }, 
  { 0x1d6cc, 1, 5654 }, 
  { 0x1d6cd, 1, 10 }, 
  { 0x1d6ce, 1, 5655 }, 
  { 0x1d6cf, 1, 5656 }, 
  { 0x1d6d0, 1, 5657 }, 
  { 0x1d6d1, 1, 672 }, 
  { 0x1d6d2, 1, 674 }, 
  { 0x1d6d3, 1, 675 }, 
  { 0x1d6d4, 1, 5658 }, 
  { 0x1d6d5, 1, 5659 }, 
  { 0x1d6d6, 1, 5660 }, 
  { 0x1d6d7, 1, 671 }, 
  { 0x1d6d8, 1, 5661 }, 
  { 0x1d6d9, 1, 5662 }, 
  { 0x1d6da, 1, 5663 }, 
  { 0x1d6db, 1, 5664 }, 
  { 0x1d6dc, 1, 677 }, 
  { 0x1d6dd, 1, 669 }, 
  { 0x1d6de, 1, 673 }, 
  { 0x1d6df, 1, 671 }, 
  { 0x1d6e0, 1, 674 }, 
  { 0x1d6e1, 1, 672 }, 
  { 0x1d6e2, 1, 5630 }, 
  { 0x1d6e3, 1, 5631 }, 
  { 0x1d6e4, 1, 2233 }, 
  { 0x1d6e5, 1, 5632 }, 
  { 0x1d6e6, 1, 5633 }, 
  { 0x1d6e7, 1, 5634 }, 
  { 0x1d6e8, 1, 5635 }, 
  { 0x1d6e9, 1, 676 }, 
  { 0x1d6ea, 1, 5636 }, 
  { 0x1d6eb, 1, 5637 }, 
  { 0x1d6ec, 1, 5638 }, 
  { 0x1d6ed, 1, 5639 }, 
  { 0x1d6ee, 1, 5640 }, 
  { 0x1d6ef, 1, 5641 }, 
  { 0x1d6f0, 1, 5642 }, 
  { 0x1d6f1, 1, 2234 }, 
  { 0x1d6f2, 1, 5643 }, 
  { 0x1d6f3, 1, 676 }, 
  { 0x1d6f4, 1, 5644 }, 
  { 0x1d6f5, 1, 5645 }, 
  { 0x1d6f6, 1, 670 }, 
  { 0x1d6f7, 1, 5646 }, 
  { 0x1d6f8, 1, 5647 }, 
  { 0x1d6f9, 1, 5648 }, 
  { 0x1d6fa, 1, 2221 }, 
  { 0x1d6fb, 1, 5649 }, 
  { 0x1d6fc, 1, 5650 }, 
  { 0x1d6fd, 1, 668 }, 
  { 0x1d6fe, 1, 2232 }, 
  { 0x1d6ff, 1, 5651 }, 
  { 0x1d700, 1, 677 }, 
  { 0x1d701, 1, 5652 }, 
  { 0x1d702, 1, 5653 }, 
  { 0x1d703, 1, 669 }, 
  { 0x1d704, 1, 2025 }, 
  { 0x1d705, 1, 673 }, 
  { 0x1d706, 1, 5654 }, 
  { 0x1d707, 1, 10 }, 
  { 0x1d708, 1, 5655 }, 
  { 0x1d709, 1, 5656 }, 
  { 0x1d70a, 1, 5657 }, 
  { 0x1d70b, 1, 672 }, 
  { 0x1d70c, 1, 674 }, 
  { 0x1d70d, 1, 675 }, 
  { 0x1d70e, 1, 5658 }, 
  { 0x1d70f, 1, 5659 }, 
  { 0x1d710, 1, 5660 }, 
  { 0x1d711, 1, 671 }, 
  { 0x1d712, 1, 5661 }, 
  { 0x1d713, 1, 5662 }, 
  { 0x1d714, 1, 5663 }, 
  { 0x1d715, 1, 5664 }, 
  { 0x1d716, 1, 677 }, 
  { 0x1d717, 1, 669 }, 
  { 0x1d718, 1, 673 }, 
  { 0x1d719, 1, 671 }, 
  { 0x1d71a, 1, 674 }, 
  { 0x1d71b, 1, 672 }, 
  { 0x1d71c, 1, 5630 }, 
  { 0x1d71d, 1, 5631 }, 
  { 0x1d71e, 1, 2233 }, 
  { 0x1d71f, 1, 5632 }, 
  { 0x1d720, 1, 5633 }, 
  { 0x1d721, 1, 5634 }, 
  { 0x1d722, 1, 5635 }, 
  { 0x1d723, 1, 676 }, 
  { 0x1d724, 1, 5636 }, 
  { 0x1d725, 1, 5637 }, 
  { 0x1d726, 1, 5638 }, 
  { 0x1d727, 1, 5639 }, 
  { 0x1d728, 1, 5640 }, 
  { 0x1d729, 1, 5641 }, 
  { 0x1d72a, 1, 5642 }, 
  { 0x1d72b, 1, 2234 }, 
  { 0x1d72c, 1, 5643 }, 
  { 0x1d72d, 1, 676 }, 
  { 0x1d72e, 1, 5644 }, 
  { 0x1d72f, 1, 5645 }, 
  { 0x1d730, 1, 670 }, 
  { 0x1d731, 1, 5646 }, 
  { 0x1d732, 1, 5647 }, 
  { 0x1d733, 1, 5648 }, 
  { 0x1d734, 1, 2221 }, 
  { 0x1d735, 1, 5649 }, 
  { 0x1d736, 1, 5650 }, 
  { 0x1d737, 1, 668 }, 
  { 0x1d738, 1, 2232 }, 
  { 0x1d739, 1, 5651 }, 
  { 0x1d73a, 1, 677 }, 
  { 0x1d73b, 1, 5652 }, 
  { 0x1d73c, 1, 5653 }, 
  { 0x1d73d, 1, 669 }, 
  { 0x1d73e, 1, 2025 }, 
  { 0x1d73f, 1, 673 }, 
  { 0x1d740, 1, 5654 }, 
  { 0x1d741, 1, 10 }, 
  { 0x1d742, 1, 5655 }, 
  { 0x1d743, 1, 5656 }, 
  { 0x1d744, 1, 5657 }, 
  { 0x1d745, 1, 672 }, 
  { 0x1d746, 1, 674 }, 
  { 0x1d747, 1, 675 }, 
  { 0x1d748, 1, 5658 }, 
  { 0x1d749, 1, 5659 }, 
  { 0x1d74a, 1, 5660 }, 
  { 0x1d74b, 1, 671 }, 
  { 0x1d74c, 1, 5661 }, 
  { 0x1d74d, 1, 5662 }, 
  { 0x1d74e, 1, 5663 }, 
  { 0x1d74f, 1, 5664 }, 
  { 0x1d750, 1, 677 }, 
  { 0x1d751, 1, 669 }, 
  { 0x1d752, 1, 673 }, 
  { 0x1d753, 1, 671 }, 
  { 0x1d754, 1, 674 }, 
  { 0x1d755, 1, 672 }, 
  { 0x1d756, 1, 5630 }, 
  { 0x1d757, 1, 5631 }, 
  { 0x1d758, 1, 2233 }, 
  { 0x1d759, 1, 5632 }, 
  { 0x1d75a, 1, 5633 }, 
  { 0x1d75b, 1, 5634 }, 
  { 0x1d75c, 1, 5635 }, 
  { 0x1d75d, 1, 676 }, 
  { 0x1d75e, 1, 5636 }, 
  { 0x1d75f, 1, 5637 }, 
  { 0x1d760, 1, 5638 }, 
  { 0x1d761, 1, 5639 }, 
  { 0x1d762, 1, 5640 }, 
  { 0x1d763, 1, 5641 }, 
  { 0x1d764, 1, 5642 }, 
  { 0x1d765, 1, 2234 }, 
  { 0x1d766, 1, 5643 }, 
  { 0x1d767, 1, 676 }, 
  { 0x1d768, 1, 5644 }, 
  { 0x1d769, 1, 5645 }, 
  { 0x1d76a, 1, 670 }, 
  { 0x1d76b, 1, 5646 }, 
  { 0x1d76c, 1, 5647 }, 
  { 0x1d76d, 1, 5648 }, 
  { 0x1d76e, 1, 2221 }, 
  { 0x1d76f, 1, 5649 }, 
  { 0x1d770, 1, 5650 }, 
  { 0x1d771, 1, 668 }, 
  { 0x1d772, 1, 2232 }, 
  { 0x1d773, 1, 5651 }, 
  { 0x1d774, 1, 677 }, 
  { 0x1d775, 1, 5652 }, 
  { 0x1d776, 1, 5653 }, 
  { 0x1d777, 1, 669 }, 
  { 0x1d778, 1, 2025 }, 
  { 0x1d779, 1, 673 }, 
  { 0x1d77a, 1, 5654 }, 
  { 0x1d77b, 1, 10 }, 
  { 0x1d77c, 1, 5655 }, 
  { 0x1d77d, 1, 5656 }, 
  { 0x1d77e, 1, 5657 }, 
  { 0x1d77f, 1, 672 }, 
  { 0x1d780, 1, 674 }, 
  { 0x1d781, 1, 675 }, 
  { 0x1d782, 1, 5658 }, 
  { 0x1d783, 1, 5659 }, 
  { 0x1d784, 1, 5660 }, 
  { 0x1d785, 1, 671 }, 
  { 0x1d786, 1, 5661 }, 
  { 0x1d787, 1, 5662 }, 
  { 0x1d788, 1, 5663 }, 
  { 0x1d789, 1, 5664 }, 
  { 0x1d78a, 1, 677 }, 
  { 0x1d78b, 1, 669 }, 
  { 0x1d78c, 1, 673 }, 
  { 0x1d78d, 1, 671 }, 
  { 0x1d78e, 1, 674 }, 
  { 0x1d78f, 1, 672 }, 
  { 0x1d790, 1, 5630 }, 
  { 0x1d791, 1, 5631 }, 
  { 0x1d792, 1, 2233 }, 
  { 0x1d793, 1, 5632 }, 
  { 0x1d794, 1, 5633 }, 
  { 0x1d795, 1, 5634 }, 
  { 0x1d796, 1, 5635 }, 
  { 0x1d797, 1, 676 }, 
  { 0x1d798, 1, 5636 }, 
  { 0x1d799, 1, 5637 }, 
  { 0x1d79a, 1, 5638 }, 
  { 0x1d79b, 1, 5639 }, 
  { 0x1d79c, 1, 5640 }, 
  { 0x1d79d, 1, 5641 }, 
  { 0x1d79e, 1, 5642 }, 
  { 0x1d79f, 1, 2234 }, 
  { 0x1d7a0, 1, 5643 }, 
  { 0x1d7a1, 1, 676 }, 
  { 0x1d7a2, 1, 5644 }, 
  { 0x1d7a3, 1, 5645 }, 
  { 0x1d7a4, 1, 670 }, 
  { 0x1d7a5, 1, 5646 }, 
  { 0x1d7a6, 1, 5647 }, 
  { 0x1d7a7, 1, 5648 }, 
  { 0x1d7a8, 1, 2221 }, 
  { 0x1d7a9, 1, 5649 }, 
  { 0x1d7aa, 1, 5650 }, 
  { 0x1d7ab, 1, 668 }, 
  { 0x1d7ac, 1, 2232 }, 
  { 0x1d7ad, 1, 5651 }, 
  { 0x1d7ae, 1, 677 }, 
  { 0x1d7af, 1, 5652 }, 
  { 0x1d7b0, 1, 5653 }, 
  { 0x1d7b1, 1, 669 }, 
  { 0x1d7b2, 1, 2025 }, 
  { 0x1d7b3, 1, 673 }, 
  { 0x1d7b4, 1, 5654 }, 
  { 0x1d7b5, 1, 10 }, 
  { 0x1d7b6, 1, 5655 }, 
  { 0x1d7b7, 1, 5656 }, 
  { 0x1d7b8, 1, 5657 }, 
  { 0x1d7b9, 1, 672 }, 
  { 0x1d7ba, 1, 674 }, 
  { 0x1d7bb, 1, 675 }, 
  { 0x1d7bc, 1, 5658 }, 
  { 0x1d7bd, 1, 5659 }, 
  { 0x1d7be, 1, 5660 }, 
  { 0x1d7bf, 1, 671 }, 
  { 0x1d7c0, 1, 5661 }, 
  { 0x1d7c1, 1, 5662 }, 
  { 0x1d7c2, 1, 5663 }, 
  { 0x1d7c3, 1, 5664 }, 
  { 0x1d7c4, 1, 677 }, 
  { 0x1d7c5, 1, 669 }, 
  { 0x1d7c6, 1, 673 }, 
  { 0x1d7c7, 1, 671 }, 
  { 0x1d7c8, 1, 674 }, 
  { 0x1d7c9, 1, 672 }, 
  { 0x1d7ce, 1, 2168 }, 
  { 0x1d7cf, 1, 13 }, 
  { 0x1d7d0, 1, 6 }, 
  { 0x1d7d1, 1, 7 }, 
  { 0x1d7d2, 1, 2170 }, 
  { 0x1d7d3, 1, 2171 }, 
  { 0x1d7d4, 1, 2172 }, 
  { 0x1d7d5, 1, 2173 }, 
  { 0x1d7d6, 1, 2174 }, 
  { 0x1d7d7, 1, 2175 }, 
  { 0x1d7d8, 1, 2168 }, 
  { 0x1d7d9, 1, 13 }, 
  { 0x1d7da, 1, 6 }, 
  { 0x1d7db, 1, 7 }, 
  { 0x1d7dc, 1, 2170 }, 
  { 0x1d7dd, 1, 2171 }, 
  { 0x1d7de, 1, 2172 }, 
  { 0x1d7df, 1, 2173 }, 
  { 0x1d7e0, 1, 2174 }, 
  { 0x1d7e1, 1, 2175 }, 
  { 0x1d7e2, 1, 2168 }, 
  { 0x1d7e3, 1, 13 }, 
  { 0x1d7e4, 1, 6 }, 
  { 0x1d7e5, 1, 7 }, 
  { 0x1d7e6, 1, 2170 }, 
  { 0x1d7e7, 1, 2171 }, 
  { 0x1d7e8, 1, 2172 }, 
  { 0x1d7e9, 1, 2173 }, 
  { 0x1d7ea, 1, 2174 }, 
  { 0x1d7eb, 1, 2175 }, 
  { 0x1d7ec, 1, 2168 }, 
  { 0x1d7ed, 1, 13 }, 
  { 0x1d7ee, 1, 6 }, 
  { 0x1d7ef, 1, 7 }, 
  { 0x1d7f0, 1, 2170 }, 
  { 0x1d7f1, 1, 2171 }, 
  { 0x1d7f2, 1, 2172 }, 
  { 0x1d7f3, 1, 2173 }, 
  { 0x1d7f4, 1, 2174 }, 
  { 0x1d7f5, 1, 2175 }, 
  { 0x1d7f6, 1, 2168 }, 
  { 0x1d7f7, 1, 13 }, 
  { 0x1d7f8, 1, 6 }, 
  { 0x1d7f9, 1, 7 }, 
  { 0x1d7fa, 1, 2170 }, 
  { 0x1d7fb, 1, 2171 }, 
  { 0x1d7fc, 1, 2172 }, 
  { 0x1d7fd, 1, 2173 }, 
  { 0x1d7fe, 1, 2174 }, 
  { 0x1d7ff, 1, 2175 }, 
  { 0x2f800, 1, 5665 }, 
  { 0x2f801, 1, 5666 }, 
  { 0x2f802, 1, 5667 }, 
  { 0x2f803, 1, 5668 }, 
  { 0x2f804, 1, 5669 }, 
  { 0x2f805, 1, 4549 }, 
  { 0x2f806, 1, 5670 }, 
  { 0x2f807, 1, 5671 }, 
  { 0x2f808, 1, 5672 }, 
  { 0x2f809, 1, 5673 }, 
  { 0x2f80a, 1, 4550 }, 
  { 0x2f80b, 1, 5674 }, 
  { 0x2f80c, 1, 5675 }, 
  { 0x2f80d, 1, 5676 }, 
  { 0x2f80e, 1, 4551 }, 
  { 0x2f80f, 1, 5677 }, 
  { 0x2f810, 1, 5678 }, 
  { 0x2f811, 1, 5679 }, 
  { 0x2f812, 1, 5680 }, 
  { 0x2f813, 1, 5681 }, 
  { 0x2f814, 1, 5682 }, 
  { 0x2f815, 1, 5683 }, 
  { 0x2f816, 1, 5684 }, 
  { 0x2f817, 1, 5685 }, 
  { 0x2f818, 1, 5686 }, 
  { 0x2f819, 1, 5687 }, 
  { 0x2f81a, 1, 5688 }, 
  { 0x2f81b, 1, 5689 }, 
  { 0x2f81c, 1, 5690 }, 
  { 0x2f81d, 1, 2698 }, 
  { 0x2f81e, 1, 5691 }, 
  { 0x2f81f, 1, 5692 }, 
  { 0x2f820, 1, 5693 }, 
  { 0x2f821, 1, 5694 }, 
  { 0x2f822, 1, 5695 }, 
  { 0x2f823, 1, 5696 }, 
  { 0x2f824, 1, 5697 }, 
  { 0x2f825, 1, 5698 }, 
  { 0x2f826, 1, 4552 }, 
  { 0x2f827, 1, 4553 }, 
  { 0x2f828, 1, 5699 }, 
  { 0x2f829, 1, 5700 }, 
  { 0x2f82a, 1, 5701 }, 
  { 0x2f82b, 1, 4372 }, 
  { 0x2f82c, 1, 5702 }, 
  { 0x2f82d, 1, 4554 }, 
  { 0x2f82e, 1, 5703 }, 
  { 0x2f82f, 1, 5704 }, 
  { 0x2f830, 1, 5705 }, 
  { 0x2f831, 1, 5706 }, 
  { 0x2f832, 1, 5706 }, 
  { 0x2f833, 1, 5706 }, 
  { 0x2f834, 1, 5707 }, 
  { 0x2f835, 1, 5708 }, 
  { 0x2f836, 1, 5709 }, 
  { 0x2f837, 1, 5710 }, 
  { 0x2f838, 1, 5711 }, 
  { 0x2f839, 1, 5712 }, 
  { 0x2f83a, 1, 5713 }, 
  { 0x2f83b, 1, 5714 }, 
  { 0x2f83c, 1, 5715 }, 
  { 0x2f83d, 1, 5716 }, 
  { 0x2f83e, 1, 5717 }, 
  { 0x2f83f, 1, 5718 }, 
  { 0x2f840, 1, 5719 }, 
  { 0x2f841, 1, 5720 }, 
  { 0x2f842, 1, 5721 }, 
  { 0x2f843, 1, 5722 }, 
  { 0x2f844, 1, 5723 }, 
  { 0x2f845, 1, 5724 }, 
  { 0x2f846, 1, 5724 }, 
  { 0x2f847, 1, 5725 }, 
  { 0x2f848, 1, 5726 }, 
  { 0x2f849, 1, 5727 }, 
  { 0x2f84a, 1, 5728 }, 
  { 0x2f84b, 1, 5729 }, 
  { 0x2f84c, 1, 4556 }, 
  { 0x2f84d, 1, 5730 }, 
  { 0x2f84e, 1, 5731 }, 
  { 0x2f84f, 1, 5732 }, 
  { 0x2f850, 1, 4518 }, 
  { 0x2f851, 1, 5733 }, 
  { 0x2f852, 1, 5734 }, 
  { 0x2f853, 1, 5735 }, 
  { 0x2f854, 1, 5736 }, 
  { 0x2f855, 1, 5737 }, 
  { 0x2f856, 1, 5738 }, 
  { 0x2f857, 1, 5739 }, 
  { 0x2f858, 1, 5740 }, 
  { 0x2f859, 1, 5741 }, 
  { 0x2f85a, 1, 5742 }, 
  { 0x2f85b, 1, 5743 }, 
  { 0x2f85c, 1, 5744 }, 
  { 0x2f85d, 1, 5745 }, 
  { 0x2f85e, 1, 5746 }, 
  { 0x2f85f, 1, 5747 }, 
  { 0x2f860, 1, 5748 }, 
  { 0x2f861, 1, 5749 }, 
  { 0x2f862, 1, 5750 }, 
  { 0x2f863, 1, 5751 }, 
  { 0x2f864, 1, 5752 }, 
  { 0x2f865, 1, 5753 }, 
  { 0x2f866, 1, 5754 }, 
  { 0x2f867, 1, 5755 }, 
  { 0x2f868, 1, 5756 }, 
  { 0x2f869, 1, 5757 }, 
  { 0x2f86a, 1, 5758 }, 
  { 0x2f86b, 1, 5758 }, 
  { 0x2f86c, 1, 5759 }, 
  { 0x2f86d, 1, 5760 }, 
  { 0x2f86e, 1, 5761 }, 
  { 0x2f86f, 1, 4368 }, 
  { 0x2f870, 1, 5762 }, 
  { 0x2f871, 1, 5763 }, 
  { 0x2f872, 1, 5764 }, 
  { 0x2f873, 1, 5765 }, 
  { 0x2f874, 1, 5766 }, 
  { 0x2f875, 1, 2724 }, 
  { 0x2f876, 1, 5767 }, 
  { 0x2f877, 1, 5768 }, 
  { 0x2f878, 1, 2726 }, 
  { 0x2f879, 1, 5769 }, 
  { 0x2f87a, 1, 5770 }, 
  { 0x2f87b, 1, 5771 }, 
  { 0x2f87c, 1, 5772 }, 
  { 0x2f87d, 1, 5773 }, 
  { 0x2f87e, 1, 5774 }, 
  { 0x2f87f, 1, 5775 }, 
  { 0x2f880, 1, 5776 }, 
  { 0x2f881, 1, 5777 }, 
  { 0x2f882, 1, 5778 }, 
  { 0x2f883, 1, 5779 }, 
  { 0x2f884, 1, 5780 }, 
  { 0x2f885, 1, 5781 }, 
  { 0x2f886, 1, 5782 }, 
  { 0x2f887, 1, 5783 }, 
  { 0x2f888, 1, 5784 }, 
  { 0x2f889, 1, 5785 }, 
  { 0x2f88a, 1, 5786 }, 
  { 0x2f88b, 1, 5787 }, 
  { 0x2f88c, 1, 5788 }, 
  { 0x2f88d, 1, 5789 }, 
  { 0x2f88e, 1, 4316 }, 
  { 0x2f88f, 1, 5790 }, 
  { 0x2f890, 1, 2736 }, 
  { 0x2f891, 1, 5791 }, 
  { 0x2f892, 1, 5791 }, 
  { 0x2f893, 1, 5792 }, 
  { 0x2f894, 1, 5793 }, 
  { 0x2f895, 1, 5793 }, 
  { 0x2f896, 1, 5794 }, 
  { 0x2f897, 1, 5795 }, 
  { 0x2f898, 1, 5796 }, 
  { 0x2f899, 1, 5797 }, 
  { 0x2f89a, 1, 5798 }, 
  { 0x2f89b, 1, 5799 }, 
  { 0x2f89c, 1, 5800 }, 
  { 0x2f89d, 1, 5801 }, 
  { 0x2f89e, 1, 5802 }, 
  { 0x2f89f, 1, 5803 }, 
  { 0x2f8a0, 1, 5804 }, 
  { 0x2f8a1, 1, 5805 }, 
  { 0x2f8a2, 1, 5806 }, 
  { 0x2f8a3, 1, 4561 }, 
  { 0x2f8a4, 1, 5807 }, 
  { 0x2f8a5, 1, 5808 }, 
  { 0x2f8a6, 1, 5809 }, 
  { 0x2f8a7, 1, 5810 }, 
  { 0x2f8a8, 1, 5811 }, 
  { 0x2f8a9, 1, 5810 }, 
  { 0x2f8aa, 1, 5812 }, 
  { 0x2f8ab, 1, 4563 }, 
  { 0x2f8ac, 1, 5813 }, 
  { 0x2f8ad, 1, 5814 }, 
  { 0x2f8ae, 1, 5815 }, 
  { 0x2f8af, 1, 5816 }, 
  { 0x2f8b0, 1, 4564 }, 
  { 0x2f8b1, 1, 4289 }, 
  { 0x2f8b2, 1, 5817 }, 
  { 0x2f8b3, 1, 5818 }, 
  { 0x2f8b4, 1, 5819 }, 
  { 0x2f8b5, 1, 5820 }, 
  { 0x2f8b6, 1, 5821 }, 
  { 0x2f8b7, 1, 5822 }, 
  { 0x2f8b8, 1, 5823 }, 
  { 0x2f8b9, 1, 5824 }, 
  { 0x2f8ba, 1, 5825 }, 
  { 0x2f8bb, 1, 5826 }, 
  { 0x2f8bc, 1, 5827 }, 
  { 0x2f8bd, 1, 5828 }, 
  { 0x2f8be, 1, 5829 }, 
  { 0x2f8bf, 1, 5830 }, 
  { 0x2f8c0, 1, 5831 }, 
  { 0x2f8c1, 1, 5832 }, 
  { 0x2f8c2, 1, 5833 }, 
  { 0x2f8c3, 1, 5834 }, 
  { 0x2f8c4, 1, 5835 }, 
  { 0x2f8c5, 1, 5836 }, 
  { 0x2f8c6, 1, 5837 }, 
  { 0x2f8c7, 1, 5838 }, 
  { 0x2f8c8, 1, 4565 }, 
  { 0x2f8c9, 1, 5839 }, 
  { 0x2f8ca, 1, 5840 }, 
  { 0x2f8cb, 1, 5841 }, 
  { 0x2f8cc, 1, 5842 }, 
  { 0x2f8cd, 1, 5843 }, 
  { 0x2f8ce, 1, 5844 }, 
  { 0x2f8cf, 1, 4567 }, 
  { 0x2f8d0, 1, 5845 }, 
  { 0x2f8d1, 1, 5846 }, 
  { 0x2f8d2, 1, 5847 }, 
  { 0x2f8d3, 1, 5848 }, 
  { 0x2f8d4, 1, 5849 }, 
  { 0x2f8d5, 1, 5850 }, 
  { 0x2f8d6, 1, 5851 }, 
  { 0x2f8d7, 1, 5852 }, 
  { 0x2f8d8, 1, 4317 }, 
  { 0x2f8d9, 1, 5853 }, 
  { 0x2f8da, 1, 5854 }, 
  { 0x2f8db, 1, 5855 }, 
  { 0x2f8dc, 1, 5856 }, 
  { 0x2f8dd, 1, 5857 }, 
  { 0x2f8de, 1, 5858 }, 
  { 0x2f8df, 1, 5859 }, 
  { 0x2f8e0, 1, 5860 }, 
  { 0x2f8e1, 1, 5861 }, 
  { 0x2f8e2, 1, 4568 }, 
  { 0x2f8e3, 1, 5862 }, 
  { 0x2f8e4, 1, 5863 }, 
  { 0x2f8e5, 1, 5864 }, 
  { 0x2f8e6, 1, 5865 }, 
  { 0x2f8e7, 1, 5866 }, 
  { 0x2f8e8, 1, 5867 }, 
  { 0x2f8e9, 1, 5868 }, 
  { 0x2f8ea, 1, 5869 }, 
  { 0x2f8eb, 1, 5870 }, 
  { 0x2f8ec, 1, 5871 }, 
  { 0x2f8ed, 1, 5872 }, 
  { 0x2f8ee, 1, 5873 }, 
  { 0x2f8ef, 1, 5874 }, 
  { 0x2f8f0, 1, 5875 }, 
  { 0x2f8f1, 1, 5876 }, 
  { 0x2f8f2, 1, 5877 }, 
  { 0x2f8f3, 1, 5878 }, 
  { 0x2f8f4, 1, 5879 }, 
  { 0x2f8f5, 1, 4385 }, 
  { 0x2f8f6, 1, 5880 }, 
  { 0x2f8f7, 1, 5881 }, 
  { 0x2f8f8, 1, 5882 }, 
  { 0x2f8f9, 1, 5883 }, 
  { 0x2f8fa, 1, 5884 }, 
  { 0x2f8fb, 1, 5885 }, 
  { 0x2f8fc, 1, 5886 }, 
  { 0x2f8fd, 1, 5887 }, 
  { 0x2f8fe, 1, 5888 }, 
  { 0x2f8ff, 1, 5889 }, 
  { 0x2f900, 1, 5890 }, 
  { 0x2f901, 1, 4569 }, 
  { 0x2f902, 1, 4468 }, 
  { 0x2f903, 1, 5891 }, 
  { 0x2f904, 1, 5892 }, 
  { 0x2f905, 1, 5893 }, 
  { 0x2f906, 1, 5894 }, 
  { 0x2f907, 1, 5895 }, 
  { 0x2f908, 1, 5896 }, 
  { 0x2f909, 1, 5897 }, 
  { 0x2f90a, 1, 5898 }, 
  { 0x2f90b, 1, 5899 }, 
  { 0x2f90c, 1, 5900 }, 
  { 0x2f90d, 1, 5901 }, 
  { 0x2f90e, 1, 5902 }, 
  { 0x2f90f, 1, 5903 }, 
  { 0x2f910, 1, 5904 }, 
  { 0x2f911, 1, 5905 }, 
  { 0x2f912, 1, 5906 }, 
  { 0x2f913, 1, 5907 }, 
  { 0x2f914, 1, 5908 }, 
  { 0x2f915, 1, 5909 }, 
  { 0x2f916, 1, 5910 }, 
  { 0x2f917, 1, 5911 }, 
  { 0x2f918, 1, 5912 }, 
  { 0x2f919, 1, 5913 }, 
  { 0x2f91a, 1, 5914 }, 
  { 0x2f91b, 1, 5915 }, 
  { 0x2f91c, 1, 5916 }, 
  { 0x2f91d, 1, 5917 }, 
  { 0x2f91e, 1, 5918 }, 
  { 0x2f91f, 1, 5919 }, 
  { 0x2f920, 1, 5920 }, 
  { 0x2f921, 1, 5921 }, 
  { 0x2f922, 1, 5922 }, 
  { 0x2f923, 1, 5923 }, 
  { 0x2f924, 1, 5924 }, 
  { 0x2f925, 1, 5925 }, 
  { 0x2f926, 1, 5926 }, 
  { 0x2f927, 1, 5927 }, 
  { 0x2f928, 1, 5928 }, 
  { 0x2f929, 1, 5929 }, 
  { 0x2f92a, 1, 5930 }, 
  { 0x2f92b, 1, 5931 }, 
  { 0x2f92c, 1, 5932 }, 
  { 0x2f92d, 1, 5932 }, 
  { 0x2f92e, 1, 5933 }, 
  { 0x2f92f, 1, 5934 }, 
  { 0x2f930, 1, 5935 }, 
  { 0x2f931, 1, 5936 }, 
  { 0x2f932, 1, 5937 }, 
  { 0x2f933, 1, 5938 }, 
  { 0x2f934, 1, 5939 }, 
  { 0x2f935, 1, 5940 }, 
  { 0x2f936, 1, 5941 }, 
  { 0x2f937, 1, 5942 }, 
  { 0x2f938, 1, 4371 }, 
  { 0x2f939, 1, 5943 }, 
  { 0x2f93a, 1, 5944 }, 
  { 0x2f93b, 1, 5945 }, 
  { 0x2f93c, 1, 5946 }, 
  { 0x2f93d, 1, 5947 }, 
  { 0x2f93e, 1, 5948 }, 
  { 0x2f93f, 1, 5949 }, 
  { 0x2f940, 1, 5950 }, 
  { 0x2f941, 1, 5951 }, 
  { 0x2f942, 1, 5952 }, 
  { 0x2f943, 1, 5953 }, 
  { 0x2f944, 1, 5954 }, 
  { 0x2f945, 1, 5955 }, 
  { 0x2f946, 1, 5956 }, 
  { 0x2f947, 1, 5956 }, 
  { 0x2f948, 1, 5957 }, 
  { 0x2f949, 1, 5958 }, 
  { 0x2f94a, 1, 5959 }, 
  { 0x2f94b, 1, 5960 }, 
  { 0x2f94c, 1, 5961 }, 
  { 0x2f94d, 1, 5962 }, 
  { 0x2f94e, 1, 5963 }, 
  { 0x2f94f, 1, 4334 }, 
  { 0x2f950, 1, 5964 }, 
  { 0x2f951, 1, 5965 }, 
  { 0x2f952, 1, 5966 }, 
  { 0x2f953, 1, 4579 }, 
  { 0x2f954, 1, 5967 }, 
  { 0x2f955, 1, 5968 }, 
  { 0x2f956, 1, 4538 }, 
  { 0x2f957, 1, 5969 }, 
  { 0x2f958, 1, 5970 }, 
  { 0x2f959, 1, 4582 }, 
  { 0x2f95a, 1, 5971 }, 
  { 0x2f95b, 1, 5972 }, 
  { 0x2f95c, 1, 5973 }, 
  { 0x2f95d, 1, 5974 }, 
  { 0x2f95e, 1, 5974 }, 
  { 0x2f95f, 1, 5975 }, 
  { 0x2f960, 1, 5976 }, 
  { 0x2f961, 1, 5977 }, 
  { 0x2f962, 1, 5978 }, 
  { 0x2f963, 1, 5979 }, 
  { 0x2f964, 1, 5980 }, 
  { 0x2f965, 1, 5981 }, 
  { 0x2f966, 1, 5982 }, 
  { 0x2f967, 1, 5983 }, 
  { 0x2f968, 1, 5984 }, 
  { 0x2f969, 1, 5985 }, 
  { 0x2f96a, 1, 5986 }, 
  { 0x2f96b, 1, 5987 }, 
  { 0x2f96c, 1, 5988 }, 
  { 0x2f96d, 1, 5989 }, 
  { 0x2f96e, 1, 5990 }, 
  { 0x2f96f, 1, 5991 }, 
  { 0x2f970, 1, 5992 }, 
  { 0x2f971, 1, 5993 }, 
  { 0x2f972, 1, 5994 }, 
  { 0x2f973, 1, 5995 }, 
  { 0x2f974, 1, 5996 }, 
  { 0x2f975, 1, 5997 }, 
  { 0x2f976, 1, 5998 }, 
  { 0x2f977, 1, 5999 }, 
  { 0x2f978, 1, 6000 }, 
  { 0x2f979, 1, 6001 }, 
  { 0x2f97a, 1, 4588 }, 
  { 0x2f97b, 1, 6002 }, 
  { 0x2f97c, 1, 6003 }, 
  { 0x2f97d, 1, 6004 }, 
  { 0x2f97e, 1, 6005 }, 
  { 0x2f97f, 1, 6006 }, 
  { 0x2f980, 1, 6007 }, 
  { 0x2f981, 1, 6008 }, 
  { 0x2f982, 1, 6009 }, 
  { 0x2f983, 1, 6010 }, 
  { 0x2f984, 1, 6011 }, 
  { 0x2f985, 1, 6012 }, 
  { 0x2f986, 1, 6013 }, 
  { 0x2f987, 1, 6014 }, 
  { 0x2f988, 1, 6015 }, 
  { 0x2f989, 1, 6016 }, 
  { 0x2f98a, 1, 6017 }, 
  { 0x2f98b, 1, 5792 }, 
  { 0x2f98c, 1, 6018 }, 
  { 0x2f98d, 1, 6019 }, 
  { 0x2f98e, 1, 6020 }, 
  { 0x2f98f, 1, 6021 }, 
  { 0x2f990, 1, 6022 }, 
  { 0x2f991, 1, 6023 }, 
  { 0x2f992, 1, 6024 }, 
  { 0x2f993, 1, 6025 }, 
  { 0x2f994, 1, 6026 }, 
  { 0x2f995, 1, 6027 }, 
  { 0x2f996, 1, 6028 }, 
  { 0x2f997, 1, 6029 }, 
  { 0x2f998, 1, 4388 }, 
  { 0x2f999, 1, 6030 }, 
  { 0x2f99a, 1, 6031 }, 
  { 0x2f99b, 1, 6032 }, 
  { 0x2f99c, 1, 6033 }, 
  { 0x2f99d, 1, 6034 }, 
  { 0x2f99e, 1, 6035 }, 
  { 0x2f99f, 1, 4591 }, 
  { 0x2f9a0, 1, 6036 }, 
  { 0x2f9a1, 1, 6037 }, 
  { 0x2f9a2, 1, 6038 }, 
  { 0x2f9a3, 1, 6039 }, 
  { 0x2f9a4, 1, 6040 }, 
  { 0x2f9a5, 1, 6041 }, 
  { 0x2f9a6, 1, 6042 }, 
  { 0x2f9a7, 1, 6043 }, 
  { 0x2f9a8, 1, 6044 }, 
  { 0x2f9a9, 1, 6045 }, 
  { 0x2f9aa, 1, 6046 }, 
  { 0x2f9ab, 1, 6047 }, 
  { 0x2f9ac, 1, 6048 }, 
  { 0x2f9ad, 1, 6049 }, 
  { 0x2f9ae, 1, 6050 }, 
  { 0x2f9af, 1, 6051 }, 
  { 0x2f9b0, 1, 6052 }, 
  { 0x2f9b1, 1, 6053 }, 
  { 0x2f9b2, 1, 6054 }, 
  { 0x2f9b3, 1, 6055 }, 
  { 0x2f9b4, 1, 4329 }, 
  { 0x2f9b5, 1, 6056 }, 
  { 0x2f9b6, 1, 6057 }, 
  { 0x2f9b7, 1, 6058 }, 
  { 0x2f9b8, 1, 6059 }, 
  { 0x2f9b9, 1, 6060 }, 
  { 0x2f9ba, 1, 6061 }, 
  { 0x2f9bb, 1, 6062 }, 
  { 0x2f9bc, 1, 6063 }, 
  { 0x2f9bd, 1, 6064 }, 
  { 0x2f9be, 1, 6065 }, 
  { 0x2f9bf, 1, 6066 }, 
  { 0x2f9c0, 1, 6067 }, 
  { 0x2f9c1, 1, 6068 }, 
  { 0x2f9c2, 1, 6069 }, 
  { 0x2f9c3, 1, 6070 }, 
  { 0x2f9c4, 1, 2826 }, 
  { 0x2f9c5, 1, 6071 }, 
  { 0x2f9c6, 1, 6072 }, 
  { 0x2f9c7, 1, 6073 }, 
  { 0x2f9c8, 1, 6074 }, 
  { 0x2f9c9, 1, 6075 }, 
  { 0x2f9ca, 1, 6076 }, 
  { 0x2f9cb, 1, 6077 }, 
  { 0x2f9cc, 1, 6078 }, 
  { 0x2f9cd, 1, 6079 }, 
  { 0x2f9ce, 1, 6080 }, 
  { 0x2f9cf, 1, 6081 }, 
  { 0x2f9d0, 1, 6082 }, 
  { 0x2f9d1, 1, 6083 }, 
  { 0x2f9d2, 1, 2833 }, 
  { 0x2f9d3, 1, 6084 }, 
  { 0x2f9d4, 1, 6085 }, 
  { 0x2f9d5, 1, 6086 }, 
  { 0x2f9d6, 1, 6087 }, 
  { 0x2f9d7, 1, 6088 }, 
  { 0x2f9d8, 1, 6089 }, 
  { 0x2f9d9, 1, 6090 }, 
  { 0x2f9da, 1, 6091 }, 
  { 0x2f9db, 1, 6092 }, 
  { 0x2f9dc, 1, 6093 }, 
  { 0x2f9dd, 1, 6094 }, 
  { 0x2f9de, 1, 6095 }, 
  { 0x2f9df, 1, 6096 }, 
  { 0x2f9e0, 1, 6097 }, 
  { 0x2f9e1, 1, 6098 }, 
  { 0x2f9e2, 1, 6099 }, 
  { 0x2f9e3, 1, 6100 }, 
  { 0x2f9e4, 1, 6101 }, 
  { 0x2f9e5, 1, 6102 }, 
  { 0x2f9e6, 1, 6103 }, 
  { 0x2f9e7, 1, 6104 }, 
  { 0x2f9e8, 1, 6105 }, 
  { 0x2f9e9, 1, 6106 }, 
  { 0x2f9ea, 1, 6107 }, 
  { 0x2f9eb, 1, 6108 }, 
  { 0x2f9ec, 1, 6109 }, 
  { 0x2f9ed, 1, 6110 }, 
  { 0x2f9ee, 1, 6111 }, 
  { 0x2f9ef, 1, 6112 }, 
  { 0x2f9f0, 1, 6113 }, 
  { 0x2f9f1, 1, 6114 }, 
  { 0x2f9f2, 1, 6115 }, 
  { 0x2f9f3, 1, 6116 }, 
  { 0x2f9f4, 1, 6117 }, 
  { 0x2f9f5, 1, 6118 }, 
  { 0x2f9f6, 1, 6119 }, 
  { 0x2f9f7, 1, 6120 }, 
  { 0x2f9f8, 1, 6121 }, 
  { 0x2f9f9, 1, 6122 }, 
  { 0x2f9fa, 1, 6123 }, 
  { 0x2f9fb, 1, 6124 }, 
  { 0x2f9fc, 1, 6125 }, 
  { 0x2f9fd, 1, 6126 }, 
  { 0x2f9fe, 1, 6127 }, 
  { 0x2f9ff, 1, 6127 }, 
  { 0x2fa00, 1, 6128 }, 
  { 0x2fa01, 1, 6129 }, 
  { 0x2fa02, 1, 6130 }, 
  { 0x2fa03, 1, 6131 }, 
  { 0x2fa04, 1, 6132 }, 
  { 0x2fa05, 1, 6133 }, 
  { 0x2fa06, 1, 6134 }, 
  { 0x2fa07, 1, 6135 }, 
  { 0x2fa08, 1, 6136 }, 
  { 0x2fa09, 1, 6137 }, 
  { 0x2fa0a, 1, 6138 }, 
  { 0x2fa0b, 1, 6139 }, 
  { 0x2fa0c, 1, 6140 }, 
  { 0x2fa0d, 1, 6141 }, 
  { 0x2fa0e, 1, 6142 }, 
  { 0x2fa0f, 1, 6143 }, 
  { 0x2fa10, 1, 6144 }, 
  { 0x2fa11, 1, 6145 }, 
  { 0x2fa12, 1, 6146 }, 
  { 0x2fa13, 1, 6147 }, 
  { 0x2fa14, 1, 6148 }, 
  { 0x2fa15, 1, 2881 }, 
  { 0x2fa16, 1, 6149 }, 
  { 0x2fa17, 1, 2885 }, 
  { 0x2fa18, 1, 6150 }, 
  { 0x2fa19, 1, 6151 }, 
  { 0x2fa1a, 1, 6152 }, 
  { 0x2fa1b, 1, 6153 }, 
  { 0x2fa1c, 1, 2890 }, 
  { 0x2fa1d, 1, 6154 }
};

static const Unicode decomp_expansion[] = {
  0x20 /* offset 0 */ , 
  0x20, 0x308 /* offset 1 */ , 
  0x61 /* offset 3 */ , 
  0x20, 0x304 /* offset 4 */ , 
  0x32 /* offset 6 */ , 
  0x33 /* offset 7 */ , 
  0x20, 0x301 /* offset 8 */ , 
  0x3bc /* offset 10 */ , 
  0x20, 0x327 /* offset 11 */ , 
  0x31 /* offset 13 */ , 
  0x6f /* offset 14 */ , 
  0x31, 0x2044, 0x34 /* offset 15 */ , 
  0x31, 0x2044, 0x32 /* offset 18 */ , 
  0x33, 0x2044, 0x34 /* offset 21 */ , 
  0x41, 0x300 /* offset 24 */ , 
  0x41, 0x301 /* offset 26 */ , 
  0x41, 0x302 /* offset 28 */ , 
  0x41, 0x303 /* offset 30 */ , 
  0x41, 0x308 /* offset 32 */ , 
  0x41, 0x30a /* offset 34 */ , 
  0x43, 0x327 /* offset 36 */ , 
  0x45, 0x300 /* offset 38 */ , 
  0x45, 0x301 /* offset 40 */ , 
  0x45, 0x302 /* offset 42 */ , 
  0x45, 0x308 /* offset 44 */ , 
  0x49, 0x300 /* offset 46 */ , 
  0x49, 0x301 /* offset 48 */ , 
  0x49, 0x302 /* offset 50 */ , 
  0x49, 0x308 /* offset 52 */ , 
  0x4e, 0x303 /* offset 54 */ , 
  0x4f, 0x300 /* offset 56 */ , 
  0x4f, 0x301 /* offset 58 */ , 
  0x4f, 0x302 /* offset 60 */ , 
  0x4f, 0x303 /* offset 62 */ , 
  0x4f, 0x308 /* offset 64 */ , 
  0x55, 0x300 /* offset 66 */ , 
  0x55, 0x301 /* offset 68 */ , 
  0x55, 0x302 /* offset 70 */ , 
  0x55, 0x308 /* offset 72 */ , 
  0x59, 0x301 /* offset 74 */ , 
  0x61, 0x300 /* offset 76 */ , 
  0x61, 0x301 /* offset 78 */ , 
  0x61, 0x302 /* offset 80 */ , 
  0x61, 0x303 /* offset 82 */ , 
  0x61, 0x308 /* offset 84 */ , 
  0x61, 0x30a /* offset 86 */ , 
  0x63, 0x327 /* offset 88 */ , 
  0x65, 0x300 /* offset 90 */ , 
  0x65, 0x301 /* offset 92 */ , 
  0x65, 0x302 /* offset 94 */ , 
  0x65, 0x308 /* offset 96 */ , 
  0x69, 0x300 /* offset 98 */ , 
  0x69, 0x301 /* offset 100 */ , 
  0x69, 0x302 /* offset 102 */ , 
  0x69, 0x308 /* offset 104 */ , 
  0x6e, 0x303 /* offset 106 */ , 
  0x6f, 0x300 /* offset 108 */ , 
  0x6f, 0x301 /* offset 110 */ , 
  0x6f, 0x302 /* offset 112 */ , 
  0x6f, 0x303 /* offset 114 */ , 
  0x6f, 0x308 /* offset 116 */ , 
  0x75, 0x300 /* offset 118 */ , 
  0x75, 0x301 /* offset 120 */ , 
  0x75, 0x302 /* offset 122 */ , 
  0x75, 0x308 /* offset 124 */ , 
  0x79, 0x301 /* offset 126 */ , 
  0x79, 0x308 /* offset 128 */ , 
  0x41, 0x304 /* offset 130 */ , 
  0x61, 0x304 /* offset 132 */ , 
  0x41, 0x306 /* offset 134 */ , 
  0x61, 0x306 /* offset 136 */ , 
  0x41, 0x328 /* offset 138 */ , 
  0x61, 0x328 /* offset 140 */ , 
  0x43, 0x301 /* offset 142 */ , 
  0x63, 0x301 /* offset 144 */ , 
  0x43, 0x302 /* offset 146 */ , 
  0x63, 0x302 /* offset 148 */ , 
  0x43, 0x307 /* offset 150 */ , 
  0x63, 0x307 /* offset 152 */ , 
  0x43, 0x30c /* offset 154 */ , 
  0x63, 0x30c /* offset 156 */ , 
  0x44, 0x30c /* offset 158 */ , 
  0x64, 0x30c /* offset 160 */ , 
  0x45, 0x304 /* offset 162 */ , 
  0x65, 0x304 /* offset 164 */ , 
  0x45, 0x306 /* offset 166 */ , 
  0x65, 0x306 /* offset 168 */ , 
  0x45, 0x307 /* offset 170 */ , 
  0x65, 0x307 /* offset 172 */ , 
  0x45, 0x328 /* offset 174 */ , 
  0x65, 0x328 /* offset 176 */ , 
  0x45, 0x30c /* offset 178 */ , 
  0x65, 0x30c /* offset 180 */ , 
  0x47, 0x302 /* offset 182 */ , 
  0x67, 0x302 /* offset 184 */ , 
  0x47, 0x306 /* offset 186 */ , 
  0x67, 0x306 /* offset 188 */ , 
  0x47, 0x307 /* offset 190 */ , 
  0x67, 0x307 /* offset 192 */ , 
  0x47, 0x327 /* offset 194 */ , 
  0x67, 0x327 /* offset 196 */ , 
  0x48, 0x302 /* offset 198 */ , 
  0x68, 0x302 /* offset 200 */ , 
  0x49, 0x303 /* offset 202 */ , 
  0x69, 0x303 /* offset 204 */ , 
  0x49, 0x304 /* offset 206 */ , 
  0x69, 0x304 /* offset 208 */ , 
  0x49, 0x306 /* offset 210 */ , 
  0x69, 0x306 /* offset 212 */ , 
  0x49, 0x328 /* offset 214 */ , 
  0x69, 0x328 /* offset 216 */ , 
  0x49, 0x307 /* offset 218 */ , 
  0x49, 0x4a /* offset 220 */ , 
  0x69, 0x6a /* offset 222 */ , 
  0x4a, 0x302 /* offset 224 */ , 
  0x6a, 0x302 /* offset 226 */ , 
  0x4b, 0x327 /* offset 228 */ , 
  0x6b, 0x327 /* offset 230 */ , 
  0x4c, 0x301 /* offset 232 */ , 
  0x6c, 0x301 /* offset 234 */ , 
  0x4c, 0x327 /* offset 236 */ , 
  0x6c, 0x327 /* offset 238 */ , 
  0x4c, 0x30c /* offset 240 */ , 
  0x6c, 0x30c /* offset 242 */ , 
  0x4c, 0xb7 /* offset 244 */ , 
  0x6c, 0xb7 /* offset 246 */ , 
  0x4e, 0x301 /* offset 248 */ , 
  0x6e, 0x301 /* offset 250 */ , 
  0x4e, 0x327 /* offset 252 */ , 
  0x6e, 0x327 /* offset 254 */ , 
  0x4e, 0x30c /* offset 256 */ , 
  0x6e, 0x30c /* offset 258 */ , 
  0x2bc, 0x6e /* offset 260 */ , 
  0x4f, 0x304 /* offset 262 */ , 
  0x6f, 0x304 /* offset 264 */ , 
  0x4f, 0x306 /* offset 266 */ , 
  0x6f, 0x306 /* offset 268 */ , 
  0x4f, 0x30b /* offset 270 */ , 
  0x6f, 0x30b /* offset 272 */ , 
  0x52, 0x301 /* offset 274 */ , 
  0x72, 0x301 /* offset 276 */ , 
  0x52, 0x327 /* offset 278 */ , 
  0x72, 0x327 /* offset 280 */ , 
  0x52, 0x30c /* offset 282 */ , 
  0x72, 0x30c /* offset 284 */ , 
  0x53, 0x301 /* offset 286 */ , 
  0x73, 0x301 /* offset 288 */ , 
  0x53, 0x302 /* offset 290 */ , 
  0x73, 0x302 /* offset 292 */ , 
  0x53, 0x327 /* offset 294 */ , 
  0x73, 0x327 /* offset 296 */ , 
  0x53, 0x30c /* offset 298 */ , 
  0x73, 0x30c /* offset 300 */ , 
  0x54, 0x327 /* offset 302 */ , 
  0x74, 0x327 /* offset 304 */ , 
  0x54, 0x30c /* offset 306 */ , 
  0x74, 0x30c /* offset 308 */ , 
  0x55, 0x303 /* offset 310 */ , 
  0x75, 0x303 /* offset 312 */ , 
  0x55, 0x304 /* offset 314 */ , 
  0x75, 0x304 /* offset 316 */ , 
  0x55, 0x306 /* offset 318 */ , 
  0x75, 0x306 /* offset 320 */ , 
  0x55, 0x30a /* offset 322 */ , 
  0x75, 0x30a /* offset 324 */ , 
  0x55, 0x30b /* offset 326 */ , 
  0x75, 0x30b /* offset 328 */ , 
  0x55, 0x328 /* offset 330 */ , 
  0x75, 0x328 /* offset 332 */ , 
  0x57, 0x302 /* offset 334 */ , 
  0x77, 0x302 /* offset 336 */ , 
  0x59, 0x302 /* offset 338 */ , 
  0x79, 0x302 /* offset 340 */ , 
  0x59, 0x308 /* offset 342 */ , 
  0x5a, 0x301 /* offset 344 */ , 
  0x7a, 0x301 /* offset 346 */ , 
  0x5a, 0x307 /* offset 348 */ , 
  0x7a, 0x307 /* offset 350 */ , 
  0x5a, 0x30c /* offset 352 */ , 
  0x7a, 0x30c /* offset 354 */ , 
  0x73 /* offset 356 */ , 
  0x4f, 0x31b /* offset 357 */ , 
  0x6f, 0x31b /* offset 359 */ , 
  0x55, 0x31b /* offset 361 */ , 
  0x75, 0x31b /* offset 363 */ , 
  0x44, 0x5a, 0x30c /* offset 365 */ , 
  0x44, 0x7a, 0x30c /* offset 368 */ , 
  0x64, 0x7a, 0x30c /* offset 371 */ , 
  0x4c, 0x4a /* offset 374 */ , 
  0x4c, 0x6a /* offset 376 */ , 
  0x6c, 0x6a /* offset 378 */ , 
  0x4e, 0x4a /* offset 380 */ , 
  0x4e, 0x6a /* offset 382 */ , 
  0x6e, 0x6a /* offset 384 */ , 
  0x41, 0x30c /* offset 386 */ , 
  0x61, 0x30c /* offset 388 */ , 
  0x49, 0x30c /* offset 390 */ , 
  0x69, 0x30c /* offset 392 */ , 
  0x4f, 0x30c /* offset 394 */ , 
  0x6f, 0x30c /* offset 396 */ , 
  0x55, 0x30c /* offset 398 */ , 
  0x75, 0x30c /* offset 400 */ , 
  0x55, 0x308, 0x304 /* offset 402 */ , 
  0x75, 0x308, 0x304 /* offset 405 */ , 
  0x55, 0x308, 0x301 /* offset 408 */ , 
  0x75, 0x308, 0x301 /* offset 411 */ , 
  0x55, 0x308, 0x30c /* offset 414 */ , 
  0x75, 0x308, 0x30c /* offset 417 */ , 
  0x55, 0x308, 0x300 /* offset 420 */ , 
  0x75, 0x308, 0x300 /* offset 423 */ , 
  0x41, 0x308, 0x304 /* offset 426 */ , 
  0x61, 0x308, 0x304 /* offset 429 */ , 
  0x41, 0x307, 0x304 /* offset 432 */ , 
  0x61, 0x307, 0x304 /* offset 435 */ , 
  0xc6, 0x304 /* offset 438 */ , 
  0xe6, 0x304 /* offset 440 */ , 
  0x47, 0x30c /* offset 442 */ , 
  0x67, 0x30c /* offset 444 */ , 
  0x4b, 0x30c /* offset 446 */ , 
  0x6b, 0x30c /* offset 448 */ , 
  0x4f, 0x328 /* offset 450 */ , 
  0x6f, 0x328 /* offset 452 */ , 
  0x4f, 0x328, 0x304 /* offset 454 */ , 
  0x6f, 0x328, 0x304 /* offset 457 */ , 
  0x1b7, 0x30c /* offset 460 */ , 
  0x292, 0x30c /* offset 462 */ , 
  0x6a, 0x30c /* offset 464 */ , 
  0x44, 0x5a /* offset 466 */ , 
  0x44, 0x7a /* offset 468 */ , 
  0x64, 0x7a /* offset 470 */ , 
  0x47, 0x301 /* offset 472 */ , 
  0x67, 0x301 /* offset 474 */ , 
  0x4e, 0x300 /* offset 476 */ , 
  0x6e, 0x300 /* offset 478 */ , 
  0x41, 0x30a, 0x301 /* offset 480 */ , 
  0x61, 0x30a, 0x301 /* offset 483 */ , 
  0xc6, 0x301 /* offset 486 */ , 
  0xe6, 0x301 /* offset 488 */ , 
  0xd8, 0x301 /* offset 490 */ , 
  0xf8, 0x301 /* offset 492 */ , 
  0x41, 0x30f /* offset 494 */ , 
  0x61, 0x30f /* offset 496 */ , 
  0x41, 0x311 /* offset 498 */ , 
  0x61, 0x311 /* offset 500 */ , 
  0x45, 0x30f /* offset 502 */ , 
  0x65, 0x30f /* offset 504 */ , 
  0x45, 0x311 /* offset 506 */ , 
  0x65, 0x311 /* offset 508 */ , 
  0x49, 0x30f /* offset 510 */ , 
  0x69, 0x30f /* offset 512 */ , 
  0x49, 0x311 /* offset 514 */ , 
  0x69, 0x311 /* offset 516 */ , 
  0x4f, 0x30f /* offset 518 */ , 
  0x6f, 0x30f /* offset 520 */ , 
  0x4f, 0x311 /* offset 522 */ , 
  0x6f, 0x311 /* offset 524 */ , 
  0x52, 0x30f /* offset 526 */ , 
  0x72, 0x30f /* offset 528 */ , 
  0x52, 0x311 /* offset 530 */ , 
  0x72, 0x311 /* offset 532 */ , 
  0x55, 0x30f /* offset 534 */ , 
  0x75, 0x30f /* offset 536 */ , 
  0x55, 0x311 /* offset 538 */ , 
  0x75, 0x311 /* offset 540 */ , 
  0x53, 0x326 /* offset 542 */ , 
  0x73, 0x326 /* offset 544 */ , 
  0x54, 0x326 /* offset 546 */ , 
  0x74, 0x326 /* offset 548 */ , 
  0x48, 0x30c /* offset 550 */ , 
  0x68, 0x30c /* offset 552 */ , 
  0x41, 0x307 /* offset 554 */ , 
  0x61, 0x307 /* offset 556 */ , 
  0x45, 0x327 /* offset 558 */ , 
  0x65, 0x327 /* offset 560 */ , 
  0x4f, 0x308, 0x304 /* offset 562 */ , 
  0x6f, 0x308, 0x304 /* offset 565 */ , 
  0x4f, 0x303, 0x304 /* offset 568 */ , 
  0x6f, 0x303, 0x304 /* offset 571 */ , 
  0x4f, 0x307 /* offset 574 */ , 
  0x6f, 0x307 /* offset 576 */ , 
  0x4f, 0x307, 0x304 /* offset 578 */ , 
  0x6f, 0x307, 0x304 /* offset 581 */ , 
  0x59, 0x304 /* offset 584 */ , 
  0x79, 0x304 /* offset 586 */ , 
  0x68 /* offset 588 */ , 
  0x266 /* offset 589 */ , 
  0x6a /* offset 590 */ , 
  0x72 /* offset 591 */ , 
  0x279 /* offset 592 */ , 
  0x27b /* offset 593 */ , 
  0x281 /* offset 594 */ , 
  0x77 /* offset 595 */ , 
  0x79 /* offset 596 */ , 
  0x20, 0x306 /* offset 597 */ , 
  0x20, 0x307 /* offset 599 */ , 
  0x20, 0x30a /* offset 601 */ , 
  0x20, 0x328 /* offset 603 */ , 
  0x20, 0x303 /* offset 605 */ , 
  0x20, 0x30b /* offset 607 */ , 
  0x263 /* offset 609 */ , 
  0x6c /* offset 610 */ , 
  0x78 /* offset 611 */ , 
  0x295 /* offset 612 */ , 
  0x300 /* offset 613 */ , 
  0x301 /* offset 614 */ , 
  0x313 /* offset 615 */ , 
  0x308, 0x301 /* offset 616 */ , 
  0x2b9 /* offset 618 */ , 
  0x20, 0x345 /* offset 619 */ , 
  0x3b /* offset 621 */ , 
  0x20, 0x308, 0x301 /* offset 622 */ , 
  0x391, 0x301 /* offset 625 */ , 
  0xb7 /* offset 627 */ , 
  0x395, 0x301 /* offset 628 */ , 
  0x397, 0x301 /* offset 630 */ , 
  0x399, 0x301 /* offset 632 */ , 
  0x39f, 0x301 /* offset 634 */ , 
  0x3a5, 0x301 /* offset 636 */ , 
  0x3a9, 0x301 /* offset 638 */ , 
  0x3b9, 0x308, 0x301 /* offset 640 */ , 
  0x399, 0x308 /* offset 643 */ , 
  0x3a5, 0x308 /* offset 645 */ , 
  0x3b1, 0x301 /* offset 647 */ , 
  0x3b5, 0x301 /* offset 649 */ , 
  0x3b7, 0x301 /* offset 651 */ , 
  0x3b9, 0x301 /* offset 653 */ , 
  0x3c5, 0x308, 0x301 /* offset 655 */ , 
  0x3b9, 0x308 /* offset 658 */ , 
  0x3c5, 0x308 /* offset 660 */ , 
  0x3bf, 0x301 /* offset 662 */ , 
  0x3c5, 0x301 /* offset 664 */ , 
  0x3c9, 0x301 /* offset 666 */ , 
  0x3b2 /* offset 668 */ , 
  0x3b8 /* offset 669 */ , 
  0x3a5 /* offset 670 */ , 
  0x3c6 /* offset 671 */ , 
  0x3c0 /* offset 672 */ , 
  0x3ba /* offset 673 */ , 
  0x3c1 /* offset 674 */ , 
  0x3c2 /* offset 675 */ , 
  0x398 /* offset 676 */ , 
  0x3b5 /* offset 677 */ , 
  0x415, 0x300 /* offset 678 */ , 
  0x415, 0x308 /* offset 680 */ , 
  0x413, 0x301 /* offset 682 */ , 
  0x406, 0x308 /* offset 684 */ , 
  0x41a, 0x301 /* offset 686 */ , 
  0x418, 0x300 /* offset 688 */ , 
  0x423, 0x306 /* offset 690 */ , 
  0x418, 0x306 /* offset 692 */ , 
  0x438, 0x306 /* offset 694 */ , 
  0x435, 0x300 /* offset 696 */ , 
  0x435, 0x308 /* offset 698 */ , 
  0x433, 0x301 /* offset 700 */ , 
  0x456, 0x308 /* offset 702 */ , 
  0x43a, 0x301 /* offset 704 */ , 
  0x438, 0x300 /* offset 706 */ , 
  0x443, 0x306 /* offset 708 */ , 
  0x474, 0x30f /* offset 710 */ , 
  0x475, 0x30f /* offset 712 */ , 
  0x416, 0x306 /* offset 714 */ , 
  0x436, 0x306 /* offset 716 */ , 
  0x410, 0x306 /* offset 718 */ , 
  0x430, 0x306 /* offset 720 */ , 
  0x410, 0x308 /* offset 722 */ , 
  0x430, 0x308 /* offset 724 */ , 
  0x415, 0x306 /* offset 726 */ , 
  0x435, 0x306 /* offset 728 */ , 
  0x4d8, 0x308 /* offset 730 */ , 
  0x4d9, 0x308 /* offset 732 */ , 
  0x416, 0x308 /* offset 734 */ , 
  0x436, 0x308 /* offset 736 */ , 
  0x417, 0x308 /* offset 738 */ , 
  0x437, 0x308 /* offset 740 */ , 
  0x418, 0x304 /* offset 742 */ , 
  0x438, 0x304 /* offset 744 */ , 
  0x418, 0x308 /* offset 746 */ , 
  0x438, 0x308 /* offset 748 */ , 
  0x41e, 0x308 /* offset 750 */ , 
  0x43e, 0x308 /* offset 752 */ , 
  0x4e8, 0x308 /* offset 754 */ , 
  0x4e9, 0x308 /* offset 756 */ , 
  0x42d, 0x308 /* offset 758 */ , 
  0x44d, 0x308 /* offset 760 */ , 
  0x423, 0x304 /* offset 762 */ , 
  0x443, 0x304 /* offset 764 */ , 
  0x423, 0x308 /* offset 766 */ , 
  0x443, 0x308 /* offset 768 */ , 
  0x423, 0x30b /* offset 770 */ , 
  0x443, 0x30b /* offset 772 */ , 
  0x427, 0x308 /* offset 774 */ , 
  0x447, 0x308 /* offset 776 */ , 
  0x42b, 0x308 /* offset 778 */ , 
  0x44b, 0x308 /* offset 780 */ , 
  0x565, 0x582 /* offset 782 */ , 
  0x627, 0x653 /* offset 784 */ , 
  0x627, 0x654 /* offset 786 */ , 
  0x648, 0x654 /* offset 788 */ , 
  0x627, 0x655 /* offset 790 */ , 
  0x64a, 0x654 /* offset 792 */ , 
  0x627, 0x674 /* offset 794 */ , 
  0x648, 0x674 /* offset 796 */ , 
  0x6c7, 0x674 /* offset 798 */ , 
  0x64a, 0x674 /* offset 800 */ , 
  0x6d5, 0x654 /* offset 802 */ , 
  0x6c1, 0x654 /* offset 804 */ , 
  0x6d2, 0x654 /* offset 806 */ , 
  0x928, 0x93c /* offset 808 */ , 
  0x930, 0x93c /* offset 810 */ , 
  0x933, 0x93c /* offset 812 */ , 
  0x915, 0x93c /* offset 814 */ , 
  0x916, 0x93c /* offset 816 */ , 
  0x917, 0x93c /* offset 818 */ , 
  0x91c, 0x93c /* offset 820 */ , 
  0x921, 0x93c /* offset 822 */ , 
  0x922, 0x93c /* offset 824 */ , 
  0x92b, 0x93c /* offset 826 */ , 
  0x92f, 0x93c /* offset 828 */ , 
  0x9c7, 0x9be /* offset 830 */ , 
  0x9c7, 0x9d7 /* offset 832 */ , 
  0x9a1, 0x9bc /* offset 834 */ , 
  0x9a2, 0x9bc /* offset 836 */ , 
  0x9af, 0x9bc /* offset 838 */ , 
  0xa32, 0xa3c /* offset 840 */ , 
  0xa38, 0xa3c /* offset 842 */ , 
  0xa16, 0xa3c /* offset 844 */ , 
  0xa17, 0xa3c /* offset 846 */ , 
  0xa1c, 0xa3c /* offset 848 */ , 
  0xa2b, 0xa3c /* offset 850 */ , 
  0xb47, 0xb56 /* offset 852 */ , 
  0xb47, 0xb3e /* offset 854 */ , 
  0xb47, 0xb57 /* offset 856 */ , 
  0xb21, 0xb3c /* offset 858 */ , 
  0xb22, 0xb3c /* offset 860 */ , 
  0xb92, 0xbd7 /* offset 862 */ , 
  0xbc6, 0xbbe /* offset 864 */ , 
  0xbc7, 0xbbe /* offset 866 */ , 
  0xbc6, 0xbd7 /* offset 868 */ , 
  0xc46, 0xc56 /* offset 870 */ , 
  0xcbf, 0xcd5 /* offset 872 */ , 
  0xcc6, 0xcd5 /* offset 874 */ , 
  0xcc6, 0xcd6 /* offset 876 */ , 
  0xcc6, 0xcc2 /* offset 878 */ , 
  0xcc6, 0xcc2, 0xcd5 /* offset 880 */ , 
  0xd46, 0xd3e /* offset 883 */ , 
  0xd47, 0xd3e /* offset 885 */ , 
  0xd46, 0xd57 /* offset 887 */ , 
  0xdd9, 0xdca /* offset 889 */ , 
  0xdd9, 0xdcf /* offset 891 */ , 
  0xdd9, 0xdcf, 0xdca /* offset 893 */ , 
  0xdd9, 0xddf /* offset 896 */ , 
  0xe4d, 0xe32 /* offset 898 */ , 
  0xecd, 0xeb2 /* offset 900 */ , 
  0xeab, 0xe99 /* offset 902 */ , 
  0xeab, 0xea1 /* offset 904 */ , 
  0xf0b /* offset 906 */ , 
  0xf42, 0xfb7 /* offset 907 */ , 
  0xf4c, 0xfb7 /* offset 909 */ , 
  0xf51, 0xfb7 /* offset 911 */ , 
  0xf56, 0xfb7 /* offset 913 */ , 
  0xf5b, 0xfb7 /* offset 915 */ , 
  0xf40, 0xfb5 /* offset 917 */ , 
  0xf71, 0xf72 /* offset 919 */ , 
  0xf71, 0xf74 /* offset 921 */ , 
  0xfb2, 0xf80 /* offset 923 */ , 
  0xfb2, 0xf71, 0xf80 /* offset 925 */ , 
  0xfb3, 0xf80 /* offset 928 */ , 
  0xfb3, 0xf71, 0xf80 /* offset 930 */ , 
  0xf71, 0xf80 /* offset 933 */ , 
  0xf92, 0xfb7 /* offset 935 */ , 
  0xf9c, 0xfb7 /* offset 937 */ , 
  0xfa1, 0xfb7 /* offset 939 */ , 
  0xfa6, 0xfb7 /* offset 941 */ , 
  0xfab, 0xfb7 /* offset 943 */ , 
  0xf90, 0xfb5 /* offset 945 */ , 
  0x1025, 0x102e /* offset 947 */ , 
  0x41, 0x325 /* offset 949 */ , 
  0x61, 0x325 /* offset 951 */ , 
  0x42, 0x307 /* offset 953 */ , 
  0x62, 0x307 /* offset 955 */ , 
  0x42, 0x323 /* offset 957 */ , 
  0x62, 0x323 /* offset 959 */ , 
  0x42, 0x331 /* offset 961 */ , 
  0x62, 0x331 /* offset 963 */ , 
  0x43, 0x327, 0x301 /* offset 965 */ , 
  0x63, 0x327, 0x301 /* offset 968 */ , 
  0x44, 0x307 /* offset 971 */ , 
  0x64, 0x307 /* offset 973 */ , 
  0x44, 0x323 /* offset 975 */ , 
  0x64, 0x323 /* offset 977 */ , 
  0x44, 0x331 /* offset 979 */ , 
  0x64, 0x331 /* offset 981 */ , 
  0x44, 0x327 /* offset 983 */ , 
  0x64, 0x327 /* offset 985 */ , 
  0x44, 0x32d /* offset 987 */ , 
  0x64, 0x32d /* offset 989 */ , 
  0x45, 0x304, 0x300 /* offset 991 */ , 
  0x65, 0x304, 0x300 /* offset 994 */ , 
  0x45, 0x304, 0x301 /* offset 997 */ , 
  0x65, 0x304, 0x301 /* offset 1000 */ , 
  0x45, 0x32d /* offset 1003 */ , 
  0x65, 0x32d /* offset 1005 */ , 
  0x45, 0x330 /* offset 1007 */ , 
  0x65, 0x330 /* offset 1009 */ , 
  0x45, 0x327, 0x306 /* offset 1011 */ , 
  0x65, 0x327, 0x306 /* offset 1014 */ , 
  0x46, 0x307 /* offset 1017 */ , 
  0x66, 0x307 /* offset 1019 */ , 
  0x47, 0x304 /* offset 1021 */ , 
  0x67, 0x304 /* offset 1023 */ , 
  0x48, 0x307 /* offset 1025 */ , 
  0x68, 0x307 /* offset 1027 */ , 
  0x48, 0x323 /* offset 1029 */ , 
  0x68, 0x323 /* offset 1031 */ , 
  0x48, 0x308 /* offset 1033 */ , 
  0x68, 0x308 /* offset 1035 */ , 
  0x48, 0x327 /* offset 1037 */ , 
  0x68, 0x327 /* offset 1039 */ , 
  0x48, 0x32e /* offset 1041 */ , 
  0x68, 0x32e /* offset 1043 */ , 
  0x49, 0x330 /* offset 1045 */ , 
  0x69, 0x330 /* offset 1047 */ , 
  0x49, 0x308, 0x301 /* offset 1049 */ , 
  0x69, 0x308, 0x301 /* offset 1052 */ , 
  0x4b, 0x301 /* offset 1055 */ , 
  0x6b, 0x301 /* offset 1057 */ , 
  0x4b, 0x323 /* offset 1059 */ , 
  0x6b, 0x323 /* offset 1061 */ , 
  0x4b, 0x331 /* offset 1063 */ , 
  0x6b, 0x331 /* offset 1065 */ , 
  0x4c, 0x323 /* offset 1067 */ , 
  0x6c, 0x323 /* offset 1069 */ , 
  0x4c, 0x323, 0x304 /* offset 1071 */ , 
  0x6c, 0x323, 0x304 /* offset 1074 */ , 
  0x4c, 0x331 /* offset 1077 */ , 
  0x6c, 0x331 /* offset 1079 */ , 
  0x4c, 0x32d /* offset 1081 */ , 
  0x6c, 0x32d /* offset 1083 */ , 
  0x4d, 0x301 /* offset 1085 */ , 
  0x6d, 0x301 /* offset 1087 */ , 
  0x4d, 0x307 /* offset 1089 */ , 
  0x6d, 0x307 /* offset 1091 */ , 
  0x4d, 0x323 /* offset 1093 */ , 
  0x6d, 0x323 /* offset 1095 */ , 
  0x4e, 0x307 /* offset 1097 */ , 
  0x6e, 0x307 /* offset 1099 */ , 
  0x4e, 0x323 /* offset 1101 */ , 
  0x6e, 0x323 /* offset 1103 */ , 
  0x4e, 0x331 /* offset 1105 */ , 
  0x6e, 0x331 /* offset 1107 */ , 
  0x4e, 0x32d /* offset 1109 */ , 
  0x6e, 0x32d /* offset 1111 */ , 
  0x4f, 0x303, 0x301 /* offset 1113 */ , 
  0x6f, 0x303, 0x301 /* offset 1116 */ , 
  0x4f, 0x303, 0x308 /* offset 1119 */ , 
  0x6f, 0x303, 0x308 /* offset 1122 */ , 
  0x4f, 0x304, 0x300 /* offset 1125 */ , 
  0x6f, 0x304, 0x300 /* offset 1128 */ , 
  0x4f, 0x304, 0x301 /* offset 1131 */ , 
  0x6f, 0x304, 0x301 /* offset 1134 */ , 
  0x50, 0x301 /* offset 1137 */ , 
  0x70, 0x301 /* offset 1139 */ , 
  0x50, 0x307 /* offset 1141 */ , 
  0x70, 0x307 /* offset 1143 */ , 
  0x52, 0x307 /* offset 1145 */ , 
  0x72, 0x307 /* offset 1147 */ , 
  0x52, 0x323 /* offset 1149 */ , 
  0x72, 0x323 /* offset 1151 */ , 
  0x52, 0x323, 0x304 /* offset 1153 */ , 
  0x72, 0x323, 0x304 /* offset 1156 */ , 
  0x52, 0x331 /* offset 1159 */ , 
  0x72, 0x331 /* offset 1161 */ , 
  0x53, 0x307 /* offset 1163 */ , 
  0x73, 0x307 /* offset 1165 */ , 
  0x53, 0x323 /* offset 1167 */ , 
  0x73, 0x323 /* offset 1169 */ , 
  0x53, 0x301, 0x307 /* offset 1171 */ , 
  0x73, 0x301, 0x307 /* offset 1174 */ , 
  0x53, 0x30c, 0x307 /* offset 1177 */ , 
  0x73, 0x30c, 0x307 /* offset 1180 */ , 
  0x53, 0x323, 0x307 /* offset 1183 */ , 
  0x73, 0x323, 0x307 /* offset 1186 */ , 
  0x54, 0x307 /* offset 1189 */ , 
  0x74, 0x307 /* offset 1191 */ , 
  0x54, 0x323 /* offset 1193 */ , 
  0x74, 0x323 /* offset 1195 */ , 
  0x54, 0x331 /* offset 1197 */ , 
  0x74, 0x331 /* offset 1199 */ , 
  0x54, 0x32d /* offset 1201 */ , 
  0x74, 0x32d /* offset 1203 */ , 
  0x55, 0x324 /* offset 1205 */ , 
  0x75, 0x324 /* offset 1207 */ , 
  0x55, 0x330 /* offset 1209 */ , 
  0x75, 0x330 /* offset 1211 */ , 
  0x55, 0x32d /* offset 1213 */ , 
  0x75, 0x32d /* offset 1215 */ , 
  0x55, 0x303, 0x301 /* offset 1217 */ , 
  0x75, 0x303, 0x301 /* offset 1220 */ , 
  0x55, 0x304, 0x308 /* offset 1223 */ , 
  0x75, 0x304, 0x308 /* offset 1226 */ , 
  0x56, 0x303 /* offset 1229 */ , 
  0x76, 0x303 /* offset 1231 */ , 
  0x56, 0x323 /* offset 1233 */ , 
  0x76, 0x323 /* offset 1235 */ , 
  0x57, 0x300 /* offset 1237 */ , 
  0x77, 0x300 /* offset 1239 */ , 
  0x57, 0x301 /* offset 1241 */ , 
  0x77, 0x301 /* offset 1243 */ , 
  0x57, 0x308 /* offset 1245 */ , 
  0x77, 0x308 /* offset 1247 */ , 
  0x57, 0x307 /* offset 1249 */ , 
  0x77, 0x307 /* offset 1251 */ , 
  0x57, 0x323 /* offset 1253 */ , 
  0x77, 0x323 /* offset 1255 */ , 
  0x58, 0x307 /* offset 1257 */ , 
  0x78, 0x307 /* offset 1259 */ , 
  0x58, 0x308 /* offset 1261 */ , 
  0x78, 0x308 /* offset 1263 */ , 
  0x59, 0x307 /* offset 1265 */ , 
  0x79, 0x307 /* offset 1267 */ , 
  0x5a, 0x302 /* offset 1269 */ , 
  0x7a, 0x302 /* offset 1271 */ , 
  0x5a, 0x323 /* offset 1273 */ , 
  0x7a, 0x323 /* offset 1275 */ , 
  0x5a, 0x331 /* offset 1277 */ , 
  0x7a, 0x331 /* offset 1279 */ , 
  0x68, 0x331 /* offset 1281 */ , 
  0x74, 0x308 /* offset 1283 */ , 
  0x77, 0x30a /* offset 1285 */ , 
  0x79, 0x30a /* offset 1287 */ , 
  0x61, 0x2be /* offset 1289 */ , 
  0x41, 0x323 /* offset 1291 */ , 
  0x61, 0x323 /* offset 1293 */ , 
  0x41, 0x309 /* offset 1295 */ , 
  0x61, 0x309 /* offset 1297 */ , 
  0x41, 0x302, 0x301 /* offset 1299 */ , 
  0x61, 0x302, 0x301 /* offset 1302 */ , 
  0x41, 0x302, 0x300 /* offset 1305 */ , 
  0x61, 0x302, 0x300 /* offset 1308 */ , 
  0x41, 0x302, 0x309 /* offset 1311 */ , 
  0x61, 0x302, 0x309 /* offset 1314 */ , 
  0x41, 0x302, 0x303 /* offset 1317 */ , 
  0x61, 0x302, 0x303 /* offset 1320 */ , 
  0x41, 0x323, 0x302 /* offset 1323 */ , 
  0x61, 0x323, 0x302 /* offset 1326 */ , 
  0x41, 0x306, 0x301 /* offset 1329 */ , 
  0x61, 0x306, 0x301 /* offset 1332 */ , 
  0x41, 0x306, 0x300 /* offset 1335 */ , 
  0x61, 0x306, 0x300 /* offset 1338 */ , 
  0x41, 0x306, 0x309 /* offset 1341 */ , 
  0x61, 0x306, 0x309 /* offset 1344 */ , 
  0x41, 0x306, 0x303 /* offset 1347 */ , 
  0x61, 0x306, 0x303 /* offset 1350 */ , 
  0x41, 0x323, 0x306 /* offset 1353 */ , 
  0x61, 0x323, 0x306 /* offset 1356 */ , 
  0x45, 0x323 /* offset 1359 */ , 
  0x65, 0x323 /* offset 1361 */ , 
  0x45, 0x309 /* offset 1363 */ , 
  0x65, 0x309 /* offset 1365 */ , 
  0x45, 0x303 /* offset 1367 */ , 
  0x65, 0x303 /* offset 1369 */ , 
  0x45, 0x302, 0x301 /* offset 1371 */ , 
  0x65, 0x302, 0x301 /* offset 1374 */ , 
  0x45, 0x302, 0x300 /* offset 1377 */ , 
  0x65, 0x302, 0x300 /* offset 1380 */ , 
  0x45, 0x302, 0x309 /* offset 1383 */ , 
  0x65, 0x302, 0x309 /* offset 1386 */ , 
  0x45, 0x302, 0x303 /* offset 1389 */ , 
  0x65, 0x302, 0x303 /* offset 1392 */ , 
  0x45, 0x323, 0x302 /* offset 1395 */ , 
  0x65, 0x323, 0x302 /* offset 1398 */ , 
  0x49, 0x309 /* offset 1401 */ , 
  0x69, 0x309 /* offset 1403 */ , 
  0x49, 0x323 /* offset 1405 */ , 
  0x69, 0x323 /* offset 1407 */ , 
  0x4f, 0x323 /* offset 1409 */ , 
  0x6f, 0x323 /* offset 1411 */ , 
  0x4f, 0x309 /* offset 1413 */ , 
  0x6f, 0x309 /* offset 1415 */ , 
  0x4f, 0x302, 0x301 /* offset 1417 */ , 
  0x6f, 0x302, 0x301 /* offset 1420 */ , 
  0x4f, 0x302, 0x300 /* offset 1423 */ , 
  0x6f, 0x302, 0x300 /* offset 1426 */ , 
  0x4f, 0x302, 0x309 /* offset 1429 */ , 
  0x6f, 0x302, 0x309 /* offset 1432 */ , 
  0x4f, 0x302, 0x303 /* offset 1435 */ , 
  0x6f, 0x302, 0x303 /* offset 1438 */ , 
  0x4f, 0x323, 0x302 /* offset 1441 */ , 
  0x6f, 0x323, 0x302 /* offset 1444 */ , 
  0x4f, 0x31b, 0x301 /* offset 1447 */ , 
  0x6f, 0x31b, 0x301 /* offset 1450 */ , 
  0x4f, 0x31b, 0x300 /* offset 1453 */ , 
  0x6f, 0x31b, 0x300 /* offset 1456 */ , 
  0x4f, 0x31b, 0x309 /* offset 1459 */ , 
  0x6f, 0x31b, 0x309 /* offset 1462 */ , 
  0x4f, 0x31b, 0x303 /* offset 1465 */ , 
  0x6f, 0x31b, 0x303 /* offset 1468 */ , 
  0x4f, 0x31b, 0x323 /* offset 1471 */ , 
  0x6f, 0x31b, 0x323 /* offset 1474 */ , 
  0x55, 0x323 /* offset 1477 */ , 
  0x75, 0x323 /* offset 1479 */ , 
  0x55, 0x309 /* offset 1481 */ , 
  0x75, 0x309 /* offset 1483 */ , 
  0x55, 0x31b, 0x301 /* offset 1485 */ , 
  0x75, 0x31b, 0x301 /* offset 1488 */ , 
  0x55, 0x31b, 0x300 /* offset 1491 */ , 
  0x75, 0x31b, 0x300 /* offset 1494 */ , 
  0x55, 0x31b, 0x309 /* offset 1497 */ , 
  0x75, 0x31b, 0x309 /* offset 1500 */ , 
  0x55, 0x31b, 0x303 /* offset 1503 */ , 
  0x75, 0x31b, 0x303 /* offset 1506 */ , 
  0x55, 0x31b, 0x323 /* offset 1509 */ , 
  0x75, 0x31b, 0x323 /* offset 1512 */ , 
  0x59, 0x300 /* offset 1515 */ , 
  0x79, 0x300 /* offset 1517 */ , 
  0x59, 0x323 /* offset 1519 */ , 
  0x79, 0x323 /* offset 1521 */ , 
  0x59, 0x309 /* offset 1523 */ , 
  0x79, 0x309 /* offset 1525 */ , 
  0x59, 0x303 /* offset 1527 */ , 
  0x79, 0x303 /* offset 1529 */ , 
  0x3b1, 0x313 /* offset 1531 */ , 
  0x3b1, 0x314 /* offset 1533 */ , 
  0x3b1, 0x313, 0x300 /* offset 1535 */ , 
  0x3b1, 0x314, 0x300 /* offset 1538 */ , 
  0x3b1, 0x313, 0x301 /* offset 1541 */ , 
  0x3b1, 0x314, 0x301 /* offset 1544 */ , 
  0x3b1, 0x313, 0x342 /* offset 1547 */ , 
  0x3b1, 0x314, 0x342 /* offset 1550 */ , 
  0x391, 0x313 /* offset 1553 */ , 
  0x391, 0x314 /* offset 1555 */ , 
  0x391, 0x313, 0x300 /* offset 1557 */ , 
  0x391, 0x314, 0x300 /* offset 1560 */ , 
  0x391, 0x313, 0x301 /* offset 1563 */ , 
  0x391, 0x314, 0x301 /* offset 1566 */ , 
  0x391, 0x313, 0x342 /* offset 1569 */ , 
  0x391, 0x314, 0x342 /* offset 1572 */ , 
  0x3b5, 0x313 /* offset 1575 */ , 
  0x3b5, 0x314 /* offset 1577 */ , 
  0x3b5, 0x313, 0x300 /* offset 1579 */ , 
  0x3b5, 0x314, 0x300 /* offset 1582 */ , 
  0x3b5, 0x313, 0x301 /* offset 1585 */ , 
  0x3b5, 0x314, 0x301 /* offset 1588 */ , 
  0x395, 0x313 /* offset 1591 */ , 
  0x395, 0x314 /* offset 1593 */ , 
  0x395, 0x313, 0x300 /* offset 1595 */ , 
  0x395, 0x314, 0x300 /* offset 1598 */ , 
  0x395, 0x313, 0x301 /* offset 1601 */ , 
  0x395, 0x314, 0x301 /* offset 1604 */ , 
  0x3b7, 0x313 /* offset 1607 */ , 
  0x3b7, 0x314 /* offset 1609 */ , 
  0x3b7, 0x313, 0x300 /* offset 1611 */ , 
  0x3b7, 0x314, 0x300 /* offset 1614 */ , 
  0x3b7, 0x313, 0x301 /* offset 1617 */ , 
  0x3b7, 0x314, 0x301 /* offset 1620 */ , 
  0x3b7, 0x313, 0x342 /* offset 1623 */ , 
  0x3b7, 0x314, 0x342 /* offset 1626 */ , 
  0x397, 0x313 /* offset 1629 */ , 
  0x397, 0x314 /* offset 1631 */ , 
  0x397, 0x313, 0x300 /* offset 1633 */ , 
  0x397, 0x314, 0x300 /* offset 1636 */ , 
  0x397, 0x313, 0x301 /* offset 1639 */ , 
  0x397, 0x314, 0x301 /* offset 1642 */ , 
  0x397, 0x313, 0x342 /* offset 1645 */ , 
  0x397, 0x314, 0x342 /* offset 1648 */ , 
  0x3b9, 0x313 /* offset 1651 */ , 
  0x3b9, 0x314 /* offset 1653 */ , 
  0x3b9, 0x313, 0x300 /* offset 1655 */ , 
  0x3b9, 0x314, 0x300 /* offset 1658 */ , 
  0x3b9, 0x313, 0x301 /* offset 1661 */ , 
  0x3b9, 0x314, 0x301 /* offset 1664 */ , 
  0x3b9, 0x313, 0x342 /* offset 1667 */ , 
  0x3b9, 0x314, 0x342 /* offset 1670 */ , 
  0x399, 0x313 /* offset 1673 */ , 
  0x399, 0x314 /* offset 1675 */ , 
  0x399, 0x313, 0x300 /* offset 1677 */ , 
  0x399, 0x314, 0x300 /* offset 1680 */ , 
  0x399, 0x313, 0x301 /* offset 1683 */ , 
  0x399, 0x314, 0x301 /* offset 1686 */ , 
  0x399, 0x313, 0x342 /* offset 1689 */ , 
  0x399, 0x314, 0x342 /* offset 1692 */ , 
  0x3bf, 0x313 /* offset 1695 */ , 
  0x3bf, 0x314 /* offset 1697 */ , 
  0x3bf, 0x313, 0x300 /* offset 1699 */ , 
  0x3bf, 0x314, 0x300 /* offset 1702 */ , 
  0x3bf, 0x313, 0x301 /* offset 1705 */ , 
  0x3bf, 0x314, 0x301 /* offset 1708 */ , 
  0x39f, 0x313 /* offset 1711 */ , 
  0x39f, 0x314 /* offset 1713 */ , 
  0x39f, 0x313, 0x300 /* offset 1715 */ , 
  0x39f, 0x314, 0x300 /* offset 1718 */ , 
  0x39f, 0x313, 0x301 /* offset 1721 */ , 
  0x39f, 0x314, 0x301 /* offset 1724 */ , 
  0x3c5, 0x313 /* offset 1727 */ , 
  0x3c5, 0x314 /* offset 1729 */ , 
  0x3c5, 0x313, 0x300 /* offset 1731 */ , 
  0x3c5, 0x314, 0x300 /* offset 1734 */ , 
  0x3c5, 0x313, 0x301 /* offset 1737 */ , 
  0x3c5, 0x314, 0x301 /* offset 1740 */ , 
  0x3c5, 0x313, 0x342 /* offset 1743 */ , 
  0x3c5, 0x314, 0x342 /* offset 1746 */ , 
  0x3a5, 0x314 /* offset 1749 */ , 
  0x3a5, 0x314, 0x300 /* offset 1751 */ , 
  0x3a5, 0x314, 0x301 /* offset 1754 */ , 
  0x3a5, 0x314, 0x342 /* offset 1757 */ , 
  0x3c9, 0x313 /* offset 1760 */ , 
  0x3c9, 0x314 /* offset 1762 */ , 
  0x3c9, 0x313, 0x300 /* offset 1764 */ , 
  0x3c9, 0x314, 0x300 /* offset 1767 */ , 
  0x3c9, 0x313, 0x301 /* offset 1770 */ , 
  0x3c9, 0x314, 0x301 /* offset 1773 */ , 
  0x3c9, 0x313, 0x342 /* offset 1776 */ , 
  0x3c9, 0x314, 0x342 /* offset 1779 */ , 
  0x3a9, 0x313 /* offset 1782 */ , 
  0x3a9, 0x314 /* offset 1784 */ , 
  0x3a9, 0x313, 0x300 /* offset 1786 */ , 
  0x3a9, 0x314, 0x300 /* offset 1789 */ , 
  0x3a9, 0x313, 0x301 /* offset 1792 */ , 
  0x3a9, 0x314, 0x301 /* offset 1795 */ , 
  0x3a9, 0x313, 0x342 /* offset 1798 */ , 
  0x3a9, 0x314, 0x342 /* offset 1801 */ , 
  0x3b1, 0x300 /* offset 1804 */ , 
  0x3b5, 0x300 /* offset 1806 */ , 
  0x3b7, 0x300 /* offset 1808 */ , 
  0x3b9, 0x300 /* offset 1810 */ , 
  0x3bf, 0x300 /* offset 1812 */ , 
  0x3c5, 0x300 /* offset 1814 */ , 
  0x3c9, 0x300 /* offset 1816 */ , 
  0x3b1, 0x313, 0x345 /* offset 1818 */ , 
  0x3b1, 0x314, 0x345 /* offset 1821 */ , 
  0x3b1, 0x313, 0x300, 0x345 /* offset 1824 */ , 
  0x3b1, 0x314, 0x300, 0x345 /* offset 1828 */ , 
  0x3b1, 0x313, 0x301, 0x345 /* offset 1832 */ , 
  0x3b1, 0x314, 0x301, 0x345 /* offset 1836 */ , 
  0x3b1, 0x313, 0x342, 0x345 /* offset 1840 */ , 
  0x3b1, 0x314, 0x342, 0x345 /* offset 1844 */ , 
  0x391, 0x313, 0x345 /* offset 1848 */ , 
  0x391, 0x314, 0x345 /* offset 1851 */ , 
  0x391, 0x313, 0x300, 0x345 /* offset 1854 */ , 
  0x391, 0x314, 0x300, 0x345 /* offset 1858 */ , 
  0x391, 0x313, 0x301, 0x345 /* offset 1862 */ , 
  0x391, 0x314, 0x301, 0x345 /* offset 1866 */ , 
  0x391, 0x313, 0x342, 0x345 /* offset 1870 */ , 
  0x391, 0x314, 0x342, 0x345 /* offset 1874 */ , 
  0x3b7, 0x313, 0x345 /* offset 1878 */ , 
  0x3b7, 0x314, 0x345 /* offset 1881 */ , 
  0x3b7, 0x313, 0x300, 0x345 /* offset 1884 */ , 
  0x3b7, 0x314, 0x300, 0x345 /* offset 1888 */ , 
  0x3b7, 0x313, 0x301, 0x345 /* offset 1892 */ , 
  0x3b7, 0x314, 0x301, 0x345 /* offset 1896 */ , 
  0x3b7, 0x313, 0x342, 0x345 /* offset 1900 */ , 
  0x3b7, 0x314, 0x342, 0x345 /* offset 1904 */ , 
  0x397, 0x313, 0x345 /* offset 1908 */ , 
  0x397, 0x314, 0x345 /* offset 1911 */ , 
  0x397, 0x313, 0x300, 0x345 /* offset 1914 */ , 
  0x397, 0x314, 0x300, 0x345 /* offset 1918 */ , 
  0x397, 0x313, 0x301, 0x345 /* offset 1922 */ , 
  0x397, 0x314, 0x301, 0x345 /* offset 1926 */ , 
  0x397, 0x313, 0x342, 0x345 /* offset 1930 */ , 
  0x397, 0x314, 0x342, 0x345 /* offset 1934 */ , 
  0x3c9, 0x313, 0x345 /* offset 1938 */ , 
  0x3c9, 0x314, 0x345 /* offset 1941 */ , 
  0x3c9, 0x313, 0x300, 0x345 /* offset 1944 */ , 
  0x3c9, 0x314, 0x300, 0x345 /* offset 1948 */ , 
  0x3c9, 0x313, 0x301, 0x345 /* offset 1952 */ , 
  0x3c9, 0x314, 0x301, 0x345 /* offset 1956 */ , 
  0x3c9, 0x313, 0x342, 0x345 /* offset 1960 */ , 
  0x3c9, 0x314, 0x342, 0x345 /* offset 1964 */ , 
  0x3a9, 0x313, 0x345 /* offset 1968 */ , 
  0x3a9, 0x314, 0x345 /* offset 1971 */ , 
  0x3a9, 0x313, 0x300, 0x345 /* offset 1974 */ , 
  0x3a9, 0x314, 0x300, 0x345 /* offset 1978 */ , 
  0x3a9, 0x313, 0x301, 0x345 /* offset 1982 */ , 
  0x3a9, 0x314, 0x301, 0x345 /* offset 1986 */ , 
  0x3a9, 0x313, 0x342, 0x345 /* offset 1990 */ , 
  0x3a9, 0x314, 0x342, 0x345 /* offset 1994 */ , 
  0x3b1, 0x306 /* offset 1998 */ , 
  0x3b1, 0x304 /* offset 2000 */ , 
  0x3b1, 0x300, 0x345 /* offset 2002 */ , 
  0x3b1, 0x345 /* offset 2005 */ , 
  0x3b1, 0x301, 0x345 /* offset 2007 */ , 
  0x3b1, 0x342 /* offset 2010 */ , 
  0x3b1, 0x342, 0x345 /* offset 2012 */ , 
  0x391, 0x306 /* offset 2015 */ , 
  0x391, 0x304 /* offset 2017 */ , 
  0x391, 0x300 /* offset 2019 */ , 
  0x391, 0x345 /* offset 2021 */ , 
  0x20, 0x313 /* offset 2023 */ , 
  0x3b9 /* offset 2025 */ , 
  0x20, 0x342 /* offset 2026 */ , 
  0x20, 0x308, 0x342 /* offset 2028 */ , 
  0x3b7, 0x300, 0x345 /* offset 2031 */ , 
  0x3b7, 0x345 /* offset 2034 */ , 
  0x3b7, 0x301, 0x345 /* offset 2036 */ , 
  0x3b7, 0x342 /* offset 2039 */ , 
  0x3b7, 0x342, 0x345 /* offset 2041 */ , 
  0x395, 0x300 /* offset 2044 */ , 
  0x397, 0x300 /* offset 2046 */ , 
  0x397, 0x345 /* offset 2048 */ , 
  0x20, 0x313, 0x300 /* offset 2050 */ , 
  0x20, 0x313, 0x301 /* offset 2053 */ , 
  0x20, 0x313, 0x342 /* offset 2056 */ , 
  0x3b9, 0x306 /* offset 2059 */ , 
  0x3b9, 0x304 /* offset 2061 */ , 
  0x3b9, 0x308, 0x300 /* offset 2063 */ , 
  0x3b9, 0x342 /* offset 2066 */ , 
  0x3b9, 0x308, 0x342 /* offset 2068 */ , 
  0x399, 0x306 /* offset 2071 */ , 
  0x399, 0x304 /* offset 2073 */ , 
  0x399, 0x300 /* offset 2075 */ , 
  0x20, 0x314, 0x300 /* offset 2077 */ , 
  0x20, 0x314, 0x301 /* offset 2080 */ , 
  0x20, 0x314, 0x342 /* offset 2083 */ , 
  0x3c5, 0x306 /* offset 2086 */ , 
  0x3c5, 0x304 /* offset 2088 */ , 
  0x3c5, 0x308, 0x300 /* offset 2090 */ , 
  0x3c1, 0x313 /* offset 2093 */ , 
  0x3c1, 0x314 /* offset 2095 */ , 
  0x3c5, 0x342 /* offset 2097 */ , 
  0x3c5, 0x308, 0x342 /* offset 2099 */ , 
  0x3a5, 0x306 /* offset 2102 */ , 
  0x3a5, 0x304 /* offset 2104 */ , 
  0x3a5, 0x300 /* offset 2106 */ , 
  0x3a1, 0x314 /* offset 2108 */ , 
  0x20, 0x308, 0x300 /* offset 2110 */ , 
  0x60 /* offset 2113 */ , 
  0x3c9, 0x300, 0x345 /* offset 2114 */ , 
  0x3c9, 0x345 /* offset 2117 */ , 
  0x3c9, 0x301, 0x345 /* offset 2119 */ , 
  0x3c9, 0x342 /* offset 2122 */ , 
  0x3c9, 0x342, 0x345 /* offset 2124 */ , 
  0x39f, 0x300 /* offset 2127 */ , 
  0x3a9, 0x300 /* offset 2129 */ , 
  0x3a9, 0x345 /* offset 2131 */ , 
  0x20, 0x314 /* offset 2133 */ , 
  0x2010 /* offset 2135 */ , 
  0x20, 0x333 /* offset 2136 */ , 
  0x2e /* offset 2138 */ , 
  0x2e, 0x2e /* offset 2139 */ , 
  0x2e, 0x2e, 0x2e /* offset 2141 */ , 
  0x2032, 0x2032 /* offset 2144 */ , 
  0x2032, 0x2032, 0x2032 /* offset 2146 */ , 
  0x2035, 0x2035 /* offset 2149 */ , 
  0x2035, 0x2035, 0x2035 /* offset 2151 */ , 
  0x21, 0x21 /* offset 2154 */ , 
  0x20, 0x305 /* offset 2156 */ , 
  0x3f, 0x3f /* offset 2158 */ , 
  0x3f, 0x21 /* offset 2160 */ , 
  0x21, 0x3f /* offset 2162 */ , 
  0x2032, 0x2032, 0x2032, 0x2032 /* offset 2164 */ , 
  0x30 /* offset 2168 */ , 
  0x69 /* offset 2169 */ , 
  0x34 /* offset 2170 */ , 
  0x35 /* offset 2171 */ , 
  0x36 /* offset 2172 */ , 
  0x37 /* offset 2173 */ , 
  0x38 /* offset 2174 */ , 
  0x39 /* offset 2175 */ , 
  0x2b /* offset 2176 */ , 
  0x2212 /* offset 2177 */ , 
  0x3d /* offset 2178 */ , 
  0x28 /* offset 2179 */ , 
  0x29 /* offset 2180 */ , 
  0x6e /* offset 2181 */ , 
  0x52, 0x73 /* offset 2182 */ , 
  0x61, 0x2f, 0x63 /* offset 2184 */ , 
  0x61, 0x2f, 0x73 /* offset 2187 */ , 
  0x43 /* offset 2190 */ , 
  0xb0, 0x43 /* offset 2191 */ , 
  0x63, 0x2f, 0x6f /* offset 2193 */ , 
  0x63, 0x2f, 0x75 /* offset 2196 */ , 
  0x190 /* offset 2199 */ , 
  0xb0, 0x46 /* offset 2200 */ , 
  0x67 /* offset 2202 */ , 
  0x48 /* offset 2203 */ , 
  0x127 /* offset 2204 */ , 
  0x49 /* offset 2205 */ , 
  0x4c /* offset 2206 */ , 
  0x4e /* offset 2207 */ , 
  0x4e, 0x6f /* offset 2208 */ , 
  0x50 /* offset 2210 */ , 
  0x51 /* offset 2211 */ , 
  0x52 /* offset 2212 */ , 
  0x53, 0x4d /* offset 2213 */ , 
  0x54, 0x45, 0x4c /* offset 2215 */ , 
  0x54, 0x4d /* offset 2218 */ , 
  0x5a /* offset 2220 */ , 
  0x3a9 /* offset 2221 */ , 
  0x4b /* offset 2222 */ , 
  0x42 /* offset 2223 */ , 
  0x65 /* offset 2224 */ , 
  0x45 /* offset 2225 */ , 
  0x46 /* offset 2226 */ , 
  0x4d /* offset 2227 */ , 
  0x5d0 /* offset 2228 */ , 
  0x5d1 /* offset 2229 */ , 
  0x5d2 /* offset 2230 */ , 
  0x5d3 /* offset 2231 */ , 
  0x3b3 /* offset 2232 */ , 
  0x393 /* offset 2233 */ , 
  0x3a0 /* offset 2234 */ , 
  0x2211 /* offset 2235 */ , 
  0x44 /* offset 2236 */ , 
  0x64 /* offset 2237 */ , 
  0x31, 0x2044, 0x33 /* offset 2238 */ , 
  0x32, 0x2044, 0x33 /* offset 2241 */ , 
  0x31, 0x2044, 0x35 /* offset 2244 */ , 
  0x32, 0x2044, 0x35 /* offset 2247 */ , 
  0x33, 0x2044, 0x35 /* offset 2250 */ , 
  0x34, 0x2044, 0x35 /* offset 2253 */ , 
  0x31, 0x2044, 0x36 /* offset 2256 */ , 
  0x35, 0x2044, 0x36 /* offset 2259 */ , 
  0x31, 0x2044, 0x38 /* offset 2262 */ , 
  0x33, 0x2044, 0x38 /* offset 2265 */ , 
  0x35, 0x2044, 0x38 /* offset 2268 */ , 
  0x37, 0x2044, 0x38 /* offset 2271 */ , 
  0x31, 0x2044 /* offset 2274 */ , 
  0x49, 0x49 /* offset 2276 */ , 
  0x49, 0x49, 0x49 /* offset 2278 */ , 
  0x49, 0x56 /* offset 2281 */ , 
  0x56 /* offset 2283 */ , 
  0x56, 0x49 /* offset 2284 */ , 
  0x56, 0x49, 0x49 /* offset 2286 */ , 
  0x56, 0x49, 0x49, 0x49 /* offset 2289 */ , 
  0x49, 0x58 /* offset 2293 */ , 
  0x58 /* offset 2295 */ , 
  0x58, 0x49 /* offset 2296 */ , 
  0x58, 0x49, 0x49 /* offset 2298 */ , 
  0x69, 0x69 /* offset 2301 */ , 
  0x69, 0x69, 0x69 /* offset 2303 */ , 
  0x69, 0x76 /* offset 2306 */ , 
  0x76 /* offset 2308 */ , 
  0x76, 0x69 /* offset 2309 */ , 
  0x76, 0x69, 0x69 /* offset 2311 */ , 
  0x76, 0x69, 0x69, 0x69 /* offset 2314 */ , 
  0x69, 0x78 /* offset 2318 */ , 
  0x78, 0x69 /* offset 2320 */ , 
  0x78, 0x69, 0x69 /* offset 2322 */ , 
  0x63 /* offset 2325 */ , 
  0x6d /* offset 2326 */ , 
  0x2190, 0x338 /* offset 2327 */ , 
  0x2192, 0x338 /* offset 2329 */ , 
  0x2194, 0x338 /* offset 2331 */ , 
  0x21d0, 0x338 /* offset 2333 */ , 
  0x21d4, 0x338 /* offset 2335 */ , 
  0x21d2, 0x338 /* offset 2337 */ , 
  0x2203, 0x338 /* offset 2339 */ , 
  0x2208, 0x338 /* offset 2341 */ , 
  0x220b, 0x338 /* offset 2343 */ , 
  0x2223, 0x338 /* offset 2345 */ , 
  0x2225, 0x338 /* offset 2347 */ , 
  0x222b, 0x222b /* offset 2349 */ , 
  0x222b, 0x222b, 0x222b /* offset 2351 */ , 
  0x222e, 0x222e /* offset 2354 */ , 
  0x222e, 0x222e, 0x222e /* offset 2356 */ , 
  0x223c, 0x338 /* offset 2359 */ , 
  0x2243, 0x338 /* offset 2361 */ , 
  0x2245, 0x338 /* offset 2363 */ , 
  0x2248, 0x338 /* offset 2365 */ , 
  0x3d, 0x338 /* offset 2367 */ , 
  0x2261, 0x338 /* offset 2369 */ , 
  0x224d, 0x338 /* offset 2371 */ , 
  0x3c, 0x338 /* offset 2373 */ , 
  0x3e, 0x338 /* offset 2375 */ , 
  0x2264, 0x338 /* offset 2377 */ , 
  0x2265, 0x338 /* offset 2379 */ , 
  0x2272, 0x338 /* offset 2381 */ , 
  0x2273, 0x338 /* offset 2383 */ , 
  0x2276, 0x338 /* offset 2385 */ , 
  0x2277, 0x338 /* offset 2387 */ , 
  0x227a, 0x338 /* offset 2389 */ , 
  0x227b, 0x338 /* offset 2391 */ , 
  0x2282, 0x338 /* offset 2393 */ , 
  0x2283, 0x338 /* offset 2395 */ , 
  0x2286, 0x338 /* offset 2397 */ , 
  0x2287, 0x338 /* offset 2399 */ , 
  0x22a2, 0x338 /* offset 2401 */ , 
  0x22a8, 0x338 /* offset 2403 */ , 
  0x22a9, 0x338 /* offset 2405 */ , 
  0x22ab, 0x338 /* offset 2407 */ , 
  0x227c, 0x338 /* offset 2409 */ , 
  0x227d, 0x338 /* offset 2411 */ , 
  0x2291, 0x338 /* offset 2413 */ , 
  0x2292, 0x338 /* offset 2415 */ , 
  0x22b2, 0x338 /* offset 2417 */ , 
  0x22b3, 0x338 /* offset 2419 */ , 
  0x22b4, 0x338 /* offset 2421 */ , 
  0x22b5, 0x338 /* offset 2423 */ , 
  0x3008 /* offset 2425 */ , 
  0x3009 /* offset 2426 */ , 
  0x31, 0x30 /* offset 2427 */ , 
  0x31, 0x31 /* offset 2429 */ , 
  0x31, 0x32 /* offset 2431 */ , 
  0x31, 0x33 /* offset 2433 */ , 
  0x31, 0x34 /* offset 2435 */ , 
  0x31, 0x35 /* offset 2437 */ , 
  0x31, 0x36 /* offset 2439 */ , 
  0x31, 0x37 /* offset 2441 */ , 
  0x31, 0x38 /* offset 2443 */ , 
  0x31, 0x39 /* offset 2445 */ , 
  0x32, 0x30 /* offset 2447 */ , 
  0x28, 0x31, 0x29 /* offset 2449 */ , 
  0x28, 0x32, 0x29 /* offset 2452 */ , 
  0x28, 0x33, 0x29 /* offset 2455 */ , 
  0x28, 0x34, 0x29 /* offset 2458 */ , 
  0x28, 0x35, 0x29 /* offset 2461 */ , 
  0x28, 0x36, 0x29 /* offset 2464 */ , 
  0x28, 0x37, 0x29 /* offset 2467 */ , 
  0x28, 0x38, 0x29 /* offset 2470 */ , 
  0x28, 0x39, 0x29 /* offset 2473 */ , 
  0x28, 0x31, 0x30, 0x29 /* offset 2476 */ , 
  0x28, 0x31, 0x31, 0x29 /* offset 2480 */ , 
  0x28, 0x31, 0x32, 0x29 /* offset 2484 */ , 
  0x28, 0x31, 0x33, 0x29 /* offset 2488 */ , 
  0x28, 0x31, 0x34, 0x29 /* offset 2492 */ , 
  0x28, 0x31, 0x35, 0x29 /* offset 2496 */ , 
  0x28, 0x31, 0x36, 0x29 /* offset 2500 */ , 
  0x28, 0x31, 0x37, 0x29 /* offset 2504 */ , 
  0x28, 0x31, 0x38, 0x29 /* offset 2508 */ , 
  0x28, 0x31, 0x39, 0x29 /* offset 2512 */ , 
  0x28, 0x32, 0x30, 0x29 /* offset 2516 */ , 
  0x31, 0x2e /* offset 2520 */ , 
  0x32, 0x2e /* offset 2522 */ , 
  0x33, 0x2e /* offset 2524 */ , 
  0x34, 0x2e /* offset 2526 */ , 
  0x35, 0x2e /* offset 2528 */ , 
  0x36, 0x2e /* offset 2530 */ , 
  0x37, 0x2e /* offset 2532 */ , 
  0x38, 0x2e /* offset 2534 */ , 
  0x39, 0x2e /* offset 2536 */ , 
  0x31, 0x30, 0x2e /* offset 2538 */ , 
  0x31, 0x31, 0x2e /* offset 2541 */ , 
  0x31, 0x32, 0x2e /* offset 2544 */ , 
  0x31, 0x33, 0x2e /* offset 2547 */ , 
  0x31, 0x34, 0x2e /* offset 2550 */ , 
  0x31, 0x35, 0x2e /* offset 2553 */ , 
  0x31, 0x36, 0x2e /* offset 2556 */ , 
  0x31, 0x37, 0x2e /* offset 2559 */ , 
  0x31, 0x38, 0x2e /* offset 2562 */ , 
  0x31, 0x39, 0x2e /* offset 2565 */ , 
  0x32, 0x30, 0x2e /* offset 2568 */ , 
  0x28, 0x61, 0x29 /* offset 2571 */ , 
  0x28, 0x62, 0x29 /* offset 2574 */ , 
  0x28, 0x63, 0x29 /* offset 2577 */ , 
  0x28, 0x64, 0x29 /* offset 2580 */ , 
  0x28, 0x65, 0x29 /* offset 2583 */ , 
  0x28, 0x66, 0x29 /* offset 2586 */ , 
  0x28, 0x67, 0x29 /* offset 2589 */ , 
  0x28, 0x68, 0x29 /* offset 2592 */ , 
  0x28, 0x69, 0x29 /* offset 2595 */ , 
  0x28, 0x6a, 0x29 /* offset 2598 */ , 
  0x28, 0x6b, 0x29 /* offset 2601 */ , 
  0x28, 0x6c, 0x29 /* offset 2604 */ , 
  0x28, 0x6d, 0x29 /* offset 2607 */ , 
  0x28, 0x6e, 0x29 /* offset 2610 */ , 
  0x28, 0x6f, 0x29 /* offset 2613 */ , 
  0x28, 0x70, 0x29 /* offset 2616 */ , 
  0x28, 0x71, 0x29 /* offset 2619 */ , 
  0x28, 0x72, 0x29 /* offset 2622 */ , 
  0x28, 0x73, 0x29 /* offset 2625 */ , 
  0x28, 0x74, 0x29 /* offset 2628 */ , 
  0x28, 0x75, 0x29 /* offset 2631 */ , 
  0x28, 0x76, 0x29 /* offset 2634 */ , 
  0x28, 0x77, 0x29 /* offset 2637 */ , 
  0x28, 0x78, 0x29 /* offset 2640 */ , 
  0x28, 0x79, 0x29 /* offset 2643 */ , 
  0x28, 0x7a, 0x29 /* offset 2646 */ , 
  0x41 /* offset 2649 */ , 
  0x47 /* offset 2650 */ , 
  0x4a /* offset 2651 */ , 
  0x4f /* offset 2652 */ , 
  0x53 /* offset 2653 */ , 
  0x54 /* offset 2654 */ , 
  0x55 /* offset 2655 */ , 
  0x57 /* offset 2656 */ , 
  0x59 /* offset 2657 */ , 
  0x62 /* offset 2658 */ , 
  0x66 /* offset 2659 */ , 
  0x6b /* offset 2660 */ , 
  0x70 /* offset 2661 */ , 
  0x71 /* offset 2662 */ , 
  0x74 /* offset 2663 */ , 
  0x75 /* offset 2664 */ , 
  0x7a /* offset 2665 */ , 
  0x222b, 0x222b, 0x222b, 0x222b /* offset 2666 */ , 
  0x3a, 0x3a, 0x3d /* offset 2670 */ , 
  0x3d, 0x3d /* offset 2673 */ , 
  0x3d, 0x3d, 0x3d /* offset 2675 */ , 
  0x2add, 0x338 /* offset 2678 */ , 
  0x6bcd /* offset 2680 */ , 
  0x9f9f /* offset 2681 */ , 
  0x4e00 /* offset 2682 */ , 
  0x4e28 /* offset 2683 */ , 
  0x4e36 /* offset 2684 */ , 
  0x4e3f /* offset 2685 */ , 
  0x4e59 /* offset 2686 */ , 
  0x4e85 /* offset 2687 */ , 
  0x4e8c /* offset 2688 */ , 
  0x4ea0 /* offset 2689 */ , 
  0x4eba /* offset 2690 */ , 
  0x513f /* offset 2691 */ , 
  0x5165 /* offset 2692 */ , 
  0x516b /* offset 2693 */ , 
  0x5182 /* offset 2694 */ , 
  0x5196 /* offset 2695 */ , 
  0x51ab /* offset 2696 */ , 
  0x51e0 /* offset 2697 */ , 
  0x51f5 /* offset 2698 */ , 
  0x5200 /* offset 2699 */ , 
  0x529b /* offset 2700 */ , 
  0x52f9 /* offset 2701 */ , 
  0x5315 /* offset 2702 */ , 
  0x531a /* offset 2703 */ , 
  0x5338 /* offset 2704 */ , 
  0x5341 /* offset 2705 */ , 
  0x535c /* offset 2706 */ , 
  0x5369 /* offset 2707 */ , 
  0x5382 /* offset 2708 */ , 
  0x53b6 /* offset 2709 */ , 
  0x53c8 /* offset 2710 */ , 
  0x53e3 /* offset 2711 */ , 
  0x56d7 /* offset 2712 */ , 
  0x571f /* offset 2713 */ , 
  0x58eb /* offset 2714 */ , 
  0x5902 /* offset 2715 */ , 
  0x590a /* offset 2716 */ , 
  0x5915 /* offset 2717 */ , 
  0x5927 /* offset 2718 */ , 
  0x5973 /* offset 2719 */ , 
  0x5b50 /* offset 2720 */ , 
  0x5b80 /* offset 2721 */ , 
  0x5bf8 /* offset 2722 */ , 
  0x5c0f /* offset 2723 */ , 
  0x5c22 /* offset 2724 */ , 
  0x5c38 /* offset 2725 */ , 
  0x5c6e /* offset 2726 */ , 
  0x5c71 /* offset 2727 */ , 
  0x5ddb /* offset 2728 */ , 
  0x5de5 /* offset 2729 */ , 
  0x5df1 /* offset 2730 */ , 
  0x5dfe /* offset 2731 */ , 
  0x5e72 /* offset 2732 */ , 
  0x5e7a /* offset 2733 */ , 
  0x5e7f /* offset 2734 */ , 
  0x5ef4 /* offset 2735 */ , 
  0x5efe /* offset 2736 */ , 
  0x5f0b /* offset 2737 */ , 
  0x5f13 /* offset 2738 */ , 
  0x5f50 /* offset 2739 */ , 
  0x5f61 /* offset 2740 */ , 
  0x5f73 /* offset 2741 */ , 
  0x5fc3 /* offset 2742 */ , 
  0x6208 /* offset 2743 */ , 
  0x6236 /* offset 2744 */ , 
  0x624b /* offset 2745 */ , 
  0x652f /* offset 2746 */ , 
  0x6534 /* offset 2747 */ , 
  0x6587 /* offset 2748 */ , 
  0x6597 /* offset 2749 */ , 
  0x65a4 /* offset 2750 */ , 
  0x65b9 /* offset 2751 */ , 
  0x65e0 /* offset 2752 */ , 
  0x65e5 /* offset 2753 */ , 
  0x66f0 /* offset 2754 */ , 
  0x6708 /* offset 2755 */ , 
  0x6728 /* offset 2756 */ , 
  0x6b20 /* offset 2757 */ , 
  0x6b62 /* offset 2758 */ , 
  0x6b79 /* offset 2759 */ , 
  0x6bb3 /* offset 2760 */ , 
  0x6bcb /* offset 2761 */ , 
  0x6bd4 /* offset 2762 */ , 
  0x6bdb /* offset 2763 */ , 
  0x6c0f /* offset 2764 */ , 
  0x6c14 /* offset 2765 */ , 
  0x6c34 /* offset 2766 */ , 
  0x706b /* offset 2767 */ , 
  0x722a /* offset 2768 */ , 
  0x7236 /* offset 2769 */ , 
  0x723b /* offset 2770 */ , 
  0x723f /* offset 2771 */ , 
  0x7247 /* offset 2772 */ , 
  0x7259 /* offset 2773 */ , 
  0x725b /* offset 2774 */ , 
  0x72ac /* offset 2775 */ , 
  0x7384 /* offset 2776 */ , 
  0x7389 /* offset 2777 */ , 
  0x74dc /* offset 2778 */ , 
  0x74e6 /* offset 2779 */ , 
  0x7518 /* offset 2780 */ , 
  0x751f /* offset 2781 */ , 
  0x7528 /* offset 2782 */ , 
  0x7530 /* offset 2783 */ , 
  0x758b /* offset 2784 */ , 
  0x7592 /* offset 2785 */ , 
  0x7676 /* offset 2786 */ , 
  0x767d /* offset 2787 */ , 
  0x76ae /* offset 2788 */ , 
  0x76bf /* offset 2789 */ , 
  0x76ee /* offset 2790 */ , 
  0x77db /* offset 2791 */ , 
  0x77e2 /* offset 2792 */ , 
  0x77f3 /* offset 2793 */ , 
  0x793a /* offset 2794 */ , 
  0x79b8 /* offset 2795 */ , 
  0x79be /* offset 2796 */ , 
  0x7a74 /* offset 2797 */ , 
  0x7acb /* offset 2798 */ , 
  0x7af9 /* offset 2799 */ , 
  0x7c73 /* offset 2800 */ , 
  0x7cf8 /* offset 2801 */ , 
  0x7f36 /* offset 2802 */ , 
  0x7f51 /* offset 2803 */ , 
  0x7f8a /* offset 2804 */ , 
  0x7fbd /* offset 2805 */ , 
  0x8001 /* offset 2806 */ , 
  0x800c /* offset 2807 */ , 
  0x8012 /* offset 2808 */ , 
  0x8033 /* offset 2809 */ , 
  0x807f /* offset 2810 */ , 
  0x8089 /* offset 2811 */ , 
  0x81e3 /* offset 2812 */ , 
  0x81ea /* offset 2813 */ , 
  0x81f3 /* offset 2814 */ , 
  0x81fc /* offset 2815 */ , 
  0x820c /* offset 2816 */ , 
  0x821b /* offset 2817 */ , 
  0x821f /* offset 2818 */ , 
  0x826e /* offset 2819 */ , 
  0x8272 /* offset 2820 */ , 
  0x8278 /* offset 2821 */ , 
  0x864d /* offset 2822 */ , 
  0x866b /* offset 2823 */ , 
  0x8840 /* offset 2824 */ , 
  0x884c /* offset 2825 */ , 
  0x8863 /* offset 2826 */ , 
  0x897e /* offset 2827 */ , 
  0x898b /* offset 2828 */ , 
  0x89d2 /* offset 2829 */ , 
  0x8a00 /* offset 2830 */ , 
  0x8c37 /* offset 2831 */ , 
  0x8c46 /* offset 2832 */ , 
  0x8c55 /* offset 2833 */ , 
  0x8c78 /* offset 2834 */ , 
  0x8c9d /* offset 2835 */ , 
  0x8d64 /* offset 2836 */ , 
  0x8d70 /* offset 2837 */ , 
  0x8db3 /* offset 2838 */ , 
  0x8eab /* offset 2839 */ , 
  0x8eca /* offset 2840 */ , 
  0x8f9b /* offset 2841 */ , 
  0x8fb0 /* offset 2842 */ , 
  0x8fb5 /* offset 2843 */ , 
  0x9091 /* offset 2844 */ , 
  0x9149 /* offset 2845 */ , 
  0x91c6 /* offset 2846 */ , 
  0x91cc /* offset 2847 */ , 
  0x91d1 /* offset 2848 */ , 
  0x9577 /* offset 2849 */ , 
  0x9580 /* offset 2850 */ , 
  0x961c /* offset 2851 */ , 
  0x96b6 /* offset 2852 */ , 
  0x96b9 /* offset 2853 */ , 
  0x96e8 /* offset 2854 */ , 
  0x9751 /* offset 2855 */ , 
  0x975e /* offset 2856 */ , 
  0x9762 /* offset 2857 */ , 
  0x9769 /* offset 2858 */ , 
  0x97cb /* offset 2859 */ , 
  0x97ed /* offset 2860 */ , 
  0x97f3 /* offset 2861 */ , 
  0x9801 /* offset 2862 */ , 
  0x98a8 /* offset 2863 */ , 
  0x98db /* offset 2864 */ , 
  0x98df /* offset 2865 */ , 
  0x9996 /* offset 2866 */ , 
  0x9999 /* offset 2867 */ , 
  0x99ac /* offset 2868 */ , 
  0x9aa8 /* offset 2869 */ , 
  0x9ad8 /* offset 2870 */ , 
  0x9adf /* offset 2871 */ , 
  0x9b25 /* offset 2872 */ , 
  0x9b2f /* offset 2873 */ , 
  0x9b32 /* offset 2874 */ , 
  0x9b3c /* offset 2875 */ , 
  0x9b5a /* offset 2876 */ , 
  0x9ce5 /* offset 2877 */ , 
  0x9e75 /* offset 2878 */ , 
  0x9e7f /* offset 2879 */ , 
  0x9ea5 /* offset 2880 */ , 
  0x9ebb /* offset 2881 */ , 
  0x9ec3 /* offset 2882 */ , 
  0x9ecd /* offset 2883 */ , 
  0x9ed1 /* offset 2884 */ , 
  0x9ef9 /* offset 2885 */ , 
  0x9efd /* offset 2886 */ , 
  0x9f0e /* offset 2887 */ , 
  0x9f13 /* offset 2888 */ , 
  0x9f20 /* offset 2889 */ , 
  0x9f3b /* offset 2890 */ , 
  0x9f4a /* offset 2891 */ , 
  0x9f52 /* offset 2892 */ , 
  0x9f8d /* offset 2893 */ , 
  0x9f9c /* offset 2894 */ , 
  0x9fa0 /* offset 2895 */ , 
  0x3012 /* offset 2896 */ , 
  0x5344 /* offset 2897 */ , 
  0x5345 /* offset 2898 */ , 
  0x304b, 0x3099 /* offset 2899 */ , 
  0x304d, 0x3099 /* offset 2901 */ , 
  0x304f, 0x3099 /* offset 2903 */ , 
  0x3051, 0x3099 /* offset 2905 */ , 
  0x3053, 0x3099 /* offset 2907 */ , 
  0x3055, 0x3099 /* offset 2909 */ , 
  0x3057, 0x3099 /* offset 2911 */ , 
  0x3059, 0x3099 /* offset 2913 */ , 
  0x305b, 0x3099 /* offset 2915 */ , 
  0x305d, 0x3099 /* offset 2917 */ , 
  0x305f, 0x3099 /* offset 2919 */ , 
  0x3061, 0x3099 /* offset 2921 */ , 
  0x3064, 0x3099 /* offset 2923 */ , 
  0x3066, 0x3099 /* offset 2925 */ , 
  0x3068, 0x3099 /* offset 2927 */ , 
  0x306f, 0x3099 /* offset 2929 */ , 
  0x306f, 0x309a /* offset 2931 */ , 
  0x3072, 0x3099 /* offset 2933 */ , 
  0x3072, 0x309a /* offset 2935 */ , 
  0x3075, 0x3099 /* offset 2937 */ , 
  0x3075, 0x309a /* offset 2939 */ , 
  0x3078, 0x3099 /* offset 2941 */ , 
  0x3078, 0x309a /* offset 2943 */ , 
  0x307b, 0x3099 /* offset 2945 */ , 
  0x307b, 0x309a /* offset 2947 */ , 
  0x3046, 0x3099 /* offset 2949 */ , 
  0x20, 0x3099 /* offset 2951 */ , 
  0x20, 0x309a /* offset 2953 */ , 
  0x309d, 0x3099 /* offset 2955 */ , 
  0x3088, 0x308a /* offset 2957 */ , 
  0x30ab, 0x3099 /* offset 2959 */ , 
  0x30ad, 0x3099 /* offset 2961 */ , 
  0x30af, 0x3099 /* offset 2963 */ , 
  0x30b1, 0x3099 /* offset 2965 */ , 
  0x30b3, 0x3099 /* offset 2967 */ , 
  0x30b5, 0x3099 /* offset 2969 */ , 
  0x30b7, 0x3099 /* offset 2971 */ , 
  0x30b9, 0x3099 /* offset 2973 */ , 
  0x30bb, 0x3099 /* offset 2975 */ , 
  0x30bd, 0x3099 /* offset 2977 */ , 
  0x30bf, 0x3099 /* offset 2979 */ , 
  0x30c1, 0x3099 /* offset 2981 */ , 
  0x30c4, 0x3099 /* offset 2983 */ , 
  0x30c6, 0x3099 /* offset 2985 */ , 
  0x30c8, 0x3099 /* offset 2987 */ , 
  0x30cf, 0x3099 /* offset 2989 */ , 
  0x30cf, 0x309a /* offset 2991 */ , 
  0x30d2, 0x3099 /* offset 2993 */ , 
  0x30d2, 0x309a /* offset 2995 */ , 
  0x30d5, 0x3099 /* offset 2997 */ , 
  0x30d5, 0x309a /* offset 2999 */ , 
  0x30d8, 0x3099 /* offset 3001 */ , 
  0x30d8, 0x309a /* offset 3003 */ , 
  0x30db, 0x3099 /* offset 3005 */ , 
  0x30db, 0x309a /* offset 3007 */ , 
  0x30a6, 0x3099 /* offset 3009 */ , 
  0x30ef, 0x3099 /* offset 3011 */ , 
  0x30f0, 0x3099 /* offset 3013 */ , 
  0x30f1, 0x3099 /* offset 3015 */ , 
  0x30f2, 0x3099 /* offset 3017 */ , 
  0x30fd, 0x3099 /* offset 3019 */ , 
  0x30b3, 0x30c8 /* offset 3021 */ , 
  0x1100 /* offset 3023 */ , 
  0x1101 /* offset 3024 */ , 
  0x11aa /* offset 3025 */ , 
  0x1102 /* offset 3026 */ , 
  0x11ac /* offset 3027 */ , 
  0x11ad /* offset 3028 */ , 
  0x1103 /* offset 3029 */ , 
  0x1104 /* offset 3030 */ , 
  0x1105 /* offset 3031 */ , 
  0x11b0 /* offset 3032 */ , 
  0x11b1 /* offset 3033 */ , 
  0x11b2 /* offset 3034 */ , 
  0x11b3 /* offset 3035 */ , 
  0x11b4 /* offset 3036 */ , 
  0x11b5 /* offset 3037 */ , 
  0x111a /* offset 3038 */ , 
  0x1106 /* offset 3039 */ , 
  0x1107 /* offset 3040 */ , 
  0x1108 /* offset 3041 */ , 
  0x1121 /* offset 3042 */ , 
  0x1109 /* offset 3043 */ , 
  0x110a /* offset 3044 */ , 
  0x110b /* offset 3045 */ , 
  0x110c /* offset 3046 */ , 
  0x110d /* offset 3047 */ , 
  0x110e /* offset 3048 */ , 
  0x110f /* offset 3049 */ , 
  0x1110 /* offset 3050 */ , 
  0x1111 /* offset 3051 */ , 
  0x1112 /* offset 3052 */ , 
  0x1161 /* offset 3053 */ , 
  0x1162 /* offset 3054 */ , 
  0x1163 /* offset 3055 */ , 
  0x1164 /* offset 3056 */ , 
  0x1165 /* offset 3057 */ , 
  0x1166 /* offset 3058 */ , 
  0x1167 /* offset 3059 */ , 
  0x1168 /* offset 3060 */ , 
  0x1169 /* offset 3061 */ , 
  0x116a /* offset 3062 */ , 
  0x116b /* offset 3063 */ , 
  0x116c /* offset 3064 */ , 
  0x116d /* offset 3065 */ , 
  0x116e /* offset 3066 */ , 
  0x116f /* offset 3067 */ , 
  0x1170 /* offset 3068 */ , 
  0x1171 /* offset 3069 */ , 
  0x1172 /* offset 3070 */ , 
  0x1173 /* offset 3071 */ , 
  0x1174 /* offset 3072 */ , 
  0x1175 /* offset 3073 */ , 
  0x1160 /* offset 3074 */ , 
  0x1114 /* offset 3075 */ , 
  0x1115 /* offset 3076 */ , 
  0x11c7 /* offset 3077 */ , 
  0x11c8 /* offset 3078 */ , 
  0x11cc /* offset 3079 */ , 
  0x11ce /* offset 3080 */ , 
  0x11d3 /* offset 3081 */ , 
  0x11d7 /* offset 3082 */ , 
  0x11d9 /* offset 3083 */ , 
  0x111c /* offset 3084 */ , 
  0x11dd /* offset 3085 */ , 
  0x11df /* offset 3086 */ , 
  0x111d /* offset 3087 */ , 
  0x111e /* offset 3088 */ , 
  0x1120 /* offset 3089 */ , 
  0x1122 /* offset 3090 */ , 
  0x1123 /* offset 3091 */ , 
  0x1127 /* offset 3092 */ , 
  0x1129 /* offset 3093 */ , 
  0x112b /* offset 3094 */ , 
  0x112c /* offset 3095 */ , 
  0x112d /* offset 3096 */ , 
  0x112e /* offset 3097 */ , 
  0x112f /* offset 3098 */ , 
  0x1132 /* offset 3099 */ , 
  0x1136 /* offset 3100 */ , 
  0x1140 /* offset 3101 */ , 
  0x1147 /* offset 3102 */ , 
  0x114c /* offset 3103 */ , 
  0x11f1 /* offset 3104 */ , 
  0x11f2 /* offset 3105 */ , 
  0x1157 /* offset 3106 */ , 
  0x1158 /* offset 3107 */ , 
  0x1159 /* offset 3108 */ , 
  0x1184 /* offset 3109 */ , 
  0x1185 /* offset 3110 */ , 
  0x1188 /* offset 3111 */ , 
  0x1191 /* offset 3112 */ , 
  0x1192 /* offset 3113 */ , 
  0x1194 /* offset 3114 */ , 
  0x119e /* offset 3115 */ , 
  0x11a1 /* offset 3116 */ , 
  0x4e09 /* offset 3117 */ , 
  0x56db /* offset 3118 */ , 
  0x4e0a /* offset 3119 */ , 
  0x4e2d /* offset 3120 */ , 
  0x4e0b /* offset 3121 */ , 
  0x7532 /* offset 3122 */ , 
  0x4e19 /* offset 3123 */ , 
  0x4e01 /* offset 3124 */ , 
  0x5929 /* offset 3125 */ , 
  0x5730 /* offset 3126 */ , 
  0x28, 0x1100, 0x29 /* offset 3127 */ , 
  0x28, 0x1102, 0x29 /* offset 3130 */ , 
  0x28, 0x1103, 0x29 /* offset 3133 */ , 
  0x28, 0x1105, 0x29 /* offset 3136 */ , 
  0x28, 0x1106, 0x29 /* offset 3139 */ , 
  0x28, 0x1107, 0x29 /* offset 3142 */ , 
  0x28, 0x1109, 0x29 /* offset 3145 */ , 
  0x28, 0x110b, 0x29 /* offset 3148 */ , 
  0x28, 0x110c, 0x29 /* offset 3151 */ , 
  0x28, 0x110e, 0x29 /* offset 3154 */ , 
  0x28, 0x110f, 0x29 /* offset 3157 */ , 
  0x28, 0x1110, 0x29 /* offset 3160 */ , 
  0x28, 0x1111, 0x29 /* offset 3163 */ , 
  0x28, 0x1112, 0x29 /* offset 3166 */ , 
  0x28, 0x1100, 0x1161, 0x29 /* offset 3169 */ , 
  0x28, 0x1102, 0x1161, 0x29 /* offset 3173 */ , 
  0x28, 0x1103, 0x1161, 0x29 /* offset 3177 */ , 
  0x28, 0x1105, 0x1161, 0x29 /* offset 3181 */ , 
  0x28, 0x1106, 0x1161, 0x29 /* offset 3185 */ , 
  0x28, 0x1107, 0x1161, 0x29 /* offset 3189 */ , 
  0x28, 0x1109, 0x1161, 0x29 /* offset 3193 */ , 
  0x28, 0x110b, 0x1161, 0x29 /* offset 3197 */ , 
  0x28, 0x110c, 0x1161, 0x29 /* offset 3201 */ , 
  0x28, 0x110e, 0x1161, 0x29 /* offset 3205 */ , 
  0x28, 0x110f, 0x1161, 0x29 /* offset 3209 */ , 
  0x28, 0x1110, 0x1161, 0x29 /* offset 3213 */ , 
  0x28, 0x1111, 0x1161, 0x29 /* offset 3217 */ , 
  0x28, 0x1112, 0x1161, 0x29 /* offset 3221 */ , 
  0x28, 0x110c, 0x116e, 0x29 /* offset 3225 */ , 
  0x28, 0x4e00, 0x29 /* offset 3229 */ , 
  0x28, 0x4e8c, 0x29 /* offset 3232 */ , 
  0x28, 0x4e09, 0x29 /* offset 3235 */ , 
  0x28, 0x56db, 0x29 /* offset 3238 */ , 
  0x28, 0x4e94, 0x29 /* offset 3241 */ , 
  0x28, 0x516d, 0x29 /* offset 3244 */ , 
  0x28, 0x4e03, 0x29 /* offset 3247 */ , 
  0x28, 0x516b, 0x29 /* offset 3250 */ , 
  0x28, 0x4e5d, 0x29 /* offset 3253 */ , 
  0x28, 0x5341, 0x29 /* offset 3256 */ , 
  0x28, 0x6708, 0x29 /* offset 3259 */ , 
  0x28, 0x706b, 0x29 /* offset 3262 */ , 
  0x28, 0x6c34, 0x29 /* offset 3265 */ , 
  0x28, 0x6728, 0x29 /* offset 3268 */ , 
  0x28, 0x91d1, 0x29 /* offset 3271 */ , 
  0x28, 0x571f, 0x29 /* offset 3274 */ , 
  0x28, 0x65e5, 0x29 /* offset 3277 */ , 
  0x28, 0x682a, 0x29 /* offset 3280 */ , 
  0x28, 0x6709, 0x29 /* offset 3283 */ , 
  0x28, 0x793e, 0x29 /* offset 3286 */ , 
  0x28, 0x540d, 0x29 /* offset 3289 */ , 
  0x28, 0x7279, 0x29 /* offset 3292 */ , 
  0x28, 0x8ca1, 0x29 /* offset 3295 */ , 
  0x28, 0x795d, 0x29 /* offset 3298 */ , 
  0x28, 0x52b4, 0x29 /* offset 3301 */ , 
  0x28, 0x4ee3, 0x29 /* offset 3304 */ , 
  0x28, 0x547c, 0x29 /* offset 3307 */ , 
  0x28, 0x5b66, 0x29 /* offset 3310 */ , 
  0x28, 0x76e3, 0x29 /* offset 3313 */ , 
  0x28, 0x4f01, 0x29 /* offset 3316 */ , 
  0x28, 0x8cc7, 0x29 /* offset 3319 */ , 
  0x28, 0x5354, 0x29 /* offset 3322 */ , 
  0x28, 0x796d, 0x29 /* offset 3325 */ , 
  0x28, 0x4f11, 0x29 /* offset 3328 */ , 
  0x28, 0x81ea, 0x29 /* offset 3331 */ , 
  0x28, 0x81f3, 0x29 /* offset 3334 */ , 
  0x32, 0x31 /* offset 3337 */ , 
  0x32, 0x32 /* offset 3339 */ , 
  0x32, 0x33 /* offset 3341 */ , 
  0x32, 0x34 /* offset 3343 */ , 
  0x32, 0x35 /* offset 3345 */ , 
  0x32, 0x36 /* offset 3347 */ , 
  0x32, 0x37 /* offset 3349 */ , 
  0x32, 0x38 /* offset 3351 */ , 
  0x32, 0x39 /* offset 3353 */ , 
  0x33, 0x30 /* offset 3355 */ , 
  0x33, 0x31 /* offset 3357 */ , 
  0x33, 0x32 /* offset 3359 */ , 
  0x33, 0x33 /* offset 3361 */ , 
  0x33, 0x34 /* offset 3363 */ , 
  0x33, 0x35 /* offset 3365 */ , 
  0x1100, 0x1161 /* offset 3367 */ , 
  0x1102, 0x1161 /* offset 3369 */ , 
  0x1103, 0x1161 /* offset 3371 */ , 
  0x1105, 0x1161 /* offset 3373 */ , 
  0x1106, 0x1161 /* offset 3375 */ , 
  0x1107, 0x1161 /* offset 3377 */ , 
  0x1109, 0x1161 /* offset 3379 */ , 
  0x110b, 0x1161 /* offset 3381 */ , 
  0x110c, 0x1161 /* offset 3383 */ , 
  0x110e, 0x1161 /* offset 3385 */ , 
  0x110f, 0x1161 /* offset 3387 */ , 
  0x1110, 0x1161 /* offset 3389 */ , 
  0x1111, 0x1161 /* offset 3391 */ , 
  0x1112, 0x1161 /* offset 3393 */ , 
  0x4e94 /* offset 3395 */ , 
  0x516d /* offset 3396 */ , 
  0x4e03 /* offset 3397 */ , 
  0x4e5d /* offset 3398 */ , 
  0x682a /* offset 3399 */ , 
  0x6709 /* offset 3400 */ , 
  0x793e /* offset 3401 */ , 
  0x540d /* offset 3402 */ , 
  0x7279 /* offset 3403 */ , 
  0x8ca1 /* offset 3404 */ , 
  0x795d /* offset 3405 */ , 
  0x52b4 /* offset 3406 */ , 
  0x79d8 /* offset 3407 */ , 
  0x7537 /* offset 3408 */ , 
  0x9069 /* offset 3409 */ , 
  0x512a /* offset 3410 */ , 
  0x5370 /* offset 3411 */ , 
  0x6ce8 /* offset 3412 */ , 
  0x9805 /* offset 3413 */ , 
  0x4f11 /* offset 3414 */ , 
  0x5199 /* offset 3415 */ , 
  0x6b63 /* offset 3416 */ , 
  0x5de6 /* offset 3417 */ , 
  0x53f3 /* offset 3418 */ , 
  0x533b /* offset 3419 */ , 
  0x5b97 /* offset 3420 */ , 
  0x5b66 /* offset 3421 */ , 
  0x76e3 /* offset 3422 */ , 
  0x4f01 /* offset 3423 */ , 
  0x8cc7 /* offset 3424 */ , 
  0x5354 /* offset 3425 */ , 
  0x591c /* offset 3426 */ , 
  0x33, 0x36 /* offset 3427 */ , 
  0x33, 0x37 /* offset 3429 */ , 
  0x33, 0x38 /* offset 3431 */ , 
  0x33, 0x39 /* offset 3433 */ , 
  0x34, 0x30 /* offset 3435 */ , 
  0x34, 0x31 /* offset 3437 */ , 
  0x34, 0x32 /* offset 3439 */ , 
  0x34, 0x33 /* offset 3441 */ , 
  0x34, 0x34 /* offset 3443 */ , 
  0x34, 0x35 /* offset 3445 */ , 
  0x34, 0x36 /* offset 3447 */ , 
  0x34, 0x37 /* offset 3449 */ , 
  0x34, 0x38 /* offset 3451 */ , 
  0x34, 0x39 /* offset 3453 */ , 
  0x35, 0x30 /* offset 3455 */ , 
  0x31, 0x6708 /* offset 3457 */ , 
  0x32, 0x6708 /* offset 3459 */ , 
  0x33, 0x6708 /* offset 3461 */ , 
  0x34, 0x6708 /* offset 3463 */ , 
  0x35, 0x6708 /* offset 3465 */ , 
  0x36, 0x6708 /* offset 3467 */ , 
  0x37, 0x6708 /* offset 3469 */ , 
  0x38, 0x6708 /* offset 3471 */ , 
  0x39, 0x6708 /* offset 3473 */ , 
  0x31, 0x30, 0x6708 /* offset 3475 */ , 
  0x31, 0x31, 0x6708 /* offset 3478 */ , 
  0x31, 0x32, 0x6708 /* offset 3481 */ , 
  0x30a2 /* offset 3484 */ , 
  0x30a4 /* offset 3485 */ , 
  0x30a6 /* offset 3486 */ , 
  0x30a8 /* offset 3487 */ , 
  0x30aa /* offset 3488 */ , 
  0x30ab /* offset 3489 */ , 
  0x30ad /* offset 3490 */ , 
  0x30af /* offset 3491 */ , 
  0x30b1 /* offset 3492 */ , 
  0x30b3 /* offset 3493 */ , 
  0x30b5 /* offset 3494 */ , 
  0x30b7 /* offset 3495 */ , 
  0x30b9 /* offset 3496 */ , 
  0x30bb /* offset 3497 */ , 
  0x30bd /* offset 3498 */ , 
  0x30bf /* offset 3499 */ , 
  0x30c1 /* offset 3500 */ , 
  0x30c4 /* offset 3501 */ , 
  0x30c6 /* offset 3502 */ , 
  0x30c8 /* offset 3503 */ , 
  0x30ca /* offset 3504 */ , 
  0x30cb /* offset 3505 */ , 
  0x30cc /* offset 3506 */ , 
  0x30cd /* offset 3507 */ , 
  0x30ce /* offset 3508 */ , 
  0x30cf /* offset 3509 */ , 
  0x30d2 /* offset 3510 */ , 
  0x30d5 /* offset 3511 */ , 
  0x30d8 /* offset 3512 */ , 
  0x30db /* offset 3513 */ , 
  0x30de /* offset 3514 */ , 
  0x30df /* offset 3515 */ , 
  0x30e0 /* offset 3516 */ , 
  0x30e1 /* offset 3517 */ , 
  0x30e2 /* offset 3518 */ , 
  0x30e4 /* offset 3519 */ , 
  0x30e6 /* offset 3520 */ , 
  0x30e8 /* offset 3521 */ , 
  0x30e9 /* offset 3522 */ , 
  0x30ea /* offset 3523 */ , 
  0x30eb /* offset 3524 */ , 
  0x30ec /* offset 3525 */ , 
  0x30ed /* offset 3526 */ , 
  0x30ef /* offset 3527 */ , 
  0x30f0 /* offset 3528 */ , 
  0x30f1 /* offset 3529 */ , 
  0x30f2 /* offset 3530 */ , 
  0x30a2, 0x30cf, 0x309a, 0x30fc, 0x30c8 /* offset 3531 */ , 
  0x30a2, 0x30eb, 0x30d5, 0x30a1 /* offset 3536 */ , 
  0x30a2, 0x30f3, 0x30d8, 0x309a, 0x30a2 /* offset 3540 */ , 
  0x30a2, 0x30fc, 0x30eb /* offset 3545 */ , 
  0x30a4, 0x30cb, 0x30f3, 0x30af, 0x3099 /* offset 3548 */ , 
  0x30a4, 0x30f3, 0x30c1 /* offset 3553 */ , 
  0x30a6, 0x30a9, 0x30f3 /* offset 3556 */ , 
  0x30a8, 0x30b9, 0x30af, 0x30fc, 0x30c8, 0x3099 /* offset 3559 */ , 
  0x30a8, 0x30fc, 0x30ab, 0x30fc /* offset 3565 */ , 
  0x30aa, 0x30f3, 0x30b9 /* offset 3569 */ , 
  0x30aa, 0x30fc, 0x30e0 /* offset 3572 */ , 
  0x30ab, 0x30a4, 0x30ea /* offset 3575 */ , 
  0x30ab, 0x30e9, 0x30c3, 0x30c8 /* offset 3578 */ , 
  0x30ab, 0x30ed, 0x30ea, 0x30fc /* offset 3582 */ , 
  0x30ab, 0x3099, 0x30ed, 0x30f3 /* offset 3586 */ , 
  0x30ab, 0x3099, 0x30f3, 0x30de /* offset 3590 */ , 
  0x30ad, 0x3099, 0x30ab, 0x3099 /* offset 3594 */ , 
  0x30ad, 0x3099, 0x30cb, 0x30fc /* offset 3598 */ , 
  0x30ad, 0x30e5, 0x30ea, 0x30fc /* offset 3602 */ , 
  0x30ad, 0x3099, 0x30eb, 0x30bf, 0x3099, 0x30fc /* offset 3606 */ , 
  0x30ad, 0x30ed /* offset 3612 */ , 
  0x30ad, 0x30ed, 0x30af, 0x3099, 0x30e9, 0x30e0 /* offset 3614 */ , 
  0x30ad, 0x30ed, 0x30e1, 0x30fc, 0x30c8, 0x30eb /* offset 3620 */ , 
  0x30ad, 0x30ed, 0x30ef, 0x30c3, 0x30c8 /* offset 3626 */ , 
  0x30af, 0x3099, 0x30e9, 0x30e0 /* offset 3631 */ , 
  0x30af, 0x3099, 0x30e9, 0x30e0, 0x30c8, 0x30f3 /* offset 3635 */ , 
  0x30af, 0x30eb, 0x30bb, 0x3099, 0x30a4, 0x30ed /* offset 3641 */ , 
  0x30af, 0x30ed, 0x30fc, 0x30cd /* offset 3647 */ , 
  0x30b1, 0x30fc, 0x30b9 /* offset 3651 */ , 
  0x30b3, 0x30eb, 0x30ca /* offset 3654 */ , 
  0x30b3, 0x30fc, 0x30db, 0x309a /* offset 3657 */ , 
  0x30b5, 0x30a4, 0x30af, 0x30eb /* offset 3661 */ , 
  0x30b5, 0x30f3, 0x30c1, 0x30fc, 0x30e0 /* offset 3665 */ , 
  0x30b7, 0x30ea, 0x30f3, 0x30af, 0x3099 /* offset 3670 */ , 
  0x30bb, 0x30f3, 0x30c1 /* offset 3675 */ , 
  0x30bb, 0x30f3, 0x30c8 /* offset 3678 */ , 
  0x30bf, 0x3099, 0x30fc, 0x30b9 /* offset 3681 */ , 
  0x30c6, 0x3099, 0x30b7 /* offset 3685 */ , 
  0x30c8, 0x3099, 0x30eb /* offset 3688 */ , 
  0x30c8, 0x30f3 /* offset 3691 */ , 
  0x30ca, 0x30ce /* offset 3693 */ , 
  0x30ce, 0x30c3, 0x30c8 /* offset 3695 */ , 
  0x30cf, 0x30a4, 0x30c4 /* offset 3698 */ , 
  0x30cf, 0x309a, 0x30fc, 0x30bb, 0x30f3, 0x30c8 /* offset 3701 */ , 
  0x30cf, 0x309a, 0x30fc, 0x30c4 /* offset 3707 */ , 
  0x30cf, 0x3099, 0x30fc, 0x30ec, 0x30eb /* offset 3711 */ , 
  0x30d2, 0x309a, 0x30a2, 0x30b9, 0x30c8, 0x30eb /* offset 3716 */ , 
  0x30d2, 0x309a, 0x30af, 0x30eb /* offset 3722 */ , 
  0x30d2, 0x309a, 0x30b3 /* offset 3726 */ , 
  0x30d2, 0x3099, 0x30eb /* offset 3729 */ , 
  0x30d5, 0x30a1, 0x30e9, 0x30c3, 0x30c8, 0x3099 /* offset 3732 */ , 
  0x30d5, 0x30a3, 0x30fc, 0x30c8 /* offset 3738 */ , 
  0x30d5, 0x3099, 0x30c3, 0x30b7, 0x30a7, 0x30eb /* offset 3742 */ , 
  0x30d5, 0x30e9, 0x30f3 /* offset 3748 */ , 
  0x30d8, 0x30af, 0x30bf, 0x30fc, 0x30eb /* offset 3751 */ , 
  0x30d8, 0x309a, 0x30bd /* offset 3756 */ , 
  0x30d8, 0x309a, 0x30cb, 0x30d2 /* offset 3759 */ , 
  0x30d8, 0x30eb, 0x30c4 /* offset 3763 */ , 
  0x30d8, 0x309a, 0x30f3, 0x30b9 /* offset 3766 */ , 
  0x30d8, 0x309a, 0x30fc, 0x30b7, 0x3099 /* offset 3770 */ , 
  0x30d8, 0x3099, 0x30fc, 0x30bf /* offset 3775 */ , 
  0x30db, 0x309a, 0x30a4, 0x30f3, 0x30c8 /* offset 3779 */ , 
  0x30db, 0x3099, 0x30eb, 0x30c8 /* offset 3784 */ , 
  0x30db, 0x30f3 /* offset 3788 */ , 
  0x30db, 0x309a, 0x30f3, 0x30c8, 0x3099 /* offset 3790 */ , 
  0x30db, 0x30fc, 0x30eb /* offset 3795 */ , 
  0x30db, 0x30fc, 0x30f3 /* offset 3798 */ , 
  0x30de, 0x30a4, 0x30af, 0x30ed /* offset 3801 */ , 
  0x30de, 0x30a4, 0x30eb /* offset 3805 */ , 
  0x30de, 0x30c3, 0x30cf /* offset 3808 */ , 
  0x30de, 0x30eb, 0x30af /* offset 3811 */ , 
  0x30de, 0x30f3, 0x30b7, 0x30e7, 0x30f3 /* offset 3814 */ , 
  0x30df, 0x30af, 0x30ed, 0x30f3 /* offset 3819 */ , 
  0x30df, 0x30ea /* offset 3823 */ , 
  0x30df, 0x30ea, 0x30cf, 0x3099, 0x30fc, 0x30eb /* offset 3825 */ , 
  0x30e1, 0x30ab, 0x3099 /* offset 3831 */ , 
  0x30e1, 0x30ab, 0x3099, 0x30c8, 0x30f3 /* offset 3834 */ , 
  0x30e1, 0x30fc, 0x30c8, 0x30eb /* offset 3839 */ , 
  0x30e4, 0x30fc, 0x30c8, 0x3099 /* offset 3843 */ , 
  0x30e4, 0x30fc, 0x30eb /* offset 3847 */ , 
  0x30e6, 0x30a2, 0x30f3 /* offset 3850 */ , 
  0x30ea, 0x30c3, 0x30c8, 0x30eb /* offset 3853 */ , 
  0x30ea, 0x30e9 /* offset 3857 */ , 
  0x30eb, 0x30d2, 0x309a, 0x30fc /* offset 3859 */ , 
  0x30eb, 0x30fc, 0x30d5, 0x3099, 0x30eb /* offset 3863 */ , 
  0x30ec, 0x30e0 /* offset 3868 */ , 
  0x30ec, 0x30f3, 0x30c8, 0x30b1, 0x3099, 0x30f3 /* offset 3870 */ , 
  0x30ef, 0x30c3, 0x30c8 /* offset 3876 */ , 
  0x30, 0x70b9 /* offset 3879 */ , 
  0x31, 0x70b9 /* offset 3881 */ , 
  0x32, 0x70b9 /* offset 3883 */ , 
  0x33, 0x70b9 /* offset 3885 */ , 
  0x34, 0x70b9 /* offset 3887 */ , 
  0x35, 0x70b9 /* offset 3889 */ , 
  0x36, 0x70b9 /* offset 3891 */ , 
  0x37, 0x70b9 /* offset 3893 */ , 
  0x38, 0x70b9 /* offset 3895 */ , 
  0x39, 0x70b9 /* offset 3897 */ , 
  0x31, 0x30, 0x70b9 /* offset 3899 */ , 
  0x31, 0x31, 0x70b9 /* offset 3902 */ , 
  0x31, 0x32, 0x70b9 /* offset 3905 */ , 
  0x31, 0x33, 0x70b9 /* offset 3908 */ , 
  0x31, 0x34, 0x70b9 /* offset 3911 */ , 
  0x31, 0x35, 0x70b9 /* offset 3914 */ , 
  0x31, 0x36, 0x70b9 /* offset 3917 */ , 
  0x31, 0x37, 0x70b9 /* offset 3920 */ , 
  0x31, 0x38, 0x70b9 /* offset 3923 */ , 
  0x31, 0x39, 0x70b9 /* offset 3926 */ , 
  0x32, 0x30, 0x70b9 /* offset 3929 */ , 
  0x32, 0x31, 0x70b9 /* offset 3932 */ , 
  0x32, 0x32, 0x70b9 /* offset 3935 */ , 
  0x32, 0x33, 0x70b9 /* offset 3938 */ , 
  0x32, 0x34, 0x70b9 /* offset 3941 */ , 
  0x68, 0x50, 0x61 /* offset 3944 */ , 
  0x64, 0x61 /* offset 3947 */ , 
  0x41, 0x55 /* offset 3949 */ , 
  0x62, 0x61, 0x72 /* offset 3951 */ , 
  0x6f, 0x56 /* offset 3954 */ , 
  0x70, 0x63 /* offset 3956 */ , 
  0x5e73, 0x6210 /* offset 3958 */ , 
  0x662d, 0x548c /* offset 3960 */ , 
  0x5927, 0x6b63 /* offset 3962 */ , 
  0x660e, 0x6cbb /* offset 3964 */ , 
  0x682a, 0x5f0f, 0x4f1a, 0x793e /* offset 3966 */ , 
  0x70, 0x41 /* offset 3970 */ , 
  0x6e, 0x41 /* offset 3972 */ , 
  0x3bc, 0x41 /* offset 3974 */ , 
  0x6d, 0x41 /* offset 3976 */ , 
  0x6b, 0x41 /* offset 3978 */ , 
  0x4b, 0x42 /* offset 3980 */ , 
  0x4d, 0x42 /* offset 3982 */ , 
  0x47, 0x42 /* offset 3984 */ , 
  0x63, 0x61, 0x6c /* offset 3986 */ , 
  0x6b, 0x63, 0x61, 0x6c /* offset 3989 */ , 
  0x70, 0x46 /* offset 3993 */ , 
  0x6e, 0x46 /* offset 3995 */ , 
  0x3bc, 0x46 /* offset 3997 */ , 
  0x3bc, 0x67 /* offset 3999 */ , 
  0x6d, 0x67 /* offset 4001 */ , 
  0x6b, 0x67 /* offset 4003 */ , 
  0x48, 0x7a /* offset 4005 */ , 
  0x6b, 0x48, 0x7a /* offset 4007 */ , 
  0x4d, 0x48, 0x7a /* offset 4010 */ , 
  0x47, 0x48, 0x7a /* offset 4013 */ , 
  0x54, 0x48, 0x7a /* offset 4016 */ , 
  0x3bc, 0x6c /* offset 4019 */ , 
  0x6d, 0x6c /* offset 4021 */ , 
  0x64, 0x6c /* offset 4023 */ , 
  0x6b, 0x6c /* offset 4025 */ , 
  0x66, 0x6d /* offset 4027 */ , 
  0x6e, 0x6d /* offset 4029 */ , 
  0x3bc, 0x6d /* offset 4031 */ , 
  0x6d, 0x6d /* offset 4033 */ , 
  0x63, 0x6d /* offset 4035 */ , 
  0x6b, 0x6d /* offset 4037 */ , 
  0x6d, 0x6d, 0x32 /* offset 4039 */ , 
  0x63, 0x6d, 0x32 /* offset 4042 */ , 
  0x6d, 0x32 /* offset 4045 */ , 
  0x6b, 0x6d, 0x32 /* offset 4047 */ , 
  0x6d, 0x6d, 0x33 /* offset 4050 */ , 
  0x63, 0x6d, 0x33 /* offset 4053 */ , 
  0x6d, 0x33 /* offset 4056 */ , 
  0x6b, 0x6d, 0x33 /* offset 4058 */ , 
  0x6d, 0x2215, 0x73 /* offset 4061 */ , 
  0x6d, 0x2215, 0x73, 0x32 /* offset 4064 */ , 
  0x50, 0x61 /* offset 4068 */ , 
  0x6b, 0x50, 0x61 /* offset 4070 */ , 
  0x4d, 0x50, 0x61 /* offset 4073 */ , 
  0x47, 0x50, 0x61 /* offset 4076 */ , 
  0x72, 0x61, 0x64 /* offset 4079 */ , 
  0x72, 0x61, 0x64, 0x2215, 0x73 /* offset 4082 */ , 
  0x72, 0x61, 0x64, 0x2215, 0x73, 0x32 /* offset 4087 */ , 
  0x70, 0x73 /* offset 4093 */ , 
  0x6e, 0x73 /* offset 4095 */ , 
  0x3bc, 0x73 /* offset 4097 */ , 
  0x6d, 0x73 /* offset 4099 */ , 
  0x70, 0x56 /* offset 4101 */ , 
  0x6e, 0x56 /* offset 4103 */ , 
  0x3bc, 0x56 /* offset 4105 */ , 
  0x6d, 0x56 /* offset 4107 */ , 
  0x6b, 0x56 /* offset 4109 */ , 
  0x4d, 0x56 /* offset 4111 */ , 
  0x70, 0x57 /* offset 4113 */ , 
  0x6e, 0x57 /* offset 4115 */ , 
  0x3bc, 0x57 /* offset 4117 */ , 
  0x6d, 0x57 /* offset 4119 */ , 
  0x6b, 0x57 /* offset 4121 */ , 
  0x4d, 0x57 /* offset 4123 */ , 
  0x6b, 0x3a9 /* offset 4125 */ , 
  0x4d, 0x3a9 /* offset 4127 */ , 
  0x61, 0x2e, 0x6d, 0x2e /* offset 4129 */ , 
  0x42, 0x71 /* offset 4133 */ , 
  0x63, 0x63 /* offset 4135 */ , 
  0x63, 0x64 /* offset 4137 */ , 
  0x43, 0x2215, 0x6b, 0x67 /* offset 4139 */ , 
  0x43, 0x6f, 0x2e /* offset 4143 */ , 
  0x64, 0x42 /* offset 4146 */ , 
  0x47, 0x79 /* offset 4148 */ , 
  0x68, 0x61 /* offset 4150 */ , 
  0x48, 0x50 /* offset 4152 */ , 
  0x69, 0x6e /* offset 4154 */ , 
  0x4b, 0x4b /* offset 4156 */ , 
  0x4b, 0x4d /* offset 4158 */ , 
  0x6b, 0x74 /* offset 4160 */ , 
  0x6c, 0x6d /* offset 4162 */ , 
  0x6c, 0x6e /* offset 4164 */ , 
  0x6c, 0x6f, 0x67 /* offset 4166 */ , 
  0x6c, 0x78 /* offset 4169 */ , 
  0x6d, 0x62 /* offset 4171 */ , 
  0x6d, 0x69, 0x6c /* offset 4173 */ , 
  0x6d, 0x6f, 0x6c /* offset 4176 */ , 
  0x50, 0x48 /* offset 4179 */ , 
  0x70, 0x2e, 0x6d, 0x2e /* offset 4181 */ , 
  0x50, 0x50, 0x4d /* offset 4185 */ , 
  0x50, 0x52 /* offset 4188 */ , 
  0x73, 0x72 /* offset 4190 */ , 
  0x53, 0x76 /* offset 4192 */ , 
  0x57, 0x62 /* offset 4194 */ , 
  0x31, 0x65e5 /* offset 4196 */ , 
  0x32, 0x65e5 /* offset 4198 */ , 
  0x33, 0x65e5 /* offset 4200 */ , 
  0x34, 0x65e5 /* offset 4202 */ , 
  0x35, 0x65e5 /* offset 4204 */ , 
  0x36, 0x65e5 /* offset 4206 */ , 
  0x37, 0x65e5 /* offset 4208 */ , 
  0x38, 0x65e5 /* offset 4210 */ , 
  0x39, 0x65e5 /* offset 4212 */ , 
  0x31, 0x30, 0x65e5 /* offset 4214 */ , 
  0x31, 0x31, 0x65e5 /* offset 4217 */ , 
  0x31, 0x32, 0x65e5 /* offset 4220 */ , 
  0x31, 0x33, 0x65e5 /* offset 4223 */ , 
  0x31, 0x34, 0x65e5 /* offset 4226 */ , 
  0x31, 0x35, 0x65e5 /* offset 4229 */ , 
  0x31, 0x36, 0x65e5 /* offset 4232 */ , 
  0x31, 0x37, 0x65e5 /* offset 4235 */ , 
  0x31, 0x38, 0x65e5 /* offset 4238 */ , 
  0x31, 0x39, 0x65e5 /* offset 4241 */ , 
  0x32, 0x30, 0x65e5 /* offset 4244 */ , 
  0x32, 0x31, 0x65e5 /* offset 4247 */ , 
  0x32, 0x32, 0x65e5 /* offset 4250 */ , 
  0x32, 0x33, 0x65e5 /* offset 4253 */ , 
  0x32, 0x34, 0x65e5 /* offset 4256 */ , 
  0x32, 0x35, 0x65e5 /* offset 4259 */ , 
  0x32, 0x36, 0x65e5 /* offset 4262 */ , 
  0x32, 0x37, 0x65e5 /* offset 4265 */ , 
  0x32, 0x38, 0x65e5 /* offset 4268 */ , 
  0x32, 0x39, 0x65e5 /* offset 4271 */ , 
  0x33, 0x30, 0x65e5 /* offset 4274 */ , 
  0x33, 0x31, 0x65e5 /* offset 4277 */ , 
  0x8c48 /* offset 4280 */ , 
  0x66f4 /* offset 4281 */ , 
  0x8cc8 /* offset 4282 */ , 
  0x6ed1 /* offset 4283 */ , 
  0x4e32 /* offset 4284 */ , 
  0x53e5 /* offset 4285 */ , 
  0x5951 /* offset 4286 */ , 
  0x5587 /* offset 4287 */ , 
  0x5948 /* offset 4288 */ , 
  0x61f6 /* offset 4289 */ , 
  0x7669 /* offset 4290 */ , 
  0x7f85 /* offset 4291 */ , 
  0x863f /* offset 4292 */ , 
  0x87ba /* offset 4293 */ , 
  0x88f8 /* offset 4294 */ , 
  0x908f /* offset 4295 */ , 
  0x6a02 /* offset 4296 */ , 
  0x6d1b /* offset 4297 */ , 
  0x70d9 /* offset 4298 */ , 
  0x73de /* offset 4299 */ , 
  0x843d /* offset 4300 */ , 
  0x916a /* offset 4301 */ , 
  0x99f1 /* offset 4302 */ , 
  0x4e82 /* offset 4303 */ , 
  0x5375 /* offset 4304 */ , 
  0x6b04 /* offset 4305 */ , 
  0x721b /* offset 4306 */ , 
  0x862d /* offset 4307 */ , 
  0x9e1e /* offset 4308 */ , 
  0x5d50 /* offset 4309 */ , 
  0x6feb /* offset 4310 */ , 
  0x85cd /* offset 4311 */ , 
  0x8964 /* offset 4312 */ , 
  0x62c9 /* offset 4313 */ , 
  0x81d8 /* offset 4314 */ , 
  0x881f /* offset 4315 */ , 
  0x5eca /* offset 4316 */ , 
  0x6717 /* offset 4317 */ , 
  0x6d6a /* offset 4318 */ , 
  0x72fc /* offset 4319 */ , 
  0x90ce /* offset 4320 */ , 
  0x4f86 /* offset 4321 */ , 
  0x51b7 /* offset 4322 */ , 
  0x52de /* offset 4323 */ , 
  0x64c4 /* offset 4324 */ , 
  0x6ad3 /* offset 4325 */ , 
  0x7210 /* offset 4326 */ , 
  0x76e7 /* offset 4327 */ , 
  0x8606 /* offset 4328 */ , 
  0x865c /* offset 4329 */ , 
  0x8def /* offset 4330 */ , 
  0x9732 /* offset 4331 */ , 
  0x9b6f /* offset 4332 */ , 
  0x9dfa /* offset 4333 */ , 
  0x788c /* offset 4334 */ , 
  0x797f /* offset 4335 */ , 
  0x7da0 /* offset 4336 */ , 
  0x83c9 /* offset 4337 */ , 
  0x9304 /* offset 4338 */ , 
  0x8ad6 /* offset 4339 */ , 
  0x58df /* offset 4340 */ , 
  0x5f04 /* offset 4341 */ , 
  0x7c60 /* offset 4342 */ , 
  0x807e /* offset 4343 */ , 
  0x7262 /* offset 4344 */ , 
  0x78ca /* offset 4345 */ , 
  0x8cc2 /* offset 4346 */ , 
  0x96f7 /* offset 4347 */ , 
  0x58d8 /* offset 4348 */ , 
  0x5c62 /* offset 4349 */ , 
  0x6a13 /* offset 4350 */ , 
  0x6dda /* offset 4351 */ , 
  0x6f0f /* offset 4352 */ , 
  0x7d2f /* offset 4353 */ , 
  0x7e37 /* offset 4354 */ , 
  0x964b /* offset 4355 */ , 
  0x52d2 /* offset 4356 */ , 
  0x808b /* offset 4357 */ , 
  0x51dc /* offset 4358 */ , 
  0x51cc /* offset 4359 */ , 
  0x7a1c /* offset 4360 */ , 
  0x7dbe /* offset 4361 */ , 
  0x83f1 /* offset 4362 */ , 
  0x9675 /* offset 4363 */ , 
  0x8b80 /* offset 4364 */ , 
  0x62cf /* offset 4365 */ , 
  0x8afe /* offset 4366 */ , 
  0x4e39 /* offset 4367 */ , 
  0x5be7 /* offset 4368 */ , 
  0x6012 /* offset 4369 */ , 
  0x7387 /* offset 4370 */ , 
  0x7570 /* offset 4371 */ , 
  0x5317 /* offset 4372 */ , 
  0x78fb /* offset 4373 */ , 
  0x4fbf /* offset 4374 */ , 
  0x5fa9 /* offset 4375 */ , 
  0x4e0d /* offset 4376 */ , 
  0x6ccc /* offset 4377 */ , 
  0x6578 /* offset 4378 */ , 
  0x7d22 /* offset 4379 */ , 
  0x53c3 /* offset 4380 */ , 
  0x585e /* offset 4381 */ , 
  0x7701 /* offset 4382 */ , 
  0x8449 /* offset 4383 */ , 
  0x8aaa /* offset 4384 */ , 
  0x6bba /* offset 4385 */ , 
  0x6c88 /* offset 4386 */ , 
  0x62fe /* offset 4387 */ , 
  0x82e5 /* offset 4388 */ , 
  0x63a0 /* offset 4389 */ , 
  0x7565 /* offset 4390 */ , 
  0x4eae /* offset 4391 */ , 
  0x5169 /* offset 4392 */ , 
  0x51c9 /* offset 4393 */ , 
  0x6881 /* offset 4394 */ , 
  0x7ce7 /* offset 4395 */ , 
  0x826f /* offset 4396 */ , 
  0x8ad2 /* offset 4397 */ , 
  0x91cf /* offset 4398 */ , 
  0x52f5 /* offset 4399 */ , 
  0x5442 /* offset 4400 */ , 
  0x5eec /* offset 4401 */ , 
  0x65c5 /* offset 4402 */ , 
  0x6ffe /* offset 4403 */ , 
  0x792a /* offset 4404 */ , 
  0x95ad /* offset 4405 */ , 
  0x9a6a /* offset 4406 */ , 
  0x9e97 /* offset 4407 */ , 
  0x9ece /* offset 4408 */ , 
  0x66c6 /* offset 4409 */ , 
  0x6b77 /* offset 4410 */ , 
  0x8f62 /* offset 4411 */ , 
  0x5e74 /* offset 4412 */ , 
  0x6190 /* offset 4413 */ , 
  0x6200 /* offset 4414 */ , 
  0x649a /* offset 4415 */ , 
  0x6f23 /* offset 4416 */ , 
  0x7149 /* offset 4417 */ , 
  0x7489 /* offset 4418 */ , 
  0x79ca /* offset 4419 */ , 
  0x7df4 /* offset 4420 */ , 
  0x806f /* offset 4421 */ , 
  0x8f26 /* offset 4422 */ , 
  0x84ee /* offset 4423 */ , 
  0x9023 /* offset 4424 */ , 
  0x934a /* offset 4425 */ , 
  0x5217 /* offset 4426 */ , 
  0x52a3 /* offset 4427 */ , 
  0x54bd /* offset 4428 */ , 
  0x70c8 /* offset 4429 */ , 
  0x88c2 /* offset 4430 */ , 
  0x5ec9 /* offset 4431 */ , 
  0x5ff5 /* offset 4432 */ , 
  0x637b /* offset 4433 */ , 
  0x6bae /* offset 4434 */ , 
  0x7c3e /* offset 4435 */ , 
  0x7375 /* offset 4436 */ , 
  0x4ee4 /* offset 4437 */ , 
  0x56f9 /* offset 4438 */ , 
  0x5dba /* offset 4439 */ , 
  0x601c /* offset 4440 */ , 
  0x73b2 /* offset 4441 */ , 
  0x7469 /* offset 4442 */ , 
  0x7f9a /* offset 4443 */ , 
  0x8046 /* offset 4444 */ , 
  0x9234 /* offset 4445 */ , 
  0x96f6 /* offset 4446 */ , 
  0x9748 /* offset 4447 */ , 
  0x9818 /* offset 4448 */ , 
  0x4f8b /* offset 4449 */ , 
  0x79ae /* offset 4450 */ , 
  0x91b4 /* offset 4451 */ , 
  0x96b8 /* offset 4452 */ , 
  0x60e1 /* offset 4453 */ , 
  0x4e86 /* offset 4454 */ , 
  0x50da /* offset 4455 */ , 
  0x5bee /* offset 4456 */ , 
  0x5c3f /* offset 4457 */ , 
  0x6599 /* offset 4458 */ , 
  0x71ce /* offset 4459 */ , 
  0x7642 /* offset 4460 */ , 
  0x84fc /* offset 4461 */ , 
  0x907c /* offset 4462 */ , 
  0x6688 /* offset 4463 */ , 
  0x962e /* offset 4464 */ , 
  0x5289 /* offset 4465 */ , 
  0x677b /* offset 4466 */ , 
  0x67f3 /* offset 4467 */ , 
  0x6d41 /* offset 4468 */ , 
  0x6e9c /* offset 4469 */ , 
  0x7409 /* offset 4470 */ , 
  0x7559 /* offset 4471 */ , 
  0x786b /* offset 4472 */ , 
  0x7d10 /* offset 4473 */ , 
  0x985e /* offset 4474 */ , 
  0x622e /* offset 4475 */ , 
  0x9678 /* offset 4476 */ , 
  0x502b /* offset 4477 */ , 
  0x5d19 /* offset 4478 */ , 
  0x6dea /* offset 4479 */ , 
  0x8f2a /* offset 4480 */ , 
  0x5f8b /* offset 4481 */ , 
  0x6144 /* offset 4482 */ , 
  0x6817 /* offset 4483 */ , 
  0x9686 /* offset 4484 */ , 
  0x5229 /* offset 4485 */ , 
  0x540f /* offset 4486 */ , 
  0x5c65 /* offset 4487 */ , 
  0x6613 /* offset 4488 */ , 
  0x674e /* offset 4489 */ , 
  0x68a8 /* offset 4490 */ , 
  0x6ce5 /* offset 4491 */ , 
  0x7406 /* offset 4492 */ , 
  0x75e2 /* offset 4493 */ , 
  0x7f79 /* offset 4494 */ , 
  0x88cf /* offset 4495 */ , 
  0x88e1 /* offset 4496 */ , 
  0x96e2 /* offset 4497 */ , 
  0x533f /* offset 4498 */ , 
  0x6eba /* offset 4499 */ , 
  0x541d /* offset 4500 */ , 
  0x71d0 /* offset 4501 */ , 
  0x7498 /* offset 4502 */ , 
  0x85fa /* offset 4503 */ , 
  0x96a3 /* offset 4504 */ , 
  0x9c57 /* offset 4505 */ , 
  0x9e9f /* offset 4506 */ , 
  0x6797 /* offset 4507 */ , 
  0x6dcb /* offset 4508 */ , 
  0x81e8 /* offset 4509 */ , 
  0x7b20 /* offset 4510 */ , 
  0x7c92 /* offset 4511 */ , 
  0x72c0 /* offset 4512 */ , 
  0x7099 /* offset 4513 */ , 
  0x8b58 /* offset 4514 */ , 
  0x4ec0 /* offset 4515 */ , 
  0x8336 /* offset 4516 */ , 
  0x523a /* offset 4517 */ , 
  0x5207 /* offset 4518 */ , 
  0x5ea6 /* offset 4519 */ , 
  0x62d3 /* offset 4520 */ , 
  0x7cd6 /* offset 4521 */ , 
  0x5b85 /* offset 4522 */ , 
  0x6d1e /* offset 4523 */ , 
  0x66b4 /* offset 4524 */ , 
  0x8f3b /* offset 4525 */ , 
  0x964d /* offset 4526 */ , 
  0x5ed3 /* offset 4527 */ , 
  0x5140 /* offset 4528 */ , 
  0x55c0 /* offset 4529 */ , 
  0x585a /* offset 4530 */ , 
  0x6674 /* offset 4531 */ , 
  0x51de /* offset 4532 */ , 
  0x732a /* offset 4533 */ , 
  0x76ca /* offset 4534 */ , 
  0x793c /* offset 4535 */ , 
  0x795e /* offset 4536 */ , 
  0x7965 /* offset 4537 */ , 
  0x798f /* offset 4538 */ , 
  0x9756 /* offset 4539 */ , 
  0x7cbe /* offset 4540 */ , 
  0x8612 /* offset 4541 */ , 
  0x8af8 /* offset 4542 */ , 
  0x9038 /* offset 4543 */ , 
  0x90fd /* offset 4544 */ , 
  0x98ef /* offset 4545 */ , 
  0x98fc /* offset 4546 */ , 
  0x9928 /* offset 4547 */ , 
  0x9db4 /* offset 4548 */ , 
  0x4fae /* offset 4549 */ , 
  0x50e7 /* offset 4550 */ , 
  0x514d /* offset 4551 */ , 
  0x52c9 /* offset 4552 */ , 
  0x52e4 /* offset 4553 */ , 
  0x5351 /* offset 4554 */ , 
  0x559d /* offset 4555 */ , 
  0x5606 /* offset 4556 */ , 
  0x5668 /* offset 4557 */ , 
  0x5840 /* offset 4558 */ , 
  0x58a8 /* offset 4559 */ , 
  0x5c64 /* offset 4560 */ , 
  0x6094 /* offset 4561 */ , 
  0x6168 /* offset 4562 */ , 
  0x618e /* offset 4563 */ , 
  0x61f2 /* offset 4564 */ , 
  0x654f /* offset 4565 */ , 
  0x65e2 /* offset 4566 */ , 
  0x6691 /* offset 4567 */ , 
  0x6885 /* offset 4568 */ , 
  0x6d77 /* offset 4569 */ , 
  0x6e1a /* offset 4570 */ , 
  0x6f22 /* offset 4571 */ , 
  0x716e /* offset 4572 */ , 
  0x722b /* offset 4573 */ , 
  0x7422 /* offset 4574 */ , 
  0x7891 /* offset 4575 */ , 
  0x7949 /* offset 4576 */ , 
  0x7948 /* offset 4577 */ , 
  0x7950 /* offset 4578 */ , 
  0x7956 /* offset 4579 */ , 
  0x798d /* offset 4580 */ , 
  0x798e /* offset 4581 */ , 
  0x7a40 /* offset 4582 */ , 
  0x7a81 /* offset 4583 */ , 
  0x7bc0 /* offset 4584 */ , 
  0x7e09 /* offset 4585 */ , 
  0x7e41 /* offset 4586 */ , 
  0x7f72 /* offset 4587 */ , 
  0x8005 /* offset 4588 */ , 
  0x81ed /* offset 4589 */ , 
  0x8279 /* offset 4590 */ , 
  0x8457 /* offset 4591 */ , 
  0x8910 /* offset 4592 */ , 
  0x8996 /* offset 4593 */ , 
  0x8b01 /* offset 4594 */ , 
  0x8b39 /* offset 4595 */ , 
  0x8cd3 /* offset 4596 */ , 
  0x8d08 /* offset 4597 */ , 
  0x8fb6 /* offset 4598 */ , 
  0x96e3 /* offset 4599 */ , 
  0x97ff /* offset 4600 */ , 
  0x983b /* offset 4601 */ , 
  0x66, 0x66 /* offset 4602 */ , 
  0x66, 0x69 /* offset 4604 */ , 
  0x66, 0x6c /* offset 4606 */ , 
  0x66, 0x66, 0x69 /* offset 4608 */ , 
  0x66, 0x66, 0x6c /* offset 4611 */ , 
  0x73, 0x74 /* offset 4614 */ , 
  0x574, 0x576 /* offset 4616 */ , 
  0x574, 0x565 /* offset 4618 */ , 
  0x574, 0x56b /* offset 4620 */ , 
  0x57e, 0x576 /* offset 4622 */ , 
  0x574, 0x56d /* offset 4624 */ , 
  0x5d9, 0x5b4 /* offset 4626 */ , 
  0x5f2, 0x5b7 /* offset 4628 */ , 
  0x5e2 /* offset 4630 */ , 
  0x5d4 /* offset 4631 */ , 
  0x5db /* offset 4632 */ , 
  0x5dc /* offset 4633 */ , 
  0x5dd /* offset 4634 */ , 
  0x5e8 /* offset 4635 */ , 
  0x5ea /* offset 4636 */ , 
  0x5e9, 0x5c1 /* offset 4637 */ , 
  0x5e9, 0x5c2 /* offset 4639 */ , 
  0x5e9, 0x5bc, 0x5c1 /* offset 4641 */ , 
  0x5e9, 0x5bc, 0x5c2 /* offset 4644 */ , 
  0x5d0, 0x5b7 /* offset 4647 */ , 
  0x5d0, 0x5b8 /* offset 4649 */ , 
  0x5d0, 0x5bc /* offset 4651 */ , 
  0x5d1, 0x5bc /* offset 4653 */ , 
  0x5d2, 0x5bc /* offset 4655 */ , 
  0x5d3, 0x5bc /* offset 4657 */ , 
  0x5d4, 0x5bc /* offset 4659 */ , 
  0x5d5, 0x5bc /* offset 4661 */ , 
  0x5d6, 0x5bc /* offset 4663 */ , 
  0x5d8, 0x5bc /* offset 4665 */ , 
  0x5d9, 0x5bc /* offset 4667 */ , 
  0x5da, 0x5bc /* offset 4669 */ , 
  0x5db, 0x5bc /* offset 4671 */ , 
  0x5dc, 0x5bc /* offset 4673 */ , 
  0x5de, 0x5bc /* offset 4675 */ , 
  0x5e0, 0x5bc /* offset 4677 */ , 
  0x5e1, 0x5bc /* offset 4679 */ , 
  0x5e3, 0x5bc /* offset 4681 */ , 
  0x5e4, 0x5bc /* offset 4683 */ , 
  0x5e6, 0x5bc /* offset 4685 */ , 
  0x5e7, 0x5bc /* offset 4687 */ , 
  0x5e8, 0x5bc /* offset 4689 */ , 
  0x5e9, 0x5bc /* offset 4691 */ , 
  0x5ea, 0x5bc /* offset 4693 */ , 
  0x5d5, 0x5b9 /* offset 4695 */ , 
  0x5d1, 0x5bf /* offset 4697 */ , 
  0x5db, 0x5bf /* offset 4699 */ , 
  0x5e4, 0x5bf /* offset 4701 */ , 
  0x5d0, 0x5dc /* offset 4703 */ , 
  0x671 /* offset 4705 */ , 
  0x67b /* offset 4706 */ , 
  0x67e /* offset 4707 */ , 
  0x680 /* offset 4708 */ , 
  0x67a /* offset 4709 */ , 
  0x67f /* offset 4710 */ , 
  0x679 /* offset 4711 */ , 
  0x6a4 /* offset 4712 */ , 
  0x6a6 /* offset 4713 */ , 
  0x684 /* offset 4714 */ , 
  0x683 /* offset 4715 */ , 
  0x686 /* offset 4716 */ , 
  0x687 /* offset 4717 */ , 
  0x68d /* offset 4718 */ , 
  0x68c /* offset 4719 */ , 
  0x68e /* offset 4720 */ , 
  0x688 /* offset 4721 */ , 
  0x698 /* offset 4722 */ , 
  0x691 /* offset 4723 */ , 
  0x6a9 /* offset 4724 */ , 
  0x6af /* offset 4725 */ , 
  0x6b3 /* offset 4726 */ , 
  0x6b1 /* offset 4727 */ , 
  0x6ba /* offset 4728 */ , 
  0x6bb /* offset 4729 */ , 
  0x6c1 /* offset 4730 */ , 
  0x6be /* offset 4731 */ , 
  0x6d2 /* offset 4732 */ , 
  0x6ad /* offset 4733 */ , 
  0x6c7 /* offset 4734 */ , 
  0x6c6 /* offset 4735 */ , 
  0x6c8 /* offset 4736 */ , 
  0x6cb /* offset 4737 */ , 
  0x6c5 /* offset 4738 */ , 
  0x6c9 /* offset 4739 */ , 
  0x6d0 /* offset 4740 */ , 
  0x649 /* offset 4741 */ , 
  0x64a, 0x654, 0x627 /* offset 4742 */ , 
  0x64a, 0x654, 0x6d5 /* offset 4745 */ , 
  0x64a, 0x654, 0x648 /* offset 4748 */ , 
  0x64a, 0x654, 0x6c7 /* offset 4751 */ , 
  0x64a, 0x654, 0x6c6 /* offset 4754 */ , 
  0x64a, 0x654, 0x6c8 /* offset 4757 */ , 
  0x64a, 0x654, 0x6d0 /* offset 4760 */ , 
  0x64a, 0x654, 0x649 /* offset 4763 */ , 
  0x6cc /* offset 4766 */ , 
  0x64a, 0x654, 0x62c /* offset 4767 */ , 
  0x64a, 0x654, 0x62d /* offset 4770 */ , 
  0x64a, 0x654, 0x645 /* offset 4773 */ , 
  0x64a, 0x654, 0x64a /* offset 4776 */ , 
  0x628, 0x62c /* offset 4779 */ , 
  0x628, 0x62d /* offset 4781 */ , 
  0x628, 0x62e /* offset 4783 */ , 
  0x628, 0x645 /* offset 4785 */ , 
  0x628, 0x649 /* offset 4787 */ , 
  0x628, 0x64a /* offset 4789 */ , 
  0x62a, 0x62c /* offset 4791 */ , 
  0x62a, 0x62d /* offset 4793 */ , 
  0x62a, 0x62e /* offset 4795 */ , 
  0x62a, 0x645 /* offset 4797 */ , 
  0x62a, 0x649 /* offset 4799 */ , 
  0x62a, 0x64a /* offset 4801 */ , 
  0x62b, 0x62c /* offset 4803 */ , 
  0x62b, 0x645 /* offset 4805 */ , 
  0x62b, 0x649 /* offset 4807 */ , 
  0x62b, 0x64a /* offset 4809 */ , 
  0x62c, 0x62d /* offset 4811 */ , 
  0x62c, 0x645 /* offset 4813 */ , 
  0x62d, 0x62c /* offset 4815 */ , 
  0x62d, 0x645 /* offset 4817 */ , 
  0x62e, 0x62c /* offset 4819 */ , 
  0x62e, 0x62d /* offset 4821 */ , 
  0x62e, 0x645 /* offset 4823 */ , 
  0x633, 0x62c /* offset 4825 */ , 
  0x633, 0x62d /* offset 4827 */ , 
  0x633, 0x62e /* offset 4829 */ , 
  0x633, 0x645 /* offset 4831 */ , 
  0x635, 0x62d /* offset 4833 */ , 
  0x635, 0x645 /* offset 4835 */ , 
  0x636, 0x62c /* offset 4837 */ , 
  0x636, 0x62d /* offset 4839 */ , 
  0x636, 0x62e /* offset 4841 */ , 
  0x636, 0x645 /* offset 4843 */ , 
  0x637, 0x62d /* offset 4845 */ , 
  0x637, 0x645 /* offset 4847 */ , 
  0x638, 0x645 /* offset 4849 */ , 
  0x639, 0x62c /* offset 4851 */ , 
  0x639, 0x645 /* offset 4853 */ , 
  0x63a, 0x62c /* offset 4855 */ , 
  0x63a, 0x645 /* offset 4857 */ , 
  0x641, 0x62c /* offset 4859 */ , 
  0x641, 0x62d /* offset 4861 */ , 
  0x641, 0x62e /* offset 4863 */ , 
  0x641, 0x645 /* offset 4865 */ , 
  0x641, 0x649 /* offset 4867 */ , 
  0x641, 0x64a /* offset 4869 */ , 
  0x642, 0x62d /* offset 4871 */ , 
  0x642, 0x645 /* offset 4873 */ , 
  0x642, 0x649 /* offset 4875 */ , 
  0x642, 0x64a /* offset 4877 */ , 
  0x643, 0x627 /* offset 4879 */ , 
  0x643, 0x62c /* offset 4881 */ , 
  0x643, 0x62d /* offset 4883 */ , 
  0x643, 0x62e /* offset 4885 */ , 
  0x643, 0x644 /* offset 4887 */ , 
  0x643, 0x645 /* offset 4889 */ , 
  0x643, 0x649 /* offset 4891 */ , 
  0x643, 0x64a /* offset 4893 */ , 
  0x644, 0x62c /* offset 4895 */ , 
  0x644, 0x62d /* offset 4897 */ , 
  0x644, 0x62e /* offset 4899 */ , 
  0x644, 0x645 /* offset 4901 */ , 
  0x644, 0x649 /* offset 4903 */ , 
  0x644, 0x64a /* offset 4905 */ , 
  0x645, 0x62c /* offset 4907 */ , 
  0x645, 0x62d /* offset 4909 */ , 
  0x645, 0x62e /* offset 4911 */ , 
  0x645, 0x645 /* offset 4913 */ , 
  0x645, 0x649 /* offset 4915 */ , 
  0x645, 0x64a /* offset 4917 */ , 
  0x646, 0x62c /* offset 4919 */ , 
  0x646, 0x62d /* offset 4921 */ , 
  0x646, 0x62e /* offset 4923 */ , 
  0x646, 0x645 /* offset 4925 */ , 
  0x646, 0x649 /* offset 4927 */ , 
  0x646, 0x64a /* offset 4929 */ , 
  0x647, 0x62c /* offset 4931 */ , 
  0x647, 0x645 /* offset 4933 */ , 
  0x647, 0x649 /* offset 4935 */ , 
  0x647, 0x64a /* offset 4937 */ , 
  0x64a, 0x62c /* offset 4939 */ , 
  0x64a, 0x62d /* offset 4941 */ , 
  0x64a, 0x62e /* offset 4943 */ , 
  0x64a, 0x645 /* offset 4945 */ , 
  0x64a, 0x649 /* offset 4947 */ , 
  0x64a, 0x64a /* offset 4949 */ , 
  0x630, 0x670 /* offset 4951 */ , 
  0x631, 0x670 /* offset 4953 */ , 
  0x649, 0x670 /* offset 4955 */ , 
  0x20, 0x64c, 0x651 /* offset 4957 */ , 
  0x20, 0x64d, 0x651 /* offset 4960 */ , 
  0x20, 0x64e, 0x651 /* offset 4963 */ , 
  0x20, 0x64f, 0x651 /* offset 4966 */ , 
  0x20, 0x650, 0x651 /* offset 4969 */ , 
  0x20, 0x651, 0x670 /* offset 4972 */ , 
  0x64a, 0x654, 0x631 /* offset 4975 */ , 
  0x64a, 0x654, 0x632 /* offset 4978 */ , 
  0x64a, 0x654, 0x646 /* offset 4981 */ , 
  0x628, 0x631 /* offset 4984 */ , 
  0x628, 0x632 /* offset 4986 */ , 
  0x628, 0x646 /* offset 4988 */ , 
  0x62a, 0x631 /* offset 4990 */ , 
  0x62a, 0x632 /* offset 4992 */ , 
  0x62a, 0x646 /* offset 4994 */ , 
  0x62b, 0x631 /* offset 4996 */ , 
  0x62b, 0x632 /* offset 4998 */ , 
  0x62b, 0x646 /* offset 5000 */ , 
  0x645, 0x627 /* offset 5002 */ , 
  0x646, 0x631 /* offset 5004 */ , 
  0x646, 0x632 /* offset 5006 */ , 
  0x646, 0x646 /* offset 5008 */ , 
  0x64a, 0x631 /* offset 5010 */ , 
  0x64a, 0x632 /* offset 5012 */ , 
  0x64a, 0x646 /* offset 5014 */ , 
  0x64a, 0x654, 0x62e /* offset 5016 */ , 
  0x64a, 0x654, 0x647 /* offset 5019 */ , 
  0x628, 0x647 /* offset 5022 */ , 
  0x62a, 0x647 /* offset 5024 */ , 
  0x635, 0x62e /* offset 5026 */ , 
  0x644, 0x647 /* offset 5028 */ , 
  0x646, 0x647 /* offset 5030 */ , 
  0x647, 0x670 /* offset 5032 */ , 
  0x64a, 0x647 /* offset 5034 */ , 
  0x62b, 0x647 /* offset 5036 */ , 
  0x633, 0x647 /* offset 5038 */ , 
  0x634, 0x645 /* offset 5040 */ , 
  0x634, 0x647 /* offset 5042 */ , 
  0x640, 0x64e, 0x651 /* offset 5044 */ , 
  0x640, 0x64f, 0x651 /* offset 5047 */ , 
  0x640, 0x650, 0x651 /* offset 5050 */ , 
  0x637, 0x649 /* offset 5053 */ , 
  0x637, 0x64a /* offset 5055 */ , 
  0x639, 0x649 /* offset 5057 */ , 
  0x639, 0x64a /* offset 5059 */ , 
  0x63a, 0x649 /* offset 5061 */ , 
  0x63a, 0x64a /* offset 5063 */ , 
  0x633, 0x649 /* offset 5065 */ , 
  0x633, 0x64a /* offset 5067 */ , 
  0x634, 0x649 /* offset 5069 */ , 
  0x634, 0x64a /* offset 5071 */ , 
  0x62d, 0x649 /* offset 5073 */ , 
  0x62d, 0x64a /* offset 5075 */ , 
  0x62c, 0x649 /* offset 5077 */ , 
  0x62c, 0x64a /* offset 5079 */ , 
  0x62e, 0x649 /* offset 5081 */ , 
  0x62e, 0x64a /* offset 5083 */ , 
  0x635, 0x649 /* offset 5085 */ , 
  0x635, 0x64a /* offset 5087 */ , 
  0x636, 0x649 /* offset 5089 */ , 
  0x636, 0x64a /* offset 5091 */ , 
  0x634, 0x62c /* offset 5093 */ , 
  0x634, 0x62d /* offset 5095 */ , 
  0x634, 0x62e /* offset 5097 */ , 
  0x634, 0x631 /* offset 5099 */ , 
  0x633, 0x631 /* offset 5101 */ , 
  0x635, 0x631 /* offset 5103 */ , 
  0x636, 0x631 /* offset 5105 */ , 
  0x627, 0x64b /* offset 5107 */ , 
  0x62a, 0x62c, 0x645 /* offset 5109 */ , 
  0x62a, 0x62d, 0x62c /* offset 5112 */ , 
  0x62a, 0x62d, 0x645 /* offset 5115 */ , 
  0x62a, 0x62e, 0x645 /* offset 5118 */ , 
  0x62a, 0x645, 0x62c /* offset 5121 */ , 
  0x62a, 0x645, 0x62d /* offset 5124 */ , 
  0x62a, 0x645, 0x62e /* offset 5127 */ , 
  0x62c, 0x645, 0x62d /* offset 5130 */ , 
  0x62d, 0x645, 0x64a /* offset 5133 */ , 
  0x62d, 0x645, 0x649 /* offset 5136 */ , 
  0x633, 0x62d, 0x62c /* offset 5139 */ , 
  0x633, 0x62c, 0x62d /* offset 5142 */ , 
  0x633, 0x62c, 0x649 /* offset 5145 */ , 
  0x633, 0x645, 0x62d /* offset 5148 */ , 
  0x633, 0x645, 0x62c /* offset 5151 */ , 
  0x633, 0x645, 0x645 /* offset 5154 */ , 
  0x635, 0x62d, 0x62d /* offset 5157 */ , 
  0x635, 0x645, 0x645 /* offset 5160 */ , 
  0x634, 0x62d, 0x645 /* offset 5163 */ , 
  0x634, 0x62c, 0x64a /* offset 5166 */ , 
  0x634, 0x645, 0x62e /* offset 5169 */ , 
  0x634, 0x645, 0x645 /* offset 5172 */ , 
  0x636, 0x62d, 0x649 /* offset 5175 */ , 
  0x636, 0x62e, 0x645 /* offset 5178 */ , 
  0x637, 0x645, 0x62d /* offset 5181 */ , 
  0x637, 0x645, 0x645 /* offset 5184 */ , 
  0x637, 0x645, 0x64a /* offset 5187 */ , 
  0x639, 0x62c, 0x645 /* offset 5190 */ , 
  0x639, 0x645, 0x645 /* offset 5193 */ , 
  0x639, 0x645, 0x649 /* offset 5196 */ , 
  0x63a, 0x645, 0x645 /* offset 5199 */ , 
  0x63a, 0x645, 0x64a /* offset 5202 */ , 
  0x63a, 0x645, 0x649 /* offset 5205 */ , 
  0x641, 0x62e, 0x645 /* offset 5208 */ , 
  0x642, 0x645, 0x62d /* offset 5211 */ , 
  0x642, 0x645, 0x645 /* offset 5214 */ , 
  0x644, 0x62d, 0x645 /* offset 5217 */ , 
  0x644, 0x62d, 0x64a /* offset 5220 */ , 
  0x644, 0x62d, 0x649 /* offset 5223 */ , 
  0x644, 0x62c, 0x62c /* offset 5226 */ , 
  0x644, 0x62e, 0x645 /* offset 5229 */ , 
  0x644, 0x645, 0x62d /* offset 5232 */ , 
  0x645, 0x62d, 0x62c /* offset 5235 */ , 
  0x645, 0x62d, 0x645 /* offset 5238 */ , 
  0x645, 0x62d, 0x64a /* offset 5241 */ , 
  0x645, 0x62c, 0x62d /* offset 5244 */ , 
  0x645, 0x62c, 0x645 /* offset 5247 */ , 
  0x645, 0x62e, 0x62c /* offset 5250 */ , 
  0x645, 0x62e, 0x645 /* offset 5253 */ , 
  0x645, 0x62c, 0x62e /* offset 5256 */ , 
  0x647, 0x645, 0x62c /* offset 5259 */ , 
  0x647, 0x645, 0x645 /* offset 5262 */ , 
  0x646, 0x62d, 0x645 /* offset 5265 */ , 
  0x646, 0x62d, 0x649 /* offset 5268 */ , 
  0x646, 0x62c, 0x645 /* offset 5271 */ , 
  0x646, 0x62c, 0x649 /* offset 5274 */ , 
  0x646, 0x645, 0x64a /* offset 5277 */ , 
  0x646, 0x645, 0x649 /* offset 5280 */ , 
  0x64a, 0x645, 0x645 /* offset 5283 */ , 
  0x628, 0x62e, 0x64a /* offset 5286 */ , 
  0x62a, 0x62c, 0x64a /* offset 5289 */ , 
  0x62a, 0x62c, 0x649 /* offset 5292 */ , 
  0x62a, 0x62e, 0x64a /* offset 5295 */ , 
  0x62a, 0x62e, 0x649 /* offset 5298 */ , 
  0x62a, 0x645, 0x64a /* offset 5301 */ , 
  0x62a, 0x645, 0x649 /* offset 5304 */ , 
  0x62c, 0x645, 0x64a /* offset 5307 */ , 
  0x62c, 0x62d, 0x649 /* offset 5310 */ , 
  0x62c, 0x645, 0x649 /* offset 5313 */ , 
  0x633, 0x62e, 0x649 /* offset 5316 */ , 
  0x635, 0x62d, 0x64a /* offset 5319 */ , 
  0x634, 0x62d, 0x64a /* offset 5322 */ , 
  0x636, 0x62d, 0x64a /* offset 5325 */ , 
  0x644, 0x62c, 0x64a /* offset 5328 */ , 
  0x644, 0x645, 0x64a /* offset 5331 */ , 
  0x64a, 0x62d, 0x64a /* offset 5334 */ , 
  0x64a, 0x62c, 0x64a /* offset 5337 */ , 
  0x64a, 0x645, 0x64a /* offset 5340 */ , 
  0x645, 0x645, 0x64a /* offset 5343 */ , 
  0x642, 0x645, 0x64a /* offset 5346 */ , 
  0x646, 0x62d, 0x64a /* offset 5349 */ , 
  0x639, 0x645, 0x64a /* offset 5352 */ , 
  0x643, 0x645, 0x64a /* offset 5355 */ , 
  0x646, 0x62c, 0x62d /* offset 5358 */ , 
  0x645, 0x62e, 0x64a /* offset 5361 */ , 
  0x644, 0x62c, 0x645 /* offset 5364 */ , 
  0x643, 0x645, 0x645 /* offset 5367 */ , 
  0x62c, 0x62d, 0x64a /* offset 5370 */ , 
  0x62d, 0x62c, 0x64a /* offset 5373 */ , 
  0x645, 0x62c, 0x64a /* offset 5376 */ , 
  0x641, 0x645, 0x64a /* offset 5379 */ , 
  0x628, 0x62d, 0x64a /* offset 5382 */ , 
  0x633, 0x62e, 0x64a /* offset 5385 */ , 
  0x646, 0x62c, 0x64a /* offset 5388 */ , 
  0x635, 0x644, 0x6d2 /* offset 5391 */ , 
  0x642, 0x644, 0x6d2 /* offset 5394 */ , 
  0x627, 0x644, 0x644, 0x647 /* offset 5397 */ , 
  0x627, 0x643, 0x628, 0x631 /* offset 5401 */ , 
  0x645, 0x62d, 0x645, 0x62f /* offset 5405 */ , 
  0x635, 0x644, 0x639, 0x645 /* offset 5409 */ , 
  0x631, 0x633, 0x648, 0x644 /* offset 5413 */ , 
  0x639, 0x644, 0x64a, 0x647 /* offset 5417 */ , 
  0x648, 0x633, 0x644, 0x645 /* offset 5421 */ , 
  0x635, 0x644, 0x649 /* offset 5425 */ , 
  0x635, 0x644, 0x649, 0x20, 0x627, 0x644, 0x644, 0x647, 0x20, 0x639, 0x644, 0x64a, 0x647, 0x20, 0x648, 0x633, 0x644, 0x645 /* offset 5428 */ , 
  0x62c, 0x644, 0x20, 0x62c, 0x644, 0x627, 0x644, 0x647 /* offset 5446 */ , 
  0x631, 0x6cc, 0x627, 0x644 /* offset 5454 */ , 
  0x2014 /* offset 5458 */ , 
  0x2013 /* offset 5459 */ , 
  0x5f /* offset 5460 */ , 
  0x7b /* offset 5461 */ , 
  0x7d /* offset 5462 */ , 
  0x3014 /* offset 5463 */ , 
  0x3015 /* offset 5464 */ , 
  0x3010 /* offset 5465 */ , 
  0x3011 /* offset 5466 */ , 
  0x300a /* offset 5467 */ , 
  0x300b /* offset 5468 */ , 
  0x300c /* offset 5469 */ , 
  0x300d /* offset 5470 */ , 
  0x300e /* offset 5471 */ , 
  0x300f /* offset 5472 */ , 
  0x2c /* offset 5473 */ , 
  0x3001 /* offset 5474 */ , 
  0x3a /* offset 5475 */ , 
  0x3f /* offset 5476 */ , 
  0x21 /* offset 5477 */ , 
  0x23 /* offset 5478 */ , 
  0x26 /* offset 5479 */ , 
  0x2a /* offset 5480 */ , 
  0x2d /* offset 5481 */ , 
  0x3c /* offset 5482 */ , 
  0x3e /* offset 5483 */ , 
  0x5c /* offset 5484 */ , 
  0x24 /* offset 5485 */ , 
  0x25 /* offset 5486 */ , 
  0x40 /* offset 5487 */ , 
  0x20, 0x64b /* offset 5488 */ , 
  0x640, 0x64b /* offset 5490 */ , 
  0x20, 0x64c /* offset 5492 */ , 
  0x20, 0x64d /* offset 5494 */ , 
  0x20, 0x64e /* offset 5496 */ , 
  0x640, 0x64e /* offset 5498 */ , 
  0x20, 0x64f /* offset 5500 */ , 
  0x640, 0x64f /* offset 5502 */ , 
  0x20, 0x650 /* offset 5504 */ , 
  0x640, 0x650 /* offset 5506 */ , 
  0x20, 0x651 /* offset 5508 */ , 
  0x640, 0x651 /* offset 5510 */ , 
  0x20, 0x652 /* offset 5512 */ , 
  0x640, 0x652 /* offset 5514 */ , 
  0x621 /* offset 5516 */ , 
  0x627 /* offset 5517 */ , 
  0x628 /* offset 5518 */ , 
  0x629 /* offset 5519 */ , 
  0x62a /* offset 5520 */ , 
  0x62b /* offset 5521 */ , 
  0x62c /* offset 5522 */ , 
  0x62d /* offset 5523 */ , 
  0x62e /* offset 5524 */ , 
  0x62f /* offset 5525 */ , 
  0x630 /* offset 5526 */ , 
  0x631 /* offset 5527 */ , 
  0x632 /* offset 5528 */ , 
  0x633 /* offset 5529 */ , 
  0x634 /* offset 5530 */ , 
  0x635 /* offset 5531 */ , 
  0x636 /* offset 5532 */ , 
  0x637 /* offset 5533 */ , 
  0x638 /* offset 5534 */ , 
  0x639 /* offset 5535 */ , 
  0x63a /* offset 5536 */ , 
  0x641 /* offset 5537 */ , 
  0x642 /* offset 5538 */ , 
  0x643 /* offset 5539 */ , 
  0x644 /* offset 5540 */ , 
  0x645 /* offset 5541 */ , 
  0x646 /* offset 5542 */ , 
  0x647 /* offset 5543 */ , 
  0x648 /* offset 5544 */ , 
  0x64a /* offset 5545 */ , 
  0x644, 0x627, 0x653 /* offset 5546 */ , 
  0x644, 0x627, 0x654 /* offset 5549 */ , 
  0x644, 0x627, 0x655 /* offset 5552 */ , 
  0x644, 0x627 /* offset 5555 */ , 
  0x22 /* offset 5557 */ , 
  0x27 /* offset 5558 */ , 
  0x2f /* offset 5559 */ , 
  0x5b /* offset 5560 */ , 
  0x5d /* offset 5561 */ , 
  0x5e /* offset 5562 */ , 
  0x7c /* offset 5563 */ , 
  0x7e /* offset 5564 */ , 
  0x2985 /* offset 5565 */ , 
  0x2986 /* offset 5566 */ , 
  0x3002 /* offset 5567 */ , 
  0x30fb /* offset 5568 */ , 
  0x30a1 /* offset 5569 */ , 
  0x30a3 /* offset 5570 */ , 
  0x30a5 /* offset 5571 */ , 
  0x30a7 /* offset 5572 */ , 
  0x30a9 /* offset 5573 */ , 
  0x30e3 /* offset 5574 */ , 
  0x30e5 /* offset 5575 */ , 
  0x30e7 /* offset 5576 */ , 
  0x30c3 /* offset 5577 */ , 
  0x30fc /* offset 5578 */ , 
  0x30f3 /* offset 5579 */ , 
  0x3099 /* offset 5580 */ , 
  0x309a /* offset 5581 */ , 
  0xa2 /* offset 5582 */ , 
  0xa3 /* offset 5583 */ , 
  0xac /* offset 5584 */ , 
  0xa6 /* offset 5585 */ , 
  0xa5 /* offset 5586 */ , 
  0x20a9 /* offset 5587 */ , 
  0x2502 /* offset 5588 */ , 
  0x2190 /* offset 5589 */ , 
  0x2191 /* offset 5590 */ , 
  0x2192 /* offset 5591 */ , 
  0x2193 /* offset 5592 */ , 
  0x25a0 /* offset 5593 */ , 
  0x25cb /* offset 5594 */ , 
  0x1d157, 0x1d165 /* offset 5595 */ , 
  0x1d158, 0x1d165 /* offset 5597 */ , 
  0x1d158, 0x1d165, 0x1d16e /* offset 5599 */ , 
  0x1d158, 0x1d165, 0x1d16f /* offset 5602 */ , 
  0x1d158, 0x1d165, 0x1d170 /* offset 5605 */ , 
  0x1d158, 0x1d165, 0x1d171 /* offset 5608 */ , 
  0x1d158, 0x1d165, 0x1d172 /* offset 5611 */ , 
  0x1d1b9, 0x1d165 /* offset 5614 */ , 
  0x1d1ba, 0x1d165 /* offset 5616 */ , 
  0x1d1b9, 0x1d165, 0x1d16e /* offset 5618 */ , 
  0x1d1ba, 0x1d165, 0x1d16e /* offset 5621 */ , 
  0x1d1b9, 0x1d165, 0x1d16f /* offset 5624 */ , 
  0x1d1ba, 0x1d165, 0x1d16f /* offset 5627 */ , 
  0x391 /* offset 5630 */ , 
  0x392 /* offset 5631 */ , 
  0x394 /* offset 5632 */ , 
  0x395 /* offset 5633 */ , 
  0x396 /* offset 5634 */ , 
  0x397 /* offset 5635 */ , 
  0x399 /* offset 5636 */ , 
  0x39a /* offset 5637 */ , 
  0x39b /* offset 5638 */ , 
  0x39c /* offset 5639 */ , 
  0x39d /* offset 5640 */ , 
  0x39e /* offset 5641 */ , 
  0x39f /* offset 5642 */ , 
  0x3a1 /* offset 5643 */ , 
  0x3a3 /* offset 5644 */ , 
  0x3a4 /* offset 5645 */ , 
  0x3a6 /* offset 5646 */ , 
  0x3a7 /* offset 5647 */ , 
  0x3a8 /* offset 5648 */ , 
  0x2207 /* offset 5649 */ , 
  0x3b1 /* offset 5650 */ , 
  0x3b4 /* offset 5651 */ , 
  0x3b6 /* offset 5652 */ , 
  0x3b7 /* offset 5653 */ , 
  0x3bb /* offset 5654 */ , 
  0x3bd /* offset 5655 */ , 
  0x3be /* offset 5656 */ , 
  0x3bf /* offset 5657 */ , 
  0x3c3 /* offset 5658 */ , 
  0x3c4 /* offset 5659 */ , 
  0x3c5 /* offset 5660 */ , 
  0x3c7 /* offset 5661 */ , 
  0x3c8 /* offset 5662 */ , 
  0x3c9 /* offset 5663 */ , 
  0x2202 /* offset 5664 */ , 
  0x4e3d /* offset 5665 */ , 
  0x4e38 /* offset 5666 */ , 
  0x4e41 /* offset 5667 */ , 
  0x20122 /* offset 5668 */ , 
  0x4f60 /* offset 5669 */ , 
  0x4fbb /* offset 5670 */ , 
  0x5002 /* offset 5671 */ , 
  0x507a /* offset 5672 */ , 
  0x5099 /* offset 5673 */ , 
  0x50cf /* offset 5674 */ , 
  0x349e /* offset 5675 */ , 
  0x2063a /* offset 5676 */ , 
  0x5154 /* offset 5677 */ , 
  0x5164 /* offset 5678 */ , 
  0x5177 /* offset 5679 */ , 
  0x2051c /* offset 5680 */ , 
  0x34b9 /* offset 5681 */ , 
  0x5167 /* offset 5682 */ , 
  0x518d /* offset 5683 */ , 
  0x2054b /* offset 5684 */ , 
  0x5197 /* offset 5685 */ , 
  0x51a4 /* offset 5686 */ , 
  0x4ecc /* offset 5687 */ , 
  0x51ac /* offset 5688 */ , 
  0x51b5 /* offset 5689 */ , 
  0x291df /* offset 5690 */ , 
  0x5203 /* offset 5691 */ , 
  0x34df /* offset 5692 */ , 
  0x523b /* offset 5693 */ , 
  0x5246 /* offset 5694 */ , 
  0x5272 /* offset 5695 */ , 
  0x5277 /* offset 5696 */ , 
  0x3515 /* offset 5697 */ , 
  0x52c7 /* offset 5698 */ , 
  0x52fa /* offset 5699 */ , 
  0x5305 /* offset 5700 */ , 
  0x5306 /* offset 5701 */ , 
  0x5349 /* offset 5702 */ , 
  0x535a /* offset 5703 */ , 
  0x5373 /* offset 5704 */ , 
  0x537d /* offset 5705 */ , 
  0x537f /* offset 5706 */ , 
  0x20a2c /* offset 5707 */ , 
  0x7070 /* offset 5708 */ , 
  0x53ca /* offset 5709 */ , 
  0x53df /* offset 5710 */ , 
  0x20b63 /* offset 5711 */ , 
  0x53eb /* offset 5712 */ , 
  0x53f1 /* offset 5713 */ , 
  0x5406 /* offset 5714 */ , 
  0x549e /* offset 5715 */ , 
  0x5438 /* offset 5716 */ , 
  0x5448 /* offset 5717 */ , 
  0x5468 /* offset 5718 */ , 
  0x54a2 /* offset 5719 */ , 
  0x54f6 /* offset 5720 */ , 
  0x5510 /* offset 5721 */ , 
  0x5553 /* offset 5722 */ , 
  0x5563 /* offset 5723 */ , 
  0x5584 /* offset 5724 */ , 
  0x5599 /* offset 5725 */ , 
  0x55ab /* offset 5726 */ , 
  0x55b3 /* offset 5727 */ , 
  0x55c2 /* offset 5728 */ , 
  0x5716 /* offset 5729 */ , 
  0x5717 /* offset 5730 */ , 
  0x5651 /* offset 5731 */ , 
  0x5674 /* offset 5732 */ , 
  0x58ee /* offset 5733 */ , 
  0x57ce /* offset 5734 */ , 
  0x57f4 /* offset 5735 */ , 
  0x580d /* offset 5736 */ , 
  0x578b /* offset 5737 */ , 
  0x5832 /* offset 5738 */ , 
  0x5831 /* offset 5739 */ , 
  0x58ac /* offset 5740 */ , 
  0x214e4 /* offset 5741 */ , 
  0x58f2 /* offset 5742 */ , 
  0x58f7 /* offset 5743 */ , 
  0x5906 /* offset 5744 */ , 
  0x591a /* offset 5745 */ , 
  0x5922 /* offset 5746 */ , 
  0x5962 /* offset 5747 */ , 
  0x216a8 /* offset 5748 */ , 
  0x216ea /* offset 5749 */ , 
  0x59ec /* offset 5750 */ , 
  0x5a1b /* offset 5751 */ , 
  0x5a27 /* offset 5752 */ , 
  0x59d8 /* offset 5753 */ , 
  0x5a66 /* offset 5754 */ , 
  0x36ee /* offset 5755 */ , 
  0x2136a /* offset 5756 */ , 
  0x5b08 /* offset 5757 */ , 
  0x5b3e /* offset 5758 */ , 
  0x219c8 /* offset 5759 */ , 
  0x5bc3 /* offset 5760 */ , 
  0x5bd8 /* offset 5761 */ , 
  0x5bf3 /* offset 5762 */ , 
  0x21b18 /* offset 5763 */ , 
  0x5bff /* offset 5764 */ , 
  0x5c06 /* offset 5765 */ , 
  0x5f33 /* offset 5766 */ , 
  0x3781 /* offset 5767 */ , 
  0x5c60 /* offset 5768 */ , 
  0x5cc0 /* offset 5769 */ , 
  0x5c8d /* offset 5770 */ , 
  0x21de4 /* offset 5771 */ , 
  0x5d43 /* offset 5772 */ , 
  0x21de6 /* offset 5773 */ , 
  0x5d6e /* offset 5774 */ , 
  0x5d6b /* offset 5775 */ , 
  0x5d7c /* offset 5776 */ , 
  0x5de1 /* offset 5777 */ , 
  0x5de2 /* offset 5778 */ , 
  0x382f /* offset 5779 */ , 
  0x5dfd /* offset 5780 */ , 
  0x5e28 /* offset 5781 */ , 
  0x5e3d /* offset 5782 */ , 
  0x5e69 /* offset 5783 */ , 
  0x3862 /* offset 5784 */ , 
  0x22183 /* offset 5785 */ , 
  0x387c /* offset 5786 */ , 
  0x5eb0 /* offset 5787 */ , 
  0x5eb3 /* offset 5788 */ , 
  0x5eb6 /* offset 5789 */ , 
  0x2a392 /* offset 5790 */ , 
  0x22331 /* offset 5791 */ , 
  0x8201 /* offset 5792 */ , 
  0x5f22 /* offset 5793 */ , 
  0x38c7 /* offset 5794 */ , 
  0x232b8 /* offset 5795 */ , 
  0x261da /* offset 5796 */ , 
  0x5f62 /* offset 5797 */ , 
  0x5f6b /* offset 5798 */ , 
  0x38e3 /* offset 5799 */ , 
  0x5f9a /* offset 5800 */ , 
  0x5fcd /* offset 5801 */ , 
  0x5fd7 /* offset 5802 */ , 
  0x5ff9 /* offset 5803 */ , 
  0x6081 /* offset 5804 */ , 
  0x393a /* offset 5805 */ , 
  0x391c /* offset 5806 */ , 
  0x226d4 /* offset 5807 */ , 
  0x60c7 /* offset 5808 */ , 
  0x6148 /* offset 5809 */ , 
  0x614c /* offset 5810 */ , 
  0x614e /* offset 5811 */ , 
  0x617a /* offset 5812 */ , 
  0x61b2 /* offset 5813 */ , 
  0x61a4 /* offset 5814 */ , 
  0x61af /* offset 5815 */ , 
  0x61de /* offset 5816 */ , 
  0x6210 /* offset 5817 */ , 
  0x621b /* offset 5818 */ , 
  0x625d /* offset 5819 */ , 
  0x62b1 /* offset 5820 */ , 
  0x62d4 /* offset 5821 */ , 
  0x6350 /* offset 5822 */ , 
  0x22b0c /* offset 5823 */ , 
  0x633d /* offset 5824 */ , 
  0x62fc /* offset 5825 */ , 
  0x6368 /* offset 5826 */ , 
  0x6383 /* offset 5827 */ , 
  0x63e4 /* offset 5828 */ , 
  0x22bf1 /* offset 5829 */ , 
  0x6422 /* offset 5830 */ , 
  0x63c5 /* offset 5831 */ , 
  0x63a9 /* offset 5832 */ , 
  0x3a2e /* offset 5833 */ , 
  0x6469 /* offset 5834 */ , 
  0x647e /* offset 5835 */ , 
  0x649d /* offset 5836 */ , 
  0x6477 /* offset 5837 */ , 
  0x3a6c /* offset 5838 */ , 
  0x656c /* offset 5839 */ , 
  0x2300a /* offset 5840 */ , 
  0x65e3 /* offset 5841 */ , 
  0x66f8 /* offset 5842 */ , 
  0x6649 /* offset 5843 */ , 
  0x3b19 /* offset 5844 */ , 
  0x3b08 /* offset 5845 */ , 
  0x3ae4 /* offset 5846 */ , 
  0x5192 /* offset 5847 */ , 
  0x5195 /* offset 5848 */ , 
  0x6700 /* offset 5849 */ , 
  0x669c /* offset 5850 */ , 
  0x80ad /* offset 5851 */ , 
  0x43d9 /* offset 5852 */ , 
  0x671b /* offset 5853 */ , 
  0x6721 /* offset 5854 */ , 
  0x675e /* offset 5855 */ , 
  0x6753 /* offset 5856 */ , 
  0x233c3 /* offset 5857 */ , 
  0x3b49 /* offset 5858 */ , 
  0x67fa /* offset 5859 */ , 
  0x6785 /* offset 5860 */ , 
  0x6852 /* offset 5861 */ , 
  0x2346d /* offset 5862 */ , 
  0x688e /* offset 5863 */ , 
  0x681f /* offset 5864 */ , 
  0x6914 /* offset 5865 */ , 
  0x3b9d /* offset 5866 */ , 
  0x6942 /* offset 5867 */ , 
  0x69a3 /* offset 5868 */ , 
  0x69ea /* offset 5869 */ , 
  0x6aa8 /* offset 5870 */ , 
  0x236a3 /* offset 5871 */ , 
  0x6adb /* offset 5872 */ , 
  0x3c18 /* offset 5873 */ , 
  0x6b21 /* offset 5874 */ , 
  0x238a7 /* offset 5875 */ , 
  0x6b54 /* offset 5876 */ , 
  0x3c4e /* offset 5877 */ , 
  0x6b72 /* offset 5878 */ , 
  0x6b9f /* offset 5879 */ , 
  0x6bbb /* offset 5880 */ , 
  0x23a8d /* offset 5881 */ , 
  0x21d0b /* offset 5882 */ , 
  0x23afa /* offset 5883 */ , 
  0x6c4e /* offset 5884 */ , 
  0x23cbc /* offset 5885 */ , 
  0x6cbf /* offset 5886 */ , 
  0x6ccd /* offset 5887 */ , 
  0x6c67 /* offset 5888 */ , 
  0x6d16 /* offset 5889 */ , 
  0x6d3e /* offset 5890 */ , 
  0x6d69 /* offset 5891 */ , 
  0x6d78 /* offset 5892 */ , 
  0x6d85 /* offset 5893 */ , 
  0x23d1e /* offset 5894 */ , 
  0x6d34 /* offset 5895 */ , 
  0x6e2f /* offset 5896 */ , 
  0x6e6e /* offset 5897 */ , 
  0x3d33 /* offset 5898 */ , 
  0x6ecb /* offset 5899 */ , 
  0x6ec7 /* offset 5900 */ , 
  0x23ed1 /* offset 5901 */ , 
  0x6df9 /* offset 5902 */ , 
  0x6f6e /* offset 5903 */ , 
  0x23f5e /* offset 5904 */ , 
  0x23f8e /* offset 5905 */ , 
  0x6fc6 /* offset 5906 */ , 
  0x7039 /* offset 5907 */ , 
  0x701e /* offset 5908 */ , 
  0x701b /* offset 5909 */ , 
  0x3d96 /* offset 5910 */ , 
  0x704a /* offset 5911 */ , 
  0x707d /* offset 5912 */ , 
  0x7077 /* offset 5913 */ , 
  0x70ad /* offset 5914 */ , 
  0x20525 /* offset 5915 */ , 
  0x7145 /* offset 5916 */ , 
  0x24263 /* offset 5917 */ , 
  0x719c /* offset 5918 */ , 
  0x43ab /* offset 5919 */ , 
  0x7228 /* offset 5920 */ , 
  0x7235 /* offset 5921 */ , 
  0x7250 /* offset 5922 */ , 
  0x24608 /* offset 5923 */ , 
  0x7280 /* offset 5924 */ , 
  0x7295 /* offset 5925 */ , 
  0x24735 /* offset 5926 */ , 
  0x24814 /* offset 5927 */ , 
  0x737a /* offset 5928 */ , 
  0x738b /* offset 5929 */ , 
  0x3eac /* offset 5930 */ , 
  0x73a5 /* offset 5931 */ , 
  0x3eb8 /* offset 5932 */ , 
  0x7447 /* offset 5933 */ , 
  0x745c /* offset 5934 */ , 
  0x7471 /* offset 5935 */ , 
  0x7485 /* offset 5936 */ , 
  0x74ca /* offset 5937 */ , 
  0x3f1b /* offset 5938 */ , 
  0x7524 /* offset 5939 */ , 
  0x24c36 /* offset 5940 */ , 
  0x753e /* offset 5941 */ , 
  0x24c92 /* offset 5942 */ , 
  0x2219f /* offset 5943 */ , 
  0x7610 /* offset 5944 */ , 
  0x24fa1 /* offset 5945 */ , 
  0x24fb8 /* offset 5946 */ , 
  0x25044 /* offset 5947 */ , 
  0x3ffc /* offset 5948 */ , 
  0x4008 /* offset 5949 */ , 
  0x76f4 /* offset 5950 */ , 
  0x250f3 /* offset 5951 */ , 
  0x250f2 /* offset 5952 */ , 
  0x25119 /* offset 5953 */ , 
  0x25133 /* offset 5954 */ , 
  0x771e /* offset 5955 */ , 
  0x771f /* offset 5956 */ , 
  0x774a /* offset 5957 */ , 
  0x4039 /* offset 5958 */ , 
  0x778b /* offset 5959 */ , 
  0x4046 /* offset 5960 */ , 
  0x4096 /* offset 5961 */ , 
  0x2541d /* offset 5962 */ , 
  0x784e /* offset 5963 */ , 
  0x78cc /* offset 5964 */ , 
  0x40e3 /* offset 5965 */ , 
  0x25626 /* offset 5966 */ , 
  0x2569a /* offset 5967 */ , 
  0x256c5 /* offset 5968 */ , 
  0x79eb /* offset 5969 */ , 
  0x412f /* offset 5970 */ , 
  0x7a4a /* offset 5971 */ , 
  0x7a4f /* offset 5972 */ , 
  0x2597c /* offset 5973 */ , 
  0x25aa7 /* offset 5974 */ , 
  0x7aae /* offset 5975 */ , 
  0x4202 /* offset 5976 */ , 
  0x25bab /* offset 5977 */ , 
  0x7bc6 /* offset 5978 */ , 
  0x7bc9 /* offset 5979 */ , 
  0x4227 /* offset 5980 */ , 
  0x25c80 /* offset 5981 */ , 
  0x7cd2 /* offset 5982 */ , 
  0x42a0 /* offset 5983 */ , 
  0x7ce8 /* offset 5984 */ , 
  0x7ce3 /* offset 5985 */ , 
  0x7d00 /* offset 5986 */ , 
  0x25f86 /* offset 5987 */ , 
  0x7d63 /* offset 5988 */ , 
  0x4301 /* offset 5989 */ , 
  0x7dc7 /* offset 5990 */ , 
  0x7e02 /* offset 5991 */ , 
  0x7e45 /* offset 5992 */ , 
  0x4334 /* offset 5993 */ , 
  0x26228 /* offset 5994 */ , 
  0x26247 /* offset 5995 */ , 
  0x4359 /* offset 5996 */ , 
  0x262d9 /* offset 5997 */ , 
  0x7f7a /* offset 5998 */ , 
  0x2633e /* offset 5999 */ , 
  0x7f95 /* offset 6000 */ , 
  0x7ffa /* offset 6001 */ , 
  0x264da /* offset 6002 */ , 
  0x26523 /* offset 6003 */ , 
  0x8060 /* offset 6004 */ , 
  0x265a8 /* offset 6005 */ , 
  0x8070 /* offset 6006 */ , 
  0x2335f /* offset 6007 */ , 
  0x43d5 /* offset 6008 */ , 
  0x80b2 /* offset 6009 */ , 
  0x8103 /* offset 6010 */ , 
  0x440b /* offset 6011 */ , 
  0x813e /* offset 6012 */ , 
  0x5ab5 /* offset 6013 */ , 
  0x267a7 /* offset 6014 */ , 
  0x267b5 /* offset 6015 */ , 
  0x23393 /* offset 6016 */ , 
  0x2339c /* offset 6017 */ , 
  0x8204 /* offset 6018 */ , 
  0x8f9e /* offset 6019 */ , 
  0x446b /* offset 6020 */ , 
  0x8291 /* offset 6021 */ , 
  0x828b /* offset 6022 */ , 
  0x829d /* offset 6023 */ , 
  0x52b3 /* offset 6024 */ , 
  0x82b1 /* offset 6025 */ , 
  0x82b3 /* offset 6026 */ , 
  0x82bd /* offset 6027 */ , 
  0x82e6 /* offset 6028 */ , 
  0x26b3c /* offset 6029 */ , 
  0x831d /* offset 6030 */ , 
  0x8363 /* offset 6031 */ , 
  0x83ad /* offset 6032 */ , 
  0x8323 /* offset 6033 */ , 
  0x83bd /* offset 6034 */ , 
  0x83e7 /* offset 6035 */ , 
  0x8353 /* offset 6036 */ , 
  0x83ca /* offset 6037 */ , 
  0x83cc /* offset 6038 */ , 
  0x83dc /* offset 6039 */ , 
  0x26c36 /* offset 6040 */ , 
  0x26d6b /* offset 6041 */ , 
  0x26cd5 /* offset 6042 */ , 
  0x452b /* offset 6043 */ , 
  0x84f1 /* offset 6044 */ , 
  0x84f3 /* offset 6045 */ , 
  0x8516 /* offset 6046 */ , 
  0x273ca /* offset 6047 */ , 
  0x8564 /* offset 6048 */ , 
  0x26f2c /* offset 6049 */ , 
  0x455d /* offset 6050 */ , 
  0x4561 /* offset 6051 */ , 
  0x26fb1 /* offset 6052 */ , 
  0x270d2 /* offset 6053 */ , 
  0x456b /* offset 6054 */ , 
  0x8650 /* offset 6055 */ , 
  0x8667 /* offset 6056 */ , 
  0x8669 /* offset 6057 */ , 
  0x86a9 /* offset 6058 */ , 
  0x8688 /* offset 6059 */ , 
  0x870e /* offset 6060 */ , 
  0x86e2 /* offset 6061 */ , 
  0x8779 /* offset 6062 */ , 
  0x8728 /* offset 6063 */ , 
  0x876b /* offset 6064 */ , 
  0x8786 /* offset 6065 */ , 
  0x4d57 /* offset 6066 */ , 
  0x87e1 /* offset 6067 */ , 
  0x8801 /* offset 6068 */ , 
  0x45f9 /* offset 6069 */ , 
  0x8860 /* offset 6070 */ , 
  0x27667 /* offset 6071 */ , 
  0x88d7 /* offset 6072 */ , 
  0x88de /* offset 6073 */ , 
  0x4635 /* offset 6074 */ , 
  0x88fa /* offset 6075 */ , 
  0x34bb /* offset 6076 */ , 
  0x278ae /* offset 6077 */ , 
  0x27966 /* offset 6078 */ , 
  0x46be /* offset 6079 */ , 
  0x46c7 /* offset 6080 */ , 
  0x8aa0 /* offset 6081 */ , 
  0x8aed /* offset 6082 */ , 
  0x8b8a /* offset 6083 */ , 
  0x27ca8 /* offset 6084 */ , 
  0x8cab /* offset 6085 */ , 
  0x8cc1 /* offset 6086 */ , 
  0x8d1b /* offset 6087 */ , 
  0x8d77 /* offset 6088 */ , 
  0x27f2f /* offset 6089 */ , 
  0x20804 /* offset 6090 */ , 
  0x8dcb /* offset 6091 */ , 
  0x8dbc /* offset 6092 */ , 
  0x8df0 /* offset 6093 */ , 
  0x208de /* offset 6094 */ , 
  0x8ed4 /* offset 6095 */ , 
  0x8f38 /* offset 6096 */ , 
  0x285d2 /* offset 6097 */ , 
  0x285ed /* offset 6098 */ , 
  0x9094 /* offset 6099 */ , 
  0x90f1 /* offset 6100 */ , 
  0x9111 /* offset 6101 */ , 
  0x2872e /* offset 6102 */ , 
  0x911b /* offset 6103 */ , 
  0x9238 /* offset 6104 */ , 
  0x92d7 /* offset 6105 */ , 
  0x92d8 /* offset 6106 */ , 
  0x927c /* offset 6107 */ , 
  0x93f9 /* offset 6108 */ , 
  0x9415 /* offset 6109 */ , 
  0x28bfa /* offset 6110 */ , 
  0x958b /* offset 6111 */ , 
  0x4995 /* offset 6112 */ , 
  0x95b7 /* offset 6113 */ , 
  0x28d77 /* offset 6114 */ , 
  0x49e6 /* offset 6115 */ , 
  0x96c3 /* offset 6116 */ , 
  0x5db2 /* offset 6117 */ , 
  0x9723 /* offset 6118 */ , 
  0x29145 /* offset 6119 */ , 
  0x2921a /* offset 6120 */ , 
  0x4a6e /* offset 6121 */ , 
  0x4a76 /* offset 6122 */ , 
  0x97e0 /* offset 6123 */ , 
  0x2940a /* offset 6124 */ , 
  0x4ab2 /* offset 6125 */ , 
  0x29496 /* offset 6126 */ , 
  0x980b /* offset 6127 */ , 
  0x9829 /* offset 6128 */ , 
  0x295b6 /* offset 6129 */ , 
  0x98e2 /* offset 6130 */ , 
  0x4b33 /* offset 6131 */ , 
  0x9929 /* offset 6132 */ , 
  0x99a7 /* offset 6133 */ , 
  0x99c2 /* offset 6134 */ , 
  0x99fe /* offset 6135 */ , 
  0x4bce /* offset 6136 */ , 
  0x29b30 /* offset 6137 */ , 
  0x9b12 /* offset 6138 */ , 
  0x9c40 /* offset 6139 */ , 
  0x9cfd /* offset 6140 */ , 
  0x4cce /* offset 6141 */ , 
  0x4ced /* offset 6142 */ , 
  0x9d67 /* offset 6143 */ , 
  0x2a0ce /* offset 6144 */ , 
  0x4cf8 /* offset 6145 */ , 
  0x2a105 /* offset 6146 */ , 
  0x2a20e /* offset 6147 */ , 
  0x2a291 /* offset 6148 */ , 
  0x4d56 /* offset 6149 */ , 
  0x9efe /* offset 6150 */ , 
  0x9f05 /* offset 6151 */ , 
  0x9f0f /* offset 6152 */ , 
  0x9f16 /* offset 6153 */ , 
  0x2a600 /* offset 6154 */ 
};

