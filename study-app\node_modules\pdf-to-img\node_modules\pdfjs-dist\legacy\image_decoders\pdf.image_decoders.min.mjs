/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */var A={9306:(A,I,g)=>{var Q=g(4901),B=g(6823),C=TypeError;A.exports=function(A){if(Q(A))return A;throw new C(B(A)+" is not a function")}},3506:(A,I,g)=>{var Q=g(3925),B=String,C=TypeError;A.exports=function(A){if(Q(A))return A;throw new C("Can't set "+B(A)+" as a prototype")}},7080:(A,I,g)=>{var Q=g(4402).has;A.exports=function(A){Q(A);return A}},679:(A,I,g)=>{var Q=g(1625),B=TypeError;A.exports=function(A,I){if(Q(I,A))return A;throw new B("Incorrect invocation")}},8551:(A,I,g)=>{var Q=g(34),B=String,C=TypeError;A.exports=function(A){if(Q(A))return A;throw new C(B(A)+" is not an object")}},7811:A=>{A.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7394:(A,I,g)=>{var Q=g(6706),B=g(4576),C=TypeError;A.exports=Q(ArrayBuffer.prototype,"byteLength","get")||function(A){if("ArrayBuffer"!==B(A))throw new C("ArrayBuffer expected");return A.byteLength}},3238:(A,I,g)=>{var Q=g(9504),B=g(7394),C=Q(ArrayBuffer.prototype.slice);A.exports=function(A){if(0!==B(A))return!1;try{C(A,0,0);return!1}catch(A){return!0}}},5636:(A,I,g)=>{var Q=g(4475),B=g(9504),C=g(6706),E=g(7696),i=g(3238),o=g(7394),D=g(4483),h=g(1548),s=Q.structuredClone,a=Q.ArrayBuffer,t=Q.DataView,G=Q.TypeError,e=Math.min,y=a.prototype,c=t.prototype,r=B(y.slice),w=C(y,"resizable","get"),R=C(y,"maxByteLength","get"),n=B(c.getInt8),S=B(c.setInt8);A.exports=(h||D)&&function(A,I,g){var Q,B=o(A),C=void 0===I?B:E(I),y=!w||!w(A);if(i(A))throw new G("ArrayBuffer is detached");if(h){A=s(A,{transfer:[A]});if(B===C&&(g||y))return A}if(B>=C&&(!g||y))Q=r(A,0,C);else{var c=g&&!y&&R?{maxByteLength:R(A)}:void 0;Q=new a(C,c);for(var N=new t(A),k=new t(Q),J=e(C,B),U=0;U<J;U++)S(k,U,n(N,U))}h||D(A);return Q}},4644:(A,I,g)=>{var Q,B,C,E=g(7811),i=g(3724),o=g(4475),D=g(4901),h=g(34),s=g(9297),a=g(6955),t=g(6823),G=g(6699),e=g(6840),y=g(2106),c=g(1625),r=g(2787),w=g(2967),R=g(8227),n=g(3392),S=g(1181),N=S.enforce,k=S.get,J=o.Int8Array,U=J&&J.prototype,Y=o.Uint8ClampedArray,H=Y&&Y.prototype,d=J&&r(J),L=U&&r(U),K=Object.prototype,M=o.TypeError,l=R("toStringTag"),f=n("TYPED_ARRAY_TAG"),q="TypedArrayConstructor",b=E&&!!w&&"Opera"!==a(o.opera),u=!1,Z={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},p={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(A){var I=r(A);if(h(I)){var g=k(I);return g&&s(g,q)?g[q]:getTypedArrayConstructor(I)}},isTypedArray=function(A){if(!h(A))return!1;var I=a(A);return s(Z,I)||s(p,I)};for(Q in Z)(C=(B=o[Q])&&B.prototype)?N(C)[q]=B:b=!1;for(Q in p)(C=(B=o[Q])&&B.prototype)&&(N(C)[q]=B);if(!b||!D(d)||d===Function.prototype){d=function TypedArray(){throw new M("Incorrect invocation")};if(b)for(Q in Z)o[Q]&&w(o[Q],d)}if(!b||!L||L===K){L=d.prototype;if(b)for(Q in Z)o[Q]&&w(o[Q].prototype,L)}b&&r(H)!==L&&w(H,L);if(i&&!s(L,l)){u=!0;y(L,l,{configurable:!0,get:function(){return h(this)?this[f]:void 0}});for(Q in Z)o[Q]&&G(o[Q],f,Q)}A.exports={NATIVE_ARRAY_BUFFER_VIEWS:b,TYPED_ARRAY_TAG:u&&f,aTypedArray:function(A){if(isTypedArray(A))return A;throw new M("Target is not a typed array")},aTypedArrayConstructor:function(A){if(D(A)&&(!w||c(d,A)))return A;throw new M(t(A)+" is not a typed array constructor")},exportTypedArrayMethod:function(A,I,g,Q){if(i){if(g)for(var B in Z){var C=o[B];if(C&&s(C.prototype,A))try{delete C.prototype[A]}catch(g){try{C.prototype[A]=I}catch(A){}}}L[A]&&!g||e(L,A,g?I:b&&U[A]||I,Q)}},exportTypedArrayStaticMethod:function(A,I,g){var Q,B;if(i){if(w){if(g)for(Q in Z)if((B=o[Q])&&s(B,A))try{delete B[A]}catch(A){}if(d[A]&&!g)return;try{return e(d,A,g?I:b&&d[A]||I)}catch(A){}}for(Q in Z)!(B=o[Q])||B[A]&&!g||e(B,A,I)}},getTypedArrayConstructor,isView:function isView(A){if(!h(A))return!1;var I=a(A);return"DataView"===I||s(Z,I)||s(p,I)},isTypedArray,TypedArray:d,TypedArrayPrototype:L}},5370:(A,I,g)=>{var Q=g(6198);A.exports=function(A,I,g){for(var B=0,C=arguments.length>2?g:Q(I),E=new A(C);C>B;)E[B]=I[B++];return E}},9617:(A,I,g)=>{var Q=g(5397),B=g(5610),C=g(6198),createMethod=function(A){return function(I,g,E){var i=Q(I),o=C(i);if(0===o)return!A&&-1;var D,h=B(E,o);if(A&&g!=g){for(;o>h;)if((D=i[h++])!=D)return!0}else for(;o>h;h++)if((A||h in i)&&i[h]===g)return A||h||0;return!A&&-1}};A.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},4527:(A,I,g)=>{var Q=g(3724),B=g(4376),C=TypeError,E=Object.getOwnPropertyDescriptor,i=Q&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(A){return A instanceof TypeError}}();A.exports=i?function(A,I){if(B(A)&&!E(A,"length").writable)throw new C("Cannot set read only .length");return A.length=I}:function(A,I){return A.length=I}},7628:(A,I,g)=>{var Q=g(6198);A.exports=function(A,I){for(var g=Q(A),B=new I(g),C=0;C<g;C++)B[C]=A[g-C-1];return B}},9928:(A,I,g)=>{var Q=g(6198),B=g(1291),C=RangeError;A.exports=function(A,I,g,E){var i=Q(A),o=B(g),D=o<0?i+o:o;if(D>=i||D<0)throw new C("Incorrect index");for(var h=new I(i),s=0;s<i;s++)h[s]=s===D?E:A[s];return h}},6319:(A,I,g)=>{var Q=g(8551),B=g(9539);A.exports=function(A,I,g,C){try{return C?I(Q(g)[0],g[1]):I(g)}catch(I){B(A,"throw",I)}}},4576:(A,I,g)=>{var Q=g(9504),B=Q({}.toString),C=Q("".slice);A.exports=function(A){return C(B(A),8,-1)}},6955:(A,I,g)=>{var Q=g(2140),B=g(4901),C=g(4576),E=g(8227)("toStringTag"),i=Object,o="Arguments"===C(function(){return arguments}());A.exports=Q?C:function(A){var I,g,Q;return void 0===A?"Undefined":null===A?"Null":"string"==typeof(g=function(A,I){try{return A[I]}catch(A){}}(I=i(A),E))?g:o?C(I):"Object"===(Q=C(I))&&B(I.callee)?"Arguments":Q}},7740:(A,I,g)=>{var Q=g(9297),B=g(5031),C=g(7347),E=g(4913);A.exports=function(A,I,g){for(var i=B(I),o=E.f,D=C.f,h=0;h<i.length;h++){var s=i[h];Q(A,s)||g&&Q(g,s)||o(A,s,D(I,s))}}},2211:(A,I,g)=>{var Q=g(9039);A.exports=!Q((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},2529:A=>{A.exports=function(A,I){return{value:A,done:I}}},6699:(A,I,g)=>{var Q=g(3724),B=g(4913),C=g(6980);A.exports=Q?function(A,I,g){return B.f(A,I,C(1,g))}:function(A,I,g){A[I]=g;return A}},6980:A=>{A.exports=function(A,I){return{enumerable:!(1&A),configurable:!(2&A),writable:!(4&A),value:I}}},4659:(A,I,g)=>{var Q=g(3724),B=g(4913),C=g(6980);A.exports=function(A,I,g){Q?B.f(A,I,C(0,g)):A[I]=g}},2106:(A,I,g)=>{var Q=g(283),B=g(4913);A.exports=function(A,I,g){g.get&&Q(g.get,I,{getter:!0});g.set&&Q(g.set,I,{setter:!0});return B.f(A,I,g)}},6840:(A,I,g)=>{var Q=g(4901),B=g(4913),C=g(283),E=g(9433);A.exports=function(A,I,g,i){i||(i={});var o=i.enumerable,D=void 0!==i.name?i.name:I;Q(g)&&C(g,D,i);if(i.global)o?A[I]=g:E(I,g);else{try{i.unsafe?A[I]&&(o=!0):delete A[I]}catch(A){}o?A[I]=g:B.f(A,I,{value:g,enumerable:!1,configurable:!i.nonConfigurable,writable:!i.nonWritable})}return A}},6279:(A,I,g)=>{var Q=g(6840);A.exports=function(A,I,g){for(var B in I)Q(A,B,I[B],g);return A}},9433:(A,I,g)=>{var Q=g(4475),B=Object.defineProperty;A.exports=function(A,I){try{B(Q,A,{value:I,configurable:!0,writable:!0})}catch(g){Q[A]=I}return I}},3724:(A,I,g)=>{var Q=g(9039);A.exports=!Q((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4483:(A,I,g)=>{var Q,B,C,E,i=g(4475),o=g(9714),D=g(1548),h=i.structuredClone,s=i.ArrayBuffer,a=i.MessageChannel,t=!1;if(D)t=function(A){h(A,{transfer:[A]})};else if(s)try{a||(Q=o("worker_threads"))&&(a=Q.MessageChannel);if(a){B=new a;C=new s(2);E=function(A){B.port1.postMessage(null,[A])};if(2===C.byteLength){E(C);0===C.byteLength&&(t=E)}}}catch(A){}A.exports=t},4055:(A,I,g)=>{var Q=g(4475),B=g(34),C=Q.document,E=B(C)&&B(C.createElement);A.exports=function(A){return E?C.createElement(A):{}}},6837:A=>{var I=TypeError;A.exports=function(A){if(A>9007199254740991)throw I("Maximum allowed index exceeded");return A}},5002:A=>{A.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},7290:(A,I,g)=>{var Q=g(516),B=g(9088);A.exports=!Q&&!B&&"object"==typeof window&&"object"==typeof document},516:A=>{A.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},9088:(A,I,g)=>{var Q=g(4475),B=g(4576);A.exports="process"===B(Q.process)},9392:A=>{A.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},7388:(A,I,g)=>{var Q,B,C=g(4475),E=g(9392),i=C.process,o=C.Deno,D=i&&i.versions||o&&o.version,h=D&&D.v8;h&&(B=(Q=h.split("."))[0]>0&&Q[0]<4?1:+(Q[0]+Q[1]));!B&&E&&(!(Q=E.match(/Edge\/(\d+)/))||Q[1]>=74)&&(Q=E.match(/Chrome\/(\d+)/))&&(B=+Q[1]);A.exports=B},8727:A=>{A.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6193:(A,I,g)=>{var Q=g(9504),B=Error,C=Q("".replace),E=String(new B("zxcasd").stack),i=/\n\s*at [^:]*:[^\n]*/,o=i.test(E);A.exports=function(A,I){if(o&&"string"==typeof A&&!B.prepareStackTrace)for(;I--;)A=C(A,i,"");return A}},6518:(A,I,g)=>{var Q=g(4475),B=g(7347).f,C=g(6699),E=g(6840),i=g(9433),o=g(7740),D=g(2796);A.exports=function(A,I){var g,h,s,a,t,G=A.target,e=A.global,y=A.stat;if(g=e?Q:y?Q[G]||i(G,{}):Q[G]&&Q[G].prototype)for(h in I){a=I[h];s=A.dontCallGetSet?(t=B(g,h))&&t.value:g[h];if(!D(e?h:G+(y?".":"#")+h,A.forced)&&void 0!==s){if(typeof a==typeof s)continue;o(a,s)}(A.sham||s&&s.sham)&&C(a,"sham",!0);E(g,h,a,A)}}},9039:A=>{A.exports=function(A){try{return!!A()}catch(A){return!0}}},6080:(A,I,g)=>{var Q=g(7476),B=g(9306),C=g(616),E=Q(Q.bind);A.exports=function(A,I){B(A);return void 0===I?A:C?E(A,I):function(){return A.apply(I,arguments)}}},616:(A,I,g)=>{var Q=g(9039);A.exports=!Q((function(){var A=function(){}.bind();return"function"!=typeof A||A.hasOwnProperty("prototype")}))},9565:(A,I,g)=>{var Q=g(616),B=Function.prototype.call;A.exports=Q?B.bind(B):function(){return B.apply(B,arguments)}},350:(A,I,g)=>{var Q=g(3724),B=g(9297),C=Function.prototype,E=Q&&Object.getOwnPropertyDescriptor,i=B(C,"name"),o=i&&"something"===function something(){}.name,D=i&&(!Q||Q&&E(C,"name").configurable);A.exports={EXISTS:i,PROPER:o,CONFIGURABLE:D}},6706:(A,I,g)=>{var Q=g(9504),B=g(9306);A.exports=function(A,I,g){try{return Q(B(Object.getOwnPropertyDescriptor(A,I)[g]))}catch(A){}}},7476:(A,I,g)=>{var Q=g(4576),B=g(9504);A.exports=function(A){if("Function"===Q(A))return B(A)}},9504:(A,I,g)=>{var Q=g(616),B=Function.prototype,C=B.call,E=Q&&B.bind.bind(C,C);A.exports=Q?E:function(A){return function(){return C.apply(A,arguments)}}},7751:(A,I,g)=>{var Q=g(4475),B=g(4901);A.exports=function(A,I){return arguments.length<2?(g=Q[A],B(g)?g:void 0):Q[A]&&Q[A][I];var g}},1767:A=>{A.exports=function(A){return{iterator:A,next:A.next,done:!1}}},851:(A,I,g)=>{var Q=g(6955),B=g(5966),C=g(4117),E=g(6269),i=g(8227)("iterator");A.exports=function(A){if(!C(A))return B(A,i)||B(A,"@@iterator")||E[Q(A)]}},81:(A,I,g)=>{var Q=g(9565),B=g(9306),C=g(8551),E=g(6823),i=g(851),o=TypeError;A.exports=function(A,I){var g=arguments.length<2?i(A):I;if(B(g))return C(Q(g,A));throw new o(E(A)+" is not iterable")}},5966:(A,I,g)=>{var Q=g(9306),B=g(4117);A.exports=function(A,I){var g=A[I];return B(g)?void 0:Q(g)}},3789:(A,I,g)=>{var Q=g(9306),B=g(8551),C=g(9565),E=g(1291),i=g(1767),o="Invalid size",D=RangeError,h=TypeError,s=Math.max,SetRecord=function(A,I){this.set=A;this.size=s(I,0);this.has=Q(A.has);this.keys=Q(A.keys)};SetRecord.prototype={getIterator:function(){return i(B(C(this.keys,this.set)))},includes:function(A){return C(this.has,this.set,A)}};A.exports=function(A){B(A);var I=+A.size;if(I!=I)throw new h(o);var g=E(I);if(g<0)throw new D(o);return new SetRecord(A,g)}},4475:function(A){var check=function(A){return A&&A.Math===Math&&A};A.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:(A,I,g)=>{var Q=g(9504),B=g(8981),C=Q({}.hasOwnProperty);A.exports=Object.hasOwn||function hasOwn(A,I){return C(B(A),I)}},421:A=>{A.exports={}},397:(A,I,g)=>{var Q=g(7751);A.exports=Q("document","documentElement")},5917:(A,I,g)=>{var Q=g(3724),B=g(9039),C=g(4055);A.exports=!Q&&!B((function(){return 7!==Object.defineProperty(C("div"),"a",{get:function(){return 7}}).a}))},7055:(A,I,g)=>{var Q=g(9504),B=g(9039),C=g(4576),E=Object,i=Q("".split);A.exports=B((function(){return!E("z").propertyIsEnumerable(0)}))?function(A){return"String"===C(A)?i(A,""):E(A)}:E},3167:(A,I,g)=>{var Q=g(4901),B=g(34),C=g(2967);A.exports=function(A,I,g){var E,i;C&&Q(E=I.constructor)&&E!==g&&B(i=E.prototype)&&i!==g.prototype&&C(A,i);return A}},3706:(A,I,g)=>{var Q=g(9504),B=g(4901),C=g(7629),E=Q(Function.toString);B(C.inspectSource)||(C.inspectSource=function(A){return E(A)});A.exports=C.inspectSource},1181:(A,I,g)=>{var Q,B,C,E=g(8622),i=g(4475),o=g(34),D=g(6699),h=g(9297),s=g(7629),a=g(6119),t=g(421),G="Object already initialized",e=i.TypeError,y=i.WeakMap;if(E||s.state){var c=s.state||(s.state=new y);c.get=c.get;c.has=c.has;c.set=c.set;Q=function(A,I){if(c.has(A))throw new e(G);I.facade=A;c.set(A,I);return I};B=function(A){return c.get(A)||{}};C=function(A){return c.has(A)}}else{var r=a("state");t[r]=!0;Q=function(A,I){if(h(A,r))throw new e(G);I.facade=A;D(A,r,I);return I};B=function(A){return h(A,r)?A[r]:{}};C=function(A){return h(A,r)}}A.exports={set:Q,get:B,has:C,enforce:function(A){return C(A)?B(A):Q(A,{})},getterFor:function(A){return function(I){var g;if(!o(I)||(g=B(I)).type!==A)throw new e("Incompatible receiver, "+A+" required");return g}}}},4209:(A,I,g)=>{var Q=g(8227),B=g(6269),C=Q("iterator"),E=Array.prototype;A.exports=function(A){return void 0!==A&&(B.Array===A||E[C]===A)}},4376:(A,I,g)=>{var Q=g(4576);A.exports=Array.isArray||function isArray(A){return"Array"===Q(A)}},1108:(A,I,g)=>{var Q=g(6955);A.exports=function(A){var I=Q(A);return"BigInt64Array"===I||"BigUint64Array"===I}},4901:A=>{var I="object"==typeof document&&document.all;A.exports=void 0===I&&void 0!==I?function(A){return"function"==typeof A||A===I}:function(A){return"function"==typeof A}},2796:(A,I,g)=>{var Q=g(9039),B=g(4901),C=/#|\.prototype\./,isForced=function(A,I){var g=i[E(A)];return g===D||g!==o&&(B(I)?Q(I):!!I)},E=isForced.normalize=function(A){return String(A).replace(C,".").toLowerCase()},i=isForced.data={},o=isForced.NATIVE="N",D=isForced.POLYFILL="P";A.exports=isForced},4117:A=>{A.exports=function(A){return null==A}},34:(A,I,g)=>{var Q=g(4901);A.exports=function(A){return"object"==typeof A?null!==A:Q(A)}},3925:(A,I,g)=>{var Q=g(34);A.exports=function(A){return Q(A)||null===A}},6395:A=>{A.exports=!1},757:(A,I,g)=>{var Q=g(7751),B=g(4901),C=g(1625),E=g(7040),i=Object;A.exports=E?function(A){return"symbol"==typeof A}:function(A){var I=Q("Symbol");return B(I)&&C(I.prototype,i(A))}},507:(A,I,g)=>{var Q=g(9565);A.exports=function(A,I,g){for(var B,C,E=g?A:A.iterator,i=A.next;!(B=Q(i,E)).done;)if(void 0!==(C=I(B.value)))return C}},2652:(A,I,g)=>{var Q=g(6080),B=g(9565),C=g(8551),E=g(6823),i=g(4209),o=g(6198),D=g(1625),h=g(81),s=g(851),a=g(9539),t=TypeError,Result=function(A,I){this.stopped=A;this.result=I},G=Result.prototype;A.exports=function(A,I,g){var e,y,c,r,w,R,n,S=g&&g.that,N=!(!g||!g.AS_ENTRIES),k=!(!g||!g.IS_RECORD),J=!(!g||!g.IS_ITERATOR),U=!(!g||!g.INTERRUPTED),Y=Q(I,S),stop=function(A){e&&a(e,"normal",A);return new Result(!0,A)},callFn=function(A){if(N){C(A);return U?Y(A[0],A[1],stop):Y(A[0],A[1])}return U?Y(A,stop):Y(A)};if(k)e=A.iterator;else if(J)e=A;else{if(!(y=s(A)))throw new t(E(A)+" is not iterable");if(i(y)){for(c=0,r=o(A);r>c;c++)if((w=callFn(A[c]))&&D(G,w))return w;return new Result(!1)}e=h(A,y)}R=k?A.next:e.next;for(;!(n=B(R,e)).done;){try{w=callFn(n.value)}catch(A){a(e,"throw",A)}if("object"==typeof w&&w&&D(G,w))return w}return new Result(!1)}},9539:(A,I,g)=>{var Q=g(9565),B=g(8551),C=g(5966);A.exports=function(A,I,g){var E,i;B(A);try{if(!(E=C(A,"return"))){if("throw"===I)throw g;return g}E=Q(E,A)}catch(A){i=!0;E=A}if("throw"===I)throw g;if(i)throw E;B(E);return g}},9462:(A,I,g)=>{var Q=g(9565),B=g(2360),C=g(6699),E=g(6279),i=g(8227),o=g(1181),D=g(5966),h=g(7657).IteratorPrototype,s=g(2529),a=g(9539),t=i("toStringTag"),G="IteratorHelper",e="WrapForValidIterator",y=o.set,createIteratorProxyPrototype=function(A){var I=o.getterFor(A?e:G);return E(B(h),{next:function next(){var g=I(this);if(A)return g.nextHandler();try{var Q=g.done?void 0:g.nextHandler();return s(Q,g.done)}catch(A){g.done=!0;throw A}},return:function(){var g=I(this),B=g.iterator;g.done=!0;if(A){var C=D(B,"return");return C?Q(C,B):s(void 0,!0)}if(g.inner)try{a(g.inner.iterator,"normal")}catch(A){return a(B,"throw",A)}a(B,"normal");return s(void 0,!0)}})},c=createIteratorProxyPrototype(!0),r=createIteratorProxyPrototype(!1);C(r,t,"Iterator Helper");A.exports=function(A,I){var g=function Iterator(g,Q){if(Q){Q.iterator=g.iterator;Q.next=g.next}else Q=g;Q.type=I?e:G;Q.nextHandler=A;Q.counter=0;Q.done=!1;y(this,Q)};g.prototype=I?c:r;return g}},713:(A,I,g)=>{var Q=g(9565),B=g(9306),C=g(8551),E=g(1767),i=g(9462),o=g(6319),D=i((function(){var A=this.iterator,I=C(Q(this.next,A));if(!(this.done=!!I.done))return o(A,this.mapper,[I.value,this.counter++],!0)}));A.exports=function map(A){C(this);B(A);return new D(E(this),{mapper:A})}},7657:(A,I,g)=>{var Q,B,C,E=g(9039),i=g(4901),o=g(34),D=g(2360),h=g(2787),s=g(6840),a=g(8227),t=g(6395),G=a("iterator"),e=!1;[].keys&&("next"in(C=[].keys())?(B=h(h(C)))!==Object.prototype&&(Q=B):e=!0);!o(Q)||E((function(){var A={};return Q[G].call(A)!==A}))?Q={}:t&&(Q=D(Q));i(Q[G])||s(Q,G,(function(){return this}));A.exports={IteratorPrototype:Q,BUGGY_SAFARI_ITERATORS:e}},6269:A=>{A.exports={}},6198:(A,I,g)=>{var Q=g(8014);A.exports=function(A){return Q(A.length)}},283:(A,I,g)=>{var Q=g(9504),B=g(9039),C=g(4901),E=g(9297),i=g(3724),o=g(350).CONFIGURABLE,D=g(3706),h=g(1181),s=h.enforce,a=h.get,t=String,G=Object.defineProperty,e=Q("".slice),y=Q("".replace),c=Q([].join),r=i&&!B((function(){return 8!==G((function(){}),"length",{value:8}).length})),w=String(String).split("String"),R=A.exports=function(A,I,g){"Symbol("===e(t(I),0,7)&&(I="["+y(t(I),/^Symbol\(([^)]*)\).*$/,"$1")+"]");g&&g.getter&&(I="get "+I);g&&g.setter&&(I="set "+I);(!E(A,"name")||o&&A.name!==I)&&(i?G(A,"name",{value:I,configurable:!0}):A.name=I);r&&g&&E(g,"arity")&&A.length!==g.arity&&G(A,"length",{value:g.arity});try{g&&E(g,"constructor")&&g.constructor?i&&G(A,"prototype",{writable:!1}):A.prototype&&(A.prototype=void 0)}catch(A){}var Q=s(A);E(Q,"source")||(Q.source=c(w,"string"==typeof I?I:""));return A};Function.prototype.toString=R((function toString(){return C(this)&&a(this).source||D(this)}),"toString")},741:A=>{var I=Math.ceil,g=Math.floor;A.exports=Math.trunc||function trunc(A){var Q=+A;return(Q>0?g:I)(Q)}},2603:(A,I,g)=>{var Q=g(655);A.exports=function(A,I){return void 0===A?arguments.length<2?"":I:Q(A)}},2360:(A,I,g)=>{var Q,B=g(8551),C=g(6801),E=g(8727),i=g(421),o=g(397),D=g(4055),h=g(6119),s="prototype",a="script",t=h("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(A){return"<"+a+">"+A+"</"+a+">"},NullProtoObjectViaActiveX=function(A){A.write(scriptTag(""));A.close();var I=A.parentWindow.Object;A=null;return I},NullProtoObject=function(){try{Q=new ActiveXObject("htmlfile")}catch(A){}NullProtoObject="undefined"!=typeof document?document.domain&&Q?NullProtoObjectViaActiveX(Q):function(){var A,I=D("iframe"),g="java"+a+":";I.style.display="none";o.appendChild(I);I.src=String(g);(A=I.contentWindow.document).open();A.write(scriptTag("document.F=Object"));A.close();return A.F}():NullProtoObjectViaActiveX(Q);for(var A=E.length;A--;)delete NullProtoObject[s][E[A]];return NullProtoObject()};i[t]=!0;A.exports=Object.create||function create(A,I){var g;if(null!==A){EmptyConstructor[s]=B(A);g=new EmptyConstructor;EmptyConstructor[s]=null;g[t]=A}else g=NullProtoObject();return void 0===I?g:C.f(g,I)}},6801:(A,I,g)=>{var Q=g(3724),B=g(8686),C=g(4913),E=g(8551),i=g(5397),o=g(1072);I.f=Q&&!B?Object.defineProperties:function defineProperties(A,I){E(A);for(var g,Q=i(I),B=o(I),D=B.length,h=0;D>h;)C.f(A,g=B[h++],Q[g]);return A}},4913:(A,I,g)=>{var Q=g(3724),B=g(5917),C=g(8686),E=g(8551),i=g(6969),o=TypeError,D=Object.defineProperty,h=Object.getOwnPropertyDescriptor,s="enumerable",a="configurable",t="writable";I.f=Q?C?function defineProperty(A,I,g){E(A);I=i(I);E(g);if("function"==typeof A&&"prototype"===I&&"value"in g&&t in g&&!g[t]){var Q=h(A,I);if(Q&&Q[t]){A[I]=g.value;g={configurable:a in g?g[a]:Q[a],enumerable:s in g?g[s]:Q[s],writable:!1}}}return D(A,I,g)}:D:function defineProperty(A,I,g){E(A);I=i(I);E(g);if(B)try{return D(A,I,g)}catch(A){}if("get"in g||"set"in g)throw new o("Accessors not supported");"value"in g&&(A[I]=g.value);return A}},7347:(A,I,g)=>{var Q=g(3724),B=g(9565),C=g(8773),E=g(6980),i=g(5397),o=g(6969),D=g(9297),h=g(5917),s=Object.getOwnPropertyDescriptor;I.f=Q?s:function getOwnPropertyDescriptor(A,I){A=i(A);I=o(I);if(h)try{return s(A,I)}catch(A){}if(D(A,I))return E(!B(C.f,A,I),A[I])}},8480:(A,I,g)=>{var Q=g(1828),B=g(8727).concat("length","prototype");I.f=Object.getOwnPropertyNames||function getOwnPropertyNames(A){return Q(A,B)}},3717:(A,I)=>{I.f=Object.getOwnPropertySymbols},2787:(A,I,g)=>{var Q=g(9297),B=g(4901),C=g(8981),E=g(6119),i=g(2211),o=E("IE_PROTO"),D=Object,h=D.prototype;A.exports=i?D.getPrototypeOf:function(A){var I=C(A);if(Q(I,o))return I[o];var g=I.constructor;return B(g)&&I instanceof g?g.prototype:I instanceof D?h:null}},1625:(A,I,g)=>{var Q=g(9504);A.exports=Q({}.isPrototypeOf)},1828:(A,I,g)=>{var Q=g(9504),B=g(9297),C=g(5397),E=g(9617).indexOf,i=g(421),o=Q([].push);A.exports=function(A,I){var g,Q=C(A),D=0,h=[];for(g in Q)!B(i,g)&&B(Q,g)&&o(h,g);for(;I.length>D;)B(Q,g=I[D++])&&(~E(h,g)||o(h,g));return h}},1072:(A,I,g)=>{var Q=g(1828),B=g(8727);A.exports=Object.keys||function keys(A){return Q(A,B)}},8773:(A,I)=>{var g={}.propertyIsEnumerable,Q=Object.getOwnPropertyDescriptor,B=Q&&!g.call({1:2},1);I.f=B?function propertyIsEnumerable(A){var I=Q(this,A);return!!I&&I.enumerable}:g},2967:(A,I,g)=>{var Q=g(6706),B=g(34),C=g(7750),E=g(3506);A.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var A,I=!1,g={};try{(A=Q(Object.prototype,"__proto__","set"))(g,[]);I=g instanceof Array}catch(A){}return function setPrototypeOf(g,Q){C(g);E(Q);if(!B(g))return g;I?A(g,Q):g.__proto__=Q;return g}}():void 0)},4270:(A,I,g)=>{var Q=g(9565),B=g(4901),C=g(34),E=TypeError;A.exports=function(A,I){var g,i;if("string"===I&&B(g=A.toString)&&!C(i=Q(g,A)))return i;if(B(g=A.valueOf)&&!C(i=Q(g,A)))return i;if("string"!==I&&B(g=A.toString)&&!C(i=Q(g,A)))return i;throw new E("Can't convert object to primitive value")}},5031:(A,I,g)=>{var Q=g(7751),B=g(9504),C=g(8480),E=g(3717),i=g(8551),o=B([].concat);A.exports=Q("Reflect","ownKeys")||function ownKeys(A){var I=C.f(i(A)),g=E.f;return g?o(I,g(A)):I}},7750:(A,I,g)=>{var Q=g(4117),B=TypeError;A.exports=function(A){if(Q(A))throw new B("Can't call method on "+A);return A}},9286:(A,I,g)=>{var Q=g(4402),B=g(8469),C=Q.Set,E=Q.add;A.exports=function(A){var I=new C;B(A,(function(A){E(I,A)}));return I}},3440:(A,I,g)=>{var Q=g(7080),B=g(4402),C=g(9286),E=g(5170),i=g(3789),o=g(8469),D=g(507),h=B.has,s=B.remove;A.exports=function difference(A){var I=Q(this),g=i(A),B=C(I);E(I)<=g.size?o(I,(function(A){g.includes(A)&&s(B,A)})):D(g.getIterator(),(function(A){h(I,A)&&s(B,A)}));return B}},4402:(A,I,g)=>{var Q=g(9504),B=Set.prototype;A.exports={Set,add:Q(B.add),has:Q(B.has),remove:Q(B.delete),proto:B}},8750:(A,I,g)=>{var Q=g(7080),B=g(4402),C=g(5170),E=g(3789),i=g(8469),o=g(507),D=B.Set,h=B.add,s=B.has;A.exports=function intersection(A){var I=Q(this),g=E(A),B=new D;C(I)>g.size?o(g.getIterator(),(function(A){s(I,A)&&h(B,A)})):i(I,(function(A){g.includes(A)&&h(B,A)}));return B}},4449:(A,I,g)=>{var Q=g(7080),B=g(4402).has,C=g(5170),E=g(3789),i=g(8469),o=g(507),D=g(9539);A.exports=function isDisjointFrom(A){var I=Q(this),g=E(A);if(C(I)<=g.size)return!1!==i(I,(function(A){if(g.includes(A))return!1}),!0);var h=g.getIterator();return!1!==o(h,(function(A){if(B(I,A))return D(h,"normal",!1)}))}},3838:(A,I,g)=>{var Q=g(7080),B=g(5170),C=g(8469),E=g(3789);A.exports=function isSubsetOf(A){var I=Q(this),g=E(A);return!(B(I)>g.size)&&!1!==C(I,(function(A){if(!g.includes(A))return!1}),!0)}},8527:(A,I,g)=>{var Q=g(7080),B=g(4402).has,C=g(5170),E=g(3789),i=g(507),o=g(9539);A.exports=function isSupersetOf(A){var I=Q(this),g=E(A);if(C(I)<g.size)return!1;var D=g.getIterator();return!1!==i(D,(function(A){if(!B(I,A))return o(D,"normal",!1)}))}},8469:(A,I,g)=>{var Q=g(9504),B=g(507),C=g(4402),E=C.Set,i=C.proto,o=Q(i.forEach),D=Q(i.keys),h=D(new E).next;A.exports=function(A,I,g){return g?B({iterator:D(A),next:h},I):o(A,I)}},4916:(A,I,g)=>{var Q=g(7751),createSetLike=function(A){return{size:A,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};A.exports=function(A){var I=Q("Set");try{(new I)[A](createSetLike(0));try{(new I)[A](createSetLike(-1));return!1}catch(A){return!0}}catch(A){return!1}}},5170:(A,I,g)=>{var Q=g(6706),B=g(4402);A.exports=Q(B.proto,"size","get")||function(A){return A.size}},3650:(A,I,g)=>{var Q=g(7080),B=g(4402),C=g(9286),E=g(3789),i=g(507),o=B.add,D=B.has,h=B.remove;A.exports=function symmetricDifference(A){var I=Q(this),g=E(A).getIterator(),B=C(I);i(g,(function(A){D(I,A)?h(B,A):o(B,A)}));return B}},4204:(A,I,g)=>{var Q=g(7080),B=g(4402).add,C=g(9286),E=g(3789),i=g(507);A.exports=function union(A){var I=Q(this),g=E(A).getIterator(),o=C(I);i(g,(function(A){B(o,A)}));return o}},6119:(A,I,g)=>{var Q=g(5745),B=g(3392),C=Q("keys");A.exports=function(A){return C[A]||(C[A]=B(A))}},7629:(A,I,g)=>{var Q=g(6395),B=g(4475),C=g(9433),E="__core-js_shared__",i=A.exports=B[E]||C(E,{});(i.versions||(i.versions=[])).push({version:"3.37.0",mode:Q?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:(A,I,g)=>{var Q=g(7629);A.exports=function(A,I){return Q[A]||(Q[A]=I||{})}},1548:(A,I,g)=>{var Q=g(4475),B=g(9039),C=g(7388),E=g(7290),i=g(516),o=g(9088),D=Q.structuredClone;A.exports=!!D&&!B((function(){if(i&&C>92||o&&C>94||E&&C>97)return!1;var A=new ArrayBuffer(8),I=D(A,{transfer:[A]});return 0!==A.byteLength||8!==I.byteLength}))},4495:(A,I,g)=>{var Q=g(7388),B=g(9039),C=g(4475).String;A.exports=!!Object.getOwnPropertySymbols&&!B((function(){var A=Symbol("symbol detection");return!C(A)||!(Object(A)instanceof Symbol)||!Symbol.sham&&Q&&Q<41}))},5610:(A,I,g)=>{var Q=g(1291),B=Math.max,C=Math.min;A.exports=function(A,I){var g=Q(A);return g<0?B(g+I,0):C(g,I)}},5854:(A,I,g)=>{var Q=g(2777),B=TypeError;A.exports=function(A){var I=Q(A,"number");if("number"==typeof I)throw new B("Can't convert number to bigint");return BigInt(I)}},7696:(A,I,g)=>{var Q=g(1291),B=g(8014),C=RangeError;A.exports=function(A){if(void 0===A)return 0;var I=Q(A),g=B(I);if(I!==g)throw new C("Wrong length or index");return g}},5397:(A,I,g)=>{var Q=g(7055),B=g(7750);A.exports=function(A){return Q(B(A))}},1291:(A,I,g)=>{var Q=g(741);A.exports=function(A){var I=+A;return I!=I||0===I?0:Q(I)}},8014:(A,I,g)=>{var Q=g(1291),B=Math.min;A.exports=function(A){var I=Q(A);return I>0?B(I,9007199254740991):0}},8981:(A,I,g)=>{var Q=g(7750),B=Object;A.exports=function(A){return B(Q(A))}},2777:(A,I,g)=>{var Q=g(9565),B=g(34),C=g(757),E=g(5966),i=g(4270),o=g(8227),D=TypeError,h=o("toPrimitive");A.exports=function(A,I){if(!B(A)||C(A))return A;var g,o=E(A,h);if(o){void 0===I&&(I="default");g=Q(o,A,I);if(!B(g)||C(g))return g;throw new D("Can't convert object to primitive value")}void 0===I&&(I="number");return i(A,I)}},6969:(A,I,g)=>{var Q=g(2777),B=g(757);A.exports=function(A){var I=Q(A,"string");return B(I)?I:I+""}},2140:(A,I,g)=>{var Q={};Q[g(8227)("toStringTag")]="z";A.exports="[object z]"===String(Q)},655:(A,I,g)=>{var Q=g(6955),B=String;A.exports=function(A){if("Symbol"===Q(A))throw new TypeError("Cannot convert a Symbol value to a string");return B(A)}},9714:(A,I,g)=>{var Q=g(9088);A.exports=function(A){try{if(Q)return Function('return require("'+A+'")')()}catch(A){}}},6823:A=>{var I=String;A.exports=function(A){try{return I(A)}catch(A){return"Object"}}},3392:(A,I,g)=>{var Q=g(9504),B=0,C=Math.random(),E=Q(1..toString);A.exports=function(A){return"Symbol("+(void 0===A?"":A)+")_"+E(++B+C,36)}},7040:(A,I,g)=>{var Q=g(4495);A.exports=Q&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:(A,I,g)=>{var Q=g(3724),B=g(9039);A.exports=Q&&B((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:A=>{var I=TypeError;A.exports=function(A,g){if(A<g)throw new I("Not enough arguments");return A}},8622:(A,I,g)=>{var Q=g(4475),B=g(4901),C=Q.WeakMap;A.exports=B(C)&&/native code/.test(String(C))},8227:(A,I,g)=>{var Q=g(4475),B=g(5745),C=g(9297),E=g(3392),i=g(4495),o=g(7040),D=Q.Symbol,h=B("wks"),s=o?D.for||D:D&&D.withoutSetter||E;A.exports=function(A){C(h,A)||(h[A]=i&&C(D,A)?D[A]:s("Symbol."+A));return h[A]}},6573:(A,I,g)=>{var Q=g(3724),B=g(2106),C=g(3238),E=ArrayBuffer.prototype;Q&&!("detached"in E)&&B(E,"detached",{configurable:!0,get:function detached(){return C(this)}})},7936:(A,I,g)=>{var Q=g(6518),B=g(5636);B&&Q({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return B(this,arguments.length?arguments[0]:void 0,!1)}})},8100:(A,I,g)=>{var Q=g(6518),B=g(5636);B&&Q({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return B(this,arguments.length?arguments[0]:void 0,!0)}})},4114:(A,I,g)=>{var Q=g(6518),B=g(8981),C=g(6198),E=g(4527),i=g(6837);Q({target:"Array",proto:!0,arity:1,forced:g(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(A){return A instanceof TypeError}}()},{push:function push(A){var I=B(this),g=C(I),Q=arguments.length;i(g+Q);for(var o=0;o<Q;o++){I[g]=arguments[o];g++}E(I,g);return g}})},7642:(A,I,g)=>{var Q=g(6518),B=g(3440);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("difference")},{difference:B})},8004:(A,I,g)=>{var Q=g(6518),B=g(9039),C=g(8750);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("intersection")||B((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:C})},3853:(A,I,g)=>{var Q=g(6518),B=g(4449);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("isDisjointFrom")},{isDisjointFrom:B})},5876:(A,I,g)=>{var Q=g(6518),B=g(3838);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("isSubsetOf")},{isSubsetOf:B})},2475:(A,I,g)=>{var Q=g(6518),B=g(8527);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("isSupersetOf")},{isSupersetOf:B})},5024:(A,I,g)=>{var Q=g(6518),B=g(3650);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("symmetricDifference")},{symmetricDifference:B})},1698:(A,I,g)=>{var Q=g(6518),B=g(4204);Q({target:"Set",proto:!0,real:!0,forced:!g(4916)("union")},{union:B})},7467:(A,I,g)=>{var Q=g(7628),B=g(4644),C=B.aTypedArray,E=B.exportTypedArrayMethod,i=B.getTypedArrayConstructor;E("toReversed",(function toReversed(){return Q(C(this),i(this))}))},4732:(A,I,g)=>{var Q=g(4644),B=g(9504),C=g(9306),E=g(5370),i=Q.aTypedArray,o=Q.getTypedArrayConstructor,D=Q.exportTypedArrayMethod,h=B(Q.TypedArrayPrototype.sort);D("toSorted",(function toSorted(A){void 0!==A&&C(A);var I=i(this),g=E(o(I),I);return h(g,A)}))},9577:(A,I,g)=>{var Q=g(9928),B=g(4644),C=g(1108),E=g(1291),i=g(5854),o=B.aTypedArray,D=B.getTypedArrayConstructor,h=B.exportTypedArrayMethod,s=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(A){return 8===A}}();h("with",{with:function(A,I){var g=o(this),B=E(A),h=C(g)?i(I):+I;return Q(g,D(g),B,h)}}.with,!s)},8992:(A,I,g)=>{var Q=g(6518),B=g(4475),C=g(679),E=g(8551),i=g(4901),o=g(2787),D=g(2106),h=g(4659),s=g(9039),a=g(9297),t=g(8227),G=g(7657).IteratorPrototype,e=g(3724),y=g(6395),c="constructor",r="Iterator",w=t("toStringTag"),R=TypeError,n=B[r],S=y||!i(n)||n.prototype!==G||!s((function(){n({})})),N=function Iterator(){C(this,G);if(o(this)===G)throw new R("Abstract class Iterator not directly constructable")},defineIteratorPrototypeAccessor=function(A,I){e?D(G,A,{configurable:!0,get:function(){return I},set:function(I){E(this);if(this===G)throw new R("You can't redefine this property");a(this,A)?this[A]=I:h(this,A,I)}}):G[A]=I};a(G,w)||defineIteratorPrototypeAccessor(w,r);!S&&a(G,c)&&G[c]!==Object||defineIteratorPrototypeAccessor(c,N);N.prototype=G;Q({global:!0,constructor:!0,forced:S},{Iterator:N})},3215:(A,I,g)=>{var Q=g(6518),B=g(2652),C=g(9306),E=g(8551),i=g(1767);Q({target:"Iterator",proto:!0,real:!0},{every:function every(A){E(this);C(A);var I=i(this),g=0;return!B(I,(function(I,Q){if(!A(I,g++))return Q()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},3949:(A,I,g)=>{var Q=g(6518),B=g(2652),C=g(9306),E=g(8551),i=g(1767);Q({target:"Iterator",proto:!0,real:!0},{forEach:function forEach(A){E(this);C(A);var I=i(this),g=0;B(I,(function(I){A(I,g++)}),{IS_RECORD:!0})}})},1454:(A,I,g)=>{var Q=g(6518),B=g(713);Q({target:"Iterator",proto:!0,real:!0,forced:g(6395)},{map:B})},3375:(A,I,g)=>{g(7642)},9225:(A,I,g)=>{g(8004)},3972:(A,I,g)=>{g(3853)},9209:(A,I,g)=>{g(5876)},5714:(A,I,g)=>{g(2475)},7561:(A,I,g)=>{g(5024)},6197:(A,I,g)=>{g(1698)},4979:(A,I,g)=>{var Q=g(6518),B=g(4475),C=g(7751),E=g(6980),i=g(4913).f,o=g(9297),D=g(679),h=g(3167),s=g(2603),a=g(5002),t=g(6193),G=g(3724),e=g(6395),y="DOMException",c=C("Error"),r=C(y),w=function DOMException(){D(this,R);var A=arguments.length,I=s(A<1?void 0:arguments[0]),g=s(A<2?void 0:arguments[1],"Error"),Q=new r(I,g),B=new c(I);B.name=y;i(Q,"stack",E(1,t(B.stack,1)));h(Q,this,w);return Q},R=w.prototype=r.prototype,n="stack"in new c(y),S="stack"in new r(1,2),N=r&&G&&Object.getOwnPropertyDescriptor(B,y),k=!(!N||N.writable&&N.configurable),J=n&&!k&&!S;Q({global:!0,constructor:!0,forced:e||J},{DOMException:J?w:r});var U=C(y),Y=U.prototype;if(Y.constructor!==U){e||i(Y,"constructor",E(1,U));for(var H in a)if(o(a,H)){var d=a[H],L=d.s;o(U,L)||i(U,L,E(6,d.c))}}},3611:(A,I,g)=>{var Q=g(6518),B=g(4475),C=g(2106),E=g(3724),i=TypeError,o=Object.defineProperty,D=B.self!==B;try{if(E){var h=Object.getOwnPropertyDescriptor(B,"self");!D&&h&&h.get&&h.enumerable||C(B,"self",{get:function self(){return B},set:function self(A){if(this!==B)throw new i("Illegal invocation");o(B,"self",{value:A,writable:!0,configurable:!0,enumerable:!0})},configurable:!0,enumerable:!0})}else Q({global:!0,simple:!0,forced:D},{self:B})}catch(A){}},4603:(A,I,g)=>{var Q=g(6840),B=g(9504),C=g(655),E=g(2812),i=URLSearchParams,o=i.prototype,D=B(o.append),h=B(o.delete),s=B(o.forEach),a=B([].push),t=new i("a=1&a=2&b=3");t.delete("a",1);t.delete("b",void 0);t+""!="a=2"&&Q(o,"delete",(function(A){var I=arguments.length,g=I<2?void 0:arguments[1];if(I&&void 0===g)return h(this,A);var Q=[];s(this,(function(A,I){a(Q,{key:I,value:A})}));E(I,1);for(var B,i=C(A),o=C(g),t=0,G=0,e=!1,y=Q.length;t<y;){B=Q[t++];if(e||B.key===i){e=!0;h(this,B.key)}else G++}for(;G<y;)(B=Q[G++]).key===i&&B.value===o||D(this,B.key,B.value)}),{enumerable:!0,unsafe:!0})},7566:(A,I,g)=>{var Q=g(6840),B=g(9504),C=g(655),E=g(2812),i=URLSearchParams,o=i.prototype,D=B(o.getAll),h=B(o.has),s=new i("a=1");!s.has("a",2)&&s.has("a",void 0)||Q(o,"has",(function has(A){var I=arguments.length,g=I<2?void 0:arguments[1];if(I&&void 0===g)return h(this,A);var Q=D(this,A);E(I,1);for(var B=C(g),i=0;i<Q.length;)if(Q[i++]===B)return!0;return!1}),{enumerable:!0,unsafe:!0})},8721:(A,I,g)=>{var Q=g(3724),B=g(9504),C=g(2106),E=URLSearchParams.prototype,i=B(E.forEach);Q&&!("size"in E)&&C(E,"size",{get:function size(){var A=0;i(this,(function(){A++}));return A},configurable:!0,enumerable:!0})}},I={};function __webpack_require__(g){var Q=I[g];if(void 0!==Q)return Q.exports;var B=I[g]={exports:{}};A[g].call(B.exports,B,B.exports,__webpack_require__);return B.exports}__webpack_require__.d=(A,I)=>{for(var g in I)__webpack_require__.o(I,g)&&!__webpack_require__.o(A,g)&&Object.defineProperty(A,g,{enumerable:!0,get:I[g]})};__webpack_require__.o=(A,I)=>Object.prototype.hasOwnProperty.call(A,I);var __webpack_exports__={};(()=>{__webpack_require__.d(__webpack_exports__,{Jbig2Error:()=>Jbig2Error,Jbig2Image:()=>Jbig2Image,JpegError:()=>JpegError,JpegImage:()=>JpegImage,JpxError:()=>JpxError,JpxImage:()=>JpxImage,VerbosityLevel:()=>A,getVerbosityLevel:()=>getVerbosityLevel,setVerbosityLevel:()=>setVerbosityLevel});__webpack_require__(4114),__webpack_require__(6573),__webpack_require__(8100),__webpack_require__(7936),__webpack_require__(7467),__webpack_require__(4732),__webpack_require__(9577),__webpack_require__(4603),__webpack_require__(7566),__webpack_require__(8721);"object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&process.type;const A={ERRORS:0,WARNINGS:1,INFOS:5};let I=A.WARNINGS;function setVerbosityLevel(A){Number.isInteger(A)&&(I=A)}function getVerbosityLevel(){return I}function info(g){I>=A.INFOS&&console.log(`Info: ${g}`)}function util_warn(g){I>=A.WARNINGS&&console.log(`Warning: ${g}`)}function util_unreachable(A){throw new Error(A)}function util_shadow(A,I,g,Q=!1){Object.defineProperty(A,I,{value:g,enumerable:!Q,configurable:!0,writable:!1});return g}const g=function BaseExceptionClosure(){function BaseException(A,I){this.constructor===BaseException&&util_unreachable("Cannot initialize BaseException.");this.message=A;this.name=I}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class FormatError extends g{constructor(A){super(A,"FormatError")}}function bytesToString(A){"object"==typeof A&&void 0!==A?.length||util_unreachable("Invalid argument for bytesToString");const I=A.length,g=8192;if(I<g)return String.fromCharCode.apply(null,A);const Q=[];for(let B=0;B<I;B+=g){const C=Math.min(B+g,I),E=A.subarray(B,C);Q.push(String.fromCharCode.apply(null,E))}return Q.join("")}class util_FeatureTest{static get isLittleEndian(){return util_shadow(this,"isLittleEndian",function isLittleEndian(){const A=new Uint8Array(4);A[0]=1;return 1===new Uint32Array(A.buffer,0,1)[0]}())}static get isEvalSupported(){return util_shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return util_shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"!=typeof navigator&&"string"==typeof navigator?.platform?util_shadow(this,"platform",{isMac:navigator.platform.includes("Mac")}):util_shadow(this,"platform",{isMac:!1})}static get isCSSRoundSupported(){return util_shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}Array.from(Array(256).keys(),(A=>A.toString(16).padStart(2,"0")));__webpack_require__(8992),__webpack_require__(3215),__webpack_require__(1454),__webpack_require__(3375),__webpack_require__(9225),__webpack_require__(3972),__webpack_require__(9209),__webpack_require__(5714),__webpack_require__(7561),__webpack_require__(6197);Symbol("CIRCULAR_REF"),Symbol("EOF");Object.create(null),Object.create(null),Object.create(null);Symbol.iterator;Symbol.iterator;class base_stream_BaseStream{constructor(){this.constructor===base_stream_BaseStream&&util_unreachable("Cannot initialize BaseStream.")}get length(){util_unreachable("Abstract getter `length` accessed")}get isEmpty(){util_unreachable("Abstract getter `isEmpty` accessed")}get isDataLoaded(){return util_shadow(this,"isDataLoaded",!0)}getByte(){util_unreachable("Abstract method `getByte` called")}getBytes(A){util_unreachable("Abstract method `getBytes` called")}peekByte(){const A=this.getByte();-1!==A&&this.pos--;return A}peekBytes(A){const I=this.getBytes(A);this.pos-=I.length;return I}getUint16(){const A=this.getByte(),I=this.getByte();return-1===A||-1===I?-1:(A<<8)+I}getInt32(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()}getByteRange(A,I){util_unreachable("Abstract method `getByteRange` called")}getString(A){return bytesToString(this.getBytes(A))}skip(A){this.pos+=A||1}reset(){util_unreachable("Abstract method `reset` called")}moveStart(){util_unreachable("Abstract method `moveStart` called")}makeSubStream(A,I,g=null){util_unreachable("Abstract method `makeSubStream` called")}getBaseStreams(){return null}}function log2(A){return A<=0?0:Math.ceil(Math.log2(A))}function readInt8(A,I){return A[I]<<24>>24}function readUint16(A,I){return A[I]<<8|A[I+1]}function readUint32(A,I){return(A[I]<<24|A[I+1]<<16|A[I+2]<<8|A[I+3])>>>0}const Q=[{qe:22017,nmps:1,nlps:1,switchFlag:1},{qe:13313,nmps:2,nlps:6,switchFlag:0},{qe:6145,nmps:3,nlps:9,switchFlag:0},{qe:2753,nmps:4,nlps:12,switchFlag:0},{qe:1313,nmps:5,nlps:29,switchFlag:0},{qe:545,nmps:38,nlps:33,switchFlag:0},{qe:22017,nmps:7,nlps:6,switchFlag:1},{qe:21505,nmps:8,nlps:14,switchFlag:0},{qe:18433,nmps:9,nlps:14,switchFlag:0},{qe:14337,nmps:10,nlps:14,switchFlag:0},{qe:12289,nmps:11,nlps:17,switchFlag:0},{qe:9217,nmps:12,nlps:18,switchFlag:0},{qe:7169,nmps:13,nlps:20,switchFlag:0},{qe:5633,nmps:29,nlps:21,switchFlag:0},{qe:22017,nmps:15,nlps:14,switchFlag:1},{qe:21505,nmps:16,nlps:14,switchFlag:0},{qe:20737,nmps:17,nlps:15,switchFlag:0},{qe:18433,nmps:18,nlps:16,switchFlag:0},{qe:14337,nmps:19,nlps:17,switchFlag:0},{qe:13313,nmps:20,nlps:18,switchFlag:0},{qe:12289,nmps:21,nlps:19,switchFlag:0},{qe:10241,nmps:22,nlps:19,switchFlag:0},{qe:9217,nmps:23,nlps:20,switchFlag:0},{qe:8705,nmps:24,nlps:21,switchFlag:0},{qe:7169,nmps:25,nlps:22,switchFlag:0},{qe:6145,nmps:26,nlps:23,switchFlag:0},{qe:5633,nmps:27,nlps:24,switchFlag:0},{qe:5121,nmps:28,nlps:25,switchFlag:0},{qe:4609,nmps:29,nlps:26,switchFlag:0},{qe:4353,nmps:30,nlps:27,switchFlag:0},{qe:2753,nmps:31,nlps:28,switchFlag:0},{qe:2497,nmps:32,nlps:29,switchFlag:0},{qe:2209,nmps:33,nlps:30,switchFlag:0},{qe:1313,nmps:34,nlps:31,switchFlag:0},{qe:1089,nmps:35,nlps:32,switchFlag:0},{qe:673,nmps:36,nlps:33,switchFlag:0},{qe:545,nmps:37,nlps:34,switchFlag:0},{qe:321,nmps:38,nlps:35,switchFlag:0},{qe:273,nmps:39,nlps:36,switchFlag:0},{qe:133,nmps:40,nlps:37,switchFlag:0},{qe:73,nmps:41,nlps:38,switchFlag:0},{qe:37,nmps:42,nlps:39,switchFlag:0},{qe:21,nmps:43,nlps:40,switchFlag:0},{qe:9,nmps:44,nlps:41,switchFlag:0},{qe:5,nmps:45,nlps:42,switchFlag:0},{qe:1,nmps:45,nlps:43,switchFlag:0},{qe:22017,nmps:46,nlps:46,switchFlag:0}];class ArithmeticDecoder{constructor(A,I,g){this.data=A;this.bp=I;this.dataEnd=g;this.chigh=A[I];this.clow=0;this.byteIn();this.chigh=this.chigh<<7&65535|this.clow>>9&127;this.clow=this.clow<<7&65535;this.ct-=7;this.a=32768}byteIn(){const A=this.data;let I=this.bp;if(255===A[I])if(A[I+1]>143){this.clow+=65280;this.ct=8}else{I++;this.clow+=A[I]<<9;this.ct=7;this.bp=I}else{I++;this.clow+=I<this.dataEnd?A[I]<<8:65280;this.ct=8;this.bp=I}if(this.clow>65535){this.chigh+=this.clow>>16;this.clow&=65535}}readBit(A,I){let g=A[I]>>1,B=1&A[I];const C=Q[g],E=C.qe;let i,o=this.a-E;if(this.chigh<E)if(o<E){o=E;i=B;g=C.nmps}else{o=E;i=1^B;1===C.switchFlag&&(B=i);g=C.nlps}else{this.chigh-=E;if(0!=(32768&o)){this.a=o;return B}if(o<E){i=1^B;1===C.switchFlag&&(B=i);g=C.nlps}else{i=B;g=C.nmps}}do{0===this.ct&&this.byteIn();o<<=1;this.chigh=this.chigh<<1&65535|this.clow>>15&1;this.clow=this.clow<<1&65535;this.ct--}while(0==(32768&o));this.a=o;A[I]=g<<1|B;return i}}const B=-1,C=[[-1,-1],[-1,-1],[7,8],[7,7],[6,6],[6,6],[6,5],[6,5],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2]],E=[[-1,-1],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[12,1984],[12,2048],[12,2112],[12,2176],[12,2240],[12,2304],[11,1856],[11,1856],[11,1920],[11,1920],[12,2368],[12,2432],[12,2496],[12,2560]],i=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[8,29],[8,29],[8,30],[8,30],[8,45],[8,45],[8,46],[8,46],[7,22],[7,22],[7,22],[7,22],[7,23],[7,23],[7,23],[7,23],[8,47],[8,47],[8,48],[8,48],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[7,20],[7,20],[7,20],[7,20],[8,33],[8,33],[8,34],[8,34],[8,35],[8,35],[8,36],[8,36],[8,37],[8,37],[8,38],[8,38],[7,19],[7,19],[7,19],[7,19],[8,31],[8,31],[8,32],[8,32],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[8,53],[8,53],[8,54],[8,54],[7,26],[7,26],[7,26],[7,26],[8,39],[8,39],[8,40],[8,40],[8,41],[8,41],[8,42],[8,42],[8,43],[8,43],[8,44],[8,44],[7,21],[7,21],[7,21],[7,21],[7,28],[7,28],[7,28],[7,28],[8,61],[8,61],[8,62],[8,62],[8,63],[8,63],[8,0],[8,0],[8,320],[8,320],[8,384],[8,384],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[7,27],[7,27],[7,27],[7,27],[8,59],[8,59],[8,60],[8,60],[9,1472],[9,1536],[9,1600],[9,1728],[7,18],[7,18],[7,18],[7,18],[7,24],[7,24],[7,24],[7,24],[8,49],[8,49],[8,50],[8,50],[8,51],[8,51],[8,52],[8,52],[7,25],[7,25],[7,25],[7,25],[8,55],[8,55],[8,56],[8,56],[8,57],[8,57],[8,58],[8,58],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[8,448],[8,448],[8,512],[8,512],[9,704],[9,768],[8,640],[8,640],[8,576],[8,576],[9,832],[9,896],[9,960],[9,1024],[9,1088],[9,1152],[9,1216],[9,1280],[9,1344],[9,1408],[7,256],[7,256],[7,256],[7,256],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7]],o=[[-1,-1],[-1,-1],[12,-2],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[11,1792],[11,1792],[12,1984],[12,1984],[12,2048],[12,2048],[12,2112],[12,2112],[12,2176],[12,2176],[12,2240],[12,2240],[12,2304],[12,2304],[11,1856],[11,1856],[11,1856],[11,1856],[11,1920],[11,1920],[11,1920],[11,1920],[12,2368],[12,2368],[12,2432],[12,2432],[12,2496],[12,2496],[12,2560],[12,2560],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[12,52],[12,52],[13,640],[13,704],[13,768],[13,832],[12,55],[12,55],[12,56],[12,56],[13,1280],[13,1344],[13,1408],[13,1472],[12,59],[12,59],[12,60],[12,60],[13,1536],[13,1600],[11,24],[11,24],[11,24],[11,24],[11,25],[11,25],[11,25],[11,25],[13,1664],[13,1728],[12,320],[12,320],[12,384],[12,384],[12,448],[12,448],[13,512],[13,576],[12,53],[12,53],[12,54],[12,54],[13,896],[13,960],[13,1024],[13,1088],[13,1152],[13,1216],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64]],D=[[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[11,23],[11,23],[12,50],[12,51],[12,44],[12,45],[12,46],[12,47],[12,57],[12,58],[12,61],[12,256],[10,16],[10,16],[10,16],[10,16],[10,17],[10,17],[10,17],[10,17],[12,48],[12,49],[12,62],[12,63],[12,30],[12,31],[12,32],[12,33],[12,40],[12,41],[11,22],[11,22],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[12,128],[12,192],[12,26],[12,27],[12,28],[12,29],[11,19],[11,19],[11,20],[11,20],[12,34],[12,35],[12,36],[12,37],[12,38],[12,39],[11,21],[11,21],[12,42],[12,43],[10,0],[10,0],[10,0],[10,0],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12]],h=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[6,9],[6,8],[5,7],[5,7],[4,6],[4,6],[4,6],[4,6],[4,5],[4,5],[4,5],[4,5],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2]];class CCITTFaxDecoder{constructor(A,I={}){if(!A||"function"!=typeof A.next)throw new Error('CCITTFaxDecoder - invalid "source" parameter.');this.source=A;this.eof=!1;this.encoding=I.K||0;this.eoline=I.EndOfLine||!1;this.byteAlign=I.EncodedByteAlign||!1;this.columns=I.Columns||1728;this.rows=I.Rows||0;this.eoblock=I.EndOfBlock??!0;this.black=I.BlackIs1||!1;this.codingLine=new Uint32Array(this.columns+1);this.refLine=new Uint32Array(this.columns+2);this.codingLine[0]=this.columns;this.codingPos=0;this.row=0;this.nextLine2D=this.encoding<0;this.inputBits=0;this.inputBuf=0;this.outputBits=0;this.rowsDone=!1;let g;for(;0===(g=this._lookBits(12));)this._eatBits(1);1===g&&this._eatBits(12);if(this.encoding>0){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}}readNextChar(){if(this.eof)return-1;const A=this.refLine,I=this.codingLine,g=this.columns;let Q,C,E,i,o;if(0===this.outputBits){this.rowsDone&&(this.eof=!0);if(this.eof)return-1;this.err=!1;let E,o,D;if(this.nextLine2D){for(i=0;I[i]<g;++i)A[i]=I[i];A[i++]=g;A[i]=g;I[0]=0;this.codingPos=0;Q=0;C=0;for(;I[this.codingPos]<g;){E=this._getTwoDimCode();switch(E){case 0:this._addPixels(A[Q+1],C);A[Q+1]<g&&(Q+=2);break;case 1:E=o=0;if(C){do{E+=D=this._getBlackCode()}while(D>=64);do{o+=D=this._getWhiteCode()}while(D>=64)}else{do{E+=D=this._getWhiteCode()}while(D>=64);do{o+=D=this._getBlackCode()}while(D>=64)}this._addPixels(I[this.codingPos]+E,C);I[this.codingPos]<g&&this._addPixels(I[this.codingPos]+o,1^C);for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2;break;case 7:this._addPixels(A[Q]+3,C);C^=1;if(I[this.codingPos]<g){++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case 5:this._addPixels(A[Q]+2,C);C^=1;if(I[this.codingPos]<g){++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case 3:this._addPixels(A[Q]+1,C);C^=1;if(I[this.codingPos]<g){++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case 2:this._addPixels(A[Q],C);C^=1;if(I[this.codingPos]<g){++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case 8:this._addPixelsNeg(A[Q]-3,C);C^=1;if(I[this.codingPos]<g){Q>0?--Q:++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case 6:this._addPixelsNeg(A[Q]-2,C);C^=1;if(I[this.codingPos]<g){Q>0?--Q:++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case 4:this._addPixelsNeg(A[Q]-1,C);C^=1;if(I[this.codingPos]<g){Q>0?--Q:++Q;for(;A[Q]<=I[this.codingPos]&&A[Q]<g;)Q+=2}break;case B:this._addPixels(g,0);this.eof=!0;break;default:info("bad 2d code");this._addPixels(g,0);this.err=!0}}}else{I[0]=0;this.codingPos=0;C=0;for(;I[this.codingPos]<g;){E=0;if(C)do{E+=D=this._getBlackCode()}while(D>=64);else do{E+=D=this._getWhiteCode()}while(D>=64);this._addPixels(I[this.codingPos]+E,C);C^=1}}let h=!1;this.byteAlign&&(this.inputBits&=-8);if(this.eoblock||this.row!==this.rows-1){E=this._lookBits(12);if(this.eoline)for(;E!==B&&1!==E;){this._eatBits(1);E=this._lookBits(12)}else for(;0===E;){this._eatBits(1);E=this._lookBits(12)}if(1===E){this._eatBits(12);h=!0}else E===B&&(this.eof=!0)}else this.rowsDone=!0;if(!this.eof&&this.encoding>0&&!this.rowsDone){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}if(this.eoblock&&h&&this.byteAlign){E=this._lookBits(12);if(1===E){this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}if(this.encoding>=0)for(i=0;i<4;++i){E=this._lookBits(12);1!==E&&info("bad rtc code: "+E);this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}}this.eof=!0}}else if(this.err&&this.eoline){for(;;){E=this._lookBits(13);if(E===B){this.eof=!0;return-1}if(E>>1==1)break;this._eatBits(1)}this._eatBits(12);if(this.encoding>0){this._eatBits(1);this.nextLine2D=!(1&E)}}this.outputBits=I[0]>0?I[this.codingPos=0]:I[this.codingPos=1];this.row++}if(this.outputBits>=8){o=1&this.codingPos?0:255;this.outputBits-=8;if(0===this.outputBits&&I[this.codingPos]<g){this.codingPos++;this.outputBits=I[this.codingPos]-I[this.codingPos-1]}}else{E=8;o=0;do{if("number"!=typeof this.outputBits)throw new FormatError('Invalid /CCITTFaxDecode data, "outputBits" must be a number.');if(this.outputBits>E){o<<=E;1&this.codingPos||(o|=255>>8-E);this.outputBits-=E;E=0}else{o<<=this.outputBits;1&this.codingPos||(o|=255>>8-this.outputBits);E-=this.outputBits;this.outputBits=0;if(I[this.codingPos]<g){this.codingPos++;this.outputBits=I[this.codingPos]-I[this.codingPos-1]}else if(E>0){o<<=E;E=0}}}while(E)}this.black&&(o^=255);return o}_addPixels(A,I){const g=this.codingLine;let Q=this.codingPos;if(A>g[Q]){if(A>this.columns){info("row is wrong length");this.err=!0;A=this.columns}1&Q^I&&++Q;g[Q]=A}this.codingPos=Q}_addPixelsNeg(A,I){const g=this.codingLine;let Q=this.codingPos;if(A>g[Q]){if(A>this.columns){info("row is wrong length");this.err=!0;A=this.columns}1&Q^I&&++Q;g[Q]=A}else if(A<g[Q]){if(A<0){info("invalid code");this.err=!0;A=0}for(;Q>0&&A<g[Q-1];)--Q;g[Q]=A}this.codingPos=Q}_findTableCode(A,I,g,Q){const C=Q||0;for(let Q=A;Q<=I;++Q){let A=this._lookBits(Q);if(A===B)return[!0,1,!1];Q<I&&(A<<=I-Q);if(!C||A>=C){const I=g[A-C];if(I[0]===Q){this._eatBits(Q);return[!0,I[1],!0]}}}return[!1,0,!1]}_getTwoDimCode(){let A,I=0;if(this.eoblock){I=this._lookBits(7);A=C[I];if(A?.[0]>0){this._eatBits(A[0]);return A[1]}}else{const A=this._findTableCode(1,7,C);if(A[0]&&A[2])return A[1]}info("Bad two dim code");return B}_getWhiteCode(){let A,I=0;if(this.eoblock){I=this._lookBits(12);if(I===B)return 1;A=I>>5==0?E[I]:i[I>>3];if(A[0]>0){this._eatBits(A[0]);return A[1]}}else{let A=this._findTableCode(1,9,i);if(A[0])return A[1];A=this._findTableCode(11,12,E);if(A[0])return A[1]}info("bad white code");this._eatBits(1);return 1}_getBlackCode(){let A,I;if(this.eoblock){A=this._lookBits(13);if(A===B)return 1;I=A>>7==0?o[A]:A>>9==0&&A>>7!=0?D[(A>>1)-64]:h[A>>7];if(I[0]>0){this._eatBits(I[0]);return I[1]}}else{let A=this._findTableCode(2,6,h);if(A[0])return A[1];A=this._findTableCode(7,12,D,64);if(A[0])return A[1];A=this._findTableCode(10,13,o);if(A[0])return A[1]}info("bad black code");this._eatBits(1);return 1}_lookBits(A){let I;for(;this.inputBits<A;){if(-1===(I=this.source.next()))return 0===this.inputBits?B:this.inputBuf<<A-this.inputBits&65535>>16-A;this.inputBuf=this.inputBuf<<8|I;this.inputBits+=8}return this.inputBuf>>this.inputBits-A&65535>>16-A}_eatBits(A){(this.inputBits-=A)<0&&(this.inputBits=0)}}class Jbig2Error extends g{constructor(A){super(A,"Jbig2Error")}}class ContextCache{getContexts(A){return A in this?this[A]:this[A]=new Int8Array(65536)}}class DecodingContext{constructor(A,I,g){this.data=A;this.start=I;this.end=g}get decoder(){return util_shadow(this,"decoder",new ArithmeticDecoder(this.data,this.start,this.end))}get contextCache(){return util_shadow(this,"contextCache",new ContextCache)}}const s=2**31-1,a=-(2**31);function decodeInteger(A,I,g){const Q=A.getContexts(I);let B=1;function readBits(A){let I=0;for(let C=0;C<A;C++){const A=g.readBit(Q,B);B=B<256?B<<1|A:511&(B<<1|A)|256;I=I<<1|A}return I>>>0}const C=readBits(1),E=readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(32)+4436:readBits(12)+340:readBits(8)+84:readBits(6)+20:readBits(4)+4:readBits(2);let i;0===C?i=E:E>0&&(i=-E);return i>=a&&i<=s?i:null}function decodeIAID(A,I,g){const Q=A.getContexts("IAID");let B=1;for(let A=0;A<g;A++){B=B<<1|I.readBit(Q,B)}return g<31?B&(1<<g)-1:2147483647&B}const t=["SymbolDictionary",null,null,null,"IntermediateTextRegion",null,"ImmediateTextRegion","ImmediateLosslessTextRegion",null,null,null,null,null,null,null,null,"PatternDictionary",null,null,null,"IntermediateHalftoneRegion",null,"ImmediateHalftoneRegion","ImmediateLosslessHalftoneRegion",null,null,null,null,null,null,null,null,null,null,null,null,"IntermediateGenericRegion",null,"ImmediateGenericRegion","ImmediateLosslessGenericRegion","IntermediateGenericRefinementRegion",null,"ImmediateGenericRefinementRegion","ImmediateLosslessGenericRefinementRegion",null,null,null,null,"PageInformation","EndOfPage","EndOfStripe","EndOfFile","Profiles","Tables",null,null,null,null,null,null,null,null,"Extension"],G=[[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:2,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-2,y:0},{x:-1,y:0}],[{x:-3,y:-1},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}]],e=[{coding:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:-1,y:1},{x:0,y:1},{x:1,y:1}]},{coding:[{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:0,y:1},{x:1,y:1}]}],y=[39717,1941,229,405],c=[32,8];function decodeBitmap(A,I,g,Q,B,C,E,i){if(A){return decodeMMRBitmap(new Reader(i.data,i.start,i.end),I,g,!1)}if(0===Q&&!C&&!B&&4===E.length&&3===E[0].x&&-1===E[0].y&&-3===E[1].x&&-1===E[1].y&&2===E[2].x&&-2===E[2].y&&-2===E[3].x&&-2===E[3].y)return function decodeBitmapTemplate0(A,I,g){const Q=g.decoder,B=g.contextCache.getContexts("GB"),C=[];let E,i,o,D,h,s,a;for(i=0;i<I;i++){h=C[i]=new Uint8Array(A);s=i<1?h:C[i-1];a=i<2?h:C[i-2];E=a[0]<<13|a[1]<<12|a[2]<<11|s[0]<<7|s[1]<<6|s[2]<<5|s[3]<<4;for(o=0;o<A;o++){h[o]=D=Q.readBit(B,E);E=(31735&E)<<1|(o+3<A?a[o+3]<<11:0)|(o+4<A?s[o+4]<<4:0)|D}}return C}(I,g,i);const o=!!C,D=G[Q].concat(E);D.sort((function(A,I){return A.y-I.y||A.x-I.x}));const h=D.length,s=new Int8Array(h),a=new Int8Array(h),t=[];let e,c,r=0,w=0,R=0,n=0;for(c=0;c<h;c++){s[c]=D[c].x;a[c]=D[c].y;w=Math.min(w,D[c].x);R=Math.max(R,D[c].x);n=Math.min(n,D[c].y);c<h-1&&D[c].y===D[c+1].y&&D[c].x===D[c+1].x-1?r|=1<<h-1-c:t.push(c)}const S=t.length,N=new Int8Array(S),k=new Int8Array(S),J=new Uint16Array(S);for(e=0;e<S;e++){c=t[e];N[e]=D[c].x;k[e]=D[c].y;J[e]=1<<h-1-c}const U=-w,Y=-n,H=I-R,d=y[Q];let L=new Uint8Array(I);const K=[],M=i.decoder,l=i.contextCache.getContexts("GB");let f,q,b,u,Z,p=0,x=0;for(let A=0;A<g;A++){if(B){p^=M.readBit(l,d);if(p){K.push(L);continue}}L=new Uint8Array(L);K.push(L);for(f=0;f<I;f++){if(o&&C[A][f]){L[f]=0;continue}if(f>=U&&f<H&&A>=Y){x=x<<1&r;for(c=0;c<S;c++){q=A+k[c];b=f+N[c];u=K[q][b];if(u){u=J[c];x|=u}}}else{x=0;Z=h-1;for(c=0;c<h;c++,Z--){b=f+s[c];if(b>=0&&b<I){q=A+a[c];if(q>=0){u=K[q][b];u&&(x|=u<<Z)}}}}const g=M.readBit(l,x);L[f]=g}}return K}function decodeRefinement(A,I,g,Q,B,C,E,i,o){let D=e[g].coding;0===g&&(D=D.concat([i[0]]));const h=D.length,s=new Int32Array(h),a=new Int32Array(h);let t;for(t=0;t<h;t++){s[t]=D[t].x;a[t]=D[t].y}let G=e[g].reference;0===g&&(G=G.concat([i[1]]));const y=G.length,r=new Int32Array(y),w=new Int32Array(y);for(t=0;t<y;t++){r[t]=G[t].x;w[t]=G[t].y}const R=Q[0].length,n=Q.length,S=c[g],N=[],k=o.decoder,J=o.contextCache.getContexts("GR");let U=0;for(let g=0;g<I;g++){if(E){U^=k.readBit(J,S);if(U)throw new Jbig2Error("prediction is not supported")}const I=new Uint8Array(A);N.push(I);for(let E=0;E<A;E++){let i,o,D=0;for(t=0;t<h;t++){i=g+a[t];o=E+s[t];i<0||o<0||o>=A?D<<=1:D=D<<1|N[i][o]}for(t=0;t<y;t++){i=g+w[t]-C;o=E+r[t]-B;i<0||i>=n||o<0||o>=R?D<<=1:D=D<<1|Q[i][o]}const G=k.readBit(J,D);I[E]=G}}return N}function decodeTextRegion(A,I,g,Q,B,C,E,i,o,D,h,s,a,t,G,e,y,c,r){if(A&&I)throw new Jbig2Error("refinement with Huffman is not supported");const w=[];let R,n;for(R=0;R<Q;R++){n=new Uint8Array(g);if(B)for(let A=0;A<g;A++)n[A]=B;w.push(n)}const S=y.decoder,N=y.contextCache;let k=A?-t.tableDeltaT.decode(r):-decodeInteger(N,"IADT",S),J=0;R=0;for(;R<C;){k+=A?t.tableDeltaT.decode(r):decodeInteger(N,"IADT",S);J+=A?t.tableFirstS.decode(r):decodeInteger(N,"IAFS",S);let Q=J;for(;;){let B=0;E>1&&(B=A?r.readBits(c):decodeInteger(N,"IAIT",S));const C=E*k+B,J=A?t.symbolIDTable.decode(r):decodeIAID(N,S,o),U=I&&(A?r.readBit():decodeInteger(N,"IARI",S));let Y=i[J],H=Y[0].length,d=Y.length;if(U){const A=decodeInteger(N,"IARDW",S),I=decodeInteger(N,"IARDH",S);H+=A;d+=I;Y=decodeRefinement(H,d,G,Y,(A>>1)+decodeInteger(N,"IARDX",S),(I>>1)+decodeInteger(N,"IARDY",S),!1,e,y)}let L=0;D?1&s?L=d-1:Q+=d-1:s>1?Q+=H-1:L=H-1;const K=C-(1&s?0:d-1),M=Q-(2&s?H-1:0);let l,f,q;if(D)for(l=0;l<d;l++){n=w[M+l];if(!n)continue;q=Y[l];const A=Math.min(g-K,H);switch(a){case 0:for(f=0;f<A;f++)n[K+f]|=q[f];break;case 2:for(f=0;f<A;f++)n[K+f]^=q[f];break;default:throw new Jbig2Error(`operator ${a} is not supported`)}}else for(f=0;f<d;f++){n=w[K+f];if(n){q=Y[f];switch(a){case 0:for(l=0;l<H;l++)n[M+l]|=q[l];break;case 2:for(l=0;l<H;l++)n[M+l]^=q[l];break;default:throw new Jbig2Error(`operator ${a} is not supported`)}}}R++;const b=A?t.tableDeltaS.decode(r):decodeInteger(N,"IADS",S);if(null===b)break;Q+=L+b+h}}return w}function readSegmentHeader(A,I){const g={};g.number=readUint32(A,I);const Q=A[I+4],B=63&Q;if(!t[B])throw new Jbig2Error("invalid segment type: "+B);g.type=B;g.typeName=t[B];g.deferredNonRetain=!!(128&Q);const C=!!(64&Q),E=A[I+5];let i=E>>5&7;const o=[31&E];let D=I+6;if(7===E){i=536870911&readUint32(A,D-1);D+=3;let I=i+7>>3;o[0]=A[D++];for(;--I>0;)o.push(A[D++])}else if(5===E||6===E)throw new Jbig2Error("invalid referred-to flags");g.retainBits=o;let h=4;g.number<=256?h=1:g.number<=65536&&(h=2);const s=[];let a,G;for(a=0;a<i;a++){let I;I=1===h?A[D]:2===h?readUint16(A,D):readUint32(A,D);s.push(I);D+=h}g.referredTo=s;if(C){g.pageAssociation=readUint32(A,D);D+=4}else g.pageAssociation=A[D++];g.length=readUint32(A,D);D+=4;if(4294967295===g.length){if(38!==B)throw new Jbig2Error("invalid unknown segment length");{const I=readRegionSegmentInformation(A,D),Q=!!(1&A[D+r]),B=6,C=new Uint8Array(B);if(!Q){C[0]=255;C[1]=172}C[2]=I.height>>>24&255;C[3]=I.height>>16&255;C[4]=I.height>>8&255;C[5]=255&I.height;for(a=D,G=A.length;a<G;a++){let I=0;for(;I<B&&C[I]===A[a+I];)I++;if(I===B){g.length=a+B;break}}if(4294967295===g.length)throw new Jbig2Error("segment end was not found")}}g.headerEnd=D;return g}function readSegments(A,I,g,Q){const B=[];let C=g;for(;C<Q;){const g=readSegmentHeader(I,C);C=g.headerEnd;const Q={header:g,data:I};if(!A.randomAccess){Q.start=C;C+=g.length;Q.end=C}B.push(Q);if(51===g.type)break}if(A.randomAccess)for(let A=0,I=B.length;A<I;A++){B[A].start=C;C+=B[A].header.length;B[A].end=C}return B}function readRegionSegmentInformation(A,I){return{width:readUint32(A,I),height:readUint32(A,I+4),x:readUint32(A,I+8),y:readUint32(A,I+12),combinationOperator:7&A[I+16]}}const r=17;function processSegment(A,I){const g=A.header,Q=A.data,B=A.end;let C,E,i,o,D=A.start;switch(g.type){case 0:const A={},I=readUint16(Q,D);A.huffman=!!(1&I);A.refinement=!!(2&I);A.huffmanDHSelector=I>>2&3;A.huffmanDWSelector=I>>4&3;A.bitmapSizeSelector=I>>6&1;A.aggregationInstancesSelector=I>>7&1;A.bitmapCodingContextUsed=!!(256&I);A.bitmapCodingContextRetained=!!(512&I);A.template=I>>10&3;A.refinementTemplate=I>>12&1;D+=2;if(!A.huffman){o=0===A.template?4:1;E=[];for(i=0;i<o;i++){E.push({x:readInt8(Q,D),y:readInt8(Q,D+1)});D+=2}A.at=E}if(A.refinement&&!A.refinementTemplate){E=[];for(i=0;i<2;i++){E.push({x:readInt8(Q,D),y:readInt8(Q,D+1)});D+=2}A.refinementAt=E}A.numberOfExportedSymbols=readUint32(Q,D);D+=4;A.numberOfNewSymbols=readUint32(Q,D);D+=4;C=[A,g.number,g.referredTo,Q,D,B];break;case 6:case 7:const h={};h.info=readRegionSegmentInformation(Q,D);D+=r;const s=readUint16(Q,D);D+=2;h.huffman=!!(1&s);h.refinement=!!(2&s);h.logStripSize=s>>2&3;h.stripSize=1<<h.logStripSize;h.referenceCorner=s>>4&3;h.transposed=!!(64&s);h.combinationOperator=s>>7&3;h.defaultPixelValue=s>>9&1;h.dsOffset=s<<17>>27;h.refinementTemplate=s>>15&1;if(h.huffman){const A=readUint16(Q,D);D+=2;h.huffmanFS=3&A;h.huffmanDS=A>>2&3;h.huffmanDT=A>>4&3;h.huffmanRefinementDW=A>>6&3;h.huffmanRefinementDH=A>>8&3;h.huffmanRefinementDX=A>>10&3;h.huffmanRefinementDY=A>>12&3;h.huffmanRefinementSizeSelector=!!(16384&A)}if(h.refinement&&!h.refinementTemplate){E=[];for(i=0;i<2;i++){E.push({x:readInt8(Q,D),y:readInt8(Q,D+1)});D+=2}h.refinementAt=E}h.numberOfSymbolInstances=readUint32(Q,D);D+=4;C=[h,g.referredTo,Q,D,B];break;case 16:const a={},t=Q[D++];a.mmr=!!(1&t);a.template=t>>1&3;a.patternWidth=Q[D++];a.patternHeight=Q[D++];a.maxPatternIndex=readUint32(Q,D);D+=4;C=[a,g.number,Q,D,B];break;case 22:case 23:const G={};G.info=readRegionSegmentInformation(Q,D);D+=r;const e=Q[D++];G.mmr=!!(1&e);G.template=e>>1&3;G.enableSkip=!!(8&e);G.combinationOperator=e>>4&7;G.defaultPixelValue=e>>7&1;G.gridWidth=readUint32(Q,D);D+=4;G.gridHeight=readUint32(Q,D);D+=4;G.gridOffsetX=4294967295&readUint32(Q,D);D+=4;G.gridOffsetY=4294967295&readUint32(Q,D);D+=4;G.gridVectorX=readUint16(Q,D);D+=2;G.gridVectorY=readUint16(Q,D);D+=2;C=[G,g.referredTo,Q,D,B];break;case 38:case 39:const y={};y.info=readRegionSegmentInformation(Q,D);D+=r;const c=Q[D++];y.mmr=!!(1&c);y.template=c>>1&3;y.prediction=!!(8&c);if(!y.mmr){o=0===y.template?4:1;E=[];for(i=0;i<o;i++){E.push({x:readInt8(Q,D),y:readInt8(Q,D+1)});D+=2}y.at=E}C=[y,Q,D,B];break;case 48:const w={width:readUint32(Q,D),height:readUint32(Q,D+4),resolutionX:readUint32(Q,D+8),resolutionY:readUint32(Q,D+12)};4294967295===w.height&&delete w.height;const R=Q[D+16];readUint16(Q,D+17);w.lossless=!!(1&R);w.refinement=!!(2&R);w.defaultPixelValue=R>>2&1;w.combinationOperator=R>>3&3;w.requiresBuffer=!!(32&R);w.combinationOperatorOverride=!!(64&R);C=[w];break;case 49:case 50:case 51:case 62:break;case 53:C=[g.number,Q,D,B];break;default:throw new Jbig2Error(`segment type ${g.typeName}(${g.type}) is not implemented`)}const h="on"+g.typeName;h in I&&I[h].apply(I,C)}function processSegments(A,I){for(let g=0,Q=A.length;g<Q;g++)processSegment(A[g],I)}class SimpleSegmentVisitor{onPageInformation(A){this.currentPageInfo=A;const I=A.width+7>>3,g=new Uint8ClampedArray(I*A.height);A.defaultPixelValue&&g.fill(255);this.buffer=g}drawBitmap(A,I){const g=this.currentPageInfo,Q=A.width,B=A.height,C=g.width+7>>3,E=g.combinationOperatorOverride?A.combinationOperator:g.combinationOperator,i=this.buffer,o=128>>(7&A.x);let D,h,s,a,t=A.y*C+(A.x>>3);switch(E){case 0:for(D=0;D<B;D++){s=o;a=t;for(h=0;h<Q;h++){I[D][h]&&(i[a]|=s);s>>=1;if(!s){s=128;a++}}t+=C}break;case 2:for(D=0;D<B;D++){s=o;a=t;for(h=0;h<Q;h++){I[D][h]&&(i[a]^=s);s>>=1;if(!s){s=128;a++}}t+=C}break;default:throw new Jbig2Error(`operator ${E} is not supported`)}}onImmediateGenericRegion(A,I,g,Q){const B=A.info,C=new DecodingContext(I,g,Q),E=decodeBitmap(A.mmr,B.width,B.height,A.template,A.prediction,null,A.at,C);this.drawBitmap(B,E)}onImmediateLosslessGenericRegion(){this.onImmediateGenericRegion(...arguments)}onSymbolDictionary(A,I,g,Q,B,C){let E,i;if(A.huffman){E=function getSymbolDictionaryHuffmanTables(A,I,g){let Q,B,C,E,i=0;switch(A.huffmanDHSelector){case 0:case 1:Q=getStandardTable(A.huffmanDHSelector+4);break;case 3:Q=getCustomHuffmanTable(i,I,g);i++;break;default:throw new Jbig2Error("invalid Huffman DH selector")}switch(A.huffmanDWSelector){case 0:case 1:B=getStandardTable(A.huffmanDWSelector+2);break;case 3:B=getCustomHuffmanTable(i,I,g);i++;break;default:throw new Jbig2Error("invalid Huffman DW selector")}if(A.bitmapSizeSelector){C=getCustomHuffmanTable(i,I,g);i++}else C=getStandardTable(1);E=A.aggregationInstancesSelector?getCustomHuffmanTable(i,I,g):getStandardTable(1);return{tableDeltaHeight:Q,tableDeltaWidth:B,tableBitmapSize:C,tableAggregateInstances:E}}(A,g,this.customTables);i=new Reader(Q,B,C)}let o=this.symbols;o||(this.symbols=o={});const D=[];for(const A of g){const I=o[A];I&&D.push(...I)}const h=new DecodingContext(Q,B,C);o[I]=function decodeSymbolDictionary(A,I,g,Q,B,C,E,i,o,D,h,s){if(A&&I)throw new Jbig2Error("symbol refinement with Huffman is not supported");const a=[];let t=0,G=log2(g.length+Q);const e=h.decoder,y=h.contextCache;let c,r;if(A){c=getStandardTable(1);r=[];G=Math.max(G,1)}for(;a.length<Q;){t+=A?C.tableDeltaHeight.decode(s):decodeInteger(y,"IADH",e);let Q=0,B=0;const c=A?r.length:0;for(;;){const c=A?C.tableDeltaWidth.decode(s):decodeInteger(y,"IADW",e);if(null===c)break;Q+=c;B+=Q;let w;if(I){const B=decodeInteger(y,"IAAI",e);if(B>1)w=decodeTextRegion(A,I,Q,t,0,B,1,g.concat(a),G,0,0,1,0,C,o,D,h,0,s);else{const A=decodeIAID(y,e,G),I=decodeInteger(y,"IARDX",e),B=decodeInteger(y,"IARDY",e);w=decodeRefinement(Q,t,o,A<g.length?g[A]:a[A-g.length],I,B,!1,D,h)}a.push(w)}else if(A)r.push(Q);else{w=decodeBitmap(!1,Q,t,E,!1,null,i,h);a.push(w)}}if(A&&!I){const A=C.tableBitmapSize.decode(s);s.byteAlign();let I;if(0===A)I=readUncompressedBitmap(s,B,t);else{const g=s.end,Q=s.position+A;s.end=Q;I=decodeMMRBitmap(s,B,t,!1);s.end=g;s.position=Q}const g=r.length;if(c===g-1)a.push(I);else{let A,Q,B,C,E,i=0;for(A=c;A<g;A++){C=r[A];B=i+C;E=[];for(Q=0;Q<t;Q++)E.push(I[Q].subarray(i,B));a.push(E);i=B}}}}const w=[],R=[];let n,S,N=!1;const k=g.length+Q;for(;R.length<k;){let I=A?c.decode(s):decodeInteger(y,"IAEX",e);for(;I--;)R.push(N);N=!N}for(n=0,S=g.length;n<S;n++)R[n]&&w.push(g[n]);for(let A=0;A<Q;n++,A++)R[n]&&w.push(a[A]);return w}(A.huffman,A.refinement,D,A.numberOfNewSymbols,A.numberOfExportedSymbols,E,A.template,A.at,A.refinementTemplate,A.refinementAt,h,i)}onImmediateTextRegion(A,I,g,Q,B){const C=A.info;let E,i;const o=this.symbols,D=[];for(const A of I){const I=o[A];I&&D.push(...I)}const h=log2(D.length);if(A.huffman){i=new Reader(g,Q,B);E=function getTextRegionHuffmanTables(A,I,g,Q,B){const C=[];for(let A=0;A<=34;A++){const I=B.readBits(4);C.push(new HuffmanLine([A,I,0,0]))}const E=new HuffmanTable(C,!1);C.length=0;for(let A=0;A<Q;){const I=E.decode(B);if(I>=32){let g,Q,E;switch(I){case 32:if(0===A)throw new Jbig2Error("no previous value in symbol ID table");Q=B.readBits(2)+3;g=C[A-1].prefixLength;break;case 33:Q=B.readBits(3)+3;g=0;break;case 34:Q=B.readBits(7)+11;g=0;break;default:throw new Jbig2Error("invalid code length in symbol ID table")}for(E=0;E<Q;E++){C.push(new HuffmanLine([A,g,0,0]));A++}}else{C.push(new HuffmanLine([A,I,0,0]));A++}}B.byteAlign();const i=new HuffmanTable(C,!1);let o,D,h,s=0;switch(A.huffmanFS){case 0:case 1:o=getStandardTable(A.huffmanFS+6);break;case 3:o=getCustomHuffmanTable(s,I,g);s++;break;default:throw new Jbig2Error("invalid Huffman FS selector")}switch(A.huffmanDS){case 0:case 1:case 2:D=getStandardTable(A.huffmanDS+8);break;case 3:D=getCustomHuffmanTable(s,I,g);s++;break;default:throw new Jbig2Error("invalid Huffman DS selector")}switch(A.huffmanDT){case 0:case 1:case 2:h=getStandardTable(A.huffmanDT+11);break;case 3:h=getCustomHuffmanTable(s,I,g);s++;break;default:throw new Jbig2Error("invalid Huffman DT selector")}if(A.refinement)throw new Jbig2Error("refinement with Huffman is not supported");return{symbolIDTable:i,tableFirstS:o,tableDeltaS:D,tableDeltaT:h}}(A,I,this.customTables,D.length,i)}const s=new DecodingContext(g,Q,B),a=decodeTextRegion(A.huffman,A.refinement,C.width,C.height,A.defaultPixelValue,A.numberOfSymbolInstances,A.stripSize,D,h,A.transposed,A.dsOffset,A.referenceCorner,A.combinationOperator,E,A.refinementTemplate,A.refinementAt,s,A.logStripSize,i);this.drawBitmap(C,a)}onImmediateLosslessTextRegion(){this.onImmediateTextRegion(...arguments)}onPatternDictionary(A,I,g,Q,B){let C=this.patterns;C||(this.patterns=C={});const E=new DecodingContext(g,Q,B);C[I]=function decodePatternDictionary(A,I,g,Q,B,C){const E=[];if(!A){E.push({x:-I,y:0});0===B&&E.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const i=decodeBitmap(A,(Q+1)*I,g,B,!1,null,E,C),o=[];for(let A=0;A<=Q;A++){const Q=[],B=I*A,C=B+I;for(let A=0;A<g;A++)Q.push(i[A].subarray(B,C));o.push(Q)}return o}(A.mmr,A.patternWidth,A.patternHeight,A.maxPatternIndex,A.template,E)}onImmediateHalftoneRegion(A,I,g,Q,B){const C=this.patterns[I[0]],E=A.info,i=new DecodingContext(g,Q,B),o=function decodeHalftoneRegion(A,I,g,Q,B,C,E,i,o,D,h,s,a,t,G){if(E)throw new Jbig2Error("skip is not supported");if(0!==i)throw new Jbig2Error(`operator "${i}" is not supported in halftone region`);const e=[];let y,c,r;for(y=0;y<B;y++){r=new Uint8Array(Q);if(C)for(c=0;c<Q;c++)r[c]=C;e.push(r)}const w=I.length,R=I[0],n=R[0].length,S=R.length,N=log2(w),k=[];if(!A){k.push({x:g<=1?3:2,y:-1});0===g&&k.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const J=[];let U,Y,H,d,L,K,M,l,f,q,b;A&&(U=new Reader(G.data,G.start,G.end));for(y=N-1;y>=0;y--){Y=A?decodeMMRBitmap(U,o,D,!0):decodeBitmap(!1,o,D,g,!1,null,k,G);J[y]=Y}for(H=0;H<D;H++)for(d=0;d<o;d++){L=0;K=0;for(c=N-1;c>=0;c--){L^=J[c][H][d];K|=L<<c}M=I[K];l=h+H*t+d*a>>8;f=s+H*a-d*t>>8;if(l>=0&&l+n<=Q&&f>=0&&f+S<=B)for(y=0;y<S;y++){b=e[f+y];q=M[y];for(c=0;c<n;c++)b[l+c]|=q[c]}else{let A,I;for(y=0;y<S;y++){I=f+y;if(!(I<0||I>=B)){b=e[I];q=M[y];for(c=0;c<n;c++){A=l+c;A>=0&&A<Q&&(b[A]|=q[c])}}}}}return e}(A.mmr,C,A.template,E.width,E.height,A.defaultPixelValue,A.enableSkip,A.combinationOperator,A.gridWidth,A.gridHeight,A.gridOffsetX,A.gridOffsetY,A.gridVectorX,A.gridVectorY,i);this.drawBitmap(E,o)}onImmediateLosslessHalftoneRegion(){this.onImmediateHalftoneRegion(...arguments)}onTables(A,I,g,Q){let B=this.customTables;B||(this.customTables=B={});B[A]=function decodeTablesSegment(A,I,g){const Q=A[I],B=4294967295&readUint32(A,I+1),C=4294967295&readUint32(A,I+5),E=new Reader(A,I+9,g),i=1+(Q>>1&7),o=1+(Q>>4&7),D=[];let h,s,a=B;do{h=E.readBits(i);s=E.readBits(o);D.push(new HuffmanLine([a,h,s,0]));a+=1<<s}while(a<C);h=E.readBits(i);D.push(new HuffmanLine([B-1,h,32,0,"lower"]));h=E.readBits(i);D.push(new HuffmanLine([C,h,32,0]));if(1&Q){h=E.readBits(i);D.push(new HuffmanLine([h,0]))}return new HuffmanTable(D,!1)}(I,g,Q)}}class HuffmanLine{constructor(A){if(2===A.length){this.isOOB=!0;this.rangeLow=0;this.prefixLength=A[0];this.rangeLength=0;this.prefixCode=A[1];this.isLowerRange=!1}else{this.isOOB=!1;this.rangeLow=A[0];this.prefixLength=A[1];this.rangeLength=A[2];this.prefixCode=A[3];this.isLowerRange="lower"===A[4]}}}class HuffmanTreeNode{constructor(A){this.children=[];if(A){this.isLeaf=!0;this.rangeLength=A.rangeLength;this.rangeLow=A.rangeLow;this.isLowerRange=A.isLowerRange;this.isOOB=A.isOOB}else this.isLeaf=!1}buildTree(A,I){const g=A.prefixCode>>I&1;if(I<=0)this.children[g]=new HuffmanTreeNode(A);else{let Q=this.children[g];Q||(this.children[g]=Q=new HuffmanTreeNode(null));Q.buildTree(A,I-1)}}decodeNode(A){if(this.isLeaf){if(this.isOOB)return null;const I=A.readBits(this.rangeLength);return this.rangeLow+(this.isLowerRange?-I:I)}const I=this.children[A.readBit()];if(!I)throw new Jbig2Error("invalid Huffman data");return I.decodeNode(A)}}class HuffmanTable{constructor(A,I){I||this.assignPrefixCodes(A);this.rootNode=new HuffmanTreeNode(null);for(let I=0,g=A.length;I<g;I++){const g=A[I];g.prefixLength>0&&this.rootNode.buildTree(g,g.prefixLength-1)}}decode(A){return this.rootNode.decodeNode(A)}assignPrefixCodes(A){const I=A.length;let g=0;for(let Q=0;Q<I;Q++)g=Math.max(g,A[Q].prefixLength);const Q=new Uint32Array(g+1);for(let g=0;g<I;g++)Q[A[g].prefixLength]++;let B,C,E,i=1,o=0;Q[0]=0;for(;i<=g;){o=o+Q[i-1]<<1;B=o;C=0;for(;C<I;){E=A[C];if(E.prefixLength===i){E.prefixCode=B;B++}C++}i++}}}const w={};function getStandardTable(A){let I,g=w[A];if(g)return g;switch(A){case 1:I=[[0,1,4,0],[16,2,8,2],[272,3,16,6],[65808,3,32,7]];break;case 2:I=[[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[75,6,32,62],[6,63]];break;case 3:I=[[-256,8,8,254],[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[-257,8,32,255,"lower"],[75,7,32,126],[6,62]];break;case 4:I=[[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[76,5,32,31]];break;case 5:I=[[-255,7,8,126],[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[-256,7,32,127,"lower"],[76,6,32,62]];break;case 6:I=[[-2048,5,10,28],[-1024,4,9,8],[-512,4,8,9],[-256,4,7,10],[-128,5,6,29],[-64,5,5,30],[-32,4,5,11],[0,2,7,0],[128,3,7,2],[256,3,8,3],[512,4,9,12],[1024,4,10,13],[-2049,6,32,62,"lower"],[2048,6,32,63]];break;case 7:I=[[-1024,4,9,8],[-512,3,8,0],[-256,4,7,9],[-128,5,6,26],[-64,5,5,27],[-32,4,5,10],[0,4,5,11],[32,5,5,28],[64,5,6,29],[128,4,7,12],[256,3,8,1],[512,3,9,2],[1024,3,10,3],[-1025,5,32,30,"lower"],[2048,5,32,31]];break;case 8:I=[[-15,8,3,252],[-7,9,1,508],[-5,8,1,253],[-3,9,0,509],[-2,7,0,124],[-1,4,0,10],[0,2,1,0],[2,5,0,26],[3,6,0,58],[4,3,4,4],[20,6,1,59],[22,4,4,11],[38,4,5,12],[70,5,6,27],[134,5,7,28],[262,6,7,60],[390,7,8,125],[646,6,10,61],[-16,9,32,510,"lower"],[1670,9,32,511],[2,1]];break;case 9:I=[[-31,8,4,252],[-15,9,2,508],[-11,8,2,253],[-7,9,1,509],[-5,7,1,124],[-3,4,1,10],[-1,3,1,2],[1,3,1,3],[3,5,1,26],[5,6,1,58],[7,3,5,4],[39,6,2,59],[43,4,5,11],[75,4,6,12],[139,5,7,27],[267,5,8,28],[523,6,8,60],[779,7,9,125],[1291,6,11,61],[-32,9,32,510,"lower"],[3339,9,32,511],[2,0]];break;case 10:I=[[-21,7,4,122],[-5,8,0,252],[-4,7,0,123],[-3,5,0,24],[-2,2,2,0],[2,5,0,25],[3,6,0,54],[4,7,0,124],[5,8,0,253],[6,2,6,1],[70,5,5,26],[102,6,5,55],[134,6,6,56],[198,6,7,57],[326,6,8,58],[582,6,9,59],[1094,6,10,60],[2118,7,11,125],[-22,8,32,254,"lower"],[4166,8,32,255],[2,2]];break;case 11:I=[[1,1,0,0],[2,2,1,2],[4,4,0,12],[5,4,1,13],[7,5,1,28],[9,5,2,29],[13,6,2,60],[17,7,2,122],[21,7,3,123],[29,7,4,124],[45,7,5,125],[77,7,6,126],[141,7,32,127]];break;case 12:I=[[1,1,0,0],[2,2,0,2],[3,3,1,6],[5,5,0,28],[6,5,1,29],[8,6,1,60],[10,7,0,122],[11,7,1,123],[13,7,2,124],[17,7,3,125],[25,7,4,126],[41,8,5,254],[73,8,32,255]];break;case 13:I=[[1,1,0,0],[2,3,0,4],[3,4,0,12],[4,5,0,28],[5,4,1,13],[7,3,3,5],[15,6,1,58],[17,6,2,59],[21,6,3,60],[29,6,4,61],[45,6,5,62],[77,7,6,126],[141,7,32,127]];break;case 14:I=[[-2,3,0,4],[-1,3,0,5],[0,1,0,0],[1,3,0,6],[2,3,0,7]];break;case 15:I=[[-24,7,4,124],[-8,6,2,60],[-4,5,1,28],[-2,4,0,12],[-1,3,0,4],[0,1,0,0],[1,3,0,5],[2,4,0,13],[3,5,1,29],[5,6,2,61],[9,7,4,125],[-25,7,32,126,"lower"],[25,7,32,127]];break;default:throw new Jbig2Error(`standard table B.${A} does not exist`)}for(let A=0,g=I.length;A<g;A++)I[A]=new HuffmanLine(I[A]);g=new HuffmanTable(I,!0);w[A]=g;return g}class Reader{constructor(A,I,g){this.data=A;this.start=I;this.end=g;this.position=I;this.shift=-1;this.currentByte=0}readBit(){if(this.shift<0){if(this.position>=this.end)throw new Jbig2Error("end of data while reading bit");this.currentByte=this.data[this.position++];this.shift=7}const A=this.currentByte>>this.shift&1;this.shift--;return A}readBits(A){let I,g=0;for(I=A-1;I>=0;I--)g|=this.readBit()<<I;return g}byteAlign(){this.shift=-1}next(){return this.position>=this.end?-1:this.data[this.position++]}}function getCustomHuffmanTable(A,I,g){let Q=0;for(let B=0,C=I.length;B<C;B++){const C=g[I[B]];if(C){if(A===Q)return C;Q++}}throw new Jbig2Error("can't find custom Huffman table")}function readUncompressedBitmap(A,I,g){const Q=[];for(let B=0;B<g;B++){const g=new Uint8Array(I);Q.push(g);for(let Q=0;Q<I;Q++)g[Q]=A.readBit();A.byteAlign()}return Q}function decodeMMRBitmap(A,I,g,Q){const B=new CCITTFaxDecoder(A,{K:-1,Columns:I,Rows:g,BlackIs1:!0,EndOfBlock:Q}),C=[];let E,i=!1;for(let A=0;A<g;A++){const A=new Uint8Array(I);C.push(A);let g=-1;for(let Q=0;Q<I;Q++){if(g<0){E=B.readNextChar();if(-1===E){E=0;i=!0}g=7}A[Q]=E>>g&1;g--}}if(Q&&!i){const A=5;for(let I=0;I<A&&-1!==B.readNextChar();I++);}return C}class Jbig2Image{parseChunks(A){return function parseJbig2Chunks(A){const I=new SimpleSegmentVisitor;for(let g=0,Q=A.length;g<Q;g++){const Q=A[g];processSegments(readSegments({},Q.data,Q.start,Q.end),I)}return I.buffer}(A)}parse(A){const{imgData:I,width:g,height:Q}=function parseJbig2(A){const I=A.length;let g=0;if(151!==A[g]||74!==A[g+1]||66!==A[g+2]||50!==A[g+3]||13!==A[g+4]||10!==A[g+5]||26!==A[g+6]||10!==A[g+7])throw new Jbig2Error("parseJbig2 - invalid header.");const Q=Object.create(null);g+=8;const B=A[g++];Q.randomAccess=!(1&B);if(!(2&B)){Q.numberOfPages=readUint32(A,g);g+=4}const C=readSegments(Q,A,g,I),E=new SimpleSegmentVisitor;processSegments(C,E);const{width:i,height:o}=E.currentPageInfo,D=E.buffer,h=new Uint8ClampedArray(i*o);let s=0,a=0;for(let A=0;A<o;A++){let A,I=0;for(let g=0;g<i;g++){if(!I){I=128;A=D[a++]}h[s++]=A&I?0:255;I>>=1}}return{imgData:h,width:i,height:o}}(A);this.width=g;this.height=Q;return I}}class JpegError extends g{constructor(A){super(A,"JpegError")}}class DNLMarkerError extends g{constructor(A,I){super(A,"DNLMarkerError");this.scanLines=I}}class EOIMarkerError extends g{constructor(A){super(A,"EOIMarkerError")}}const R=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),n=4017,S=799,N=3406,k=2276,J=1567,U=3784,Y=5793,H=2896;function buildHuffmanTable(A,I){let g,Q,B=0,C=16;for(;C>0&&!A[C-1];)C--;const E=[{children:[],index:0}];let i,o=E[0];for(g=0;g<C;g++){for(Q=0;Q<A[g];Q++){o=E.pop();o.children[o.index]=I[B];for(;o.index>0;)o=E.pop();o.index++;E.push(o);for(;E.length<=g;){E.push(i={children:[],index:0});o.children[o.index]=i.children;o=i}B++}if(g+1<C){E.push(i={children:[],index:0});o.children[o.index]=i.children;o=i}}return E[0].children}function getBlockBufferOffset(A,I,g){return 64*((A.blocksPerLine+1)*I+g)}function decodeScan(A,I,g,Q,B,C,E,i,o,D=!1){const h=g.mcusPerLine,s=g.progressive,a=I;let t=0,G=0;function readBit(){if(G>0){G--;return t>>G&1}t=A[I++];if(255===t){const Q=A[I++];if(Q){if(220===Q&&D){const Q=readUint16(A,I+=2);I+=2;if(Q>0&&Q!==g.scanLines)throw new DNLMarkerError("Found DNL marker (0xFFDC) while parsing scan data",Q)}else if(217===Q){if(D){const A=r*(8===g.precision?8:0);if(A>0&&Math.round(g.scanLines/A)>=5)throw new DNLMarkerError("Found EOI marker (0xFFD9) while parsing scan data, possibly caused by incorrect `scanLines` parameter",A)}throw new EOIMarkerError("Found EOI marker (0xFFD9) while parsing scan data")}throw new JpegError(`unexpected marker ${(t<<8|Q).toString(16)}`)}}G=7;return t>>>7}function decodeHuffman(A){let I=A;for(;;){I=I[readBit()];switch(typeof I){case"number":return I;case"object":continue}throw new JpegError("invalid huffman sequence")}}function receive(A){let I=0;for(;A>0;){I=I<<1|readBit();A--}return I}function receiveAndExtend(A){if(1===A)return 1===readBit()?1:-1;const I=receive(A);return I>=1<<A-1?I:I+(-1<<A)+1}let e=0;let y,c=0;let r=0;function decodeMcu(A,I,g,Q,B){const C=g%h;r=(g/h|0)*A.v+Q;const E=C*A.h+B;I(A,getBlockBufferOffset(A,r,E))}function decodeBlock(A,I,g){r=g/A.blocksPerLine|0;const Q=g%A.blocksPerLine;I(A,getBlockBufferOffset(A,r,Q))}const w=Q.length;let n,S,N,k,J,U;U=s?0===C?0===i?function decodeDCFirst(A,I){const g=decodeHuffman(A.huffmanTableDC),Q=0===g?0:receiveAndExtend(g)<<o;A.blockData[I]=A.pred+=Q}:function decodeDCSuccessive(A,I){A.blockData[I]|=readBit()<<o}:0===i?function decodeACFirst(A,I){if(e>0){e--;return}let g=C;const Q=E;for(;g<=Q;){const Q=decodeHuffman(A.huffmanTableAC),B=15&Q,C=Q>>4;if(0===B){if(C<15){e=receive(C)+(1<<C)-1;break}g+=16;continue}g+=C;const E=R[g];A.blockData[I+E]=receiveAndExtend(B)*(1<<o);g++}}:function decodeACSuccessive(A,I){let g=C;const Q=E;let B,i,D=0;for(;g<=Q;){const Q=I+R[g],C=A.blockData[Q]<0?-1:1;switch(c){case 0:i=decodeHuffman(A.huffmanTableAC);B=15&i;D=i>>4;if(0===B)if(D<15){e=receive(D)+(1<<D);c=4}else{D=16;c=1}else{if(1!==B)throw new JpegError("invalid ACn encoding");y=receiveAndExtend(B);c=D?2:3}continue;case 1:case 2:if(A.blockData[Q])A.blockData[Q]+=C*(readBit()<<o);else{D--;0===D&&(c=2===c?3:0)}break;case 3:if(A.blockData[Q])A.blockData[Q]+=C*(readBit()<<o);else{A.blockData[Q]=y<<o;c=0}break;case 4:A.blockData[Q]&&(A.blockData[Q]+=C*(readBit()<<o))}g++}if(4===c){e--;0===e&&(c=0)}}:function decodeBaseline(A,I){const g=decodeHuffman(A.huffmanTableDC),Q=0===g?0:receiveAndExtend(g);A.blockData[I]=A.pred+=Q;let B=1;for(;B<64;){const g=decodeHuffman(A.huffmanTableAC),Q=15&g,C=g>>4;if(0===Q){if(C<15)break;B+=16;continue}B+=C;const E=R[B];A.blockData[I+E]=receiveAndExtend(Q);B++}};let Y,H=0;const d=1===w?Q[0].blocksPerLine*Q[0].blocksPerColumn:h*g.mcusPerColumn;let L,K;for(;H<=d;){const g=B?Math.min(d-H,B):d;if(g>0){for(S=0;S<w;S++)Q[S].pred=0;e=0;if(1===w){n=Q[0];for(J=0;J<g;J++){decodeBlock(n,U,H);H++}}else for(J=0;J<g;J++){for(S=0;S<w;S++){n=Q[S];L=n.h;K=n.v;for(N=0;N<K;N++)for(k=0;k<L;k++)decodeMcu(n,U,H,N,k)}H++}}G=0;Y=findNextFileMarker(A,I);if(!Y)break;if(Y.invalid){util_warn(`decodeScan - ${g>0?"unexpected":"excessive"} MCU data, current marker is: ${Y.invalid}`);I=Y.offset}if(!(Y.marker>=65488&&Y.marker<=65495))break;I+=2}return I-a}function quantizeAndInverse(A,I,g){const Q=A.quantizationTable,B=A.blockData;let C,E,i,o,D,h,s,a,t,G,e,y,c,r,w,R,d;if(!Q)throw new JpegError("missing required Quantization Table.");for(let A=0;A<64;A+=8){t=B[I+A];G=B[I+A+1];e=B[I+A+2];y=B[I+A+3];c=B[I+A+4];r=B[I+A+5];w=B[I+A+6];R=B[I+A+7];t*=Q[A];if(0!=(G|e|y|c|r|w|R)){G*=Q[A+1];e*=Q[A+2];y*=Q[A+3];c*=Q[A+4];r*=Q[A+5];w*=Q[A+6];R*=Q[A+7];C=Y*t+128>>8;E=Y*c+128>>8;i=e;o=w;D=H*(G-R)+128>>8;a=H*(G+R)+128>>8;h=y<<4;s=r<<4;C=C+E+1>>1;E=C-E;d=i*U+o*J+128>>8;i=i*J-o*U+128>>8;o=d;D=D+s+1>>1;s=D-s;a=a+h+1>>1;h=a-h;C=C+o+1>>1;o=C-o;E=E+i+1>>1;i=E-i;d=D*k+a*N+2048>>12;D=D*N-a*k+2048>>12;a=d;d=h*S+s*n+2048>>12;h=h*n-s*S+2048>>12;s=d;g[A]=C+a;g[A+7]=C-a;g[A+1]=E+s;g[A+6]=E-s;g[A+2]=i+h;g[A+5]=i-h;g[A+3]=o+D;g[A+4]=o-D}else{d=Y*t+512>>10;g[A]=d;g[A+1]=d;g[A+2]=d;g[A+3]=d;g[A+4]=d;g[A+5]=d;g[A+6]=d;g[A+7]=d}}for(let A=0;A<8;++A){t=g[A];G=g[A+8];e=g[A+16];y=g[A+24];c=g[A+32];r=g[A+40];w=g[A+48];R=g[A+56];if(0!=(G|e|y|c|r|w|R)){C=Y*t+2048>>12;E=Y*c+2048>>12;i=e;o=w;D=H*(G-R)+2048>>12;a=H*(G+R)+2048>>12;h=y;s=r;C=4112+(C+E+1>>1);E=C-E;d=i*U+o*J+2048>>12;i=i*J-o*U+2048>>12;o=d;D=D+s+1>>1;s=D-s;a=a+h+1>>1;h=a-h;C=C+o+1>>1;o=C-o;E=E+i+1>>1;i=E-i;d=D*k+a*N+2048>>12;D=D*N-a*k+2048>>12;a=d;d=h*S+s*n+2048>>12;h=h*n-s*S+2048>>12;s=d;t=C+a;R=C-a;G=E+s;w=E-s;e=i+h;r=i-h;y=o+D;c=o-D;t<16?t=0:t>=4080?t=255:t>>=4;G<16?G=0:G>=4080?G=255:G>>=4;e<16?e=0:e>=4080?e=255:e>>=4;y<16?y=0:y>=4080?y=255:y>>=4;c<16?c=0:c>=4080?c=255:c>>=4;r<16?r=0:r>=4080?r=255:r>>=4;w<16?w=0:w>=4080?w=255:w>>=4;R<16?R=0:R>=4080?R=255:R>>=4;B[I+A]=t;B[I+A+8]=G;B[I+A+16]=e;B[I+A+24]=y;B[I+A+32]=c;B[I+A+40]=r;B[I+A+48]=w;B[I+A+56]=R}else{d=Y*t+8192>>14;d=d<-2040?0:d>=2024?255:d+2056>>4;B[I+A]=d;B[I+A+8]=d;B[I+A+16]=d;B[I+A+24]=d;B[I+A+32]=d;B[I+A+40]=d;B[I+A+48]=d;B[I+A+56]=d}}}function buildComponentData(A,I){const g=I.blocksPerLine,Q=I.blocksPerColumn,B=new Int16Array(64);for(let A=0;A<Q;A++)for(let Q=0;Q<g;Q++){quantizeAndInverse(I,getBlockBufferOffset(I,A,Q),B)}return I.blockData}function findNextFileMarker(A,I,g=I){const Q=A.length-1;let B=g<I?g:I;if(I>=Q)return null;const C=readUint16(A,I);if(C>=65472&&C<=65534)return{invalid:null,marker:C,offset:I};let E=readUint16(A,B);for(;!(E>=65472&&E<=65534);){if(++B>=Q)return null;E=readUint16(A,B)}return{invalid:C.toString(16),marker:E,offset:B}}class JpegImage{constructor({decodeTransform:A=null,colorTransform:I=-1}={}){this._decodeTransform=A;this._colorTransform=I}parse(A,{dnlScanLines:I=null}={}){function readDataBlock(){const I=readUint16(A,B);B+=2;let g=B+I-2;const Q=findNextFileMarker(A,g,B);if(Q?.invalid){util_warn("readDataBlock - incorrect length, current marker is: "+Q.invalid);g=Q.offset}const C=A.subarray(B,g);B+=C.length;return C}function prepareComponents(A){const I=Math.ceil(A.samplesPerLine/8/A.maxH),g=Math.ceil(A.scanLines/8/A.maxV);for(const Q of A.components){const B=Math.ceil(Math.ceil(A.samplesPerLine/8)*Q.h/A.maxH),C=Math.ceil(Math.ceil(A.scanLines/8)*Q.v/A.maxV),E=I*Q.h,i=64*(g*Q.v)*(E+1);Q.blockData=new Int16Array(i);Q.blocksPerLine=B;Q.blocksPerColumn=C}A.mcusPerLine=I;A.mcusPerColumn=g}let g,Q,B=0,C=null,E=null,i=0;const o=[],D=[],h=[];let s=readUint16(A,B);B+=2;if(65496!==s)throw new JpegError("SOI not found");s=readUint16(A,B);B+=2;A:for(;65497!==s;){let a,t,G;switch(s){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:const e=readDataBlock();65504===s&&74===e[0]&&70===e[1]&&73===e[2]&&70===e[3]&&0===e[4]&&(C={version:{major:e[5],minor:e[6]},densityUnits:e[7],xDensity:e[8]<<8|e[9],yDensity:e[10]<<8|e[11],thumbWidth:e[12],thumbHeight:e[13],thumbData:e.subarray(14,14+3*e[12]*e[13])});65518===s&&65===e[0]&&100===e[1]&&111===e[2]&&98===e[3]&&101===e[4]&&(E={version:e[5]<<8|e[6],flags0:e[7]<<8|e[8],flags1:e[9]<<8|e[10],transformCode:e[11]});break;case 65499:const y=readUint16(A,B);B+=2;const c=y+B-2;let r;for(;B<c;){const I=A[B++],g=new Uint16Array(64);if(I>>4==0)for(t=0;t<64;t++){r=R[t];g[r]=A[B++]}else{if(I>>4!=1)throw new JpegError("DQT - invalid table spec");for(t=0;t<64;t++){r=R[t];g[r]=readUint16(A,B);B+=2}}o[15&I]=g}break;case 65472:case 65473:case 65474:if(g)throw new JpegError("Only single frame JPEGs supported");B+=2;g={};g.extended=65473===s;g.progressive=65474===s;g.precision=A[B++];const w=readUint16(A,B);B+=2;g.scanLines=I||w;g.samplesPerLine=readUint16(A,B);B+=2;g.components=[];g.componentIds={};const n=A[B++];let S=0,N=0;for(a=0;a<n;a++){const I=A[B],Q=A[B+1]>>4,C=15&A[B+1];S<Q&&(S=Q);N<C&&(N=C);const E=A[B+2];G=g.components.push({h:Q,v:C,quantizationId:E,quantizationTable:null});g.componentIds[I]=G-1;B+=3}g.maxH=S;g.maxV=N;prepareComponents(g);break;case 65476:const k=readUint16(A,B);B+=2;for(a=2;a<k;){const I=A[B++],g=new Uint8Array(16);let Q=0;for(t=0;t<16;t++,B++)Q+=g[t]=A[B];const C=new Uint8Array(Q);for(t=0;t<Q;t++,B++)C[t]=A[B];a+=17+Q;(I>>4==0?h:D)[15&I]=buildHuffmanTable(g,C)}break;case 65501:B+=2;Q=readUint16(A,B);B+=2;break;case 65498:const J=1==++i&&!I;B+=2;const U=A[B++],Y=[];for(a=0;a<U;a++){const I=A[B++],Q=g.componentIds[I],C=g.components[Q];C.index=I;const E=A[B++];C.huffmanTableDC=h[E>>4];C.huffmanTableAC=D[15&E];Y.push(C)}const H=A[B++],d=A[B++],L=A[B++];try{const I=decodeScan(A,B,g,Y,Q,H,d,L>>4,15&L,J);B+=I}catch(I){if(I instanceof DNLMarkerError){util_warn(`${I.message} -- attempting to re-parse the JPEG image.`);return this.parse(A,{dnlScanLines:I.scanLines})}if(I instanceof EOIMarkerError){util_warn(`${I.message} -- ignoring the rest of the image data.`);break A}throw I}break;case 65500:B+=4;break;case 65535:255!==A[B]&&B--;break;default:const K=findNextFileMarker(A,B-2,B-3);if(K?.invalid){util_warn("JpegImage.parse - unexpected data, current marker is: "+K.invalid);B=K.offset;break}if(!K||B>=A.length-1){util_warn("JpegImage.parse - reached the end of the image data without finding an EOI marker (0xFFD9).");break A}throw new JpegError("JpegImage.parse - unknown marker: "+s.toString(16))}s=readUint16(A,B);B+=2}if(!g)throw new JpegError("JpegImage.parse - no frame data found.");this.width=g.samplesPerLine;this.height=g.scanLines;this.jfif=C;this.adobe=E;this.components=[];for(const A of g.components){const I=o[A.quantizationId];I&&(A.quantizationTable=I);this.components.push({index:A.index,output:buildComponentData(0,A),scaleX:A.h/g.maxH,scaleY:A.v/g.maxV,blocksPerLine:A.blocksPerLine,blocksPerColumn:A.blocksPerColumn})}this.numComponents=this.components.length}_getLinearizedBlockData(A,I,g=!1){const Q=this.width/A,B=this.height/I;let C,E,i,o,D,h,s,a,t,G,e,y=0;const c=this.components.length,r=A*I*c,w=new Uint8ClampedArray(r),R=new Uint32Array(A),n=4294967288;let S;for(s=0;s<c;s++){C=this.components[s];E=C.scaleX*Q;i=C.scaleY*B;y=s;e=C.output;o=C.blocksPerLine+1<<3;if(E!==S){for(D=0;D<A;D++){a=0|D*E;R[D]=(a&n)<<3|7&a}S=E}for(h=0;h<I;h++){a=0|h*i;G=o*(a&n)|(7&a)<<3;for(D=0;D<A;D++){w[y]=e[G+R[D]];y+=c}}}let N=this._decodeTransform;g||4!==c||N||(N=new Int32Array([-256,255,-256,255,-256,255,-256,255]));if(N)for(s=0;s<r;)for(a=0,t=0;a<c;a++,s++,t+=2)w[s]=(w[s]*N[t]>>8)+N[t+1];return w}get _isColorConversionNeeded(){return this.adobe?!!this.adobe.transformCode:3===this.numComponents?0!==this._colorTransform&&(82!==this.components[0].index||71!==this.components[1].index||66!==this.components[2].index):1===this._colorTransform}_convertYccToRgb(A){let I,g,Q;for(let B=0,C=A.length;B<C;B+=3){I=A[B];g=A[B+1];Q=A[B+2];A[B]=I-179.456+1.402*Q;A[B+1]=I+135.459-.344*g-.714*Q;A[B+2]=I-226.816+1.772*g}return A}_convertYccToRgba(A,I){for(let g=0,Q=0,B=A.length;g<B;g+=3,Q+=4){const B=A[g],C=A[g+1],E=A[g+2];I[Q]=B-179.456+1.402*E;I[Q+1]=B+135.459-.344*C-.714*E;I[Q+2]=B-226.816+1.772*C;I[Q+3]=255}return I}_convertYcckToRgb(A){let I,g,Q,B,C=0;for(let E=0,i=A.length;E<i;E+=4){I=A[E];g=A[E+1];Q=A[E+2];B=A[E+3];A[C++]=g*(-660635669420364e-19*g+.000437130475926232*Q-54080610064599e-18*I+.00048449797120281*B-.154362151871126)-122.67195406894+Q*(-.000957964378445773*Q+.000817076911346625*I-.00477271405408747*B+1.53380253221734)+I*(.000961250184130688*I-.00266257332283933*B+.48357088451265)+B*(-.000336197177618394*B+.484791561490776);A[C++]=107.268039397724+g*(219927104525741e-19*g-.000640992018297945*Q+.000659397001245577*I+.000426105652938837*B-.176491792462875)+Q*(-.000778269941513683*Q+.00130872261408275*I+.000770482631801132*B-.151051492775562)+I*(.00126935368114843*I-.00265090189010898*B+.25802910206845)+B*(-.000318913117588328*B-.213742400323665);A[C++]=g*(-.000570115196973677*g-263409051004589e-19*Q+.0020741088115012*I-.00288260236853442*B+.814272968359295)-20.810012546947+Q*(-153496057440975e-19*Q-.000132689043961446*I+.000560833691242812*B-.195152027534049)+I*(.00174418132927582*I-.00255243321439347*B+.116935020465145)+B*(-.000343531996510555*B+.24165260232407)}return A.subarray(0,C)}_convertYcckToRgba(A){for(let I=0,g=A.length;I<g;I+=4){const g=A[I],Q=A[I+1],B=A[I+2],C=A[I+3];A[I]=Q*(-660635669420364e-19*Q+.000437130475926232*B-54080610064599e-18*g+.00048449797120281*C-.154362151871126)-122.67195406894+B*(-.000957964378445773*B+.000817076911346625*g-.00477271405408747*C+1.53380253221734)+g*(.000961250184130688*g-.00266257332283933*C+.48357088451265)+C*(-.000336197177618394*C+.484791561490776);A[I+1]=107.268039397724+Q*(219927104525741e-19*Q-.000640992018297945*B+.000659397001245577*g+.000426105652938837*C-.176491792462875)+B*(-.000778269941513683*B+.00130872261408275*g+.000770482631801132*C-.151051492775562)+g*(.00126935368114843*g-.00265090189010898*C+.25802910206845)+C*(-.000318913117588328*C-.213742400323665);A[I+2]=Q*(-.000570115196973677*Q-263409051004589e-19*B+.0020741088115012*g-.00288260236853442*C+.814272968359295)-20.810012546947+B*(-153496057440975e-19*B-.000132689043961446*g+.000560833691242812*C-.195152027534049)+g*(.00174418132927582*g-.00255243321439347*C+.116935020465145)+C*(-.000343531996510555*C+.24165260232407);A[I+3]=255}return A}_convertYcckToCmyk(A){let I,g,Q;for(let B=0,C=A.length;B<C;B+=4){I=A[B];g=A[B+1];Q=A[B+2];A[B]=434.456-I-1.402*Q;A[B+1]=119.541-I+.344*g+.714*Q;A[B+2]=481.816-I-1.772*g}return A}_convertCmykToRgb(A){let I,g,Q,B,C=0;for(let E=0,i=A.length;E<i;E+=4){I=A[E];g=A[E+1];Q=A[E+2];B=A[E+3];A[C++]=255+I*(-6747147073602441e-20*I+.0008379262121013727*g+.0002894718188643294*Q+.003264231057537806*B-1.1185611867203937)+g*(26374107616089405e-21*g-8626949158638572e-20*Q-.0002748769067499491*B-.02155688794978967)+Q*(-3878099212869363e-20*Q-.0003267808279485286*B+.0686742238595345)-B*(.0003361971776183937*B+.7430659151342254);A[C++]=255+I*(.00013596372813588848*I+.000924537132573585*g+.00010567359618683593*Q+.0004791864687436512*B-.3109689587515875)+g*(-.00023545346108370344*g+.0002702845253534714*Q+.0020200308977307156*B-.7488052167015494)+Q*(6834815998235662e-20*Q+.00015168452363460973*B-.09751927774728933)-B*(.0003189131175883281*B+.7364883807733168);A[C++]=255+I*(13598650411385307e-21*I+.00012423956175490851*g+.0004751985097583589*Q-36729317476630422e-22*B-.05562186980264034)+g*(.00016141380598724676*g+.0009692239130725186*Q+.0007782692450036253*B-.44015232367526463)+Q*(5.068882914068769e-7*Q+.0017778369011375071*B-.7591454649749609)-B*(.0003435319965105553*B+.7063770186160144)}return A.subarray(0,C)}_convertCmykToRgba(A){for(let I=0,g=A.length;I<g;I+=4){const g=A[I],Q=A[I+1],B=A[I+2],C=A[I+3];A[I]=255+g*(-6747147073602441e-20*g+.0008379262121013727*Q+.0002894718188643294*B+.003264231057537806*C-1.1185611867203937)+Q*(26374107616089405e-21*Q-8626949158638572e-20*B-.0002748769067499491*C-.02155688794978967)+B*(-3878099212869363e-20*B-.0003267808279485286*C+.0686742238595345)-C*(.0003361971776183937*C+.7430659151342254);A[I+1]=255+g*(.00013596372813588848*g+.000924537132573585*Q+.00010567359618683593*B+.0004791864687436512*C-.3109689587515875)+Q*(-.00023545346108370344*Q+.0002702845253534714*B+.0020200308977307156*C-.7488052167015494)+B*(6834815998235662e-20*B+.00015168452363460973*C-.09751927774728933)-C*(.0003189131175883281*C+.7364883807733168);A[I+2]=255+g*(13598650411385307e-21*g+.00012423956175490851*Q+.0004751985097583589*B-36729317476630422e-22*C-.05562186980264034)+Q*(.00016141380598724676*Q+.0009692239130725186*B+.0007782692450036253*C-.44015232367526463)+B*(5.068882914068769e-7*B+.0017778369011375071*C-.7591454649749609)-C*(.0003435319965105553*C+.7063770186160144);A[I+3]=255}return A}getData({width:A,height:I,forceRGBA:g=!1,forceRGB:Q=!1,isSourcePDF:B=!1}){if(this.numComponents>4)throw new JpegError("Unsupported color mode");const C=this._getLinearizedBlockData(A,I,B);if(1===this.numComponents&&(g||Q)){const A=C.length*(g?4:3),I=new Uint8ClampedArray(A);let Q=0;if(g)!function grayToRGBA(A,I){if(util_FeatureTest.isLittleEndian)for(let g=0,Q=A.length;g<Q;g++)I[g]=65793*A[g]|4278190080;else for(let g=0,Q=A.length;g<Q;g++)I[g]=16843008*A[g]|255}(C,new Uint32Array(I.buffer));else for(const A of C){I[Q++]=A;I[Q++]=A;I[Q++]=A}return I}if(3===this.numComponents&&this._isColorConversionNeeded){if(g){const A=new Uint8ClampedArray(C.length/3*4);return this._convertYccToRgba(C,A)}return this._convertYccToRgb(C)}if(4===this.numComponents){if(this._isColorConversionNeeded)return g?this._convertYcckToRgba(C):Q?this._convertYcckToRgb(C):this._convertYcckToCmyk(C);if(g)return this._convertCmykToRgba(C);if(Q)return this._convertCmykToRgb(C)}return C}}__webpack_require__(3949),__webpack_require__(4979),__webpack_require__(3611);var d,L=(d="file:///C:/pdfjs/botio-files-pdfjs/private/d6919bed03c6677/external/openjpeg/openjpeg.js",function(A={}){var I,g,Q=A;new Promise(((A,Q)=>{I=A;g=Q}));Q.decode=function(A,I){const g=A.length,B=Q._malloc(g);Q.HEAPU8.set(A,B);const C=Q._jp2_decode(B,g,I?1:0);Q._free(B);if(C){const{errorMessages:A}=Q;if(A){delete Q.errorMessages;return A}return"Unknown error"}const{imageData:E}=Q;Q.imageData=null;return E};var B,C=Object.assign({},Q),E="./this.program",i="";"undefined"!=typeof document&&document.currentScript&&(i=document.currentScript.src);d&&(i=d);i=i.startsWith("blob:")?"":i.substr(0,i.replace(/[?#].*/,"").lastIndexOf("/")+1);var o,D,h,s,a,t=Q.print||console.log.bind(console),G=Q.printErr||console.error.bind(console);Object.assign(Q,C);C=null;Q.arguments&&Q.arguments;Q.thisProgram&&(E=Q.thisProgram);Q.quit&&Q.quit;Q.wasmBinary&&(o=Q.wasmBinary);function tryParseAsDataURI(A){if(isDataURI(A))return function intArrayFromBase64(A){for(var I=atob(A),g=new Uint8Array(I.length),Q=0;Q<I.length;++Q)g[Q]=I.charCodeAt(Q);return g}(A.slice(S.length))}function updateMemoryViews(){var A=D.buffer;Q.HEAP8=h=new Int8Array(A);Q.HEAP16=new Int16Array(A);Q.HEAPU8=s=new Uint8Array(A);Q.HEAPU16=new Uint16Array(A);Q.HEAP32=new Int32Array(A);Q.HEAPU32=a=new Uint32Array(A);Q.HEAPF32=new Float32Array(A);Q.HEAPF64=new Float64Array(A)}var e,y=[],c=[],r=[],w=0,R=null,n=null,S="data:application/octet-stream;base64,",isDataURI=A=>A.startsWith(S);e="data:application/octet-stream;base64,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";function instantiateSync(A,I){var g,Q=function getBinarySync(A){if(A==e&&o)return new Uint8Array(o);var I=tryParseAsDataURI(A);if(I)return I;if(B)return B(A);throw'sync fetching of the wasm failed: you can preload it to Module["wasmBinary"] manually, or emcc.py will do that for you when generating HTML (but not JS)'}(A);g=new WebAssembly.Module(Q);return[new WebAssembly.Instance(g,I),g]}var N,callRuntimeCallbacks=A=>{for(;A.length>0;)A.shift()(Q)},k=(Q.noExitRuntime,A=>{var I=(A-D.buffer.byteLength+65535)/65536;try{D.grow(I);updateMemoryViews();return 1}catch(A){}}),J={},getEnvStrings=()=>{if(!getEnvStrings.strings){var A={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:E||"./this.program"};for(var I in J)void 0===J[I]?delete A[I]:A[I]=J[I];var g=[];for(var I in A)g.push(`${I}=${A[I]}`);getEnvStrings.strings=g}return getEnvStrings.strings},U=[null,[],[]],Y="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,UTF8ArrayToString=(A,I,g)=>{for(var Q=I+g,B=I;A[B]&&!(B>=Q);)++B;if(B-I>16&&A.buffer&&Y)return Y.decode(A.subarray(I,B));for(var C="";I<B;){var E=A[I++];if(128&E){var i=63&A[I++];if(192!=(224&E)){var o=63&A[I++];if((E=224==(240&E)?(15&E)<<12|i<<6|o:(7&E)<<18|i<<12|o<<6|63&A[I++])<65536)C+=String.fromCharCode(E);else{var D=E-65536;C+=String.fromCharCode(55296|D>>10,56320|1023&D)}}else C+=String.fromCharCode((31&E)<<6|i)}else C+=String.fromCharCode(E)}return C},printChar=(A,I)=>{var g=U[A];if(0===I||10===I){(1===A?t:G)(UTF8ArrayToString(g,0));g.length=0}else g.push(I)},UTF8ToString=(A,I)=>A?UTF8ArrayToString(s,A,I):"",H={f:(A,I,g)=>s.copyWithin(A,I,I+g),b:A=>{var I=s.length,g=2147483648;if((A>>>=0)>g)return!1;for(var Q,B,C=1;C<=4;C*=2){var E=I*(1+.2/C);E=Math.min(E,A+100663296);var i=Math.min(g,(Q=Math.max(A,E))+((B=65536)-Q%B)%B);if(k(i))return!0}return!1},c:(A,I)=>{var g=0;getEnvStrings().forEach(((Q,B)=>{var C=I+g;a[A+4*B>>2]=C;((A,I)=>{for(var g=0;g<A.length;++g)h[I++]=A.charCodeAt(g);h[I]=0})(Q,C);g+=Q.length+1}));return 0},d:(A,I)=>{var g=getEnvStrings();a[A>>2]=g.length;var Q=0;g.forEach((A=>Q+=A.length+1));a[I>>2]=Q;return 0},e:(A,I,g,Q)=>{for(var B=0,C=0;C<g;C++){var E=a[I>>2],i=a[I+4>>2];I+=8;for(var o=0;o<i;o++)printChar(A,s[E+o]);B+=i}a[Q>>2]=B;return 0},g:function _jsPrintWarning(A){const I=UTF8ToString(A);(Q.warn||console.warn)(`OpenJPEG: ${I}`)},h:function _setImageData(A,I){Q.imageData=new Uint8ClampedArray(Q.HEAPU8.subarray(A,A+I))},a:function _storeErrorMessage(A){const I=UTF8ToString(A);Q.errorMessages?Q.errorMessages+="\n"+I:Q.errorMessages=I}},L=function createWasm(){var A={a:H};function receiveInstance(A,I){L=A.exports;D=L.i;updateMemoryViews();!function addOnInit(A){c.unshift(A)}(L.j);!function removeRunDependency(A){w--;Q.monitorRunDependencies?.(w);if(0==w){if(null!==R){clearInterval(R);R=null}if(n){var I=n;n=null;I()}}}();return L}!function addRunDependency(A){w++;Q.monitorRunDependencies?.(w)}();if(Q.instantiateWasm)try{return Q.instantiateWasm(A,receiveInstance)}catch(A){G(`Module.instantiateWasm callback failed with error: ${A}`);g(A)}return receiveInstance(instantiateSync(e,A)[0])}();L.j,Q._malloc=L.k,Q._free=L.l,Q._jp2_decode=L.n,L._emscripten_stack_restore,L._emscripten_stack_alloc,L.emscripten_stack_get_current;n=function runCaller(){N||run();N||(n=runCaller)};function run(){if(!(w>0)){!function preRun(){if(Q.preRun){"function"==typeof Q.preRun&&(Q.preRun=[Q.preRun]);for(;Q.preRun.length;)A=Q.preRun.shift(),y.unshift(A)}var A;callRuntimeCallbacks(y)}();if(!(w>0))if(Q.setStatus){Q.setStatus("Running...");setTimeout((function(){setTimeout((function(){Q.setStatus("")}),1);doRun()}),1)}else doRun()}function doRun(){if(!N){N=!0;Q.calledRun=!0;!function initRuntime(){callRuntimeCallbacks(c)}();I(Q);Q.onRuntimeInitialized&&Q.onRuntimeInitialized();!function postRun(){if(Q.postRun){"function"==typeof Q.postRun&&(Q.postRun=[Q.postRun]);for(;Q.postRun.length;)A=Q.postRun.shift(),r.unshift(A)}var A;callRuntimeCallbacks(r)}()}}}if(Q.preInit){"function"==typeof Q.preInit&&(Q.preInit=[Q.preInit]);for(;Q.preInit.length>0;)Q.preInit.pop()()}run();return A});const K=L;class Stream extends base_stream_BaseStream{constructor(A,I,g,Q){super();this.bytes=A instanceof Uint8Array?A:new Uint8Array(A);this.start=I||0;this.pos=this.start;this.end=I+g||this.bytes.length;this.dict=Q}get length(){return this.end-this.start}get isEmpty(){return 0===this.length}getByte(){return this.pos>=this.end?-1:this.bytes[this.pos++]}getBytes(A){const I=this.bytes,g=this.pos,Q=this.end;if(!A)return I.subarray(g,Q);let B=g+A;B>Q&&(B=Q);this.pos=B;return I.subarray(g,B)}getByteRange(A,I){A<0&&(A=0);I>this.end&&(I=this.end);return this.bytes.subarray(A,I)}reset(){this.pos=this.start}moveStart(){this.start=this.pos}makeSubStream(A,I,g=null){return new Stream(this.bytes.buffer,A,I,g)}}class JpxError extends g{constructor(A){super(A,"JpxError")}}class JpxImage{static#A=null;static decode(A,I=!1){this.#A||=K({warn:util_warn});const g=this.#A.decode(A,I);if("string"==typeof g)throw new JpxError(g);return g}static cleanup(){this.#A=null}static parseImageProperties(A){if(!(A instanceof ArrayBuffer||ArrayBuffer.isView(A)))throw new JpxError("Invalid data format, must be a TypedArray.");let I=(A=new Stream(A)).getByte();for(;I>=0;){const g=I;I=A.getByte();if(65361===(g<<8|I)){A.skip(4);const I=A.getInt32()>>>0,g=A.getInt32()>>>0,Q=A.getInt32()>>>0,B=A.getInt32()>>>0;A.skip(16);return{width:I-Q,height:g-B,bitsPerComponent:8,componentsCount:A.getUint16()}}}throw new JpxError("No size marker found in JPX stream")}}})();var g=__webpack_exports__.Jbig2Error,Q=__webpack_exports__.Jbig2Image,B=__webpack_exports__.JpegError,C=__webpack_exports__.JpegImage,E=__webpack_exports__.JpxError,i=__webpack_exports__.JpxImage,o=__webpack_exports__.VerbosityLevel,D=__webpack_exports__.getVerbosityLevel,h=__webpack_exports__.setVerbosityLevel;export{g as Jbig2Error,Q as Jbig2Image,B as JpegError,C as JpegImage,E as JpxError,i as JpxImage,o as VerbosityLevel,D as getVerbosityLevel,h as setVerbosityLevel};