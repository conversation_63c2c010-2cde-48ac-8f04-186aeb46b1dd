'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface Class {
  id: number;
  name: string;
}

interface Subject {
  id: number;
  class_id: number;
  name: string;
}

interface CreateBookFormProps {
  classes: Class[];
  subjects: Subject[];
}

export default function CreateBookForm({ classes, subjects }: CreateBookFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    title: '',
    class_id: '',
    subject_id: '',
    description: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Filter subjects based on selected class
  const filteredSubjects = formData.class_id 
    ? subjects.filter(subject => subject.class_id === parseInt(formData.class_id))
    : [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/admin/books', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          class_id: parseInt(formData.class_id),
          subject_id: parseInt(formData.subject_id),
          description: formData.description || undefined
        }),
      });

      const result = await response.json();

      if (result.success) {
        router.push(`/admin/books/${result.bookId}`);
      } else {
        setError(result.error || 'Failed to create book');
      }
    } catch (error) {
      setError('An error occurred while creating the book');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
      // Reset subject when class changes
      ...(name === 'class_id' ? { subject_id: '' } : {})
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700">
          Book Title *
        </label>
        <input
          type="text"
          id="title"
          name="title"
          required
          value={formData.title}
          onChange={handleChange}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          placeholder="e.g., English Textbook Grade 1"
        />
      </div>

      <div>
        <label htmlFor="class_id" className="block text-sm font-medium text-gray-700">
          Class *
        </label>
        <select
          id="class_id"
          name="class_id"
          required
          value={formData.class_id}
          onChange={handleChange}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          <option value="">Select a class</option>
          {classes.map((cls) => (
            <option key={cls.id} value={cls.id}>
              {cls.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="subject_id" className="block text-sm font-medium text-gray-700">
          Subject *
        </label>
        <select
          id="subject_id"
          name="subject_id"
          required
          value={formData.subject_id}
          onChange={handleChange}
          disabled={!formData.class_id}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
        >
          <option value="">
            {formData.class_id ? 'Select a subject' : 'Select a class first'}
          </option>
          {filteredSubjects.map((subject) => (
            <option key={subject.id} value={subject.id}>
              {subject.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleChange}
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          placeholder="Optional description of the textbook content..."
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={() => router.back()}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isSubmitting ? 'Creating...' : 'Create Book'}
        </button>
      </div>
    </form>
  );
}
