import { redirect } from 'next/navigation';
import { requireAdmin, getAllUsers } from '@/lib/auth';
import AdminLayout from '@/components/AdminLayout';
import UsersManager from '@/components/UsersManager';

export default async function UsersPage() {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const users = getAllUsers();

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage admin and student accounts.
          </p>
        </div>

        <UsersManager initialUsers={users} currentUserId={user.id} />
      </div>
    </AdminLayout>
  );
}
