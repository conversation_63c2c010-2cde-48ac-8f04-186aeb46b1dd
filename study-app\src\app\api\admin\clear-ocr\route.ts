import { NextRequest, NextResponse } from 'next/server';
import { clearOCRData } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    clearOCRData();
    return NextResponse.json({ 
      success: true, 
      message: 'OCR data cleared successfully. Re-upload images to get real OCR processing.' 
    });
  } catch (error) {
    console.error('Error clearing OCR data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to clear OCR data' },
      { status: 500 }
    );
  }
}
