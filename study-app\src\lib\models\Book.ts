import db from '../database';

export interface Book {
  id: number;
  title: string;
  class_id: number;
  subject_id: number;
  description?: string;
  cover_image_path?: string;
  total_pages: number;
  status: 'draft' | 'processing' | 'completed';
  created_at: string;
  updated_at: string;
  // Joined fields
  class_name?: string;
  subject_name?: string;
}

export interface BookWithStats extends Book {
  uploaded_pages: number;
  processed_pages: number;
  total_questions: number;
}

export class BookModel {
  static getAll(): BookWithStats[] {
    const books = db.prepare(`
      SELECT 
        b.*,
        c.name as class_name,
        s.name as subject_name,
        COUNT(DISTINCT i.id) as uploaded_pages,
        COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
        COUNT(DISTINCT q.id) as total_questions
      FROM books b
      LEFT JOIN classes c ON b.class_id = c.id
      LEFT JOIN subjects s ON b.subject_id = s.id
      LEFT JOIN images i ON b.id = i.book_id
      LEFT JOIN ocr_text ot ON i.id = ot.image_id
      LEFT JOIN questions q ON i.id = q.image_id
      GROUP BY b.id
      ORDER BY b.updated_at DESC
    `).all() as BookWithStats[];
    
    return books;
  }

  static getById(id: number): BookWithStats | null {
    const book = db.prepare(`
      SELECT 
        b.*,
        c.name as class_name,
        s.name as subject_name,
        COUNT(DISTINCT i.id) as uploaded_pages,
        COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
        COUNT(DISTINCT q.id) as total_questions
      FROM books b
      LEFT JOIN classes c ON b.class_id = c.id
      LEFT JOIN subjects s ON b.subject_id = s.id
      LEFT JOIN images i ON b.id = i.book_id
      LEFT JOIN ocr_text ot ON i.id = ot.image_id
      LEFT JOIN questions q ON i.id = q.image_id
      WHERE b.id = ?
      GROUP BY b.id
    `).get(id) as BookWithStats | undefined;
    
    return book || null;
  }

  static getByClassAndSubject(classId: number, subjectId: number): BookWithStats[] {
    const books = db.prepare(`
      SELECT 
        b.*,
        c.name as class_name,
        s.name as subject_name,
        COUNT(DISTINCT i.id) as uploaded_pages,
        COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
        COUNT(DISTINCT q.id) as total_questions
      FROM books b
      LEFT JOIN classes c ON b.class_id = c.id
      LEFT JOIN subjects s ON b.subject_id = s.id
      LEFT JOIN images i ON b.id = i.book_id
      LEFT JOIN ocr_text ot ON i.id = ot.image_id
      LEFT JOIN questions q ON i.id = q.image_id
      WHERE b.class_id = ? AND b.subject_id = ?
      GROUP BY b.id
      ORDER BY b.updated_at DESC
    `).all(classId, subjectId) as BookWithStats[];
    
    return books;
  }

  static create(data: {
    title: string;
    class_id: number;
    subject_id: number;
    description?: string;
  }): number {
    const result = db.prepare(`
      INSERT INTO books (title, class_id, subject_id, description)
      VALUES (?, ?, ?, ?)
    `).run(data.title, data.class_id, data.subject_id, data.description || null);
    
    return result.lastInsertRowid as number;
  }

  static update(id: number, data: Partial<Book>): boolean {
    const fields = [];
    const values = [];
    
    if (data.title !== undefined) {
      fields.push('title = ?');
      values.push(data.title);
    }
    if (data.description !== undefined) {
      fields.push('description = ?');
      values.push(data.description);
    }
    if (data.cover_image_path !== undefined) {
      fields.push('cover_image_path = ?');
      values.push(data.cover_image_path);
    }
    if (data.total_pages !== undefined) {
      fields.push('total_pages = ?');
      values.push(data.total_pages);
    }
    if (data.status !== undefined) {
      fields.push('status = ?');
      values.push(data.status);
    }
    
    if (fields.length === 0) return false;
    
    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);
    
    const result = db.prepare(`
      UPDATE books SET ${fields.join(', ')} WHERE id = ?
    `).run(...values);
    
    return result.changes > 0;
  }

  static delete(id: number): boolean {
    const result = db.prepare('DELETE FROM books WHERE id = ?').run(id);
    return result.changes > 0;
  }

  static getImages(bookId: number) {
    return db.prepare(`
      SELECT 
        i.*,
        CASE WHEN ot.id IS NOT NULL THEN 1 ELSE 0 END as has_ocr,
        CASE WHEN ot.processed = 1 THEN 1 ELSE 0 END as ocr_processed
      FROM images i
      LEFT JOIN ocr_text ot ON i.id = ot.image_id
      WHERE i.book_id = ?
      ORDER BY i.page_number ASC, i.upload_order ASC
    `).all(bookId);
  }

  static updatePageCount(bookId: number): void {
    const count = db.prepare('SELECT COUNT(*) as count FROM images WHERE book_id = ?').get(bookId) as any;
    db.prepare('UPDATE books SET total_pages = ? WHERE id = ?').run(count.count, bookId);
  }
}
