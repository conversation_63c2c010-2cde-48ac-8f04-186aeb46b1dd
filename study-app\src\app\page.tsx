import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/auth';
import { initializeDatabase, createDefaultAdmin, migrateDatabase } from '@/lib/database';

export default async function Home() {
  // Initialize database on first load
  initializeDatabase();
  migrateDatabase();
  createDefaultAdmin();

  // Check if user is authenticated
  const user = await getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  // Redirect based on user role
  if (user.role === 'admin') {
    redirect('/admin');
  } else {
    redirect('/student');
  }
}
