import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { ClassModel, SubjectModel } from '@/lib/models';
import { getImages } from '@/lib/upload';
import AdminLayout from '@/components/AdminLayout';
import ImageUploader from '@/components/ImageUploader';

export default async function UploadPage() {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const classes = ClassModel.getAll();
  const subjects = SubjectModel.getAll();
  const images = getImages();

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Upload Images</h1>
          <p className="mt-1 text-sm text-gray-600">
            Upload scanned textbook images for OCR processing and question extraction.
          </p>
        </div>

        <ImageUploader 
          classes={classes} 
          subjects={subjects} 
          initialImages={images}
        />
      </div>
    </AdminLayout>
  );
}
