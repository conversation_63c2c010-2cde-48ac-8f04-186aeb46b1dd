{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/node_modules/bcryptjs/umd/index.js"], "sourcesContent": ["// GENERATED FILE. DO NOT EDIT.\n(function (global, factory) {\n  function preferDefault(exports) {\n    return exports.default || exports;\n  }\n  if (typeof define === \"function\" && define.amd) {\n    define([\"crypto\"], function (_crypto) {\n      var exports = {};\n      factory(exports, _crypto);\n      return preferDefault(exports);\n    });\n  } else if (typeof exports === \"object\") {\n    factory(exports, require(\"crypto\"));\n    if (typeof module === \"object\") module.exports = preferDefault(exports);\n  } else {\n    (function () {\n      var exports = {};\n      factory(exports, global.crypto);\n      global.bcrypt = preferDefault(exports);\n    })();\n  }\n})(\n  typeof globalThis !== \"undefined\"\n    ? globalThis\n    : typeof self !== \"undefined\"\n      ? self\n      : this,\n  function (_exports, _crypto) {\n    \"use strict\";\n\n    Object.defineProperty(_exports, \"__esModule\", {\n      value: true,\n    });\n    _exports.compare = compare;\n    _exports.compareSync = compareSync;\n    _exports.decodeBase64 = decodeBase64;\n    _exports.default = void 0;\n    _exports.encodeBase64 = encodeBase64;\n    _exports.genSalt = genSalt;\n    _exports.genSaltSync = genSaltSync;\n    _exports.getRounds = getRounds;\n    _exports.getSalt = getSalt;\n    _exports.hash = hash;\n    _exports.hashSync = hashSync;\n    _exports.setRandomFallback = setRandomFallback;\n    _exports.truncates = truncates;\n    _crypto = _interopRequireDefault(_crypto);\n    function _interopRequireDefault(e) {\n      return e && e.__esModule ? e : { default: e };\n    }\n    /*\n   Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\n   Copyright (c) 2012 Shane Girish <<EMAIL>>\n   Copyright (c) 2025 Daniel Wirtz <<EMAIL>>\n  \n   Redistribution and use in source and binary forms, with or without\n   modification, are permitted provided that the following conditions\n   are met:\n   1. Redistributions of source code must retain the above copyright\n   notice, this list of conditions and the following disclaimer.\n   2. Redistributions in binary form must reproduce the above copyright\n   notice, this list of conditions and the following disclaimer in the\n   documentation and/or other materials provided with the distribution.\n   3. The name of the author may not be used to endorse or promote products\n   derived from this software without specific prior written permission.\n  \n   THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n   IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n   OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n   IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n   INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n   NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n   THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n   (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n   THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n   */\n\n    // The Node.js crypto module is used as a fallback for the Web Crypto API. When\n    // building for the browser, inclusion of the crypto module should be disabled,\n    // which the package hints at in its package.json for bundlers that support it.\n\n    /**\n     * The random implementation to use as a fallback.\n     * @type {?function(number):!Array.<number>}\n     * @inner\n     */\n    var randomFallback = null;\n\n    /**\n     * Generates cryptographically secure random bytes.\n     * @function\n     * @param {number} len Bytes length\n     * @returns {!Array.<number>} Random bytes\n     * @throws {Error} If no random implementation is available\n     * @inner\n     */\n    function randomBytes(len) {\n      // Web Crypto API. Globally available in the browser and in Node.js >=23.\n      try {\n        return crypto.getRandomValues(new Uint8Array(len));\n      } catch {}\n      // Node.js crypto module for non-browser environments.\n      try {\n        return _crypto.default.randomBytes(len);\n      } catch {}\n      // Custom fallback specified with `setRandomFallback`.\n      if (!randomFallback) {\n        throw Error(\n          \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n        );\n      }\n      return randomFallback(len);\n    }\n\n    /**\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n     *  is seeded properly!\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n     * @see http://nodejs.org/api/crypto.html\n     * @see http://www.w3.org/TR/WebCryptoAPI/\n     */\n    function setRandomFallback(random) {\n      randomFallback = random;\n    }\n\n    /**\n     * Synchronously generates a salt.\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n     * @param {number=} seed_length Not supported.\n     * @returns {string} Resulting salt\n     * @throws {Error} If a random fallback is required but not set\n     */\n    function genSaltSync(rounds, seed_length) {\n      rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n      if (typeof rounds !== \"number\")\n        throw Error(\n          \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n        );\n      if (rounds < 4) rounds = 4;\n      else if (rounds > 31) rounds = 31;\n      var salt = [];\n      salt.push(\"$2b$\");\n      if (rounds < 10) salt.push(\"0\");\n      salt.push(rounds.toString());\n      salt.push(\"$\");\n      salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n      return salt.join(\"\");\n    }\n\n    /**\n     * Asynchronously generates a salt.\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n     * @returns {!Promise} If `callback` has been omitted\n     * @throws {Error} If `callback` is present but not a function\n     */\n    function genSalt(rounds, seed_length, callback) {\n      if (typeof seed_length === \"function\")\n        (callback = seed_length), (seed_length = undefined); // Not supported.\n      if (typeof rounds === \"function\")\n        (callback = rounds), (rounds = undefined);\n      if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n      else if (typeof rounds !== \"number\")\n        throw Error(\"illegal arguments: \" + typeof rounds);\n      function _async(callback) {\n        nextTick(function () {\n          // Pretty thin, but salting is fast enough\n          try {\n            callback(null, genSaltSync(rounds));\n          } catch (err) {\n            callback(err);\n          }\n        });\n      }\n      if (callback) {\n        if (typeof callback !== \"function\")\n          throw Error(\"Illegal callback: \" + typeof callback);\n        _async(callback);\n      } else\n        return new Promise(function (resolve, reject) {\n          _async(function (err, res) {\n            if (err) {\n              reject(err);\n              return;\n            }\n            resolve(res);\n          });\n        });\n    }\n\n    /**\n     * Synchronously generates a hash for the given password.\n     * @param {string} password Password to hash\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n     * @returns {string} Resulting hash\n     */\n    function hashSync(password, salt) {\n      if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n      if (typeof salt === \"number\") salt = genSaltSync(salt);\n      if (typeof password !== \"string\" || typeof salt !== \"string\")\n        throw Error(\n          \"Illegal arguments: \" + typeof password + \", \" + typeof salt,\n        );\n      return _hash(password, salt);\n    }\n\n    /**\n     * Asynchronously generates a hash for the given password.\n     * @param {string} password Password to hash\n     * @param {number|string} salt Salt length to generate or salt to use\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n     * @returns {!Promise} If `callback` has been omitted\n     * @throws {Error} If `callback` is present but not a function\n     */\n    function hash(password, salt, callback, progressCallback) {\n      function _async(callback) {\n        if (typeof password === \"string\" && typeof salt === \"number\")\n          genSalt(salt, function (err, salt) {\n            _hash(password, salt, callback, progressCallback);\n          });\n        else if (typeof password === \"string\" && typeof salt === \"string\")\n          _hash(password, salt, callback, progressCallback);\n        else\n          nextTick(\n            callback.bind(\n              this,\n              Error(\n                \"Illegal arguments: \" + typeof password + \", \" + typeof salt,\n              ),\n            ),\n          );\n      }\n      if (callback) {\n        if (typeof callback !== \"function\")\n          throw Error(\"Illegal callback: \" + typeof callback);\n        _async(callback);\n      } else\n        return new Promise(function (resolve, reject) {\n          _async(function (err, res) {\n            if (err) {\n              reject(err);\n              return;\n            }\n            resolve(res);\n          });\n        });\n    }\n\n    /**\n     * Compares two strings of the same length in constant time.\n     * @param {string} known Must be of the correct length\n     * @param {string} unknown Must be the same length as `known`\n     * @returns {boolean}\n     * @inner\n     */\n    function safeStringCompare(known, unknown) {\n      var diff = known.length ^ unknown.length;\n      for (var i = 0; i < known.length; ++i) {\n        diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n      }\n      return diff === 0;\n    }\n\n    /**\n     * Synchronously tests a password against a hash.\n     * @param {string} password Password to compare\n     * @param {string} hash Hash to test against\n     * @returns {boolean} true if matching, otherwise false\n     * @throws {Error} If an argument is illegal\n     */\n    function compareSync(password, hash) {\n      if (typeof password !== \"string\" || typeof hash !== \"string\")\n        throw Error(\n          \"Illegal arguments: \" + typeof password + \", \" + typeof hash,\n        );\n      if (hash.length !== 60) return false;\n      return safeStringCompare(\n        hashSync(password, hash.substring(0, hash.length - 31)),\n        hash,\n      );\n    }\n\n    /**\n     * Asynchronously tests a password against a hash.\n     * @param {string} password Password to compare\n     * @param {string} hashValue Hash to test against\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n     * @returns {!Promise} If `callback` has been omitted\n     * @throws {Error} If `callback` is present but not a function\n     */\n    function compare(password, hashValue, callback, progressCallback) {\n      function _async(callback) {\n        if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n          nextTick(\n            callback.bind(\n              this,\n              Error(\n                \"Illegal arguments: \" +\n                  typeof password +\n                  \", \" +\n                  typeof hashValue,\n              ),\n            ),\n          );\n          return;\n        }\n        if (hashValue.length !== 60) {\n          nextTick(callback.bind(this, null, false));\n          return;\n        }\n        hash(\n          password,\n          hashValue.substring(0, 29),\n          function (err, comp) {\n            if (err) callback(err);\n            else callback(null, safeStringCompare(comp, hashValue));\n          },\n          progressCallback,\n        );\n      }\n      if (callback) {\n        if (typeof callback !== \"function\")\n          throw Error(\"Illegal callback: \" + typeof callback);\n        _async(callback);\n      } else\n        return new Promise(function (resolve, reject) {\n          _async(function (err, res) {\n            if (err) {\n              reject(err);\n              return;\n            }\n            resolve(res);\n          });\n        });\n    }\n\n    /**\n     * Gets the number of rounds used to encrypt the specified hash.\n     * @param {string} hash Hash to extract the used number of rounds from\n     * @returns {number} Number of rounds used\n     * @throws {Error} If `hash` is not a string\n     */\n    function getRounds(hash) {\n      if (typeof hash !== \"string\")\n        throw Error(\"Illegal arguments: \" + typeof hash);\n      return parseInt(hash.split(\"$\")[2], 10);\n    }\n\n    /**\n     * Gets the salt portion from a hash. Does not validate the hash.\n     * @param {string} hash Hash to extract the salt from\n     * @returns {string} Extracted salt part\n     * @throws {Error} If `hash` is not a string or otherwise invalid\n     */\n    function getSalt(hash) {\n      if (typeof hash !== \"string\")\n        throw Error(\"Illegal arguments: \" + typeof hash);\n      if (hash.length !== 60)\n        throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n      return hash.substring(0, 29);\n    }\n\n    /**\n     * Tests if a password will be truncated when hashed, that is its length is\n     * greater than 72 bytes when converted to UTF-8.\n     * @param {string} password The password to test\n     * @returns {boolean} `true` if truncated, otherwise `false`\n     */\n    function truncates(password) {\n      if (typeof password !== \"string\")\n        throw Error(\"Illegal arguments: \" + typeof password);\n      return utf8Length(password) > 72;\n    }\n\n    /**\n     * Continues with the callback on the next tick.\n     * @function\n     * @param {function(...[*])} callback Callback to execute\n     * @inner\n     */\n    var nextTick =\n      typeof process !== \"undefined\" &&\n      process &&\n      typeof process.nextTick === \"function\"\n        ? typeof setImmediate === \"function\"\n          ? setImmediate\n          : process.nextTick\n        : setTimeout;\n\n    /** Calculates the byte length of a string encoded as UTF8. */\n    function utf8Length(string) {\n      var len = 0,\n        c = 0;\n      for (var i = 0; i < string.length; ++i) {\n        c = string.charCodeAt(i);\n        if (c < 128) len += 1;\n        else if (c < 2048) len += 2;\n        else if (\n          (c & 0xfc00) === 0xd800 &&\n          (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n        ) {\n          ++i;\n          len += 4;\n        } else len += 3;\n      }\n      return len;\n    }\n\n    /** Converts a string to an array of UTF8 bytes. */\n    function utf8Array(string) {\n      var offset = 0,\n        c1,\n        c2;\n      var buffer = new Array(utf8Length(string));\n      for (var i = 0, k = string.length; i < k; ++i) {\n        c1 = string.charCodeAt(i);\n        if (c1 < 128) {\n          buffer[offset++] = c1;\n        } else if (c1 < 2048) {\n          buffer[offset++] = (c1 >> 6) | 192;\n          buffer[offset++] = (c1 & 63) | 128;\n        } else if (\n          (c1 & 0xfc00) === 0xd800 &&\n          ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n        ) {\n          c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n          ++i;\n          buffer[offset++] = (c1 >> 18) | 240;\n          buffer[offset++] = ((c1 >> 12) & 63) | 128;\n          buffer[offset++] = ((c1 >> 6) & 63) | 128;\n          buffer[offset++] = (c1 & 63) | 128;\n        } else {\n          buffer[offset++] = (c1 >> 12) | 224;\n          buffer[offset++] = ((c1 >> 6) & 63) | 128;\n          buffer[offset++] = (c1 & 63) | 128;\n        }\n      }\n      return buffer;\n    }\n\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n    /**\n     * bcrypt's own non-standard base64 dictionary.\n     * @type {!Array.<string>}\n     * @const\n     * @inner\n     **/\n    var BASE64_CODE =\n      \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\n        \"\",\n      );\n\n    /**\n     * @type {!Array.<number>}\n     * @const\n     * @inner\n     **/\n    var BASE64_INDEX = [\n      -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n      -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n      -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60,\n      61, 62, 63, -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11,\n      12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1,\n      -1, -1, -1, -1, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41,\n      42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n    ];\n\n    /**\n     * Encodes a byte array to base64 with up to len bytes of input.\n     * @param {!Array.<number>} b Byte array\n     * @param {number} len Maximum input length\n     * @returns {string}\n     * @inner\n     */\n    function base64_encode(b, len) {\n      var off = 0,\n        rs = [],\n        c1,\n        c2;\n      if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n      while (off < len) {\n        c1 = b[off++] & 0xff;\n        rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n        c1 = (c1 & 0x03) << 4;\n        if (off >= len) {\n          rs.push(BASE64_CODE[c1 & 0x3f]);\n          break;\n        }\n        c2 = b[off++] & 0xff;\n        c1 |= (c2 >> 4) & 0x0f;\n        rs.push(BASE64_CODE[c1 & 0x3f]);\n        c1 = (c2 & 0x0f) << 2;\n        if (off >= len) {\n          rs.push(BASE64_CODE[c1 & 0x3f]);\n          break;\n        }\n        c2 = b[off++] & 0xff;\n        c1 |= (c2 >> 6) & 0x03;\n        rs.push(BASE64_CODE[c1 & 0x3f]);\n        rs.push(BASE64_CODE[c2 & 0x3f]);\n      }\n      return rs.join(\"\");\n    }\n\n    /**\n     * Decodes a base64 encoded string to up to len bytes of output.\n     * @param {string} s String to decode\n     * @param {number} len Maximum output length\n     * @returns {!Array.<number>}\n     * @inner\n     */\n    function base64_decode(s, len) {\n      var off = 0,\n        slen = s.length,\n        olen = 0,\n        rs = [],\n        c1,\n        c2,\n        c3,\n        c4,\n        o,\n        code;\n      if (len <= 0) throw Error(\"Illegal len: \" + len);\n      while (off < slen - 1 && olen < len) {\n        code = s.charCodeAt(off++);\n        c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n        code = s.charCodeAt(off++);\n        c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n        if (c1 == -1 || c2 == -1) break;\n        o = (c1 << 2) >>> 0;\n        o |= (c2 & 0x30) >> 4;\n        rs.push(String.fromCharCode(o));\n        if (++olen >= len || off >= slen) break;\n        code = s.charCodeAt(off++);\n        c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n        if (c3 == -1) break;\n        o = ((c2 & 0x0f) << 4) >>> 0;\n        o |= (c3 & 0x3c) >> 2;\n        rs.push(String.fromCharCode(o));\n        if (++olen >= len || off >= slen) break;\n        code = s.charCodeAt(off++);\n        c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n        o = ((c3 & 0x03) << 6) >>> 0;\n        o |= c4;\n        rs.push(String.fromCharCode(o));\n        ++olen;\n      }\n      var res = [];\n      for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n      return res;\n    }\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var BCRYPT_SALT_LEN = 16;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var BLOWFISH_NUM_ROUNDS = 16;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var MAX_EXECUTION_TIME = 100;\n\n    /**\n     * @type {Array.<number>}\n     * @const\n     * @inner\n     */\n    var P_ORIG = [\n      0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n      0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n      0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n    ];\n\n    /**\n     * @type {Array.<number>}\n     * @const\n     * @inner\n     */\n    var S_ORIG = [\n      0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n      0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n      0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n      0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n      0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n      0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n      0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n      0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n      0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n      0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n      0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n      0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n      0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n      0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n      0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n      0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n      0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n      0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n      0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n      0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n      0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n      0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n      0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n      0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n      0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n      0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n      0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n      0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n      0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n      0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n      0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n      0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n      0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n      0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n      0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n      0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n      0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n      0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n      0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n      0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n      0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n      0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n      0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n      0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n      0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n      0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n      0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n      0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n      0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n      0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n      0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n      0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n      0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n      0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n      0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n      0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n      0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n      0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n      0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n      0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n      0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n      0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n      0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n      0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n      0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n      0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n      0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n      0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n      0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n      0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n      0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n      0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n      0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n      0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n      0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n      0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n      0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n      0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n      0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n      0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n      0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n      0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n      0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n      0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n      0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n      0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n      0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n      0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n      0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n      0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n      0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n      0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n      0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n      0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n      0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n      0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n      0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n      0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n      0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n      0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n      0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n      0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n      0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n      0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n      0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n      0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n      0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n      0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n      0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n      0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n      0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n      0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n      0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n      0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n      0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n      0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n      0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n      0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n      0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n      0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n      0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n      0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n      0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n      0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n      0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n      0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n      0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n      0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n      0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n      0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n      0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n      0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n      0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n      0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n      0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n      0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n      0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n      0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n      0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n      0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n      0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n      0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n      0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n      0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n      0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n      0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n      0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n      0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n      0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n      0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n      0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n      0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n      0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n      0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n      0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n      0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n      0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n      0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n      0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n      0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n      0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n      0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n      0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n      0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n      0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n      0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n      0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n      0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n      0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n      0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n      0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n    ];\n\n    /**\n     * @type {Array.<number>}\n     * @const\n     * @inner\n     */\n    var C_ORIG = [\n      0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n    ];\n\n    /**\n     * @param {Array.<number>} lr\n     * @param {number} off\n     * @param {Array.<number>} P\n     * @param {Array.<number>} S\n     * @returns {Array.<number>}\n     * @inner\n     */\n    function _encipher(lr, off, P, S) {\n      // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n      var n,\n        l = lr[off],\n        r = lr[off + 1];\n      l ^= P[0];\n\n      /*\n      for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n          // Feistel substitution on left word\n          n  = S[l >>> 24],\n          n += S[0x100 | ((l >> 16) & 0xff)],\n          n ^= S[0x200 | ((l >> 8) & 0xff)],\n          n += S[0x300 | (l & 0xff)],\n          r ^= n ^ P[++i],\n          // Feistel substitution on right word\n          n  = S[r >>> 24],\n          n += S[0x100 | ((r >> 16) & 0xff)],\n          n ^= S[0x200 | ((r >> 8) & 0xff)],\n          n += S[0x300 | (r & 0xff)],\n          l ^= n ^ P[++i];\n      */\n\n      //The following is an unrolled version of the above loop.\n      //Iteration 0\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[1];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[2];\n      //Iteration 1\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[3];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[4];\n      //Iteration 2\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[5];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[6];\n      //Iteration 3\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[7];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[8];\n      //Iteration 4\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[9];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[10];\n      //Iteration 5\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[11];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[12];\n      //Iteration 6\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[13];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[14];\n      //Iteration 7\n      n = S[l >>> 24];\n      n += S[0x100 | ((l >> 16) & 0xff)];\n      n ^= S[0x200 | ((l >> 8) & 0xff)];\n      n += S[0x300 | (l & 0xff)];\n      r ^= n ^ P[15];\n      n = S[r >>> 24];\n      n += S[0x100 | ((r >> 16) & 0xff)];\n      n ^= S[0x200 | ((r >> 8) & 0xff)];\n      n += S[0x300 | (r & 0xff)];\n      l ^= n ^ P[16];\n      lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n      lr[off + 1] = l;\n      return lr;\n    }\n\n    /**\n     * @param {Array.<number>} data\n     * @param {number} offp\n     * @returns {{key: number, offp: number}}\n     * @inner\n     */\n    function _streamtoword(data, offp) {\n      for (var i = 0, word = 0; i < 4; ++i)\n        (word = (word << 8) | (data[offp] & 0xff)),\n          (offp = (offp + 1) % data.length);\n      return {\n        key: word,\n        offp: offp,\n      };\n    }\n\n    /**\n     * @param {Array.<number>} key\n     * @param {Array.<number>} P\n     * @param {Array.<number>} S\n     * @inner\n     */\n    function _key(key, P, S) {\n      var offset = 0,\n        lr = [0, 0],\n        plen = P.length,\n        slen = S.length,\n        sw;\n      for (var i = 0; i < plen; i++)\n        (sw = _streamtoword(key, offset)),\n          (offset = sw.offp),\n          (P[i] = P[i] ^ sw.key);\n      for (i = 0; i < plen; i += 2)\n        (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n      for (i = 0; i < slen; i += 2)\n        (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n    }\n\n    /**\n     * Expensive key schedule Blowfish.\n     * @param {Array.<number>} data\n     * @param {Array.<number>} key\n     * @param {Array.<number>} P\n     * @param {Array.<number>} S\n     * @inner\n     */\n    function _ekskey(data, key, P, S) {\n      var offp = 0,\n        lr = [0, 0],\n        plen = P.length,\n        slen = S.length,\n        sw;\n      for (var i = 0; i < plen; i++)\n        (sw = _streamtoword(key, offp)),\n          (offp = sw.offp),\n          (P[i] = P[i] ^ sw.key);\n      offp = 0;\n      for (i = 0; i < plen; i += 2)\n        (sw = _streamtoword(data, offp)),\n          (offp = sw.offp),\n          (lr[0] ^= sw.key),\n          (sw = _streamtoword(data, offp)),\n          (offp = sw.offp),\n          (lr[1] ^= sw.key),\n          (lr = _encipher(lr, 0, P, S)),\n          (P[i] = lr[0]),\n          (P[i + 1] = lr[1]);\n      for (i = 0; i < slen; i += 2)\n        (sw = _streamtoword(data, offp)),\n          (offp = sw.offp),\n          (lr[0] ^= sw.key),\n          (sw = _streamtoword(data, offp)),\n          (offp = sw.offp),\n          (lr[1] ^= sw.key),\n          (lr = _encipher(lr, 0, P, S)),\n          (S[i] = lr[0]),\n          (S[i + 1] = lr[1]);\n    }\n\n    /**\n     * Internaly crypts a string.\n     * @param {Array.<number>} b Bytes to crypt\n     * @param {Array.<number>} salt Salt bytes to use\n     * @param {number} rounds Number of rounds\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n     *  omitted, the operation will be performed synchronously.\n     *  @param {function(number)=} progressCallback Callback called with the current progress\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n     * @inner\n     */\n    function _crypt(b, salt, rounds, callback, progressCallback) {\n      var cdata = C_ORIG.slice(),\n        clen = cdata.length,\n        err;\n\n      // Validate\n      if (rounds < 4 || rounds > 31) {\n        err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n        if (callback) {\n          nextTick(callback.bind(this, err));\n          return;\n        } else throw err;\n      }\n      if (salt.length !== BCRYPT_SALT_LEN) {\n        err = Error(\n          \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n        );\n        if (callback) {\n          nextTick(callback.bind(this, err));\n          return;\n        } else throw err;\n      }\n      rounds = (1 << rounds) >>> 0;\n      var P,\n        S,\n        i = 0,\n        j;\n\n      //Use typed arrays when available - huge speedup!\n      if (typeof Int32Array === \"function\") {\n        P = new Int32Array(P_ORIG);\n        S = new Int32Array(S_ORIG);\n      } else {\n        P = P_ORIG.slice();\n        S = S_ORIG.slice();\n      }\n      _ekskey(salt, b, P, S);\n\n      /**\n       * Calcualtes the next round.\n       * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n       * @inner\n       */\n      function next() {\n        if (progressCallback) progressCallback(i / rounds);\n        if (i < rounds) {\n          var start = Date.now();\n          for (; i < rounds; ) {\n            i = i + 1;\n            _key(b, P, S);\n            _key(salt, P, S);\n            if (Date.now() - start > MAX_EXECUTION_TIME) break;\n          }\n        } else {\n          for (i = 0; i < 64; i++)\n            for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n          var ret = [];\n          for (i = 0; i < clen; i++)\n            ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n              ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n              ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n              ret.push((cdata[i] & 0xff) >>> 0);\n          if (callback) {\n            callback(null, ret);\n            return;\n          } else return ret;\n        }\n        if (callback) nextTick(next);\n      }\n\n      // Async\n      if (typeof callback !== \"undefined\") {\n        next();\n\n        // Sync\n      } else {\n        var res;\n        while (true)\n          if (typeof (res = next()) !== \"undefined\") return res || [];\n      }\n    }\n\n    /**\n     * Internally hashes a password.\n     * @param {string} password Password to hash\n     * @param {?string} salt Salt to use, actually never null\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n     *  hashing is performed synchronously.\n     *  @param {function(number)=} progressCallback Callback called with the current progress\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n     * @inner\n     */\n    function _hash(password, salt, callback, progressCallback) {\n      var err;\n      if (typeof password !== \"string\" || typeof salt !== \"string\") {\n        err = Error(\"Invalid string / salt: Not a string\");\n        if (callback) {\n          nextTick(callback.bind(this, err));\n          return;\n        } else throw err;\n      }\n\n      // Validate the salt\n      var minor, offset;\n      if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n        err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n        if (callback) {\n          nextTick(callback.bind(this, err));\n          return;\n        } else throw err;\n      }\n      if (salt.charAt(2) === \"$\")\n        (minor = String.fromCharCode(0)), (offset = 3);\n      else {\n        minor = salt.charAt(2);\n        if (\n          (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n          salt.charAt(3) !== \"$\"\n        ) {\n          err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n          if (callback) {\n            nextTick(callback.bind(this, err));\n            return;\n          } else throw err;\n        }\n        offset = 4;\n      }\n\n      // Extract number of rounds\n      if (salt.charAt(offset + 2) > \"$\") {\n        err = Error(\"Missing salt rounds\");\n        if (callback) {\n          nextTick(callback.bind(this, err));\n          return;\n        } else throw err;\n      }\n      var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n        r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n        rounds = r1 + r2,\n        real_salt = salt.substring(offset + 3, offset + 25);\n      password += minor >= \"a\" ? \"\\x00\" : \"\";\n      var passwordb = utf8Array(password),\n        saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n      /**\n       * Finishes hashing.\n       * @param {Array.<number>} bytes Byte array\n       * @returns {string}\n       * @inner\n       */\n      function finish(bytes) {\n        var res = [];\n        res.push(\"$2\");\n        if (minor >= \"a\") res.push(minor);\n        res.push(\"$\");\n        if (rounds < 10) res.push(\"0\");\n        res.push(rounds.toString());\n        res.push(\"$\");\n        res.push(base64_encode(saltb, saltb.length));\n        res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n        return res.join(\"\");\n      }\n\n      // Sync\n      if (typeof callback == \"undefined\")\n        return finish(_crypt(passwordb, saltb, rounds));\n      // Async\n      else {\n        _crypt(\n          passwordb,\n          saltb,\n          rounds,\n          function (err, bytes) {\n            if (err) callback(err, null);\n            else callback(null, finish(bytes));\n          },\n          progressCallback,\n        );\n      }\n    }\n\n    /**\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n     * @function\n     * @param {!Array.<number>} bytes Byte array\n     * @param {number} length Maximum input length\n     * @returns {string}\n     */\n    function encodeBase64(bytes, length) {\n      return base64_encode(bytes, length);\n    }\n\n    /**\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n     * @function\n     * @param {string} string String to decode\n     * @param {number} length Maximum output length\n     * @returns {!Array.<number>}\n     */\n    function decodeBase64(string, length) {\n      return base64_decode(string, length);\n    }\n    var _default = (_exports.default = {\n      setRandomFallback,\n      genSaltSync,\n      genSalt,\n      hashSync,\n      hash,\n      compareSync,\n      compare,\n      getRounds,\n      getSalt,\n      truncates,\n      encodeBase64,\n      decodeBase64,\n    });\n  },\n);\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,CAAC,SAAU,MAAM,EAAE,OAAO;IACxB,SAAS,cAAc,QAAO;QAC5B,OAAO,SAAQ,OAAO,IAAI;IAC5B;IACA,IAAI,OAAO,WAAW,cAAc,OAAO,GAAG,EAAE;QAC9C,qDAAmB,SAAU,OAAO;YAClC,IAAI,WAAU,CAAC;YACf,QAAQ,UAAS;YACjB,OAAO,cAAc;QACvB;IACF,OAAO,wCAAiC;QACtC,QAAQ;QACR,wCAAgC,OAAO,OAAO,GAAG,cAAc;IACjE,OAAO;;IAMP;AACF,CAAC,EACC,OAAO,eAAe,cAClB,aACA,OAAO,SAAS,cACd,OACA,IAAI,EACV,SAAU,QAAQ,EAAE,OAAO;IACzB;IAEA,OAAO,cAAc,CAAC,UAAU,cAAc;QAC5C,OAAO;IACT;IACA,SAAS,OAAO,GAAG;IACnB,SAAS,WAAW,GAAG;IACvB,SAAS,YAAY,GAAG;IACxB,SAAS,OAAO,GAAG,KAAK;IACxB,SAAS,YAAY,GAAG;IACxB,SAAS,OAAO,GAAG;IACnB,SAAS,WAAW,GAAG;IACvB,SAAS,SAAS,GAAG;IACrB,SAAS,OAAO,GAAG;IACnB,SAAS,IAAI,GAAG;IAChB,SAAS,QAAQ,GAAG;IACpB,SAAS,iBAAiB,GAAG;IAC7B,SAAS,SAAS,GAAG;IACrB,UAAU,uBAAuB;IACjC,SAAS,uBAAuB,CAAC;QAC/B,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;YAAE,SAAS;QAAE;IAC9C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BD,GAEC,+EAA+E;IAC/E,+EAA+E;IAC/E,+EAA+E;IAE/E;;;;KAIC,GACD,IAAI,iBAAiB;IAErB;;;;;;;KAOC,GACD,SAAS,YAAY,GAAG;QACtB,yEAAyE;QACzE,IAAI;YACF,OAAO,OAAO,eAAe,CAAC,IAAI,WAAW;QAC/C,EAAE,OAAM,CAAC;QACT,sDAAsD;QACtD,IAAI;YACF,OAAO,QAAQ,OAAO,CAAC,WAAW,CAAC;QACrC,EAAE,OAAM,CAAC;QACT,sDAAsD;QACtD,IAAI,CAAC,gBAAgB;YACnB,MAAM,MACJ;QAEJ;QACA,OAAO,eAAe;IACxB;IAEA;;;;;;;;KAQC,GACD,SAAS,kBAAkB,MAAM;QAC/B,iBAAiB;IACnB;IAEA;;;;;;KAMC,GACD,SAAS,YAAY,MAAM,EAAE,WAAW;QACtC,SAAS,UAAU;QACnB,IAAI,OAAO,WAAW,UACpB,MAAM,MACJ,wBAAwB,OAAO,SAAS,OAAO,OAAO;QAE1D,IAAI,SAAS,GAAG,SAAS;aACpB,IAAI,SAAS,IAAI,SAAS;QAC/B,IAAI,OAAO,EAAE;QACb,KAAK,IAAI,CAAC;QACV,IAAI,SAAS,IAAI,KAAK,IAAI,CAAC;QAC3B,KAAK,IAAI,CAAC,OAAO,QAAQ;QACzB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC,cAAc,YAAY,kBAAkB,mBAAmB,YAAY;QACrF,OAAO,KAAK,IAAI,CAAC;IACnB;IAEA;;;;;;;KAOC,GACD,SAAS,QAAQ,MAAM,EAAE,WAAW,EAAE,QAAQ;QAC5C,IAAI,OAAO,gBAAgB,YACzB,AAAC,WAAW,aAAe,cAAc,WAAY,iBAAiB;QACxE,IAAI,OAAO,WAAW,YACpB,AAAC,WAAW,QAAU,SAAS;QACjC,IAAI,OAAO,WAAW,aAAa,SAAS;aACvC,IAAI,OAAO,WAAW,UACzB,MAAM,MAAM,wBAAwB,OAAO;QAC7C,SAAS,OAAO,QAAQ;YACtB,SAAS;gBACP,0CAA0C;gBAC1C,IAAI;oBACF,SAAS,MAAM,YAAY;gBAC7B,EAAE,OAAO,KAAK;oBACZ,SAAS;gBACX;YACF;QACF;QACA,IAAI,UAAU;YACZ,IAAI,OAAO,aAAa,YACtB,MAAM,MAAM,uBAAuB,OAAO;YAC5C,OAAO;QACT,OACE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,OAAO,SAAU,GAAG,EAAE,GAAG;gBACvB,IAAI,KAAK;oBACP,OAAO;oBACP;gBACF;gBACA,QAAQ;YACV;QACF;IACJ;IAEA;;;;;KAKC,GACD,SAAS,SAAS,QAAQ,EAAE,IAAI;QAC9B,IAAI,OAAO,SAAS,aAAa,OAAO;QACxC,IAAI,OAAO,SAAS,UAAU,OAAO,YAAY;QACjD,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAClD,MAAM,MACJ,wBAAwB,OAAO,WAAW,OAAO,OAAO;QAE5D,OAAO,MAAM,UAAU;IACzB;IAEA;;;;;;;;;KASC,GACD,SAAS,KAAK,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB;QACtD,SAAS,OAAO,QAAQ;YACtB,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAClD,QAAQ,MAAM,SAAU,GAAG,EAAE,IAAI;gBAC/B,MAAM,UAAU,MAAM,UAAU;YAClC;iBACG,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UACvD,MAAM,UAAU,MAAM,UAAU;iBAEhC,SACE,SAAS,IAAI,CACX,IAAI,EACJ,MACE,wBAAwB,OAAO,WAAW,OAAO,OAAO;QAIlE;QACA,IAAI,UAAU;YACZ,IAAI,OAAO,aAAa,YACtB,MAAM,MAAM,uBAAuB,OAAO;YAC5C,OAAO;QACT,OACE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,OAAO,SAAU,GAAG,EAAE,GAAG;gBACvB,IAAI,KAAK;oBACP,OAAO;oBACP;gBACF;gBACA,QAAQ;YACV;QACF;IACJ;IAEA;;;;;;KAMC,GACD,SAAS,kBAAkB,KAAK,EAAE,OAAO;QACvC,IAAI,OAAO,MAAM,MAAM,GAAG,QAAQ,MAAM;QACxC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACrC,QAAQ,MAAM,UAAU,CAAC,KAAK,QAAQ,UAAU,CAAC;QACnD;QACA,OAAO,SAAS;IAClB;IAEA;;;;;;KAMC,GACD,SAAS,YAAY,QAAQ,EAAE,IAAI;QACjC,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAClD,MAAM,MACJ,wBAAwB,OAAO,WAAW,OAAO,OAAO;QAE5D,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO;QAC/B,OAAO,kBACL,SAAS,UAAU,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG,MACnD;IAEJ;IAEA;;;;;;;;;KASC,GACD,SAAS,QAAQ,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB;QAC9D,SAAS,OAAO,QAAQ;YACtB,IAAI,OAAO,aAAa,YAAY,OAAO,cAAc,UAAU;gBACjE,SACE,SAAS,IAAI,CACX,IAAI,EACJ,MACE,wBACE,OAAO,WACP,OACA,OAAO;gBAIf;YACF;YACA,IAAI,UAAU,MAAM,KAAK,IAAI;gBAC3B,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM;gBACnC;YACF;YACA,KACE,UACA,UAAU,SAAS,CAAC,GAAG,KACvB,SAAU,GAAG,EAAE,IAAI;gBACjB,IAAI,KAAK,SAAS;qBACb,SAAS,MAAM,kBAAkB,MAAM;YAC9C,GACA;QAEJ;QACA,IAAI,UAAU;YACZ,IAAI,OAAO,aAAa,YACtB,MAAM,MAAM,uBAAuB,OAAO;YAC5C,OAAO;QACT,OACE,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,OAAO,SAAU,GAAG,EAAE,GAAG;gBACvB,IAAI,KAAK;oBACP,OAAO;oBACP;gBACF;gBACA,QAAQ;YACV;QACF;IACJ;IAEA;;;;;KAKC,GACD,SAAS,UAAU,IAAI;QACrB,IAAI,OAAO,SAAS,UAClB,MAAM,MAAM,wBAAwB,OAAO;QAC7C,OAAO,SAAS,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IACtC;IAEA;;;;;KAKC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,OAAO,SAAS,UAClB,MAAM,MAAM,wBAAwB,OAAO;QAC7C,IAAI,KAAK,MAAM,KAAK,IAClB,MAAM,MAAM,0BAA0B,KAAK,MAAM,GAAG;QACtD,OAAO,KAAK,SAAS,CAAC,GAAG;IAC3B;IAEA;;;;;KAKC,GACD,SAAS,UAAU,QAAQ;QACzB,IAAI,OAAO,aAAa,UACtB,MAAM,MAAM,wBAAwB,OAAO;QAC7C,OAAO,WAAW,YAAY;IAChC;IAEA;;;;;KAKC,GACD,IAAI,WACF,OAAO,YAAY,eACnB,WACA,OAAO,QAAQ,QAAQ,KAAK,aACxB,OAAO,iBAAiB,aACtB,eACA,QAAQ,QAAQ,GAClB;IAEN,4DAA4D,GAC5D,SAAS,WAAW,MAAM;QACxB,IAAI,MAAM,GACR,IAAI;QACN,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACtC,IAAI,OAAO,UAAU,CAAC;YACtB,IAAI,IAAI,KAAK,OAAO;iBACf,IAAI,IAAI,MAAM,OAAO;iBACrB,IACH,CAAC,IAAI,MAAM,MAAM,UACjB,CAAC,OAAO,UAAU,CAAC,IAAI,KAAK,MAAM,MAAM,QACxC;gBACA,EAAE;gBACF,OAAO;YACT,OAAO,OAAO;QAChB;QACA,OAAO;IACT;IAEA,iDAAiD,GACjD,SAAS,UAAU,MAAM;QACvB,IAAI,SAAS,GACX,IACA;QACF,IAAI,SAAS,IAAI,MAAM,WAAW;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YAC7C,KAAK,OAAO,UAAU,CAAC;YACvB,IAAI,KAAK,KAAK;gBACZ,MAAM,CAAC,SAAS,GAAG;YACrB,OAAO,IAAI,KAAK,MAAM;gBACpB,MAAM,CAAC,SAAS,GAAG,AAAC,MAAM,IAAK;gBAC/B,MAAM,CAAC,SAAS,GAAG,AAAC,KAAK,KAAM;YACjC,OAAO,IACL,CAAC,KAAK,MAAM,MAAM,UAClB,CAAC,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,MAAM,MAAM,QAC/C;gBACA,KAAK,UAAU,CAAC,CAAC,KAAK,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK,MAAM;gBACnD,EAAE;gBACF,MAAM,CAAC,SAAS,GAAG,AAAC,MAAM,KAAM;gBAChC,MAAM,CAAC,SAAS,GAAG,AAAE,MAAM,KAAM,KAAM;gBACvC,MAAM,CAAC,SAAS,GAAG,AAAE,MAAM,IAAK,KAAM;gBACtC,MAAM,CAAC,SAAS,GAAG,AAAC,KAAK,KAAM;YACjC,OAAO;gBACL,MAAM,CAAC,SAAS,GAAG,AAAC,MAAM,KAAM;gBAChC,MAAM,CAAC,SAAS,GAAG,AAAE,MAAM,IAAK,KAAM;gBACtC,MAAM,CAAC,SAAS,GAAG,AAAC,KAAK,KAAM;YACjC;QACF;QACA,OAAO;IACT;IAEA,iFAAiF;IAEjF;;;;;MAKE,GACF,IAAI,cACF,mEAAmE,KAAK,CACtE;IAGJ;;;;MAIE,GACF,IAAI,eAAe;QACjB,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QACrE,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QACrE,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QACtE;QAAI;QAAI;QAAI,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;QAAI;QACpE;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI,CAAC;QAAG,CAAC;QACrE,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QACpE;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;QAAG,CAAC;KAClE;IAED;;;;;;KAMC,GACD,SAAS,cAAc,CAAC,EAAE,GAAG;QAC3B,IAAI,MAAM,GACR,KAAK,EAAE,EACP,IACA;QACF,IAAI,OAAO,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,MAAM,kBAAkB;QAC9D,MAAO,MAAM,IAAK;YAChB,KAAK,CAAC,CAAC,MAAM,GAAG;YAChB,GAAG,IAAI,CAAC,WAAW,CAAC,AAAC,MAAM,IAAK,KAAK;YACrC,KAAK,CAAC,KAAK,IAAI,KAAK;YACpB,IAAI,OAAO,KAAK;gBACd,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;gBAC9B;YACF;YACA,KAAK,CAAC,CAAC,MAAM,GAAG;YAChB,MAAM,AAAC,MAAM,IAAK;YAClB,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;YAC9B,KAAK,CAAC,KAAK,IAAI,KAAK;YACpB,IAAI,OAAO,KAAK;gBACd,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;gBAC9B;YACF;YACA,KAAK,CAAC,CAAC,MAAM,GAAG;YAChB,MAAM,AAAC,MAAM,IAAK;YAClB,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;YAC9B,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;QAChC;QACA,OAAO,GAAG,IAAI,CAAC;IACjB;IAEA;;;;;;KAMC,GACD,SAAS,cAAc,CAAC,EAAE,GAAG;QAC3B,IAAI,MAAM,GACR,OAAO,EAAE,MAAM,EACf,OAAO,GACP,KAAK,EAAE,EACP,IACA,IACA,IACA,IACA,GACA;QACF,IAAI,OAAO,GAAG,MAAM,MAAM,kBAAkB;QAC5C,MAAO,MAAM,OAAO,KAAK,OAAO,IAAK;YACnC,OAAO,EAAE,UAAU,CAAC;YACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;YACxD,OAAO,EAAE,UAAU,CAAC;YACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;YACxD,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;YAC1B,IAAI,AAAC,MAAM,MAAO;YAClB,KAAK,CAAC,KAAK,IAAI,KAAK;YACpB,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;YAC5B,IAAI,EAAE,QAAQ,OAAO,OAAO,MAAM;YAClC,OAAO,EAAE,UAAU,CAAC;YACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;YACxD,IAAI,MAAM,CAAC,GAAG;YACd,IAAI,AAAC,CAAC,KAAK,IAAI,KAAK,MAAO;YAC3B,KAAK,CAAC,KAAK,IAAI,KAAK;YACpB,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;YAC5B,IAAI,EAAE,QAAQ,OAAO,OAAO,MAAM;YAClC,OAAO,EAAE,UAAU,CAAC;YACpB,KAAK,OAAO,aAAa,MAAM,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC;YACxD,IAAI,AAAC,CAAC,KAAK,IAAI,KAAK,MAAO;YAC3B,KAAK;YACL,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;YAC5B,EAAE;QACJ;QACA,IAAI,MAAM,EAAE;QACZ,IAAK,MAAM,GAAG,MAAM,MAAM,MAAO,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QAC7D,OAAO;IACT;IAEA;;;;KAIC,GACD,IAAI,kBAAkB;IAEtB;;;;KAIC,GACD,IAAI,8BAA8B;IAElC;;;;KAIC,GACD,IAAI,sBAAsB;IAE1B;;;;KAIC,GACD,IAAI,qBAAqB;IAEzB;;;;KAIC,GACD,IAAI,SAAS;QACX;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;KAC7D;IAED;;;;KAIC,GACD,IAAI,SAAS;QACX;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;QAAY;QAAY;QAC5D;QAAY;QAAY;QAAY;KACrC;IAED;;;;KAIC,GACD,IAAI,SAAS;QACX;QAAY;QAAY;QAAY;QAAY;QAAY;KAC7D;IAED;;;;;;;KAOC,GACD,SAAS,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAC9B,kEAAkE;QAClE,IAAI,GACF,IAAI,EAAE,CAAC,IAAI,EACX,IAAI,EAAE,CAAC,MAAM,EAAE;QACjB,KAAK,CAAC,CAAC,EAAE;QAET;;;;;;;;;;;;;;MAcA,GAEA,yDAAyD;QACzD,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,aAAa;QACb,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,IAAI,CAAC,CAAC,MAAM,GAAG;QACf,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,KAAM,KAAM;QAClC,KAAK,CAAC,CAAC,QAAS,AAAC,KAAK,IAAK,KAAM;QACjC,KAAK,CAAC,CAAC,QAAS,IAAI,KAAM;QAC1B,KAAK,IAAI,CAAC,CAAC,GAAG;QACd,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,sBAAsB,EAAE;QACxC,EAAE,CAAC,MAAM,EAAE,GAAG;QACd,OAAO;IACT;IAEA;;;;;KAKC,GACD,SAAS,cAAc,IAAI,EAAE,IAAI;QAC/B,IAAK,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE,EACjC,AAAC,OAAO,AAAC,QAAQ,IAAM,IAAI,CAAC,KAAK,GAAG,MACjC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM;QACpC,OAAO;YACL,KAAK;YACL,MAAM;QACR;IACF;IAEA;;;;;KAKC,GACD,SAAS,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC;QACrB,IAAI,SAAS,GACX,KAAK;YAAC;YAAG;SAAE,EACX,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf;QACF,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACxB,AAAC,KAAK,cAAc,KAAK,SACtB,SAAS,GAAG,IAAI,EAChB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG;QACzB,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,UAAU,IAAI,GAAG,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAI,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;QAClE,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,UAAU,IAAI,GAAG,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAI,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IACpE;IAEA;;;;;;;KAOC,GACD,SAAS,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAC9B,IAAI,OAAO,GACT,KAAK;YAAC;YAAG;SAAE,EACX,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf;QACF,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACxB,AAAC,KAAK,cAAc,KAAK,OACtB,OAAO,GAAG,IAAI,EACd,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG;QACzB,OAAO;QACP,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,cAAc,MAAM,OACvB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,cAAc,MAAM,OACzB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,UAAU,IAAI,GAAG,GAAG,IACzB,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EACZ,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;QACrB,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,EACzB,AAAC,KAAK,cAAc,MAAM,OACvB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,cAAc,MAAM,OACzB,OAAO,GAAG,IAAI,EACd,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,EACf,KAAK,UAAU,IAAI,GAAG,GAAG,IACzB,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EACZ,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IACvB;IAEA;;;;;;;;;;KAUC,GACD,SAAS,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACzD,IAAI,QAAQ,OAAO,KAAK,IACtB,OAAO,MAAM,MAAM,EACnB;QAEF,WAAW;QACX,IAAI,SAAS,KAAK,SAAS,IAAI;YAC7B,MAAM,MAAM,sCAAsC;YAClD,IAAI,UAAU;gBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC7B;YACF,OAAO,MAAM;QACf;QACA,IAAI,KAAK,MAAM,KAAK,iBAAiB;YACnC,MAAM,MACJ,0BAA0B,KAAK,MAAM,GAAG,SAAS;YAEnD,IAAI,UAAU;gBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC7B;YACF,OAAO,MAAM;QACf;QACA,SAAS,AAAC,KAAK,WAAY;QAC3B,IAAI,GACF,GACA,IAAI,GACJ;QAEF,iDAAiD;QACjD,IAAI,OAAO,eAAe,YAAY;YACpC,IAAI,IAAI,WAAW;YACnB,IAAI,IAAI,WAAW;QACrB,OAAO;YACL,IAAI,OAAO,KAAK;YAChB,IAAI,OAAO,KAAK;QAClB;QACA,QAAQ,MAAM,GAAG,GAAG;QAEpB;;;;OAIC,GACD,SAAS;YACP,IAAI,kBAAkB,iBAAiB,IAAI;YAC3C,IAAI,IAAI,QAAQ;gBACd,IAAI,QAAQ,KAAK,GAAG;gBACpB,MAAO,IAAI,QAAU;oBACnB,IAAI,IAAI;oBACR,KAAK,GAAG,GAAG;oBACX,KAAK,MAAM,GAAG;oBACd,IAAI,KAAK,GAAG,KAAK,QAAQ,oBAAoB;gBAC/C;YACF,OAAO;gBACL,IAAK,IAAI,GAAG,IAAI,IAAI,IAClB,IAAK,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK,UAAU,OAAO,KAAK,GAAG,GAAG;gBAC9D,IAAI,MAAM,EAAE;gBACZ,IAAK,IAAI,GAAG,IAAI,MAAM,IACpB,IAAI,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,KAAM,IAAI,MAAM,IACrC,IAAI,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,KAAM,IAAI,MAAM,IACvC,IAAI,IAAI,CAAC,CAAC,AAAC,KAAK,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,IACtC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM;gBACnC,IAAI,UAAU;oBACZ,SAAS,MAAM;oBACf;gBACF,OAAO,OAAO;YAChB;YACA,IAAI,UAAU,SAAS;QACzB;QAEA,QAAQ;QACR,IAAI,OAAO,aAAa,aAAa;YACnC;QAEA,OAAO;QACT,OAAO;YACL,IAAI;YACJ,MAAO,KACL,IAAI,OAAO,CAAC,MAAM,MAAM,MAAM,aAAa,OAAO,OAAO,EAAE;QAC/D;IACF;IAEA;;;;;;;;;KASC,GACD,SAAS,MAAM,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB;QACvD,IAAI;QACJ,IAAI,OAAO,aAAa,YAAY,OAAO,SAAS,UAAU;YAC5D,MAAM,MAAM;YACZ,IAAI,UAAU;gBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC7B;YACF,OAAO,MAAM;QACf;QAEA,oBAAoB;QACpB,IAAI,OAAO;QACX,IAAI,KAAK,MAAM,CAAC,OAAO,OAAO,KAAK,MAAM,CAAC,OAAO,KAAK;YACpD,MAAM,MAAM,2BAA2B,KAAK,SAAS,CAAC,GAAG;YACzD,IAAI,UAAU;gBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC7B;YACF,OAAO,MAAM;QACf;QACA,IAAI,KAAK,MAAM,CAAC,OAAO,KACrB,AAAC,QAAQ,OAAO,YAAY,CAAC,IAAM,SAAS;aACzC;YACH,QAAQ,KAAK,MAAM,CAAC;YACpB,IACE,AAAC,UAAU,OAAO,UAAU,OAAO,UAAU,OAC7C,KAAK,MAAM,CAAC,OAAO,KACnB;gBACA,MAAM,MAAM,4BAA4B,KAAK,SAAS,CAAC,GAAG;gBAC1D,IAAI,UAAU;oBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC7B;gBACF,OAAO,MAAM;YACf;YACA,SAAS;QACX;QAEA,2BAA2B;QAC3B,IAAI,KAAK,MAAM,CAAC,SAAS,KAAK,KAAK;YACjC,MAAM,MAAM;YACZ,IAAI,UAAU;gBACZ,SAAS,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC7B;YACF,OAAO,MAAM;QACf;QACA,IAAI,KAAK,SAAS,KAAK,SAAS,CAAC,QAAQ,SAAS,IAAI,MAAM,IAC1D,KAAK,SAAS,KAAK,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,KACtD,SAAS,KAAK,IACd,YAAY,KAAK,SAAS,CAAC,SAAS,GAAG,SAAS;QAClD,YAAY,SAAS,MAAM,SAAS;QACpC,IAAI,YAAY,UAAU,WACxB,QAAQ,cAAc,WAAW;QAEnC;;;;;OAKC,GACD,SAAS,OAAO,KAAK;YACnB,IAAI,MAAM,EAAE;YACZ,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC;YAC1B,IAAI,IAAI,CAAC,OAAO,QAAQ;YACxB,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,cAAc,OAAO,MAAM,MAAM;YAC1C,IAAI,IAAI,CAAC,cAAc,OAAO,OAAO,MAAM,GAAG,IAAI;YAClD,OAAO,IAAI,IAAI,CAAC;QAClB;QAEA,OAAO;QACP,IAAI,OAAO,YAAY,aACrB,OAAO,OAAO,OAAO,WAAW,OAAO;aAEpC;YACH,OACE,WACA,OACA,QACA,SAAU,GAAG,EAAE,KAAK;gBAClB,IAAI,KAAK,SAAS,KAAK;qBAClB,SAAS,MAAM,OAAO;YAC7B,GACA;QAEJ;IACF;IAEA;;;;;;KAMC,GACD,SAAS,aAAa,KAAK,EAAE,MAAM;QACjC,OAAO,cAAc,OAAO;IAC9B;IAEA;;;;;;KAMC,GACD,SAAS,aAAa,MAAM,EAAE,MAAM;QAClC,OAAO,cAAc,QAAQ;IAC/B;IACA,IAAI,WAAY,SAAS,OAAO,GAAG;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}]}