import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { processOCR } from '@/lib/upload';

export async function POST(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { imageId } = await request.json();

    if (!imageId) {
      return NextResponse.json({ error: 'Image ID is required' }, { status: 400 });
    }

    const text = await processOCR(imageId);
    
    return NextResponse.json({ 
      success: true, 
      text: text,
      imageId: imageId
    });
  } catch (error) {
    console.error('OCR processing error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'OCR processing failed' 
    }, { status: 500 });
  }
}
