const CHUNK_PUBLIC_PATH = "server/app/api/admin/images/[id]/ocr/route.js";
const runtime = require("../../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_4d60851a._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_tesseract_js-core_tesseract-core_6659cb20.js");
runtime.loadChunk("server/chunks/node_modules_tesseract_js-core_tesseract-core-lstm_74560cf2.js");
runtime.loadChunk("server/chunks/node_modules_tesseract_js-core_tesseract-core-simd_4a6adfad.js");
runtime.loadChunk("server/chunks/node_modules_tesseract_js-core_tesseract-core-simd-lstm_a3a569e5.js");
runtime.loadChunk("server/chunks/node_modules_tesseract_js-core_tesseract-core_wasm_86684198.js");
runtime.loadChunk("server/chunks/node_modules_f05bd868._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__567c5107._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/images/[id]/ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/images/[id]/ocr/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/images/[id]/ocr/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
