import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { ClassModel, SubjectModel, QuestionModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';

export default async function AdminDashboard() {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  // Get dashboard statistics
  const classes = ClassModel.getAll();
  const subjects = SubjectModel.getAll();
  const questions = QuestionModel.getAll();

  const stats = {
    totalClasses: classes.length,
    totalSubjects: subjects.length,
    totalQuestions: questions.length,
    questionsByType: {
      mcq: questions.filter(q => q.type === 'mcq').length,
      true_false: questions.filter(q => q.type === 'true_false').length,
      fill_blank: questions.filter(q => q.type === 'fill_blank').length,
      short_answer: questions.filter(q => q.type === 'short_answer').length,
      long_answer: questions.filter(q => q.type === 'long_answer').length,
    }
  };

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Welcome back, {user.name}! Here's an overview of your study app.
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">C</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Classes
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.totalClasses}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">S</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Subjects
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.totalSubjects}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">Q</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Questions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.totalQuestions}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">T</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Tests Created
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      0
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Question Types Breakdown */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Questions by Type
            </h3>
            <div className="mt-5">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Multiple Choice (MCQ)</span>
                  <span className="text-sm font-medium text-gray-900">{stats.questionsByType.mcq}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">True/False</span>
                  <span className="text-sm font-medium text-gray-900">{stats.questionsByType.true_false}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Fill in the Blanks</span>
                  <span className="text-sm font-medium text-gray-900">{stats.questionsByType.fill_blank}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Short Answer</span>
                  <span className="text-sm font-medium text-gray-900">{stats.questionsByType.short_answer}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Long Answer</span>
                  <span className="text-sm font-medium text-gray-900">{stats.questionsByType.long_answer}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Quick Actions
            </h3>
            <div className="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <a
                href="/admin/classes"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-700 ring-4 ring-white">
                    <span className="text-lg">📚</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Manage Classes
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Add, edit, or delete classes and subjects
                  </p>
                </div>
              </a>

              <a
                href="/admin/upload"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                    <span className="text-lg">📤</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Upload Images
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Upload textbook images for OCR processing
                  </p>
                </div>
              </a>

              <a
                href="/admin/questions"
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg border border-gray-300 hover:border-gray-400"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                    <span className="text-lg">❓</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Manage Questions
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Review and edit extracted questions
                  </p>
                </div>
              </a>

              <button
                onClick={async () => {
                  if (confirm('This will clear all OCR data and force re-processing. Continue?')) {
                    try {
                      const response = await fetch('/api/admin/clear-ocr', { method: 'POST' });
                      const result = await response.json();
                      if (result.success) {
                        alert('OCR data cleared! Re-upload images to get real OCR processing.');
                        window.location.reload();
                      } else {
                        alert('Error: ' + result.error);
                      }
                    } catch (error) {
                      alert('Error clearing OCR data');
                    }
                  }
                }}
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-red-500 rounded-lg border border-gray-300 hover:border-red-400 text-left"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-red-50 text-red-700 ring-4 ring-white">
                    <span className="text-lg">🔄</span>
                  </span>
                </div>
                <div className="mt-4">
                  <h3 className="text-lg font-medium">
                    Clear OCR Data
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Reset OCR to use real text extraction
                  </p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
