%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (Adobe-GB1-H-Mac)
%%Title: (Adobe-GB1-H-Mac Adobe GB1 0)
%%Version: 4.006
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2015 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/CIDSystemInfo 3 dict dup begin
/Registry (Adobe) def
/Ordering (Adobe_GB1_H_Mac) def
/Supplement 2 def
end def

/CMapName /Adobe-GB1-H-Mac def

/CMapVersion 4.005 def
/CMapType 2 def

/WMode 0 def

1 begincodespacerange
<0000> <FFFF>
endcodespacerange

2 usefont
%Map CID 0 to full width space glyph.
%All other glyphs map to the same CID.
2 begincidrange	
<0000> <FFFF>	0
<0000> <0000>	96
endcidrange	

1 usefont % Prop Roman
1 beginbfrange	
<0001> <005F>	<20>
endbfrange	

0 usefont % Host Font	
100 beginbfrange	
<0060> <00BD>	<A1A1>
<00BE> <00EF>	<A2B1>
<00F0> <00F9>	<A2E5>
<00FA> <00FF>	<A2F1>
<0100> <0105>	<A2F7>
<0106> <0163>	<A3A1>
<0164> <01B6>	<A4A1>
<01B7> <01FF>	<A5A1>
<0200> <020C>	<A5EA>
<020D> <0224>	<A6A1>
<0225> <0259>	<A6C1>
<025A> <027A>	<A7A1>
<027B> <029B>	<A7D1>
<029C> <02BB>	<A8A1>
<02BC> <02E0>	<A8C5>
<02E2> <02FF>	<A9A4>
<0300> <032D>	<A9C2>
<03AC> <03FF>	<B0A1>
<0400> <0409>	<B0F5>
<040A> <0467>	<B1A1>
<0468> <04C5>	<B2A1>
<04C6> <04FF>	<B3A1>
<0500> <0523>	<B3DB>
<0524> <0581>	<B4A1>
<0582> <05DF>	<B5A1>
<05E0> <05FF>	<B6A1>
<0600> <063D>	<B6C1>
<063E> <069B>	<B7A1>
<069C> <06F9>	<B8A1>
<06FA> <06FF>	<B9A1>
<0700> <0757>	<B9A7>
<0758> <07B5>	<BAA1>
<07B6> <07FF>	<BBA1>
<0800> <0813>	<BBEB>
<0814> <0871>	<BCA1>
<0872> <08CF>	<BDA1>
<08D0> <08FF>	<BEA1>
<0900> <092D>	<BED1>
<092E> <098B>	<BFA1>
<098C> <09E9>	<C0A1>
<09EA> <09FF>	<C1A1>
<0A00> <0A47>	<C1B7>
<0A48> <0AA5>	<C2A1>
<0AA6> <0AFF>	<C3A1>
<0B00> <0B03>	<C3FB>
<0B04> <0B61>	<C4A1>
<0B62> <0BBF>	<C5A1>
<0BC0> <0BFF>	<C6A1>
<0C00> <0C1D>	<C6E1>
<0C1E> <0C7B>	<C7A1>
<0C7C> <0CD9>	<C8A1>
<0CDA> <0CFF>	<C9A1>
<0D00> <0D37>	<C9C7>
<0D38> <0D95>	<CAA1>
<0D96> <0DF3>	<CBA1>
<0DF4> <0DFF>	<CCA1>
<0E00> <0E51>	<CCAD>
<0E52> <0EAF>	<CDA1>
<0EB0> <0EFF>	<CEA1>
<0F00> <0F0D>	<CEF1>
<0F0E> <0F6B>	<CFA1>
<0F6C> <0FC9>	<D0A1>
<0FCA> <0FFF>	<D1A1>
<1000> <1027>	<D1D7>
<1028> <1085>	<D2A1>
<1086> <10E3>	<D3A1>
<10E4> <10FF>	<D4A1>
<1100> <1141>	<D4BD>
<1142> <119F>	<D5A1>
<11A0> <11FD>	<D6A1>
<11FE> <11FF>	<D7A1>
<1200> <1256>	<D7A3>
<1257> <12B4>	<D8A1>
<12B5> <12FF>	<D9A1>
<1300> <1312>	<D9EC>
<1313> <1370>	<DAA1>
<1371> <13CE>	<DBA1>
<13CF> <13FF>	<DCA1>
<1400> <142C>	<DCD2>
<142D> <148A>	<DDA1>
<148B> <14E8>	<DEA1>
<14E9> <14FF>	<DFA1>
<1500> <1546>	<DFB8>
<1547> <15A4>	<E0A1>
<15A5> <15FF>	<E1A1>
<1600> <1602>	<E1FC>
<1603> <1660>	<E2A1>
<1661> <16BE>	<E3A1>
<16BF> <16FF>	<E4A1>
<1700> <171C>	<E4E2>
<171D> <177A>	<E5A1>
<177B> <17D8>	<E6A1>
<17D9> <17FF>	<E7A1>
<1800> <1836>	<E7C8>
<1837> <1894>	<E8A1>
<1895> <18F2>	<E9A1>
<18F3> <18FF>	<EAA1>
<1900> <1950>	<EAAE>
<1951> <19AE>	<EBA1>
<19AF> <19FF>	<ECA1>
endbfrange	
18 beginbfrange	
<1A00> <1A0C>	<ECF2>
<1A0D> <1A6A>	<EDA1>
<1A6B> <1AC8>	<EEA1>
<1AC9> <1AFF>	<EFA1>
<1B00> <1B26>	<EFD8>
<1B27> <1B84>	<F0A1>
<1B85> <1BE2>	<F1A1>
<1BE3> <1BFF>	<F2A1>
<1C00> <1C40>	<F2BE>
<1C41> <1C9E>	<F3A1>
<1C9F> <1CFC>	<F4A1>
<1CFD> <1CFF>	<F5A1>
<1D00> <1D5A>	<F5A4>
<1D5B> <1DB8>	<F6A1>
<1DB9> <1DFF>	<F7A1>
<1E00> <1E16>	<F7E8>
<1E20> <1E20>	<0080>
<1E21> <1E23>	<00FD>
endbfrange
2 beginbfrange
<026F> <026F>	<A7B7>
<0270> <0270>	<A7B6>
endbfrange

endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
