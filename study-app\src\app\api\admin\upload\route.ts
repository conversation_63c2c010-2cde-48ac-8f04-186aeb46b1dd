import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { saveUploadedFile } from '@/lib/upload';

export async function POST(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const classId = parseInt(formData.get('classId') as string);
    const subjectId = parseInt(formData.get('subjectId') as string);

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!classId || !subjectId) {
      return NextResponse.json({ error: 'Class ID and Subject ID are required' }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 });
    }

    const result = await saveUploadedFile(file, classId, subjectId);
    
    // Get the saved image with class and subject names
    const db = require('@/lib/database').default;
    const image = db.prepare(`
      SELECT i.*, c.name as class_name, s.name as subject_name
      FROM images i
      JOIN classes c ON i.class_id = c.id
      JOIN subjects s ON i.subject_id = s.id
      WHERE i.id = ?
    `).get(result.id);

    return NextResponse.json({ 
      success: true, 
      image: {
        ...image,
        uploaded_at: image.uploaded_at || new Date().toISOString()
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Upload failed' 
    }, { status: 500 });
  }
}
